{"folders": [{"path": "."}, {"path": "server"}, {"path": "client"}], "settings": {"typescript.tsdk": "node_modules/typescript/lib", "php.version": "8.3", "intelephense.environment.phpVersion": "8.3", "phpstan.singleFileMode": false, "phpstan.binPath": "vendor/bin/phpstan", "phpstan.configFile": "phpstan.neon", "phpstan.paths": {"/Users/<USER>/git/clubmanager": "/var/www/html"}, "phpstan.rootDir": "/var/www/html/server", "phpstan.options": [], "phpstan.enableStatusBar": true, "phpstan.memoryLimit": "1G", "phpstan.enabled": true, "phpstan.projectTimeout": 300000, "phpstan.timeout": 300000, "phpstan.suppressTimeoutMessage": false, "phpstan.showProgress": false, "phpstan.showTypeOnHover": false, "phpstan.enableLanguageServer": false, "phpstan.ignoreErrors": ["Xdebug: .*"], "phpstan.suppressWorkspaceMessage": false, "phpstan.pro": false, "phpstan.tmpDir": "", "phpstan.checkValidity": false, "phpstan.dockerContainerName": ""}}