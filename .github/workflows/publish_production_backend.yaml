name: Publish Production Backend
run-name: Publishing backend from ${{ github.ref }} to production
on: [workflow_dispatch, workflow_call]
jobs:
  build-docker-image:
    outputs:
      hash: ${{ steps.calc_hash.outputs.hash }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Install docker
        working-directory: ./server
        run: make install-docker

      - name: Build and push deploy image
        working-directory: ./server
        run: make create-deploy-image
        env:
          DOCKER_HUB_USER: ${{ secrets.DOCKER_HUB_USER }}
          DOCKER_HUB_TOKEN: ${{ secrets.DOCKER_HUB_TOKEN }}

      - name: Calculate Dockerfile hash
        working-directory: ./server
        id: calc_hash
        run: echo "hash=$(git hash-object Dockerfile | cut -c1-7)" >> "$GITHUB_OUTPUT"

  publish-production-backend:
    needs: build-docker-image
    runs-on: ubuntu-latest
    container:
      image: "fabzo/images:${{ needs.build-docker-image.outputs.hash }}"
      credentials:
        username: ${{ secrets.DOCKER_HUB_USER }}
        password: ${{ secrets.DOCKER_HUB_TOKEN }}
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'

      - name: Build backend
        working-directory: ./server
        run: make build-production
        env:
          PRODUCTION_SERVER_ENV_PASS: ${{ secrets.PRODUCTION_SERVER_ENV_PASS }}

      - name: Push production
        working-directory: ./server
        run: make push-production
        env:
          IONOS_MAIN_ACCOUNT_USER: ${{ secrets.IONOS_MAIN_ACCOUNT_USER }}
          IONOS_MAIN_ACCOUNT_PASS_RCLONE: ${{ secrets.IONOS_MAIN_ACCOUNT_PASS_RCLONE }}

      - name: Update production
        working-directory: ./server
        run: make update-production
        env:
          IONOS_MAIN_ACCOUNT_USER: ${{ secrets.IONOS_MAIN_ACCOUNT_USER }}
          IONOS_MAIN_ACCOUNT_PASS: ${{ secrets.IONOS_MAIN_ACCOUNT_PASS }}

      - name: Run database mirations
        working-directory: ./server
        run: make migrate-production
        env:
          IONOS_MAIN_ACCOUNT_USER: ${{ secrets.IONOS_MAIN_ACCOUNT_USER }}
          IONOS_MAIN_ACCOUNT_PASS: ${{ secrets.IONOS_MAIN_ACCOUNT_PASS }}

      - name: Change Ownership of Workspace
        run: sudo chown -R $(id -u):$(id -g) .

      - name: Setup Git
        run: |
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"

      - name: Tag Commit
        run: |
          git tag -f prod_backend
          git push -f origin prod_backend
