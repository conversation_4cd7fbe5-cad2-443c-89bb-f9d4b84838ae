name: Create deploy image
run-name: Creating deploy image from ${{ github.ref }}
on: [workflow_dispatch]
jobs:
  create-deploy-image:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Install docker
        working-directory: ./server
        run: make install-docker

      - name: Build and push deploy image
        working-directory: ./server
        run: make create-deploy-image
        env:
          DOCKER_HUB_USER: ${{ secrets.DOCKER_HUB_USER }}
          DOCKER_HUB_TOKEN: ${{ secrets.DOCKER_HUB_TOKEN }}
