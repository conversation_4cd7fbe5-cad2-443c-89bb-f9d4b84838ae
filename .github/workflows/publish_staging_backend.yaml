name: Publish Staging Backend
run-name: Publishing backend from ${{ github.ref }} to staging
on: [workflow_dispatch, workflow_call]
jobs:
  build-docker-image:
    outputs:
      hash: ${{ steps.calc_hash.outputs.hash }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Install docker
        working-directory: ./server
        run: make install-docker

      - name: Build and push deploy image
        working-directory: ./server
        run: make create-deploy-image
        env:
          DOCKER_HUB_USER: ${{ secrets.DOCKER_HUB_USER }}
          DOCKER_HUB_TOKEN: ${{ secrets.DOCKER_HUB_TOKEN }}

      - name: Calculate Dockerfile hash
        working-directory: ./server
        id: calc_hash
        run: echo "hash=$(git hash-object Dockerfile | cut -c1-7)" >> "$GITHUB_OUTPUT"

  publish-staging-backend:
    needs: build-docker-image
    runs-on: ubuntu-latest
    container:
      image: "fabzo/images:${{ needs.build-docker-image.outputs.hash }}"
      credentials:
        username: ${{ secrets.DOCKER_HUB_USER }}
        password: ${{ secrets.DOCKER_HUB_TOKEN }}
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'

      - name: Build backend
        working-directory: ./server
        run: make build-staging
        env:
          STAGING_SERVER_ENV_PASS: ${{ secrets.STAGING_SERVER_ENV_PASS }}

      - name: Push staging
        working-directory: ./server
        run: make push-staging
        env:
          IONOS_MAIN_ACCOUNT_USER: ${{ secrets.IONOS_MAIN_ACCOUNT_USER }}
          IONOS_MAIN_ACCOUNT_PASS_RCLONE: ${{ secrets.IONOS_MAIN_ACCOUNT_PASS_RCLONE }}

      - name: Update staging
        working-directory: ./server
        run: make update-staging
        env:
          IONOS_MAIN_ACCOUNT_USER: ${{ secrets.IONOS_MAIN_ACCOUNT_USER }}
          IONOS_MAIN_ACCOUNT_PASS: ${{ secrets.IONOS_MAIN_ACCOUNT_PASS }}

      - name: Run database mirations
        working-directory: ./server
        run: make migrate-staging
        env:
          IONOS_MAIN_ACCOUNT_USER: ${{ secrets.IONOS_MAIN_ACCOUNT_USER }}
          IONOS_MAIN_ACCOUNT_PASS: ${{ secrets.IONOS_MAIN_ACCOUNT_PASS }}

      - name: Change Ownership of Workspace
        run: sudo chown -R $(id -u):$(id -g) .

      - name: Setup Git
        run: |
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"

      - name: Tag Commit
        run: |
          git tag -f staging_backend
          git push -f origin staging_backend
