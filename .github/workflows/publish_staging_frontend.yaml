name: Publish Staging Frontend
run-name: Publishing frontend from ${{ github.ref }} to staging
on: [workflow_dispatch, workflow_call]
jobs:
  publish-staging-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'

      - name: Download dependencies
        working-directory: ./client
        run: yarn

      - name: Build frontend
        working-directory: ./client
        run: yarn build
        env:
          MODE: staging

      - name: Install rclone
        run: sudo -v ; curl https://rclone.org/install.sh | sudo bash

      - name: Push staging
        working-directory: ./client
        run: make push-staging
        env:
          IONOS_MAIN_ACCOUNT_USER: ${{ secrets.IONOS_MAIN_ACCOUNT_USER }}
          IONOS_MAIN_ACCOUNT_PASS: ${{ secrets.IONOS_MAIN_ACCOUNT_PASS }}
          IONOS_MAIN_ACCOUNT_PASS_RCLONE: ${{ secrets.IONOS_MAIN_ACCOUNT_PASS_RCLONE }}

      - name: Update staging
        working-directory: ./client
        run: make update-staging
        env:
          IONOS_MAIN_ACCOUNT_USER: ${{ secrets.IONOS_MAIN_ACCOUNT_USER }}
          IONOS_MAIN_ACCOUNT_PASS: ${{ secrets.IONOS_MAIN_ACCOUNT_PASS }}

      - name: Setup Git
        run: |
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"

      - name: Tag Commit
        run: |
          git tag -f staging_frontend
          git push -f origin staging_frontend

      - uses: jwalton/gh-find-current-pr@v1
        id: finder
      - name: Sticky Pull Request Commen
        uses: marocchino/sticky-pull-request-comment@v2.3.1
        with:
            number: ${{ steps.finder.outputs.pr }}
            message: |
                Frontend uploaded to https://app-staging.numo-app.com
