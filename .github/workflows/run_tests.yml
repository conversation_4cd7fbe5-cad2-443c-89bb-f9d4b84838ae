name: Run Tests
run-name: Running tests of ${{ github.ref }}

on:
  pull_request:
    branches:
      - main
    paths:
      - 'server/**'
      - 'client/**'
    types:
      - opened
      - synchronize
      - reopened

jobs:
  build-docker-image:
    outputs:
      hash: ${{ steps.calc_hash.outputs.hash }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Install docker
        working-directory: ./server
        run: make install-docker

      - name: Build and push deploy image
        working-directory: ./server
        run: make create-deploy-image
        env:
          DOCKER_HUB_USER: ${{ secrets.DOCKER_HUB_USER }}
          DOCKER_HUB_TOKEN: ${{ secrets.DOCKER_HUB_TOKEN }}

      - name: Calculate Dockerfile hash
        working-directory: ./server
        id: calc_hash
        run: echo "hash=$(git hash-object Dockerfile | cut -c1-7)" >> "$GITHUB_OUTPUT"

  run-backend-and-frontend-tests:
    needs: build-docker-image
    runs-on: ubuntu-latest
    services:
      mysql:
        image: mariadb:10.11
        env:
          MARIADB_ROOT_PASSWORD: root
          MARIADB_DATABASE: laravel_test
        ports:
          - 3306:3306
    container:
      image: "fabzo/images:${{ needs.build-docker-image.outputs.hash }}"
      credentials:
        username: ${{ secrets.DOCKER_HUB_USER }}
        password: ${{ secrets.DOCKER_HUB_TOKEN }}
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'

      - name: Wait for MySQL
        run: |
          while ! mysqladmin ping -h"mysql" -P"3306" --silent; do
            sleep 1
          done

      - name: Install Composer Dependencies
        working-directory: ./server
        run: composer install

#      - name: Dump tables
#        working-directory: ./server
#        env:
#          DB_CONNECTION: mysql
#          DB_HOST: mysql
#          DB_PORT: 3306
#          DB_DATABASE: laravel_test
#          DB_USERNAME: root
#          DB_PASSWORD: root
#        run: |
#          php artisan migrate
#          mysqldump -P3306 -hmysql -uroot -proot laravel_test users

#      - name: Run Laravel Migrations
#        working-directory: ./server
#        env:
#          DB_CONNECTION: mysql
#          DB_HOST: mysql
#          DB_PORT: 3306
#          DB_DATABASE: laravel_test
#          DB_USERNAME: root
#          DB_PASSWORD: root
#        run: php artisan migrate

# Keeping this here as we might want to reuse this in the future
#      - name: Update Models Code Style
#        working-directory: ./server
#        run: ./vendor/bin/php-cs-fixer fix app/Models

#      - name: Wipe database again
#        working-directory: ./server
#        env:
#          DB_CONNECTION: mysql
#          DB_HOST: mysql
#          DB_PORT: 3306
#          DB_DATABASE: laravel_test
#          DB_USERNAME: root
#          DB_PASSWORD: root
#        run: php artisan db:wipe

      - name: Run Laravel Migrations for tests
        working-directory: ./server
        env:
          DB_CONNECTION: mysql
          DB_HOST: mysql
          DB_PORT: 3306
          DB_DATABASE: laravel_test
          DB_USERNAME: root
          DB_PASSWORD: root
        run: php artisan migrate

      # Run locally: ddev exec ./server/vendor/bin/phpstan analyse /var/www/html/server/app -c ./server/phpstan.neon
      - name: Run PHPStan
        working-directory: ./server
        run: ./vendor/bin/phpstan analyse -c phpstan.neon

      - name: Install yarn Dependencies
        working-directory: ./server
        run: yarn install

      - name: Yarn build
        working-directory: ./server
        run: yarn build

      - name: Run Backend Tests
        working-directory: ./server
        env:
          DB_CONNECTION: mysql
          DB_HOST: mysql
          DB_PORT: 3306
          DB_DATABASE: laravel_test
          DB_USERNAME: root
          DB_PASSWORD: root
        run: php artisan test

      - name: Start PHP backend for frontend tests
        working-directory: ./server
        env:
          DB_CONNECTION: mysql
          DB_HOST: mysql
          DB_PORT: 3306
          DB_DATABASE: laravel_test
          DB_TEST_DATABASE: laravel_test
          DB_USERNAME: root
          DB_PASSWORD: root
          APP_URL: 'http://localhost:8000'
          APP_API_URL: 'http://api.localhost:8000'
          APP_FRONTEND_URL: 'http://127.0.0.1:9002'
          MAIL_MAILER: array
          QUEUE_CONNECTION: sync
          CACHE_DRIVER: array
        run: |
          php artisan serve --port=8000 &

      - name: Cypress and frontend install
        uses: cypress-io/github-action@v6
        with:
          runTests: false
          working-directory: ./client
          build: yarn install

      - name: Run frontend tests
        uses: cypress-io/github-action@v6
        with:
          install: false
          working-directory: ./client
          build: yarn build --debug /tmp/quasar.log 2>&1 &
          start: yarn serve dist/spa -p 9002 --history --hostname 127.0.0.1 /tmp/quasar.log 2>&1 &
          wait-on: "http://127.0.0.1:9002, http://localhost:8000"
        env:
          #          DEBUG: cypress:*   #show cypress debug output
          CYPRESS_CI: true
          APP_INVITE_URL: 'http://127.0.0.1:9002'
          APP_API_URL: 'http://api.localhost:8000'
          CYPRESS_APP_BACKEND_URL: 'http://localhost:8000'


      - name: Archive failed frontend test screenshots
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-failed-screenshots
          path: client/test/cypress/screenshots/**/*.*

      - name: Archive failed frontend test videos
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-failed-videos
          path: client/test/cypress/videos/**/*.*

      - name: Archive Quasar logs
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: quasar-logs
          path: /tmp/quasar.log
