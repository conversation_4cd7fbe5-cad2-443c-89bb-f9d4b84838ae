name: Build Docker Image
on:
  workflow_call:
    outputs:
      hash:
        description: "The hash of the Dockerfile"
        value: ${{ jobs.calculate.outputs.hash }}
    secrets:
      DOCKER_HUB_USER:
        required: true
      DOCKER_HUB_TOKEN:
        required: true

jobs:
  create-deploy-image:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Install docker
        working-directory: ./server
        run: make install-docker

      - name: Build and push deploy image
        working-directory: ./server
        run: make create-deploy-image
        env:
          DOCKER_HUB_USER: ${{ secrets.DOCKER_HUB_USER }}
          DOCKER_HUB_TOKEN: ${{ secrets.DOCKER_HUB_TOKEN }}

  calculate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Calculate Dockerfile hash
        id: calc_hash
        run: echo "::set-output name=hash::$(git hash-object Dockerfile | cut -c1-7)"