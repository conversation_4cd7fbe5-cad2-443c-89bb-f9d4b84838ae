#!/bin/bash

docker login -u $DOCKER_HUB_USER -p $DOCKER_HUB_TOKEN

# This image is public, don't add any secrets to it!
# Generate a short hash of the Dockerfile
DOCKERFILE_HASH=$(git hash-object Dockerfile | cut -c1-7)
IMAGE_NAME=fabzo/images:$DOCKERFILE_HASH

# Check if the Docker image is available on Docker Hub
IMAGE_ON_DOCKER_HUB=$(docker manifest inspect $IMAGE_NAME > /dev/null ; echo $?)

echo "Docker response: $IMAGE_ON_DOCKER_HUB"


if [[ $IMAGE_ON_DOCKER_HUB == 1 ]]; then
  # Docker image is not available on Docker Hub, build it
  docker build --progress=plain -t $IMAGE_NAME .
  docker push $IMAGE_NAME
else
  # Docker image is available on Docker Hub, pull it
  # docker pull $IMAGE_NAME
  echo "Image $IMAGE_NAME already exists on Docker Hub."
fi
