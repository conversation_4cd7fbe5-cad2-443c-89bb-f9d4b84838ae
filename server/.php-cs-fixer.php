<?php

$finder = PhpCsFixer\Finder::create()
    ->in(__DIR__);

return (new PhpCsFixer\Config())
    ->setRules([
        'braces' => [
            'position_after_functions_and_oop_constructs' => 'same',
            'position_after_control_structures'           => 'same',
            'position_after_anonymous_constructs'         => 'same',
            'allow_single_line_closure'                   => true,
        ],

        'binary_operator_spaces' => [
            'default'   => null,
            'operators' => ['=>' => 'align_single_space'],
        ],

//        'phpdoc_align' => [
//            'tags' => ['param', 'return', 'throws', 'var', 'type', 'property', 'property-read', 'property-write'],
//        ],

        'blank_line_between_import_groups' => false,

        'no_extra_blank_lines' => [
            'tokens' => [
                'extra',
            ],
        ],

        //'array_syntax'         => ['syntax' => 'short'],  // Use short array syntax
        //'no_unused_imports'    => true,                   // Remove unused use statements
        //'single_quote'         => true,                   // Use single quotes for strings
    ])
    ->setFinder($finder);

