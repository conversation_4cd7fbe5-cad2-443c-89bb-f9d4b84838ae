<?php

namespace Database\Factories;

use App\Models\Person;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory {
    public function definition(): array {
        return [
            'email' => fake()->unique()->safeEmail(),
            'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
        ];
    }

    public function verified(): Factory|UserFactory {
        return $this->state(fn(array $attributes) => [
            'email_verified_at' => now(),
        ]);
    }

    public function withPerson(Person $person = null): Factory|UserFactory {
        if ($person === null) {
            $person = Person::factory()->create();
        }

        return $this->state(fn(array $attributes) => [
            'person_id' => $person->id,
        ]);
    }
}
