<?php

namespace Database\Seeders;

use App\Models\Person;
use App\Models\Team;
use App\Models\TeamMember;
use App\Models\User;
use App\Types\TeamRoleType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class CypressSeeder extends Seeder {
    // Emails
    static string $EMAIL_CYPRESS = '<EMAIL>';
    static string $EMAIL_CYPRESS_TEAM_MANAGER = '<EMAIL>';
    static string $EMAIL_CYPRESS_NO_TEAM = '<EMAIL>';
    static string $EMAIL_CYPRESS_EMAIL_VERIFY = '<EMAIL>';

    // Teams
    static string $TEAM_CYPRESS = 'Cypress Team';

    // Team Members
    static string $TEAM_MEMBER_CYPRESS_MANAGER = 'Cypress Manager';
    static string $TEAM_MEMBER_CYPRESS_USER = 'Cypress User';

    /**
     * Run the database seeds.
     */
    public function run(): void {
        $cleanupOnly = getenv('CYPRESS_CLEANUP_ONLY');

        $this->cleanUp();

        if ($cleanupOnly) {
            return;
        }
        $this->recreate();
    }

    function cleanUp(): void {
        $this->deleteUserWithPerson(self::$EMAIL_CYPRESS);
        $this->deleteUserWithPerson(self::$EMAIL_CYPRESS_TEAM_MANAGER);
        $this->deleteUserWithPerson(self::$EMAIL_CYPRESS_NO_TEAM);
        $this->deleteUserWithPerson(self::$EMAIL_CYPRESS_EMAIL_VERIFY);
        $this->deleteTeam(self::$TEAM_CYPRESS); // Note: This will also delete all team members
    }

    function recreate(): void {
        // Normal user
        $this->createUserWithPerson(self::$EMAIL_CYPRESS, 'Mr', 'Cypress');
        $this->verifyEmail(self::$EMAIL_CYPRESS);

        // Team manager
        $this->createUserWithPerson(self::$EMAIL_CYPRESS_TEAM_MANAGER, 'Mr', 'Cypress');
        $this->verifyEmail(self::$EMAIL_CYPRESS_TEAM_MANAGER);

        // Normal user without team
        $this->createUserWithPerson(self::$EMAIL_CYPRESS_NO_TEAM, 'Mr', 'Cypress');
        $this->verifyEmail(self::$EMAIL_CYPRESS_NO_TEAM);

        // Normal user stuck in email verification
        $this->createUserWithPerson(self::$EMAIL_CYPRESS_EMAIL_VERIFY, 'Mr', 'Cypress');

        // Team
        $this->createTeam(self::$TEAM_CYPRESS);
        $this->createTeamMember(self::$TEAM_CYPRESS, self::$TEAM_MEMBER_CYPRESS_MANAGER, TeamRoleType::MANAGER);
        $this->createTeamMember(self::$TEAM_CYPRESS, self::$TEAM_MEMBER_CYPRESS_USER);

        $this->associateTeamMemberWithUser(self::$TEAM_MEMBER_CYPRESS_MANAGER, self::$EMAIL_CYPRESS_TEAM_MANAGER);
        $this->associateTeamMemberWithUser(self::$TEAM_MEMBER_CYPRESS_USER, self::$EMAIL_CYPRESS);
    }

    function deleteUserWithPerson(string $email): void {
        DB::transaction(function () use ($email) {
            $user = User::where('email', $email)->first();
            if ($user) {
                $user->person()->delete();
                $user->delete();
            }
        });
    }

    function createUserWithPerson(string $email, string $firstname, string $lastname): void {
        $person = Person::create([
            'firstname' => $firstname,
            'lastname'  => $lastname,
        ]);

        User::create([
            'email'     => $email,
            'password'  => Hash::make('12345678'),
            'person_id' => $person->id,
        ]);
    }

    function verifyEmail(string $email): void {
        $user = User::where('email', $email)->first();
        $user->email_verified_at = now();
        $user->email_verification_token = null;
        $user->save();
    }

    function createTeam(string $name): void {
        Team::create([
            'name' => $name,
        ]);
    }

    function deleteTeam(string $name): void {
        Team::where('name', $name)->delete();
    }

    private function createTeamMember(string $teamName, string $name, TeamRoleType $role = TeamRoleType::MEMBER): void {
        $team = Team::where('name', $teamName)->first();
        $team->createMember($name, $role);
    }

    private function associateTeamMemberWithUser(string $memberName, string $email): void {
        $user = User::where('email', $email)->first();
        $member = TeamMember::where('name', $memberName)->first();
        $user->person->teamMembers()->save($member);
    }
}
