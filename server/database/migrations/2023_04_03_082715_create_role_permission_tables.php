<?php

use App\Models\Team;
use App\Models\TeamMember;
use App\Types\TeamPermissionType;
use App\Types\TeamRoleType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    public function up(): void {
        // Permissions die es generell gibt,
        // die name Spalte wird dann pro User und Team ans Frontend gegeben
        Schema::create('team_permissions', function (Blueprint $table) {
            // keine UUID, kann ruhig auto increment sein, weil diese für alle die gleichen sind
            $table->id();
            $table->string('name')->unique();
        });

        // Rollen innerhalb eines Teams,
        // Die Standard Rollen "Member" und "Manager" werden beim Erstellen des Teams eingetragen
        Schema::create('team_roles', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('team_id')->constrained()->cascadeOnDelete();
            $table->string('name');

            $table->unique(['team_id', 'name']);
        });

        // Permissions pro Team Role
        Schema::create('team_permission_team_role', function (Blueprint $table) {
            $table->foreignUuid('team_role_id')->constrained()->cascadeOnDelete();
            $table->foreignId('team_permission_id')->constrained()->cascadeOnDelete();

            $table->primary(['team_role_id', 'team_permission_id']);
        });

        // Roles pro Team Member
        Schema::create('team_member_team_role', function (Blueprint $table) {
            $table->foreignUuid('team_role_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('team_member_id')->constrained()->cascadeOnDelete();

            $table->primary(['team_role_id', 'team_member_id']);
        });


        DB::transaction(function () {
            TeamPermissionType::addOrCreateDefaultPermissions();
            Team::get()->each(function (Team $team) {
                TeamRoleType::setDefaultRolesAndPermissions($team);
            });
            TeamMember::withTrashed()->get()->each(function (TeamMember $teamMember) {
                TeamRoleType::setDefaultRolesForTeamMember($teamMember);
            });
        });
    }

    public function down(): void {
        Schema::dropIfExists('team_permission_team_role');
        Schema::dropIfExists('team_member_team_role');
        Schema::dropIfExists('team_roles');
        Schema::dropIfExists('team_permissions');

    }
};
