<?php

use App\Models\TeamEvent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    public function up(): void {
        TeamEvent::get()->each(function (TeamEvent $teamEvent) {
            $teamEvent->delete();
        });
        Schema::table('team_events', function (Blueprint $table) {
            $table->dropColumn('date_time_begin');
            $table->dropColumn('date_time_end');
            $table->dropColumn('date_time_meet');
            $table->date('date_begin');
            $table->time('time_begin');
            $table->time('time_end')->nullable();
            $table->time('time_meet')->nullable();
        });
    }

    public function down(): void {
        TeamEvent::get()->each(function (TeamEvent $teamEvent) {
            $teamEvent->delete();
        });
        Schema::table('team_events', function (Blueprint $table) {
            $table->timestamp('date_time_begin');
            $table->timestamp('date_time_end')->nullable();
            $table->timestamp('date_time_meet')->nullable();
            $table->dropColumn('date_begin');
            $table->dropColumn('time_begin');
            $table->dropColumn('time_end');
            $table->dropColumn('time_meet');
        });
    }
};
