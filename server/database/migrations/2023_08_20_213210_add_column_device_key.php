<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    public function up(): void {
        Schema::table('personal_access_tokens', function (Blueprint $table) {
            $table->string('device_name')->nullable();
            $table->string('device_model')->nullable();
            $table->string('device_platform')->nullable();
            $table->string('device_os')->nullable();
            $table->string('device_manufacturer')->nullable();
            $table->string('fcm_token')->nullable();
        });
    }

    public function down(): void {
        Schema::table('personal_access_tokens', function (Blueprint $table) {
            $table->dropColumn('device_name');
            $table->dropColumn('device_model');
            $table->dropColumn('device_platform');
            $table->dropColumn('device_os');
            $table->dropColumn('device_manufacturer');
            $table->dropColumn('fcm_token');
        });
    }
};
