<?php

use App\Models\User;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up(): void {
        foreach (User::get() as $user) {
            $user->sendEmailVerificationNotification();
        }
    }
    public function down(): void {
        foreach (User::get() as $user) {
            $user->email_verified_at = null;
            $user->email_verification_token = null;
            $user->save();
        }
    }
};
