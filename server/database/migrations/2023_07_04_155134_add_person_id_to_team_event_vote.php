<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::table('team_event_votes', function (Blueprint $table) {
            $table->foreignUuid('voter_person_id');
        });
    }

    public function down(): void {
        Schema::table('team_event_votes', function (Blueprint $table) {
            $table->dropColumn('voter_person_id');
        });
    }
};
