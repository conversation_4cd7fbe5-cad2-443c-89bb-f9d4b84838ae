<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::table('device_info', function (Blueprint $table) {
            $table->timestamp('last_seen', 0)->nullable();
        });
    }

    public function down(): void {
        Schema::table('device_info', function (Blueprint $table) {
            $table->dropColumn('last_seen');
        });
    }
};
