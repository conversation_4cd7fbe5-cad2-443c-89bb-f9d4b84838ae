<?php

use App\Models\TeamMember;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {

        TeamMember::query()->update([
            'join_date' => DB::raw('created_at'),
        ]);

        Schema::table('team_members', function (Blueprint $table) {
            $table->date('join_date')->nullable(false)->change();
        });
    }

    public function down(): void {

        Schema::table('team_members', function (Blueprint $table) {
            $table->date('join_date')->nullable()->change();
        });
    }
};
