<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('i_calendars', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('author_id')->constrained(table: 'persons');
            $table->timestamps();
        });

        Schema::create('i_calendar_targets', function (Blueprint $table) {
            $table->foreignUuid('i_calendar_id')->constrained()->cascadeOnDelete();
            $table->uuidMorphs('i_calendar_target', 'ical_targets_ical_target_type_ical_target_id_index');
        });
    }

    public function down(): void {
        Schema::dropIfExists('i_calendar_targets');
        Schema::dropIfExists('i_calendars');
    }
};
