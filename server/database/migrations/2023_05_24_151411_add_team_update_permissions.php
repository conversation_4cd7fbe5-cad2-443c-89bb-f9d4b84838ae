<?php

use App\Models\Team;
use App\Types\TeamPermissionType;
use App\Types\TeamRoleType;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up(): void {
        TeamPermissionType::removePermissionsTypes([
            'team.member.role.set',
            'team.member.update.name'
        ]);

        DB::transaction(function () {
            TeamPermissionType::addOrCreateDefaultPermissions();
            Team::get()->each(function (Team $team) {
                TeamRoleType::setDefaultRolesAndPermissions($team);
            });
        });

    }

    public function down(): void {
        TeamPermissionType::removePermissionsTypes([
            'team.update'
        ]);

        DB::transaction(function () {
            TeamPermissionType::addOrCreateDefaultPermissions();
            Team::get()->each(function (Team $team) {
                TeamRoleType::setDefaultRolesAndPermissions($team);
            });
        });
    }
};
