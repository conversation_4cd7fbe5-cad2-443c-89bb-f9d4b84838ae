<?php

use App\Types\TeamPermissionType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('team_stats_ranges', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('team_id')->constrained()->cascadeOnDelete();
            $table->date('start_date');
            $table->date('end_date');
            $table->string('name')->nullable();
            $table->timestamps();
        });

        TeamPermissionType::addOrCreateDefaultPermissions();
    }

    public function down(): void {
        Schema::dropIfExists('team_stats_ranges');
    }
};
