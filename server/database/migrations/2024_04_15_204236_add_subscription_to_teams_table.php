<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::table('teams', function (Blueprint $table) {
            $table->foreignUuid('subscription_id')->nullable();
            $table->timestamp('trial_ends_at')->nullable();
        });

        \App\Types\TeamPermissionType::addOrCreateDefaultPermissions();
    }

    public function down(): void {
        Schema::table('teams', function (Blueprint $table) {
            $table->dropColumn('subscription_id');
            $table->dropColumn('trial_ends_at');
        });
    }
};
