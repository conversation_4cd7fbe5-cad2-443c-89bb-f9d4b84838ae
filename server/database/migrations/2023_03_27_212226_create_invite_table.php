<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    public function up(): void {
        Schema::create('invites', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('team_member_id')->constrained();
            $table->string('token')->unique();
            $table->timestamps();
        });
    }

    public function down(): void {
        Schema::dropIfExists('invites');
    }
};
