<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::table('team_events', function (Blueprint $table) {
            $table->string('response_type')->default(\App\Types\EventResponseType::NONE->value);
        });
        Schema::table('team_event_votes', function (Blueprint $table) {
            $table->foreignUuid('voter_person_id')->change()->nullable();
        });
    }

    public function down(): void {
        Schema::table('team_events', function (Blueprint $table) {
            $table->dropColumn('response_type');
        });

        Schema::table('team_event_votes', function (Blueprint $table) {
            $table->foreignUuid('voter_person_id')->change()->nullable(false);
        });
    }
};
