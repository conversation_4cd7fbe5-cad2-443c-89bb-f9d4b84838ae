<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Lara<PERSON>\Fortify\Fortify;

return new class extends Migration {

    public function up(): void {
        Schema::table('teams', function (Blueprint $table) {
            $table->string('invite_token')
                ->default(null)
                ->nullable();
        });
    }

    public function down(): void {
        Schema::table('teams', function (Blueprint $table) {
            $table->dropColumn('invite_token');
        });
    }
};
