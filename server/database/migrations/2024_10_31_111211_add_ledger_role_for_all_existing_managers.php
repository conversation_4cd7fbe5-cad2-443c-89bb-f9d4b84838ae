<?php

use App\Models\Team;
use App\Models\TeamMember;
use App\Types\TeamRoleType;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up(): void {
        DB::transaction(function () {
            Team::with('roles')->each(function (Team $team) {
                $team->membersWithRole(TeamRoleType::MANAGER)->each(function (TeamMember $manager) use ($team) {
                    $treasurerRole = $team->getTeamRole(TeamRoleType::TREASURER);
                    if(!$manager->roles->contains($treasurerRole)) {
                        $manager->roles()->attach($treasurerRole);
                    }
                });
            });
        });
    }

    public function down(): void {
        
    }
};
