<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    public function up(): void {
        Schema::create('persons', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('firstname');
            $table->string('lastname');
            $table->timestamps();
        });
        Schema::table('team_members', function (Blueprint $table) {
            $table->foreignUuid('person_id')->nullable();
        });
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('name');
            $table->foreignUuid('person_id');
        });
    }

    public function down(): void {
        Schema::table('team_members', function (Blueprint $table) {
            $table->dropColumn('person_id');
        });
        Schema::table('users', function (Blueprint $table) {
            $table->string('name');
            $table->dropColumn('person_id');
        });
    }
};
