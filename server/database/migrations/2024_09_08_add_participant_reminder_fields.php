<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\TeamEvent;
use Illuminate\Database\Eloquent\Collection;

return new class extends Migration {
    
    public function up(): void {
        Schema::table('team_events', function (Blueprint $table) {
            $table->string('event_reminder_type')->default('none');
            $table->integer('event_reminder_hours')->default(48);
            $table->timestamp('event_reminder_date')->nullable();
            $table->boolean('event_reminder_sent')->default(false);
        });

//        TeamEvent::chunk(100, function (Collection $teamEvents): void {
//            foreach ($teamEvents as $event) {
//                /** @var TeamEvent $event */
//                $event->calculateAndUpdateEventReminderDate();
//                $event->save();
//            }
//        });
    }

    public function down(): void {
        Schema::table('team_events', function (Blueprint $table) {
            $table->dropColumn([
                'event_reminder_type',
                'event_reminder_hours',
                'event_reminder_date',
                'event_reminder_sent'
            ]);
        });
    }
}; 
