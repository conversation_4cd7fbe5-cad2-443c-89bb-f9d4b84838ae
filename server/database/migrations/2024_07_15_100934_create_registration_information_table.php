<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('registration_information', function (Blueprint $table) {
            $table->foreignUuid('user_id');
            $table->string('origin')->nullable();
            $table->string('competition')->nullable();
        });
    }

    public function down(): void {
        Schema::dropIfExists('registration_information');
    }
};
