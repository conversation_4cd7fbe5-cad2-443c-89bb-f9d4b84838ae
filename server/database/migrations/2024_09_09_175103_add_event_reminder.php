<?php

use App\Models\TeamEvent;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    
    public function up(): void {
        Schema::table('team_events', function (Blueprint $table) {
            $table->enum('vote_reminder_type', ['none', 'hours_after_creation', 'hours_before_event'])
                ->default('hours_before_event')
                ->after('details');
            $table->integer('vote_reminder_hours')
                ->default(48)
                ->after('vote_reminder_type');
            // Using datetime here on purpose for later more specific time handling
            $table->datetime('vote_reminder_date')
                ->nullable()
                ->after('vote_reminder_hours');
            $table->boolean('vote_reminder_sent')
                ->default(false)
                ->after('vote_reminder_date');
        });
        
        TeamEvent::chunk(100, function (Collection $teamEvents): void {
            foreach ($teamEvents as $event) {
                /** @var TeamEvent $event */
                $event->calculateAndUpdateVoteReminderDate();
                $event->save();
            }
        });
    }

    public function down(): void {
        Schema::table('team_events', function (Blueprint $table) {
            $table->dropColumn('vote_reminder_type');
            $table->dropColumn('vote_reminder_hours');
            $table->dropColumn('vote_reminder_date');
            $table->dropColumn('vote_reminder_sent');
        });
    }
};
