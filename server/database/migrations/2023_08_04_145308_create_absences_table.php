<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('absences', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('absence_type');
            $table->date('date_begin');
            $table->date('date_end')->nullable();
            $table->string('rrule_string')->nullable();
            $table->foreignUuid('author_id')->constrained(table: 'persons');
            $table->timestamps();
        });

        Schema::create('absence_targets', function (Blueprint $table) {
            $table->foreignUuid('absence_id')->constrained()->cascadeOnDelete();
            $table->uuidMorphs('absence_target');
        });

        Schema::table('team_event_votes', function (Blueprint $table) {
            $table->foreignUuid('absence_id')->nullable();
        });
    }

    public function down(): void {
        Schema::table('team_event_votes', function (Blueprint $table) {
            $table->dropColumn('absence_id');
        });
        Schema::dropIfExists('absence_targets');
        Schema::dropIfExists('absences');
    }
};
