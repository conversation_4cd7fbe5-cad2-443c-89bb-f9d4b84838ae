<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    public function up() {
        Schema::table('invites', function (Blueprint $table) {
            $table->unique('team_member_id');
        });
    }

    public function down() {
        Schema::table('invites', function (Blueprint $table) {
            $table->dropUnique('invites_team_member_id_unique');
        });
    }
};
