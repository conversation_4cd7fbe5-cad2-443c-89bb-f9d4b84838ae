<?php

use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    public function up(): void {
        Schema::table('users', function (Blueprint $table) {
            // https://github.com/laravel/framework/issues/52166#issuecomment-2233115512
            $table->bigInteger('id')->change(); // drop auto increment
            $table->dropPrimary('id');          // drop primary
            $table->uuid('id')->primary()->change();
        });

        DB::transaction(function () {
            User::get()->each(function (User $user) {
                $user->id = Str::orderedUuid();
                $user->save();
            });
        });


        DB::table('personal_access_tokens')->truncate();
        Schema::table('personal_access_tokens', function (Blueprint $table) {
            $table->dropMorphs('tokenable');
        });
        Schema::table('personal_access_tokens', function (Blueprint $table) {
            $table->uuidMorphs('tokenable');
        });

    }

    public function down(): void {
        DB::transaction(function () {
            $i = 1;
            foreach (User::get() as $user) {
                $user->id = $i;
                $user->save();
                $i++;
            }
        });

        Schema::table('users', function (Blueprint $table) {
            $table->id()->change();
        });

        DB::table('personal_access_tokens')->truncate();
        Schema::table('personal_access_tokens', function (Blueprint $table) {
            $table->dropMorphs('tokenable');
        });
        Schema::table('personal_access_tokens', function (Blueprint $table) {
            $table->morphs('tokenable');
        });
    }
};
