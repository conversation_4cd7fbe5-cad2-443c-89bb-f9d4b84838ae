<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::table('teams', function (Blueprint $table) {
            $table->dropColumn('subscription_id');
        });
        
        Schema::table('subscriptions', function (Blueprint $table) {
            // nullable because cashier creates the subscription and is linked StripeEventHandledListener afterward
            $table->foreignUuid('team_id')->nullable();
        });
    }

    public function down(): void {
        Schema::table('teams', function (Blueprint $table) {
            $table->foreignUuid('subscription_id')->nullable();
        });
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropColumn('team_id');
        });
    }
};
