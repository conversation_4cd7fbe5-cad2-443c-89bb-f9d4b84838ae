<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::table('registration_allowlist', function (Blueprint $table) {
            $table->boolean('allow_registration')
                ->after('user_id')
                ->default(false);
        });
    }

    public function down(): void {
        Schema::table('registration_allowlist', function (Blueprint $table) {
            $table->dropColumn('allow_registration');
        });
    }
};
