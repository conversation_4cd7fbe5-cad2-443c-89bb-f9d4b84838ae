<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->string('price_type')->nullable();
        });
    }

    public function down(): void {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropColumn('price_type');
        });
    }
};
