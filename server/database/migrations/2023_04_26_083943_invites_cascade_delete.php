<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    public function up(): void {
        Schema::table('invites', function (Blueprint $table) {
            $table->dropForeign('invites_team_member_id_foreign');
            $table->foreign('team_member_id', 'invites_team_member_id_foreign')
                ->references('id')->on('team_members')->cascadeOnDelete();
        });
    }

    public function down(): void {
        Schema::table('invites', function (Blueprint $table) {
            $table->dropForeign('invites_team_member_id_foreign');
            $table->foreign('team_member_id', 'invites_team_member_id_foreign')
                ->references('id')->on('team_members');
        });
    }
};
