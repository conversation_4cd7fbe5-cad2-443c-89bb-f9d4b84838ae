<?php

use App\Types\TeamPermissionType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('team_event_task_configs', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('team_id')->constrained()->cascadeOnDelete();
            $table->string('title');
            $table->timestamps();
        });

        Schema::create('team_event_tasks', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('config_id')->constrained('team_event_task_configs')->cascadeOnDelete();
            $table->foreignUuid('team_event_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('team_member_id')->nullable()->constrained()->cascadeOnDelete();
            $table->timestamps();
        });

        DB::transaction(static function () {
            TeamPermissionType::addOrCreateDefaultPermissions();
        });
    }

    public function down(): void {
        Schema::dropIfExists('team_event_tasks');
        Schema::dropIfExists('team_event_task_configs');

        TeamPermissionType::removePermissionsTypes([
            'team.event.task.create',
            'team.event.task.delete',
            'team.event.task.assign.self',
            'team.event.task.assign.other',
        ]);
    }
};
