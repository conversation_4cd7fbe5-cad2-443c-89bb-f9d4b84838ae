<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('team_ledgers', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('team_id')->unique();
            $table->timestamps();
        });

        Schema::create('team_ledger_transactions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('team_member_id')->nullable();
            $table->foreignUuid('author_id')->constrained(table: 'persons');
            $table->foreignUuid('team_ledger_id');
            $table->string('title');
            $table->integer('amount');
            $table->timestamps();
        });

        Schema::create('team_ledger_fines', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('team_ledger_id');
            $table->string('title');
            $table->integer('amount')->nullable();
            $table->string('item')->nullable();
            $table->foreignUuid('parent_id')->nullable();
            $table->date('date_begin');
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('team_ledger_dues', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('team_ledger_id');
            $table->string('title');
            $table->integer('amount')->nullable();
            $table->string('item')->nullable();
            $table->foreignUuid('parent_id')->nullable();
            $table->date('date_begin');
            $table->string('rrule_string')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('team_ledger_claims', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('team_ledger_id');
            $table->foreignUuid('team_member_id');
            $table->foreignUuid('author_id')->constrained(table: 'persons');
            $table->nullableUuidMorphs('claimable');
            $table->boolean('exempt')->default(false);
            $table->string('title');
            $table->date('due_date');
            $table->integer('amount')->nullable();
            $table->string('item')->nullable();
            $table->dateTime('fulfilled_at')->nullable();
            $table->timestamps();
        });

        Schema::table('team_members', function (Blueprint $table) {
            $table->date('join_date')->nullable();
            $table->softDeletes();
        });
    }

    public function down(): void {
        Schema::dropIfExists('team_ledgers');
        Schema::dropIfExists('team_ledger_transactions');
        Schema::dropIfExists('team_ledger_fines');
        Schema::dropIfExists('team_ledger_dues');
        Schema::dropIfExists('team_ledger_claims');

        Schema::table('team_members', function (Blueprint $table) {
            $table->dropColumn('join_date');
            $table->dropSoftDeletes();
        });
    }
};
