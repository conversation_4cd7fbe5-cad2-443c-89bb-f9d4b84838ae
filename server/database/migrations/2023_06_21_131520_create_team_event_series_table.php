<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('team_event_series', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('rrule_string');
            $table->timestamps();
        });

        Schema::table('team_events', function (Blueprint $table) {
            $table->string('series_type')->default(\App\Types\EventSeriesType::SINGLE->value);
            $table->foreignUuid('series_id')->nullable();
        });
    }

    public function down(): void {
        Schema::table('team_events', function (Blueprint $table) {
            $table->dropColumn('series_type');
            $table->dropColumn('series_id');
        });

        Schema::dropIfExists('team_event_series');

    }
};
