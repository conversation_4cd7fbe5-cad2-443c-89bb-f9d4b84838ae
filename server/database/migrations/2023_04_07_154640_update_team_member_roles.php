<?php

use App\Models\TeamMember;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    public function up(): void {
        Schema::table('team_members', function (Blueprint $table) {
            $table->foreignUuid('team_role_id');
        });

        DB::transaction(function () {
            TeamMember::withTrashed()->get()->each(function (TeamMember $teamMember) {
                $teamMember->setDefaultRoleIfMissing();
            });
        });
    }

    public function down(): void {
        Schema::table('team_members', function (Blueprint $table) {
            $table->dropColumn('team_role_id');
        });
    }
};
