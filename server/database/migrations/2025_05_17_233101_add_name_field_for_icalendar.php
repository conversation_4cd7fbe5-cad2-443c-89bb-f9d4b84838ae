<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::table('i_calendars', function (Blueprint $table) {
            $table->string('internal_name')->nullable();
        });
    }

    public function down(): void {
        Schema::table('i_calendars', function (Blueprint $table) {
            $table->dropColumn('internal_name');
        });
    }
};
