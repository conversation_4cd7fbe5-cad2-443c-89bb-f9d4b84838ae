<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    public function up(): void {
        Schema::create('team_event_votes', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('team_event_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('team_member_id')->constrained()->cascadeOnDelete();
            $table->string('vote');
            $table->timestamps();
        });
    }

    public function down(): void {
        Schema::dropIfExists('team_event_votes');
    }
};
