<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('device_info', function (Blueprint $table) {
            $table->uuid('installation_id')->primary();
            $table->timestamps();
            $table->foreignUuid('user_id')->constrained()->cascadeOnDelete();;
            $table->string('name')->nullable();
            $table->string('model')->nullable();
            $table->string('platform')->nullable();
            $table->string('manufacturer')->nullable();
            $table->string('os')->nullable();
            $table->string('fcm_token')->nullable();
        });

        DB::table('personal_access_tokens')
            ->select('installation_id', DB::raw('MAX(id) as latest_id'))
            ->groupBy('installation_id')
            ->pluck('latest_id')
            ->chunk(100)->each(function ($latestIds) {
                $tokens = DB::table('personal_access_tokens')
                    ->whereIn('id', $latestIds)
                    ->get();

                foreach ($tokens as $token) {
                    // Only insert device info if the user exists. Otherwise, it will be a foreign key violation.
                    if (DB::table('users')->where('id', $token->tokenable_id)->exists()) {

                        // Assign a random installation_id if it is null
                        if ($token->installation_id === null) {
                            $token->installation_id = Str::uuid();
                            DB::table('personal_access_tokens')
                                ->where('id', $token->id)
                                ->update(['installation_id' => $token->installation_id]);
                        }

                        DB::table('device_info')->insert([
                            'created_at'      => now(),
                            'updated_at'      => now(),
                            'installation_id' => $token->installation_id,
                            'user_id'         => $token->tokenable_id,
                            'name'            => $token->name,
                            'model'           => $token->device_model,
                            'os'              => $token->device_os,
                            'platform'        => $token->device_platform,
                            'manufacturer'    => $token->device_manufacturer,
                            'fcm_token'       => $token->fcm_token,
                        ]);
                    }
                }
            });

        Schema::table('personal_access_tokens', function (Blueprint $table) {
            $table->dropColumn('device_name');
            $table->dropColumn('device_model');
            $table->dropColumn('device_platform');
            $table->dropColumn('device_os');
            $table->dropColumn('device_manufacturer');
            $table->dropColumn('fcm_token');
        });
    }

    public function down(): void {
        Schema::table('personal_access_tokens', function (Blueprint $table) {
            $table->string('device_name')->nullable();
            $table->string('device_model')->nullable();
            $table->string('device_platform')->nullable();
            $table->string('device_os')->nullable();
            $table->string('device_manufacturer')->nullable();
            $table->string('fcm_token')->nullable();
        });

        DB::table('device_info')
            ->orderBy('installation_id')
            ->chunk(100, function ($deviceInfos) {
                foreach ($deviceInfos as $deviceInfo) {
                    DB::table('personal_access_tokens')
                        ->where('installation_id', $deviceInfo->installation_id)
                        ->update([
                            'device_name'         => $deviceInfo->name,
                            'device_model'        => $deviceInfo->model,
                            'device_platform'     => $deviceInfo->platform,
                            'device_os'           => $deviceInfo->os,
                            'device_manufacturer' => $deviceInfo->manufacturer,
                            'fcm_token'           => $deviceInfo->fcm_token,
                        ]);
                }
            });

        Schema::dropIfExists('device_info');
    }
};
