<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        // cleanup current structure
        Schema::dropIfExists('invites');
        Schema::table('teams', function (Blueprint $table) {
            $table->dropColumn('invite_token');
        });

        // create
        Schema::create('invites', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('token')->unique();
            $table->uuidMorphs('invitable');
            $table->timestamps();
        });
    }

    public function down(): void {

        Schema::dropIfExists('invites');

        // create previous structure
        Schema::create('invites', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('team_member_id')->unique();
            $table->foreign('team_member_id', 'invites_team_member_id_foreign')
                  ->references('id')->on('team_members')->cascadeOnDelete();
            $table->string('token')->unique();
            $table->timestamps();
        });

        Schema::table('teams', function (Blueprint $table) {
            $table->string('invite_token')
                  ->default(null)
                  ->nullable();
        });
    }
};
