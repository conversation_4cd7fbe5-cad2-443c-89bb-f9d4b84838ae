<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    public function up(): void {
        Schema::create('team_events', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('team_id')->constrained()->cascadeOnDelete();
            $table->string('event_type');
            $table->string('title');
            $table->string('sub_text')->nullable();
            $table->string('details')->nullable();
            $table->timestamp('date_time_begin');
            $table->timestamp('date_time_end')->nullable();
            $table->timestamp('date_time_meet')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void {
        Schema::dropIfExists('team_events');
    }
};
