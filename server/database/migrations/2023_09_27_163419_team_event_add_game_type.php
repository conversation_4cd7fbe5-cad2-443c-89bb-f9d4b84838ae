<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::table('team_events', function (Blueprint $table) {
            $table->string('title')->nullable()->change();
            $table->string('match_type')->nullable();
            $table->string('match_opponent_name')->nullable();
        });
    }

    public function down(): void {
        Schema::table('team_events', function (Blueprint $table) {
            $table->string('title')->nullable(false)->change();
            $table->dropColumn('match_type');
            $table->dropColumn('match_opponent_name');
        });
    }
};
