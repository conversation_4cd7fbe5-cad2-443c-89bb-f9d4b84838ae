<?php

use App\Types\EventReminderType;
use Illuminate\Database\Migrations\Migration;
use App\Models\TeamEvent;
use Illuminate\Database\Eloquent\Collection;

return new class extends Migration {
    public function up(): void {
        TeamEvent::chunk(100, function (Collection $teamEvents): void {
            foreach ($teamEvents as $event) {
                if ($event->event_reminder_type == EventReminderType::NONE) {
                    /** @var TeamEvent $event */
                    $event->event_reminder_type = EventReminderType::HOURS_BEFORE_EVENT;
                    $event->event_reminder_hours = 2;
                    $event->calculateAndUpdateEventReminderDate();
                    
                    // Check if the event_reminder_date is in the future and only save the changes if it is
                    if ($event->event_reminder_date != null && $event->event_reminder_date->isFuture()) {
                        $event->save();
                    } else {
                        $event->discardChanges();
                    }
                }
            }
        });
    }

    public function down(): void {
        // There is no proper way back from this.
//        TeamEvent::chunk(100, function (Collection $teamEvents): void {
//            foreach ($teamEvents as $event) {
//                /** @var TeamEvent $event */
//                $event->event_reminder_type = EventReminderType::NONE;
//                $event->event_reminder_hours = 48;
//                $event->event_reminder_date = null;
//                $event->save();
//            }
//        });
    }
}; 
