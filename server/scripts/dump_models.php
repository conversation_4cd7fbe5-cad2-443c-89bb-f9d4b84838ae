<?php

require __DIR__.'/../vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;
use Illuminate\Events\Dispatcher;
use Illuminate\Container\Container;
use Illuminate\Support\Facades\Schema;
use Dotenv\Dotenv;

// Load environment variables from .env file
$dotenv = Dotenv::createImmutable(__DIR__ . "/../");
$dotenv->load();

$capsule = new Capsule;
$capsule->addConnection([
    'driver'    => $_ENV['DB_CONNECTION'],
    'host'      => $_ENV['DB_HOST'],
    'database'  => $_ENV['DB_DATABASE'],
    'username'  => $_ENV['DB_USERNAME'],
    'password'  => $_ENV['DB_PASSWORD'],
    'charset'   => 'utf8',
    'collation' => 'utf8_unicode_ci',
    'prefix' => $_ENV['DB_PREFIX'] ?? '',
]);

$capsule->setEventDispatcher(new Dispatcher(new Container));

$capsule->setAsGlobal();

$capsule->bootEloquent();

$modelPath = __DIR__ . '/../app/Models';
if (!file_exists($modelPath)) {
    die("Model directory not found: $modelPath");
}

$schemaPath = __DIR__ . '/../app/JsonApi/V1';
if (!file_exists($schemaPath)) {
    die("Schema directory not found: $schemaPath");
}


echo "=== Models (Eloquent)\n";

$modelsDirectory = new RecursiveDirectoryIterator($modelPath);
$modelsIterator = new RecursiveIteratorIterator($modelsDirectory);
$modelsRegex = new RegexIterator($modelsIterator, '/^.+\.php$/i', RecursiveRegexIterator::GET_MATCH);

foreach ($modelsRegex as $file) {
    $filePath = $file[0];
    $fileContents = file_get_contents($filePath);
    if (preg_match('/class\s+(\w+)/', $fileContents, $matches)) {
        $className = $matches[1];
        $fullyQualifiedClassName = "\\App\\Models\\$className";
        if (class_exists($fullyQualifiedClassName)) {
            $model = new $fullyQualifiedClassName;
            if ($model instanceof Illuminate\Database\Eloquent\Model) {
                echo "- Model: $className, ";
                $tableName = $model->getTable();
                $columnNames = $capsule->getConnection()->getSchemaBuilder()->getColumnListing($tableName);
                echo "Fields: " . implode(", ", $columnNames) . "\n";
            }
        }
    }
}

echo "=== JSON:API Schema (laravel-json-api)\n";

$schemaDirectory = new RecursiveDirectoryIterator($schemaPath);
$schemaIterator = new RecursiveIteratorIterator($schemaDirectory);
$schemaRegex = new RegexIterator($schemaIterator, '/^.+Schema\.php$/i', RecursiveRegexIterator::GET_MATCH);

foreach ($schemaRegex as $file) {
    $filePath = $file[0];
    $fileContents = file_get_contents($filePath);

    if (preg_match('/class\s+(\w+)/', $fileContents, $matches)) {
        $className = $matches[1];
        $directoryPath = dirname($filePath);
        $schemaDirectoryName = basename($directoryPath);
        $schemaName = rtrim($schemaDirectoryName, 's'); // Convert from plural to singular
        $fullyQualifiedClassName = "\\App\\JsonApi\\V1\\{$schemaDirectoryName}\\{$className}";

        if (class_exists($fullyQualifiedClassName)) {
            $class = new ReflectionClass($fullyQualifiedClassName);
            if ($class->hasMethod('schema')) {
                $method = $class->getMethod('schema');
                if ($method->isStatic()) {
                    echo "Schema: {$schemaName}, ";
                    $fields = $method->invoke(null); // Call the static method
                    echo "Fields: " . implode(", ", $fields) . "\n";
                }
            }
        }
    }
}

echo "=== Tech stack\n";
echo "Client: Quasar Framework, Spraypaint.js, TanStack\n";
echo "Server: Laravel, Laravel JSON:API (laravel-json-api), Eloquent\n";
