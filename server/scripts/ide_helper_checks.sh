#!/bin/bash

# Declare the github repository path as a safe directory. Otherwise git will ignore it and throw an error.
# See: https://github.com/actions/checkout/issues/766
git config --global --add safe.directory "$GITHUB_WORKSPACE"

# Check for changes in ./app/Models/ directory
if git diff --exit-code ./app/Models/ > /dev/null; then
    echo "#### No changes detected in ./app/Models/. ####"
    exit 0
else
    echo "#"
    echo "#"
    echo "#"
    echo "################################################################"
    echo "#### Changes detected in ./app/Models/.                     ####"
    echo "#### Please run the following command to update the PHPDoc: ####"
    echo "####                                                        ####"
    echo "####    ddev artisan ide-helper:models -W -R -r -p          ####"
    echo "####                                                        ####"
    echo "################################################################"
    echo "#"
    echo "#"
    echo "#"
    echo ""
    git diff ./app/Models/
    exit 1
fi
