#!/usr/bin/env php
<?php

if (php_sapi_name() != "cli") {
    exit("should only be called from cli");
}

// Store the name of the binary for use in errors, etc.
define("NAME", basename(array_shift($argv)));

class Environment {
    public function __construct(
        public string $server,
        public string $user,
        public string $directory,
    ) {}
}

const environments = [
    "staging" => new Environment("access950750563.webspace-data.io", "u111136980", "staging/"),
    "production" => new Environment("access950750563.webspace-data.io", "u111136980", "production/"),
];

// Exit status error values
const ERR_BAD_USAGE = 1;
const ERR_SUBCOMMAND_FAILED = 2;
const ERR_MISSING_REQUIREMENT = 3;
const ERR_BAD_ENVIRONMENT = 4;

const PACKAGE_FILE_NAME = "dist.zip";

const REQUIRE_CLI_TOOLS = [
    "zip",
    "php",
    "sshpass",
    "ssh"
];

const ENV_SERVER_ENV_KEY = "ENV_KEY";
const ENV_REMOTE_PASSWORD = "REMOTE_PASSWORD";

class Config {
    public string $environment;
}

print("# Conan the deployer\n\n");

if (!getcwd()) {
    echo("Couldn't get the current working directory.");
    exit(ERR_BAD_ENVIRONMENT);
}

if (!file_exists(getcwd() . "/artisan")) {
    echo("Artisan not found. Please run this script in the server subdirectory.");
    exit(ERR_BAD_ENVIRONMENT);
}

$config = read_config($argv);

if (!count($argv)) {
    fwrite(STDERR, usage());
    exit(ERR_BAD_USAGE);
}

$command = $argv[0];

try {
    switch ($command) {
        case 'package':
            # The (zip) packaging will be something we need for AWS later as well
            package($argv, $config->environment, PACKAGE_FILE_NAME);
            break;
        case 'upload':
            upload($argv, $config->environment, PACKAGE_FILE_NAME);
            break;
        default:
            echo("unknown command");
            fwrite(STDOUT, usage(true));
            exit(ERR_BAD_USAGE);
    }
} catch (Exception $e) {
    echo($e->getMessage());
    exit($e->getCode());
}

function upload(array &$args, string $env, string $package_name): void {}

/**
 * @throws Exception
 */
function package(array &$args, string $env, string $package_name): void {
    $files_to_package = [
        "app", "bootstrap", "config", "database",
        "lang", "public", "resources", "routes",
        "storage", "tests", "artisan", "vendor",
        ".env"
    ];

    if (getenv(ENV_SERVER_ENV_KEY) == "") {
        echo("The package command requires the artisan env key for '{$env}' being set");
        exit(ERR_BAD_USAGE);
    }

    echo("Packaging\n");
    echo("- Creating dist directory and copying files\n");
    exec_cmd("rm -rf dist/; mkdir -p dist");
    exec_cmd("cp -R " . implode(" ", $files_to_package) . " dist/");

    echo("- Decrypting and copying {$env} environment file\n");
    exec_cmd("php artisan env:decrypt --env={$env} --key=" . getenv(ENV_SERVER_ENV_KEY));
    exec_cmd("mv .env.{$env} dist/");

    echo("- Zipping everything\n");
    exec_cmd("cd dist; zip -r {$package_name} *");
    exec_cmd("mv dist/{$package_name} .");

    echo("- Cleaning up dist directory\n");
    exec_cmd("rm -rf dist/");

    echo("\nDone\n");
}

class ExecResponse {
    public function __construct(
        public string $output,
        public int    $exitCode,
        public string $command,
    ) {}
}

/**
 * @throws Exception
 */
function exec_remote(string $env, string $command, bool $throw_on_fail = true): ExecResponse {
    $user = environments[$env]->user;
    $server = environments[$env]->server;

    if (getenv(ENV_REMOTE_PASSWORD) == "") {
        echo("Password for user {$user} on server {$server} required via environment variable " . ENV_REMOTE_PASSWORD);
        exit(ERR_BAD_USAGE);
    }

    $password = getenv(ENV_REMOTE_PASSWORD);

    return exec_cmd("sshpass -v -p \"{$password}\" ssh -o \"StrictHostKeyChecking=no\" {$user}@access950750563.webspace-data.io '{$command}'", $throw_on_fail);
}

/**
 * @throws Exception
 */
function exec_cmd(string $command, bool $throw_on_fail = true): ExecResponse {
    $response = trim(shell_exec("{$command}  2>&1; echo $?"));

    $lastNewlineIdx = strrpos($response, "\n");

    $exitCode = trim(substr($response, $lastNewlineIdx));
    $output = substr($response, 0, strlen($response) - strlen($exitCode));

    if ($throw_on_fail && $exitCode != 0) {
        throw new Exception("Failed to run '{$command}'. Exit code: {$exitCode}\nOutput:\n{$output}\n", ERR_SUBCOMMAND_FAILED);
    }

    return new ExecResponse($output, intval($exitCode), $command);
}

function read_config(&$args): Config {
    $config = new Config();

    while (count($args) && $args[0][0] == '-') {
        if (($arg = array_shift($args)) == '--') {
            break;
        }

        if ($arg == "--help") {
            fwrite(STDOUT, usage(true));
            exit(0);
        }

        for ($ch = 1; $ch < strlen($arg); $ch++) {
            switch ($arg[$ch]) {
                case 'e':
                    $config->environment = handle_arg($arg, $args);
                    break;
                default:
                    if (!is_numeric($arg[$ch])) {
                        fwrite(
                            STDERR,
                            NAME . ": illegal option -- " . $arg[$ch] . "\n"
                        );

                        fwrite(STDERR, usage());
                        exit(ERR_BAD_USAGE);
                    }
            }
        }
    }

    return $config;
}

function handle_arg($arg, &$args) {
    if (strlen($arg) > 2) {
        $argval = substr($arg, 2);
    } elseif (count($args)) {
        $argval = array_shift($args);
    } else {
        fwrite(STDERR, NAME . ": Option requires an argument -- " . $arg[1] . "\n");
        fwrite(STDERR, usage());
        exit(ERR_BAD_USAGE);
    }

    return $argval;
}

function usage($long = false): string {
    $out = sprintf("usage: %s [-e env] [command ...]\n", NAME)
        . sprintf("       %s [--help]\n", NAME);

    if ($long) {
        $out .= ""
            . "  -e      environment to target\n\n"
            . "  --help  display this help and exit\n";
    } else {
        $out .= sprintf("Try `%s --help' for more information.\n", NAME);
    }

    return $out;
}

function check_for_cli_tools() {
    foreach (REQUIRE_CLI_TOOLS as $tool) {
        if (!command_exist($tool)) {
            echo("Missing \n");
            exit(ERR_MISSING_REQUIREMENT);
        }
    }
}

function command_exist(string $cmd): bool {
    $return = shell_exec(sprintf("which %s", escapeshellarg($cmd)));
    return !empty($return);
}

?>
