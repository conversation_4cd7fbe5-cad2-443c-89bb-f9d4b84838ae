<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>{{$title ?? 'Numo App'}}</title>
    <link rel='shortcut icon' type='image/x-icon' href='{{Vite::image('favicon.ico')}}' />
    <link rel="stylesheet" href="{{asset('css/jquery.bxslider_4_2_12.css')}}">
    <link rel="stylesheet" href="{{asset('css/animate.css')}}">
    <link rel="stylesheet" href="{{asset('css/bootstrap.min_4_1_1.css')}}" crossorigin="anonymous">
    <link rel="stylesheet" href="{{ asset('fontawesome/css/fontawesome.min.css') }}">
    <link rel="stylesheet" href="{{ asset('fontawesome/css/solid.min.css') }}">
    <link rel="stylesheet" href="{{ asset('fontawesome/css/brands.min.css') }}">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @if(config('app.env') !== 'production')
        <meta name="robots" content="noindex">
    @endif

    @vite(['resources/js/app.js'])

</head>

<body style="position: relative" data-spy="scroll" data-target="#navbar" data-offset="75">
{{--  Navbar  --}}
<nav class="navbar navbar-expand-lg" id="navbar">
    <div class="container">
        <a class="navbar-brand navbar-logo" href="{{route('home')}}#" data-target="#home">
            <img src="{{Vite::image('numo_logo_char_only.png')}}" alt="logo" class="logo-1">
            numo
        </a>

        <div class="d-flex">
            <a href="{{ config('cashier.live_demo_url') }}" target="_blank" role="button"
               class="login-button-mobile btn mr-2 btn-sm" style="background-color: #fbd758; color: black; font-weight: bold;">
                <i class="fa fa-video"></i> Demo
            </a>

            <a href="{{ config('app.frontend.url') }}" role="button"
               class="login-button-mobile float-left btn mr-3 bg-accent btn-sm" >Login</a>

            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                    aria-expanded="false" aria-label="Toggle navigation"><span class="fa-solid fa-bars"></span></button>
        </div>
        <div class="collapse navbar-collapse" id="navbarSupportedContent">
            <ul class="navbar-nav ml-auto">
                <li class="nav-item"><a class="nav-link" href="{{route('home')}}#" data-target="#home">Startseite</a></li>
                <li class="nav-item"><a class="nav-link" href="{{route('home')}}#about" data-target="#about">Über uns</a></li>

                <li class="nav-item dropdown btn-group">
                    <a class="nav-link" href="{{route('home')}}#features" data-target="#features">Funktionen</a>
{{--                    <button type="button" class="btn btn-lg btn-light dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"></button>--}}
                    <a class="nav-link dropdown-toggler" href="" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i class="fa-solid fa-caret-down"></i></a>
                    <div class="dropdown-menu section-blue">
                        <a class="dropdown-item" href="{{ route('features') }}#terminliste" data-target="#terminliste"><i class="fa-solid fa-arrow-right pr-2"></i>Terminliste</a>
                        <a class="dropdown-item" href="{{ route('features') }}#termine-verwalten" data-target="#termine-verwalten"><i class="fa-solid fa-arrow-right pr-2"></i>Termine verwalten</a>
                        <a class="dropdown-item" href="{{ route('features') }}#abwesenheit" data-target="#abwesenheit"><i class="fa-solid fa-arrow-right pr-2"></i>Abwesenheit</a>
                        <a class="dropdown-item" href="{{ route('features') }}#aufgaben" data-target="#aufgaben"><i class="fa-solid fa-arrow-right pr-2"></i>Aufgaben</a>
                        <a class="dropdown-item" href="{{ route('features') }}#statistik" data-target="#statistik"><i class="fa-solid fa-arrow-right pr-2"></i>Statistik</a>
                        <a class="dropdown-item" href="{{ route('features') }}#teamkasse" data-target="#teamkasse"><i class="fa-solid fa-arrow-right pr-2"></i>Teamkasse / Strafenkatalog</a>
                    </div>
                </li>
                {{-- <li class="nav-item"> <a class="nav-link" href="{{route('home')}}#navbarSupportedContent">Team</a> </li> --}}
                {{-- <li class="nav-item"> <a class="nav-link" href="{{route('home')}}#navbarSupportedContent">Testimonials</a> </li> --}}
                {{-- <li class="nav-item"> <a class="nav-link" href="{{route('home')}}#navbarSupportedContent">FAQ</a> </li>  --}}
                <li class="nav-item"><a class="nav-link" href="{{route('home')}}#download" data-target="#download">Download</a></li>
                <li class="nav-item"><a class="nav-link" href="{{route('home')}}#preis" data-target="#preis">Preis</a></li>
                <li class="nav-item"><a class="nav-link" href="{{route('home')}}#contact" data-target="#contact">Kontakt</a></li>
                <li class="nav-item"><a class="nav-link" href="{{route('home')}}#partner" data-target="#partner">Partner</a></li>
                <li class="nav-item align-content-center login-button-collapse">
                    <a href="{{ config('cashier.live_demo_url') }}" target="_blank" role="button"
                       class="nav-link btn btn-lg py-1 bg-accent" style="color: black; font-weight: bold;">
                        <i class="fa fa-video"></i> Demo buchen
                    </a>
                </li>
                <li class="nav-item align-content-center login-button-collapse">
                    <a href="{{ config('app.frontend.url') }}" role="button"
                       class="nav-link btn bg-accent btn-lg py-1" style="color: black" >Login</a>
                </li>

            </ul>
        </div>
    </div>
</nav>
{{--  End Navbar  --}}

{{ $slot }}

<footer class="footer-copy">
    <div class="col-auto text-right">
        <a href="{{ config('app.frontend.url') }}/site/impressum" style="font-size: 1.1em;">Impressum</a> |
        <a href="{{ config('app.frontend.url') }}/site/datenschutz" style="font-size: 1.1em;">Datenschutz</a> |
        <a href="{{ config('app.frontend.url') }}/site/kontakt" style="font-size: 1.1em;">Kontakt</a>
    </div>
    <div class="container-fluid pt-4">
        <div class="row justify-content-between">
            <div class="">
                <p style="font-size: 0.9em;">2024 &copy; Numo App. Website Designed by <a href="http://w3Template.com" target="_blank" rel="dofollow">W3 Template</a>. Background image rawpixel.com <a
                        href="https://de.freepik.com/fotos-kostenlos/sportgeraete_18415697.htm#query=hintergrund%20sport&position=0&from_view=keyword&track=ais">auf Freepik</a></p>
            </div>
        </div>
    </div>
</footer>




<script src="{{asset('js/jquery.min.js')}}"></script>
<script src="{{asset('js/bootstrap.min.js')}}" crossorigin="anonymous"></script>
<script src="{{asset('js/jquery.bxslider.min.js')}}"></script>
<script src="{{asset('js/wow.min.js')}}"></script>

<script>
    wow = new WOW();
    wow.init();

    function applyNavbarScrollStyles() {
        var bodyScroll = $(window).scrollTop();
        var navbar = $('.navbar');
        var loginButton = $('.login-button-mobile, .login-button-collapse a');

        if (bodyScroll > 50) {
            navbar.addClass('nav-scroll');
            // loginButton.addClass('btn-black');
        } else {
            navbar.removeClass('nav-scroll');
            // loginButton.removeClass('btn-black');
        }
    }

    $(window).on('scroll', function() {
        applyNavbarScrollStyles();
    });

    $(window).on('load', function() {
        applyNavbarScrollStyles();
    });

    //Hamburger menu close on link click
    $('.navbar-nav li a').click(function (event) {
        var toggle = $('.navbar-collapse').hasClass("show");
        if (toggle) {
            $('.navbar-toggler').click();
        }
    });

    // Scroll to section
    $('#navbar .nav-link, #navbar .navbar-brand, #navbar .dropdown-item').click(function() {
        var sectionTo = $(this).attr('data-target');
        if(sectionTo) {
            $('html, body').animate({
                scrollTop: $(sectionTo).offset().top-63
            }, 900);
        }
    });

    $(document).ready(() => {
        // initial scroll position
        let url = new URL(location.href)
        if(url.hash) {
            $('html, body').animate({
                scrollTop: $(url.hash).offset().top-63
            }, 900);
        }
    });

</script>

<!-- Brevo Conversations {literal} -->
<script>
    (function(d, w, c) {
        w.BrevoConversationsID = '67e1a4cb53e46006da0e2cd0';
        w[c] = w[c] || function() {
            (w[c].q = w[c].q || []).push(arguments);
        };
        var s = d.createElement('script');
        s.async = true;
        s.src = 'https://conversations-widget.brevo.com/brevo-conversations.js';
        if (d.head) d.head.appendChild(s);
    })(document, window, 'BrevoConversations');
</script>
<!-- /Brevo Conversations {/literal} -->

</body>
</html>
