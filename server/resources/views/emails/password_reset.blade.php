@php
    $frontendUrl = config('app.frontend.url');
    $appName = config('app.name');
    $resetUrl = $frontendUrl . '/auth/reset-password?token=' . urlencode($token) . '&email=' . urlencode($user->email);
@endphp

    <!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Passwort zurücksetzen</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        .header {
            background-color: #f5f5f5;
            padding: 20px;
            text-align: center;
            font-size: 24px;
        }
        .content {
            padding: 20px;
            background-color: #ffffff;
        }
        .button {
            background-color: #3490dc;
            border: none;
            color: white;
            padding: 15px 32px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 10px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        Passwort zurücksetzen
    </div>
    <div class="content">
        <p>Hallo {{ $firstname }},</p>
        <p>Wir haben eine Anfrage zum Zurücksetzen deines Passworts erhalten. Wenn du kein Passwort-Reset angefordert hast, kannst du diese E-Mail ignorieren.</p>
        <p>Um dein Passwort zurückzusetzen, klicke bitte auf den folgenden Button:</p>
        <p>
            <a href="{{ $resetUrl }}" class="button">Passwort zurücksetzen</a>
        </p>
        <p>Wenn der Button nicht funktioniert, kannst du den folgenden Link in deinen Webbrowser kopieren und einfügen:</p>
        <p>
            <a href="{{ $resetUrl }}">{{ $resetUrl }}</a>
        </p>
        <p>Dein,</p>
        <p>{{ $appName }} Team</p>
    </div>
</div>
</body>
</html>
