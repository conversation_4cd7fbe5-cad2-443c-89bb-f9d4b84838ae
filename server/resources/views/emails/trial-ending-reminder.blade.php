<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Dein Testzeitraum bei {{ $appName }} endet bald</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
        }
        .button {
            display: inline-block;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .section {
            margin-bottom: 25px;
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
        }
    </style>
</head>
<body>
    <h1>Dein Testzeitraum endet bald</h1>
    
    <div class="section">
        <p>Hallo {{ $user->person->firstname }},</p>
        
        <p>dein kostenloser Testzeitraum für dein Team <strong>"{{ $team->name }}"</strong> endet am <strong>{{ $team->trial_ends_at->format('d.m.Y') }}</strong>.</p>
        
        <p>Um alle Funktionen von {{ $appName }} weiterhin uneingeschränkt nutzen zu können, musst du ein Abonnement abzuschließen.</p>
        
        <p><a href="{{ $subscriptionUrl }}" class="button">Jetzt Abonnement abschließen</a></p>
    </div>
    
    <div class="section">
        <h2>Was passiert, wenn der Testzeitraum endet?</h2>
        
        @include('emails.partials.trial-restrictions')
        
        <p>Du kannst jederzeit ein Abonnement abschließen, um wieder alle Funktionen nutzen zu können.</p>
    </div>
    
    <div class="section">        
        <p>Schließe jetzt dein Abonnement ab. Der Betrag wird erst nach Ablauf deines Testzeitraums eingezogen und du kannst das Abonnement bis dahin jederzeit kostenfrei kündigen.</p>
        
        <p><a href="{{ $subscriptionUrl }}" class="button">Jetzt Abonnement abschließen</a></p>
    </div>
    
    <p>Bei Fragen stehen wir dir gerne zur Verfügung. Antworte einfach auf diese E-Mail.</p>
    
    <p>Viel Erfolg mit deinem Team!<br>
    Dein {{ $appName }}-Team</p>
</body>
</html>
