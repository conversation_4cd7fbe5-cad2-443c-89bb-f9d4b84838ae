@php
    $frontendUrl = config('app.frontend.url');
    $appName = config('app.name');
    $confirmUrl = $frontendUrl . $verifyURL;
@endphp

    <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Mail Adresse bestätigen</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 16px;
            line-height: 1.5;
            color: #333;
            background-color: #f5f5f5;
            padding: 30px;
        }

        .container {
            background-color: #fff;
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            border-radius: 4px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        }

        .logo {
            display: block;
            margin-bottom: 15px;
        }

        h1 {
            font-size: 24px;
            margin-bottom: 15px;
        }

        p {
            margin-bottom: 10px;
        }

        a {
            color: #3490dc;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        .button {
            display: inline-block;
            background-color: #3490dc;
            color: #fff;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 4px;
            margin-top: 10px;
        }

        .button:hover {
            background-color: #2779bd;
        }

        .footer {
            margin-top: 20px;
            font-size: 14px;
            color: #888;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>E-Mail Adresse bestätigen</h1>
    <p>
        Hallo {{ $firstname }},
    </p>
    <p>
        Danke für deine Anmeldung bei {{ $appName }}. Um deine Anmeldung abzuschließen, klicke bitte auf den
        folgenden Button:
    </p>
    <a href="{{ $confirmUrl }}" class="button" target="_blank">
        Email Adresse bestätigen
    </a>
    <p>
        Wenn du diese Verifizierung nicht angefordert hast, ignoriere diese E-Mail bitte.
    </p>
    <p>
        Dein,<br>
        {{ $appName }} Team
    </p>
    <p class="footer">
        Falls du Probleme mit dem Button oben hast, kopiere und füge die folgende URL in deinen Webbrowser ein:<br>
        <a href="{{ $confirmUrl }}" target="_blank">{{ $confirmUrl }}</a>
    </p>
</div>
</body>
</html>
