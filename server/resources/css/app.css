/* signika-negative-700 - latin */
@font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Signika Negative';
    font-style: normal;
    font-weight: 700;
    src: url('../fonts/signika-negative-v20-latin-700.eot'); /* IE9 Compat Modes */
    src: url('../fonts/signika-negative-v20-latin-700.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url('../fonts/signika-negative-v20-latin-700.woff2') format('woff2'), /* Super Modern Browsers */
    url('../fonts/signika-negative-v20-latin-700.woff') format('woff'), /* Modern Browsers */
    url('../fonts/signika-negative-v20-latin-700.ttf') format('truetype'), /* Safari, Android, iOS */
    url('../fonts/signika-negative-v20-latin-700.svg#SignikaNegative') format('svg'); /* Legacy iOS */
}


@font-face {
    font-family: 'app-logo';
    src:  url('../fonts/app-logo.eot?wdljta');
    src:  url('../fonts/app-logo.eot?wdljta#iefix') format('embedded-opentype'),
    url('../fonts/app-logo.ttf?wdljta') format('truetype'),
    url('../fonts/app-logo.woff?wdljta') format('woff'),
    url('../fonts/app-logo.svg?wdljta#app-logo') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

[class^="app-"], [class*=" app-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'app-logo', emoji !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.app-logo:before {
    content: "\e900";
    padding-right: 10px;
}



.navbar-logo, .navbar-logo:hover {
    font-family: 'Signika Negative', serif;
    color: #fbd758;
    font-weight: bold;
}

.navbar .nav-item a {
    letter-spacing: 1px;
}

.navbar-nav .active {
    text-decoration: underline !important;
}

@media all and (max-width:991px) {
    .navbar-collapse {
        background: #fff !important;

    }
    .navbar-nav > li > a {
        color: #333 !important;
    }

    .login-button-collapse,
    .dropdown-toggler {
        display: none;
    }

    .navbar-nav .dropdown {
        display: list-item;
    }
    .navbar-nav .dropdown-menu {
        display: inline-block;
        background: transparent;
        /*padding-left: 25%;*/
        width: 60%;
        line-height: 25px;
        border: none;
        border-left: 1px solid rgba(0, 0, 0, .75);;
        border-top: 1px solid rgba(0, 0, 0, .75);;
    }
    .navbar-nav .dropdown-menu > a{
        color: #333 !important;
        font-size: smaller;
    }
}

@media all and (min-width:992px) {
    .login-button-mobile {
        display: none;
    }
}

@media all and (max-width:768px) {
    .svg-wave {
        overflow: hidden;
        direction: rtl;
    }
    .svg-hero {
        width: 768px;
    }
}

.social-icons i {
    color: #000;
    font-size: 40px;
    margin-left: 15px;
    margin-right: 15px;
}
.download ul li {
    margin: 20px 5px 0 5px;
    vertical-align: middle;
}

.single-feature {
    margin-bottom: 40px;
}

.btn-black {
    background: black;
    color: white !important;
}

.section-blue {
    background: #4169e1;
    background: linear-gradient(to left, #4169e1, #7eb1e8);
    color: white;
    position: relative;
}

.iphone-container {
    margin: 0 auto;
    position: relative; /* Make the container relative for pseudo-element positioning */
    overflow: hidden; /* Hide overflowing parts of the image and frame */
    width: 360px;
    height: 640px;
}

.iphone-container::after {
    content: ""; /* No content needed for the pseudo-element */
    position: absolute; /* Make the pseudo-element absolutely positioned */
    top: 0;
    left: 0;
    width: 100%; /* Span the entire width of the container */
    height: 100%; /* Span the entire height of the container */
    background-image: url("../images/iphone15-black-portrait-frame.png"); /* Set the background image to the frame */
    background-size: cover; /* Ensure the frame covers the entire pseudo-element */
    background-repeat: no-repeat; /* Prevent repeating the frame */
    mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1) 90%, rgba(0, 0, 0, 0) 99%);
    pointer-events: none;
    z-index: 2;
}

.iphone-container::before {
    content: ""; /* No content needed for the pseudo-element */
    position: absolute; /* Make the pseudo-element absolutely positioned */
    top: 30px;
    left: 20px;
    width: 88%; /* Span the entire width of the container */
    height: 45px; /* Span the entire height of the container */
    background-color: #444e61;
    background-image: url("../images/iphone15-black-portrait-frame-status.png"); /* Set the background image to the frame */
    background-size: contain; /* Ensure the frame covers the entire pseudo-element */
    background-repeat: no-repeat; /* Prevent repeating the frame */
    border-top-left-radius: 35px;
    border-top-right-radius: 35px;
    pointer-events: none;
    z-index: 1;
}

.iphone-container img, .iphone-container video {
    position: absolute; /* Make the image absolutely positioned */
    top: 73px;
    left: 22px;
    width: 88%; /* Ensure the image fills the container */
    mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1) 95%, rgba(0, 0, 0, 0) 100%);
}

.card p,
.card .nav-item,
.tab-content li{
    font-size: 17px;
}

.nav-tabs li {
    margin-left: 3px;
    margin-right: 3px;
}
.nav-tabs a {
    position: relative;
    background: white;
    border-bottom: 0 !important;
    border-top: 1px solid !important;
    border-left: 1px solid !important;
    border-right: 1px solid !important;
}

.nav-tabs a.active,
.nav-tabs a:hover {
    background: black !important;
    color: white !important;
}

.tab-content {
    position: relative;
    z-index: 2;
}

.dropdown-item.active, .dropdown-item:active {
    background-color: transparent !important;
}

.video-section {
    background-position: left;
}
