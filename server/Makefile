setup-mac:
	brew update
	brew install php composer
	brew install drud/ddev/ddev mkcert nss

setup-ubuntu:
	sudo apt update
	sudo apt install -y software-properties-common
	sudo add-apt-repository --yes ppa:ondrej/php
	sudo apt update
	sudo apt -y install php8.3 unzip php-xml php-curl php-mbstring sshpass
	curl -sS https://getcomposer.org/installer -o /tmp/composer-setup.php
	sudo php /tmp/composer-setup.php --install-dir=/usr/local/bin --filename=composer
	sh -c "sudo -v ; curl https://rclone.org/install.sh | sudo bash"

install-docker:
	./install-docker.sh

create-deploy-image:
	./build-docker-image.sh

deps:
	composer install --no-dev --no-scripts
	yarn install

build-staging: deps
	php artisan env:decrypt --env=staging --key=${STAGING_SERVER_ENV_PASS}
	mv .env .env_bak
	mv .env.staging .env
	cp public/php.template.ini public/php.ini
	sed -i 's/{env}/staging/g' public/php.ini
	composer dump-autoload --optimize --no-dev
	yarn build
	zip -r dist.zip app bootstrap config database lang public resources routes storage artisan vendor .env composer.json
	mv .env_bak .env

push-staging:
	rclone --config blank.config -v --sftp-host access950750563.webspace-data.io --sftp-port 22 --sftp-pass ${IONOS_MAIN_ACCOUNT_PASS_RCLONE} --sftp-user ${IONOS_MAIN_ACCOUNT_USER} --sftp-path-override /kunden/homepages/18/d950750563/htdocs/staging/upload/ --transfers=1 copy dist.zip :sftp:staging/upload/

update-staging:
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'rm -rf staging/new_backend'
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'mkdir -p staging/new_backend'
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'unzip staging/upload/dist.zip -d staging/new_backend'
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'rm -rf staging/old_backend'
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'mv staging/backend staging/old_backend'
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'mv staging/new_backend staging/backend'
	sshpass -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'cd staging/backend; php8.3-cli artisan down'

migrate-staging:
	sshpass -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'cd staging/backend; php8.3-cli artisan optimize'
	sshpass -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'cd staging/backend; php8.3-cli artisan migrate --force'
	sshpass -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'cd staging/backend; php8.3-cli artisan up'
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'rm -rf staging/old_backend'

build-production: deps
	php artisan env:decrypt --env=production --key=${PRODUCTION_SERVER_ENV_PASS}
	mv .env .env_bak
	mv .env.production .env
	cp public/php.template.ini public/php.ini
	sed -i 's/{env}/production/g' public/php.ini
	composer dump-autoload --optimize --no-dev
	php artisan optimize
	php artisan config:cache
	php artisan route:cache
	yarn build
	zip -r dist.zip app bootstrap config database lang public resources routes storage artisan vendor .env composer.json
	mv .env_bak .env

push-production:
	rclone --config blank.config -v --sftp-host access950750563.webspace-data.io --sftp-port 22 --sftp-pass ${IONOS_MAIN_ACCOUNT_PASS_RCLONE} --sftp-user ${IONOS_MAIN_ACCOUNT_USER} --sftp-path-override /kunden/homepages/18/d950750563/htdocs/production/upload/ --transfers=1 copy dist.zip :sftp:production/upload/

update-production:
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'rm -rf production/new_backend'
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'mkdir -p production/new_backend'
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'unzip production/upload/dist.zip -d production/new_backend'
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'rm -rf production/old_backend'
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'mv production/backend production/old_backend'
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'mv production/new_backend production/backend'
	sshpass -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'cd production/backend; php8.3-cli artisan down'

migrate-production:
	sshpass -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'cd production/backend; php8.3-cli artisan optimize'
	sshpass -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'cd production/backend; php8.3-cli artisan migrate --force'
	sshpass -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'cd production/backend; php8.3-cli artisan up'
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'rm -rf production/old_backend'

dd-migrate:
	ddev artisan migrate

dd-model-helper:
	ddev artisan ide-helper:models -W -R

update: dd-migrate dd-model-helper
	composer update

test-push-notification:
	php artisan app:send-push-notification --env=valet
