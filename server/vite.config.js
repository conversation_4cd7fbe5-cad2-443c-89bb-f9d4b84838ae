import {defineConfig} from 'vite';
import laravel from 'laravel-vite-plugin';

let url;
try{
    url = new URL(`${process.env.DDEV_PRIMARY_URL}`);
} catch (error) {
    // DDEV_PRIMARY_URL is not available on staging / test / production
    // since the dev server is not used there, we can replace the URL with a dummy / static URL
    url = new URL('https://server.ddev.site');
}
url.port = '5173';

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/js/app.js'],
            refresh: true,
        }),
    ],
    server: { // config for laravel vite setup within ddev
        // https://ddev.com/blog/working-with-vite-in-ddev/
        // respond to all network requests
        host: '0.0.0.0',
        hmr: {
            host: url.hostname,
            protocol: 'wss'
        },
        port: url.port,
        strictPort: true,
        // Defines the origin of the generated asset URLs during development,
        // this will also be used for the public/hot file (Vite devserver URL)
        origin: url.origin,
    }
});
