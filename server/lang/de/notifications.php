<?php

declare(strict_types=1);

return [
    'user_joined' => [
        'title' => 'Mitglied beigetreten',
        'body' => ':name ist als :member dem Team :team beigetreten.',
    ],
    'team_event_created' => [
        'title' => 'Neuer Termin',
        'time_suffix' => 'Uhr',
        'body' => ':eventName wurde im Team :team erstellt.',
    ],
    'team_event_series_created' => [
        'title' => 'Neue Terminserie im Team :team',
        'body' => ':eventName wurde für :time erstellt: :rrule',
    ],
    'team_event_changed' => [
        'title' => 'Termin aktualisiert',
        'body' => ':eventName wurde :action im Team :team',
        'meeting_rescheduled' => ':eventName wurde auf :datetime im Team :team neu geplant',
        'meeting_time_changed' => 'Der Treffzeitpunkt für den Termin :eventName wurde auf :meeting_time im Team :team geändert',
        'meeting_location_changed' => 'Der Ort für den Termin :eventName wurde auf :meeting_location im Team :team geändert',
        'time_begin_and_location_changed' => 'Die Uhrzeit für den Termin :eventName wurde auf :datetime und der Ort auf :meeting_location im Team :team geändert',
        'meeting_length_changed' => 'Die Dauer des Termins :eventName wurde auf :meeting_length im Team :team geändert',
    ],
    'vote_reminder' => [
        'title' => 'Abstimmungserinnerung',
        'body' => 'Vergiss nicht, für den Termin :eventName im Team :team abzustimmen.',
    ],
    'event_reminder' => [
        'title' => 'Terminerinnerung',
        'body' => 'Vergiss nicht den Termin :eventTitle im Team :teamName :dateTimeText',
        'datetime' => [
            'today' => 'heute um :time',
            'tomorrow' => 'morgen um :time',
            'other' => 'am :date um :time',
        ],
    ],
];
