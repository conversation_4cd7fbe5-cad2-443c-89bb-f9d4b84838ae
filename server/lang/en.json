{"(and :count more error)": "(and :count more error)", "(and :count more errors)": "(and :count more errors)", "All rights reserved.": "All rights reserved.", "Forbidden": "Forbidden", "Go to page :page": "Go to page :page", "Hello!": "Hello!", "If you did not create an account, no further action is required.": "If you did not create an account, no further action is required.", "If you did not request a password reset, no further action is required.": "If you did not request a password reset, no further action is required.", "If you're having trouble clicking the \":actionText\" button, copy and paste the URL below\ninto your web browser:": "If you're having trouble clicking the \":actionText\" button, copy and paste the URL below\ninto your web browser:", "Login": "<PERSON><PERSON>", "Logout": "Logout", "Not Found": "Not Found", "of": "of", "Page Expired": "Page Expired", "Pagination Navigation": "Pagination Navigation", "Payment Required": "Payment Required", "Please click the button below to verify your email address.": "Please click the button below to verify your email address.", "Regards": "Regards", "Register": "Register", "Reset Password": "Reset Password", "Reset Password Notification": "Reset Password Notification", "results": "results", "Server Error": "Server Error", "Service Unavailable": "Service Unavailable", "Showing": "Showing", "The :attribute must be at least :length characters and contain at least one number.": "The :attribute must be at least :length characters and contain at least one number.", "The :attribute must be at least :length characters and contain at least one special character and one number.": "The :attribute must be at least :length characters and contain at least one special character and one number.", "The :attribute must be at least :length characters and contain at least one special character.": "The :attribute must be at least :length characters and contain at least one special character.", "The :attribute must be at least :length characters and contain at least one uppercase character and one number.": "The :attribute must be at least :length characters and contain at least one uppercase character and one number.", "The :attribute must be at least :length characters and contain at least one uppercase character and one special character.": "The :attribute must be at least :length characters and contain at least one uppercase character and one special character.", "The :attribute must be at least :length characters and contain at least one uppercase character, one number, and one special character.": "The :attribute must be at least :length characters and contain at least one uppercase character, one number, and one special character.", "The :attribute must be at least :length characters and contain at least one uppercase character.": "The :attribute must be at least :length characters and contain at least one uppercase character.", "The :attribute must be at least :length characters.": "The :attribute must be at least :length characters.", "The given data was invalid.": "The given data was invalid.", "The provided password does not match your current password.": "The provided password does not match your current password.", "The provided password was incorrect.": "The provided password was incorrect.", "The provided two factor authentication code was invalid.": "The provided two factor authentication code was invalid.", "The provided two factor recovery code was invalid.": "The provided two factor recovery code was invalid.", "This password reset link will expire in :count minutes.": "This password reset link will expire in :count minutes.", "to": "to", "Toggle navigation": "Toggle navigation", "Too Many Requests": "Too Many Requests", "Unauthorized": "Unauthorized", "Verify Email Address": "Verify Em<PERSON> Address", "Whoops!": "Whoops!", "You are receiving this email because we received a password reset request for your account.": "You are receiving this email because we received a password reset request for your account."}