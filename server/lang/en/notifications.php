<?php

declare(strict_types=1);

return [
    'user_joined' => [
        'title' => 'Member joined',
        'body' => ':name has joined the team :team as :member.',
    ],
    'team_event_created' => [
        'title' => 'New event',
        'time_suffix' => 'o\'clock',
        'body' => ':eventName has been created in team :team.',
    ],
    'team_event_series_created' => [
        'title' => 'New event series in team :team',
        'body' => ':eventName has been created for :time: :rrule',
    ],
    'team_event_changed' => [
        'title' => 'Event updated',
        'body' => ':eventName has been changed in team :team',
        'meeting_rescheduled' => ':eventName has been rescheduled to :datetime in team :team',
        'meeting_time_changed' => ':eventName meeting time has been changed to :meeting_time in team :team',
        'meeting_location_changed' => ':eventName meeting location has been changed to :meeting_location in team :team',
        'time_begin_and_location_changed' => ':eventName meeting time has been changed to :datetime and location to :meeting_location in team :team',
        'meeting_length_changed' => ':eventName meeting length has been changed to :meeting_length in team :team',
    ],
    'vote_reminder' => [
        'title' => 'Vote reminder',
        'body' => 'Don\'t forget to vote for the event :eventName in team :team',
    ],
    'event_reminder' => [
        'title' => 'Event reminder',
        'body' => 'Don\'t forget about :eventTitle in team :teamName :dateTimeText',
        'datetime' => [
            'today' => 'today at :time',
            'tomorrow' => 'tomorrow at :time',
            'other' => 'on :date at :time',
        ],
    ],
];
