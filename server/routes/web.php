<?php

use App\Http\Controllers\ContactController;
use Illuminate\Support\Facades\Route;
use Spatie\Honeypot\ProtectAgainstSpam;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('main');
})->name('home');

Route::get('/features', function () {
    return view('features');
})->name('features');

Route::middleware(ProtectAgainstSpam::class)->group(function () {
    Route::post('/contact/send', [ContactController::class, 'sendEmail'])->name('contact.us.contact');
});

Route::post(
    'stripe/webhook',
    '\App\Http\Controllers\StripeWebhookController@handleWebhook',
)->name('stripe.webhook');


if(!\App::isProduction()) {
    Route::get('preview-mail', function () {
//        $markdown = new \Illuminate\Mail\Markdown(view(), config('mail.markdown') ?? []);
//        $data = "Your data to be use in blade file";
//        return $markdown->render("emails.team-created-welcome", [$data]);
        $mail = new \App\Mail\TeamCreatedWelcomeEmail(\App\Models\User::firstOrFail(), \App\Models\Team::firstOrFail());
        return $mail->render();
    });
}

Route::middleware(\Kyranb\Footprints\Middleware\CaptureAttributionDataMiddleware::class)->group(function () {
    Route::get('/r/{referrer}', function($referrer) {
        // https://eltini.atlassian.net/wiki/spaces/CLUBMANAGE/pages/593461249/Bannerplatzierungen
        return redirect()->route('home');
    });
});

//Route::get('{any}', function () {
//    return redirect()->away(config('app.frontend.url'));
//})->where('any', '.*')->name('home');
