<?php

use App\Http\Controllers\Api\V1\AbsenceController;
use App\Http\Controllers\Api\V1\AdminStatsTeamController;
use App\Http\Controllers\Api\V1\TeamEventController;
use App\Http\Controllers\Api\V1\TeamEventVoteController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ICalendarController;
use App\Http\Controllers\InviteController;
use App\Http\Controllers\PasswordResetController;
use App\Http\Controllers\PushNotificationController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\TeamLedgerController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\VerificationController;
use App\Http\Controllers\WaitListController;
use App\JsonApi\V1\Absences\AbsenceSchema;
use App\JsonApi\V1\AdminStatsTeams\AdminStatsTeamSchema;
use App\JsonApi\V1\Comments\CommentSchema;
use App\JsonApi\V1\DeviceInfo\DeviceInfoSchema;
use App\JsonApi\V1\ICalendars\ICalendarSchema;
use App\JsonApi\V1\Invites\InviteSchema;
use App\JsonApi\V1\Persons\PersonSchema;
use App\JsonApi\V1\TeamEvents\TeamEventSchema;
use App\JsonApi\V1\TeamEventSeries\TeamEventSeriesSchema;
use App\JsonApi\V1\TeamEventTaskConfigs\TeamEventTaskConfigSchema;
use App\JsonApi\V1\TeamEventTasks\TeamEventTaskSchema;
use App\JsonApi\V1\TeamEventVotes\TeamEventVoteSchema;
use App\JsonApi\V1\TeamLedgerClaims\TeamLedgerClaimSchema;
use App\JsonApi\V1\TeamLedgerDues\TeamLedgerDuesSchema;
use App\JsonApi\V1\TeamLedgerFines\TeamLedgerFineSchema;
use App\JsonApi\V1\TeamLedgers\TeamLedgerSchema;
use App\JsonApi\V1\TeamLedgerTransactions\TeamLedgerTransactionSchema;
use App\JsonApi\V1\TeamMembers\TeamMemberSchema;
use App\JsonApi\V1\Teams\TeamSchema;
use App\JsonApi\V1\TeamStatsRanges\TeamStatsRangeSchema;
use App\JsonApi\V1\Users\UserSchema;
use App\Services\NumoLiveDemoService;
use App\Types\FeatureType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use LaravelJsonApi\Laravel\Facades\JsonApiRoute;
use LaravelJsonApi\Laravel\Http\Controllers\JsonApiController;
use LaravelJsonApi\Laravel\Routing\ResourceRegistrar;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// All routes requiring authentication
Route::middleware('auth:sanctum')->group(function () {

    Route::prefix('user')->name('user.')->group(function () {
        Route::get('/', fn(Request $request) => $request->user());
        Route::post('/delete', [UserController::class, 'delete'])->name('delete');
        Route::post('/leaveTeam/{member}', [UserController::class, 'leaveTeam'])->name('leave-team');
    });

    Route::post('/user/update-device-info', [UserController::class, 'updateDeviceInfo']);
    Route::get('/user/push-notification-capable-devices', [UserController::class, 'listPushNotificationCapableDevices'])->name('user.list-push-notification-capable-devices');

    Route::prefix('verify-mail')->group(function () {
        Route::post('/resend', [VerificationController::class, 'triggerVerification']);
        Route::post('/confirm', [VerificationController::class, 'confirmVerification']);
    });

    Route::prefix('invite')->name('invite.')->group(function () {
        Route::post('/accept', [InviteController::class, 'accept'])->name('accept');
    });

    Route::prefix('push')->name('push')->group(function () {
        Route::post('/test', [PushNotificationController::class, 'sendUserInitiatedTestNotification'])->name('test');
        Route::post('/received', [PushNotificationController::class, 'markPushNotificationAsReceived'])->name('received');
        Route::get('/stats', [PushNotificationController::class, 'getStatistics'])->name('stats');
    });
    Route::prefix('teamLedger')->name('teamLedger.')->group(function () {
        Route::post('/setClaimStatus/{claim}/{status}', [TeamLedgerController::class, 'setClaimStatus'])->name('setClaimStatus');
        Route::get('/getCreditBalance/{ledger}/{member?}', [TeamLedgerController::class, 'getCreditBalance'])->name('getCreditBalance')->withTrashed();
    });
    Route::prefix('subscription')->name('subscription.')->group(function () {
        Route::post('/team/{team}/create-checkout-session/{priceType}', [SubscriptionController::class, 'createCheckoutSession'])->name('createCheckoutSession');
        Route::post('/team/{team}/resume', [SubscriptionController::class, 'resumeSubscription'])->name('resume');
        Route::post('/team/{team}/cancel', [SubscriptionController::class, 'cancelSubscription'])->name('cancel');
        Route::get('/team/{team}/invoices', [SubscriptionController::class, 'getInvoices'])->name('getInvoices');
        Route::get('/team/{team}/prices', [SubscriptionController::class, 'getPrices']);
    });
    
    
    Route::prefix('admin')->name('admin.')->group(function () {
        Route::post('/demo/resetData', function (Request $request, NumoLiveDemoService $demoService) {
            if(\Feature::active(FeatureType::ADMIN_STATS->value)) {
                $demoService->resetDemoData();
            }
        });
    });
});

Route::post('/error', function (Request $request) {
    throw new Exception('Sentry Backend Error Test');
});

// All routes not requiring authentication
Route::prefix('auth')->group(function () {
    Route::post('/register', [AuthController::class, 'register'])->name('register');
    Route::post('/login', [AuthController::class, 'login'])->name('login');
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

    Route::post('/addToWaitList', [WaitListController::class, 'addToWaitList'])->name('addToWaitList');

    Route::post('/password/forgot', [PasswordResetController::class, 'forgotPassword']);
    Route::post('/password/reset', [PasswordResetController::class, 'resetPassword']);

    Route::get('/', function (Request $request) {
        echo 'hello api';
    });
});

Route::get('/icalendar/{iCalendar}', [ICalendarController::class, 'show'])
    ->name('icalendar.publicLink');

Route::prefix('events')->group(function () {
    Route::post('/sendgrid', [\App\Http\Controllers\SendGridController::class, 'event']);
});

Route::post('/webhooks/brevo/failed', [\App\Http\Controllers\BrevoWebhookController::class, 'failed']);

// All routes for the JSON:API
JsonApiRoute::server('v1')->prefix('v1')->resources(function (ResourceRegistrar $server) {
    $server->resource(TeamSchema::type(), JsonApiController::class);
    $server->resource(InviteSchema::type(), JsonApiController::class);
    $server->resource(TeamMemberSchema::type(), JsonApiController::class);
    $server->resource(PersonSchema::type(), JsonApiController::class);
    $server->resource(UserSchema::type(), JsonApiController::class);
    $server->resource(TeamEventSchema::type(), TeamEventController::class);
    $server->resource(TeamEventVoteSchema::type(), TeamEventVoteController::class);
    $server->resource(TeamEventSeriesSchema::type(), JsonApiController::class);
    $server->resource(CommentSchema::type(), JsonApiController::class);
    $server->resource(AbsenceSchema::type(), AbsenceController::class);
    $server->resource(TeamEventTaskConfigSchema::type(), JsonApiController::class);
    $server->resource(TeamEventTaskSchema::type(), JsonApiController::class);
    $server->resource(TeamStatsRangeSchema::type(), JsonApiController::class);
    $server->resource(DeviceInfoSchema::type(), JsonApiController::class);
    $server->resource(TeamLedgerSchema::type(), JsonApiController::class)
        ->except('update', 'destroy');
    $server->resource(TeamLedgerTransactionSchema::type(), JsonApiController::class);
    $server->resource(TeamLedgerFineSchema::type(), JsonApiController::class);
    $server->resource(TeamLedgerDuesSchema::type(), JsonApiController::class);
    $server->resource(TeamLedgerClaimSchema::type(), JsonApiController::class);
    $server->resource(AdminStatsTeamSchema::type(), AdminStatsTeamController::class);
    $server->resource(ICalendarSchema::type(), JsonApiController::class);
});

