parameters:
	ignoreErrors:
		-
			message: "#^Access to an undefined property object\\:\\:\\$id\\.$#"
			count: 1
			path: app/Console/Commands/DevicesList.php

		-
			message: "#^Method App\\\\Http\\\\Controllers\\\\Api\\\\V1\\\\AdminStatsTeamController\\:\\:searched\\(\\) has parameter \\$data with no type specified\\.$#"
			count: 1
			path: app/Http/Controllers/Api/V1/AdminStatsTeamController.php

		-
			message: "#^Parameter \\#2 \\$user of method App\\\\Services\\\\PushNotificationService\\:\\:handleOnVoteChange\\(\\) expects App\\\\Models\\\\User, App\\\\Models\\\\User\\|null given\\.$#"
			count: 1
			path: app/Http/Controllers/Api/V1/TeamEventVoteController.php

		-
			message: "#^Cannot call method generateEmailVerificationToken\\(\\) on App\\\\Models\\\\User\\|null\\.$#"
			count: 1
			path: app/Http/Controllers/AuthController.php

		-
			message: "#^Cannot call method hasVerifiedEmail\\(\\) on App\\\\Models\\\\User\\|null\\.$#"
			count: 1
			path: app/Http/Controllers/AuthController.php

		-
			message: "#^Parameter \\#1 \\$haystack of function str_contains expects string, string\\|null given\\.$#"
			count: 2
			path: app/Http/Controllers/AuthController.php

		-
			message: "#^Parameter \\#1 \\$user of class Illuminate\\\\Auth\\\\Events\\\\Registered constructor expects Illuminate\\\\Contracts\\\\Auth\\\\Authenticatable, App\\\\Models\\\\User\\|null given\\.$#"
			count: 1
			path: app/Http/Controllers/AuthController.php

		-
			message: "#^Parameter \\#1 \\$user of method App\\\\Http\\\\Controllers\\\\AuthController\\:\\:createLoginResponse\\(\\) expects App\\\\Models\\\\User, App\\\\Models\\\\User\\|null given\\.$#"
			count: 1
			path: app/Http/Controllers/AuthController.php

		-
			message: "#^Parameter \\#1 \\$user of method App\\\\Http\\\\Controllers\\\\AuthController\\:\\:createTokenFromRequest\\(\\) expects App\\\\Models\\\\User, App\\\\Models\\\\User\\|null given\\.$#"
			count: 1
			path: app/Http/Controllers/AuthController.php

		-
			message: "#^Access to an undefined property App\\\\Models\\\\TeamMember\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\TeamMember\\>\\:\\:\\$statusRole\\.$#"
			count: 1
			path: app/Http/Controllers/InviteController.php

		-
			message: "#^Access to an undefined property App\\\\Models\\\\TeamMember\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\TeamMember\\>\\:\\:\\$team\\.$#"
			count: 1
			path: app/Http/Controllers/InviteController.php

		-
			message: "#^Call to an undefined method App\\\\Models\\\\TeamMember\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\TeamMember\\>\\:\\:isAvailableForTeamInvite\\(\\)\\.$#"
			count: 1
			path: app/Http/Controllers/InviteController.php

		-
			message: "#^Call to an undefined method App\\\\Models\\\\TeamMember\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\TeamMember\\>\\:\\:save\\(\\)\\.$#"
			count: 1
			path: app/Http/Controllers/InviteController.php

		-
			message: "#^Call to an undefined method App\\\\Models\\\\TeamMember\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\TeamMember\\>\\:\\:statusRole\\(\\)\\.$#"
			count: 1
			path: app/Http/Controllers/InviteController.php

		-
			message: "#^Parameter \\#1 \\$model of method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOneOrMany\\<App\\\\Models\\\\TeamMember,App\\\\Models\\\\Person,Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\TeamMember\\>\\>\\:\\:save\\(\\) expects App\\\\Models\\\\TeamMember, App\\\\Models\\\\TeamMember\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\TeamMember\\> given\\.$#"
			count: 1
			path: app/Http/Controllers/InviteController.php

		-
			message: "#^Parameter \\#2 \\$member of method App\\\\Services\\\\PushNotificationService\\:\\:handleOnTeamJoin\\(\\) expects App\\\\Models\\\\TeamMember, App\\\\Models\\\\TeamMember\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\TeamMember\\> given\\.$#"
			count: 1
			path: app/Http/Controllers/InviteController.php

		-
			message: "#^Parameter \\#1 \\$user of method App\\\\Services\\\\PasswordResetService\\:\\:sendPasswordResetEmail\\(\\) expects App\\\\Models\\\\User, App\\\\Models\\\\User\\|null given\\.$#"
			count: 1
			path: app/Http/Controllers/PasswordResetController.php

		-
			message: "#^Cannot call method format\\(\\) on string\\.$#"
			count: 1
			path: app/Http/Controllers/TeamLedgerController.php

		-
			message: "#^Cannot call method getCurrency\\(\\) on int\\|null\\.$#"
			count: 1
			path: app/Http/Controllers/TeamLedgerController.php

		-
			message: "#^Cannot access property \\$id on App\\\\Models\\\\PersonalAccessToken\\|null\\.$#"
			count: 1
			path: app/Http/Controllers/UserController.php

		-
			message: "#^Cannot access property \\$id on App\\\\Models\\\\User\\|null\\.$#"
			count: 1
			path: app/Http/Controllers/UserController.php

		-
			message: "#^Cannot access property \\$installation_id on App\\\\Models\\\\PersonalAccessToken\\|null\\.$#"
			count: 2
			path: app/Http/Controllers/UserController.php

		-
			message: "#^Cannot access property \\$manufacturer on App\\\\Models\\\\DeviceInfo\\|null\\.$#"
			count: 1
			path: app/Http/Controllers/UserController.php

		-
			message: "#^Cannot access property \\$name on App\\\\Models\\\\DeviceInfo\\|null\\.$#"
			count: 1
			path: app/Http/Controllers/UserController.php

		-
			message: "#^Cannot access property \\$platform on App\\\\Models\\\\DeviceInfo\\|null\\.$#"
			count: 1
			path: app/Http/Controllers/UserController.php

		-
			message: "#^Cannot access property \\$user on App\\\\Models\\\\Person\\|null\\.$#"
			count: 1
			path: app/Http/Controllers/UserController.php

		-
			message: "#^Cannot call method hasPermissionForTeam\\(\\) on App\\\\Models\\\\Person\\|null\\.$#"
			count: 1
			path: app/Http/Controllers/UserController.php

		-
			message: "#^Cannot call method save\\(\\) on App\\\\Models\\\\PersonalAccessToken\\|null\\.$#"
			count: 1
			path: app/Http/Controllers/UserController.php

		-
			message: "#^Call to an undefined method object\\:\\:isRelation\\(\\)\\.$#"
			count: 1
			path: app/JsonApi/CommonRessourceRequest.php

		-
			message: "#^Cannot call method format\\(\\) on Carbon\\\\Carbon\\|string\\.$#"
			count: 1
			path: app/JsonApi/CommonSchema.php

		-
			message: "#^Method App\\\\JsonApi\\\\CommonSchema\\:\\:iso8601DateFormat\\(\\) should return Closure\\(Carbon\\\\Carbon\\|null\\)\\: \\(string\\|null\\) but returns Closure\\(Carbon\\\\Carbon\\|null\\)\\: \\(Carbon\\\\Carbon\\|string\\|null\\)\\.$#"
			count: 1
			path: app/JsonApi/CommonSchema.php

		-
			message: "#^Method App\\\\JsonApi\\\\CommonSchema\\:\\:iso8601TimeFormat\\(\\) should return Closure\\(Carbon\\\\Carbon\\|null\\)\\: \\(string\\|null\\) but returns Closure\\(Carbon\\\\Carbon\\|null\\)\\: \\(Carbon\\\\Carbon\\|string\\|null\\)\\.$#"
			count: 1
			path: app/JsonApi/CommonSchema.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\AdminStatsTeams\\\\AdminStatsTeamSchema\\:\\:fields\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: app/JsonApi/V1/AdminStatsTeams/AdminStatsTeamSchema.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\AdminStatsTeams\\\\AdminStatsTeamSchema\\:\\:filters\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: app/JsonApi/V1/AdminStatsTeams/AdminStatsTeamSchema.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\Comments\\\\CommentRequest\\:\\:rules\\(\\) should return array\\<string, array\\<int, Illuminate\\\\Validation\\\\Rule\\|string\\>\\> but returns array\\<string, array\\<int, LaravelJsonApi\\\\Validation\\\\Rules\\\\HasOne\\|string\\>\\>\\.$#"
			count: 1
			path: app/JsonApi/V1/Comments/CommentRequest.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\Invites\\\\InviteRequest\\:\\:rules\\(\\) should return array\\<string, array\\<int, Illuminate\\\\Validation\\\\Rule\\|string\\>\\> but returns array\\<string, array\\<int, LaravelJsonApi\\\\Validation\\\\Rules\\\\HasOne\\|string\\>\\>\\.$#"
			count: 1
			path: app/JsonApi/V1/Invites/InviteRequest.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\Persons\\\\PersonQuery\\:\\:rules\\(\\) should return array\\<string, array\\<int, Illuminate\\\\Contracts\\\\Validation\\\\Rule\\|string\\>\\> but returns array\\<string, array\\<int, LaravelJsonApi\\\\Validation\\\\Rules\\\\AllowedCountableFields\\|LaravelJsonApi\\\\Validation\\\\Rules\\\\AllowedFieldSets\\|LaravelJsonApi\\\\Validation\\\\Rules\\\\AllowedFilterParameters\\|LaravelJsonApi\\\\Validation\\\\Rules\\\\AllowedIncludePaths\\|string\\>\\|LaravelJsonApi\\\\Validation\\\\Rules\\\\ParameterNotSupported\\>\\.$#"
			count: 1
			path: app/JsonApi/V1/Persons/PersonQuery.php

		-
			message: "#^Cannot access property \\$id on App\\\\Models\\\\User\\|null\\.$#"
			count: 1
			path: app/JsonApi/V1/Server.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamEventSeries\\\\TeamEventSeriesRequest\\:\\:rules\\(\\) should return array\\<string, array\\<int, Illuminate\\\\Validation\\\\Rule\\|string\\>\\> but returns array\\<string, array\\<int, App\\\\Rules\\\\RRule\\|LaravelJsonApi\\\\Validation\\\\Rules\\\\HasOne\\|string\\>\\>\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamEventSeries/TeamEventSeriesRequest.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamEventTaskConfigs\\\\TeamEventTaskConfigRequest\\:\\:rules\\(\\) should return array\\<string, array\\<int, Illuminate\\\\Validation\\\\Rule\\|string\\>\\> but returns array\\<string, array\\<int, LaravelJsonApi\\\\Validation\\\\Rules\\\\HasOne\\|string\\>\\>\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamEventTaskConfigs/TeamEventTaskConfigRequest.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamEventTasks\\\\TeamEventTaskRequest\\:\\:rules\\(\\) should return array\\<string, array\\<int, Illuminate\\\\Validation\\\\Rule\\|string\\>\\> but returns array\\<string, array\\<int, LaravelJsonApi\\\\Validation\\\\Rules\\\\HasOne\\|string\\>\\>\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamEventTasks/TeamEventTaskRequest.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamEventVotes\\\\TeamEventVoteRequest\\:\\:rules\\(\\) should return array\\<string, array\\<int, Illuminate\\\\Validation\\\\Rule\\|string\\>\\> but returns array\\<string, array\\<int, LaravelJsonApi\\\\Validation\\\\Rules\\\\HasOne\\|string\\>\\>\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamEventVotes/TeamEventVoteRequest.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamEvents\\\\TeamEventQuery\\:\\:rules\\(\\) should return array\\<string, array\\<int, Illuminate\\\\Contracts\\\\Validation\\\\Rule\\|string\\>\\> but returns array\\<string, array\\<int, LaravelJsonApi\\\\Validation\\\\Rules\\\\AllowedCountableFields\\|LaravelJsonApi\\\\Validation\\\\Rules\\\\AllowedFieldSets\\|LaravelJsonApi\\\\Validation\\\\Rules\\\\AllowedFilterParameters\\|LaravelJsonApi\\\\Validation\\\\Rules\\\\AllowedIncludePaths\\|string\\>\\|LaravelJsonApi\\\\Validation\\\\Rules\\\\ParameterNotSupported\\>\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamEvents/TeamEventQuery.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamEvents\\\\TeamEventRequest\\:\\:rules\\(\\) should return array\\<string, array\\<int, Illuminate\\\\Validation\\\\Rule\\|string\\>\\> but returns array\\<string, array\\<int, LaravelJsonApi\\\\Validation\\\\Rules\\\\AllowedFilterParameters\\|LaravelJsonApi\\\\Validation\\\\Rules\\\\DateTimeIso8601\\|LaravelJsonApi\\\\Validation\\\\Rules\\\\HasOne\\|string\\>\\>\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamEvents/TeamEventRequest.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamLedgerClaims\\\\TeamLedgerClaimCollectionQuery\\:\\:rules\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamLedgerClaims/TeamLedgerClaimCollectionQuery.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamLedgerClaims\\\\TeamLedgerClaimRequest\\:\\:rules\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamLedgerClaims/TeamLedgerClaimRequest.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamLedgerClaims\\\\TeamLedgerClaimSchema\\:\\:fields\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamLedgerClaims/TeamLedgerClaimSchema.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamLedgerClaims\\\\TeamLedgerClaimSchema\\:\\:filters\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamLedgerClaims/TeamLedgerClaimSchema.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamLedgerDues\\\\TeamLedgerDuesRequest\\:\\:rules\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamLedgerDues/TeamLedgerDuesRequest.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamLedgerDues\\\\TeamLedgerDuesSchema\\:\\:fields\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamLedgerDues/TeamLedgerDuesSchema.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamLedgerDues\\\\TeamLedgerDuesSchema\\:\\:filters\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamLedgerDues/TeamLedgerDuesSchema.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamLedgerFines\\\\TeamLedgerFineRequest\\:\\:rules\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamLedgerFines/TeamLedgerFineRequest.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamLedgerFines\\\\TeamLedgerFineSchema\\:\\:fields\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamLedgerFines/TeamLedgerFineSchema.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamLedgerFines\\\\TeamLedgerFineSchema\\:\\:filters\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamLedgerFines/TeamLedgerFineSchema.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamLedgerTransactions\\\\TeamLedgerTransactionCollectionQuery\\:\\:rules\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamLedgerTransactions/TeamLedgerTransactionCollectionQuery.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamLedgerTransactions\\\\TeamLedgerTransactionRequest\\:\\:rules\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamLedgerTransactions/TeamLedgerTransactionRequest.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamLedgerTransactions\\\\TeamLedgerTransactionSchema\\:\\:fields\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamLedgerTransactions/TeamLedgerTransactionSchema.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamLedgerTransactions\\\\TeamLedgerTransactionSchema\\:\\:filters\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamLedgerTransactions/TeamLedgerTransactionSchema.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamLedgers\\\\TeamLedgerRequest\\:\\:rules\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamLedgers/TeamLedgerRequest.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamLedgers\\\\TeamLedgerResource\\:\\:attributes\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamLedgers/TeamLedgerResource.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamLedgers\\\\TeamLedgerResource\\:\\:relationships\\(\\) return type has no value type specified in iterable type iterable\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamLedgers/TeamLedgerResource.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamLedgers\\\\TeamLedgerSchema\\:\\:fields\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamLedgers/TeamLedgerSchema.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamLedgers\\\\TeamLedgerSchema\\:\\:filters\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamLedgers/TeamLedgerSchema.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\TeamStatsRanges\\\\TeamStatsRangeRequest\\:\\:rules\\(\\) should return array\\<string, array\\<int, Illuminate\\\\Validation\\\\Rule\\|string\\>\\> but returns array\\<string, array\\<int, LaravelJsonApi\\\\Validation\\\\Rules\\\\HasOne\\|string\\>\\>\\.$#"
			count: 1
			path: app/JsonApi/V1/TeamStatsRanges/TeamStatsRangeRequest.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\Teams\\\\TeamQuery\\:\\:rules\\(\\) should return array\\<string, array\\<int, Illuminate\\\\Contracts\\\\Validation\\\\Rule\\|string\\>\\> but returns array\\<string, array\\<int, LaravelJsonApi\\\\Validation\\\\Rules\\\\AllowedCountableFields\\|LaravelJsonApi\\\\Validation\\\\Rules\\\\AllowedFieldSets\\|LaravelJsonApi\\\\Validation\\\\Rules\\\\AllowedFilterParameters\\|LaravelJsonApi\\\\Validation\\\\Rules\\\\AllowedIncludePaths\\|string\\>\\|LaravelJsonApi\\\\Validation\\\\Rules\\\\ParameterNotSupported\\>\\.$#"
			count: 1
			path: app/JsonApi/V1/Teams/TeamQuery.php

		-
			message: "#^Method App\\\\JsonApi\\\\V1\\\\Users\\\\UserCollectionQuery\\:\\:rules\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: app/JsonApi/V1/Users/<USER>

		-
			message: "#^Property App\\\\Models\\\\TeamLedgerClaim\\:\\:\\$amount \\(int\\|null\\) does not accept Cknow\\\\Money\\\\Money\\|null\\.$#"
			count: 3
			path: app/Models/Abstracts/TeamLedgerClaimable.php

		-
			message: "#^Method App\\\\Models\\\\DeviceInfo\\:\\:getDisplayModel\\(\\) should return string but returns string\\|null\\.$#"
			count: 2
			path: app/Models/DeviceInfo.php

		-
			message: "#^Parameter \\#1 \\$string of function strtolower expects string, string\\|null given\\.$#"
			count: 3
			path: app/Models/DeviceInfo.php

		-
			message: "#^Method App\\\\Models\\\\Invite\\:\\:getRelatedTeam\\(\\) should return App\\\\Models\\\\Team but returns App\\\\Models\\\\Team\\|null\\.$#"
			count: 1
			path: app/Models/Invite.php

		-
			message: "#^Cannot access property \\$person_id on App\\\\Models\\\\User\\|null\\.$#"
			count: 1
			path: app/Models/Scopes/LimitToCurrentUserTeamsScope.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\<App\\\\Models\\\\TeamEventVote, \\$this\\(App\\\\Models\\\\Team\\)\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\: TRelatedModel, TIntermediateModel, TDeclaringModel$#"
			count: 1
			path: app/Models/Team.php

		-
			message: "#^Method App\\\\Models\\\\Team\\:\\:getTeamRole\\(\\) should return App\\\\Models\\\\TeamRole but returns App\\\\Models\\\\TeamRole\\|null\\.$#"
			count: 1
			path: app/Models/Team.php

		-
			message: "#^Method App\\\\Models\\\\Team\\:\\:votes\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\<App\\\\Models\\\\TeamEventVote, \\$this\\(App\\\\Models\\\\Team\\)\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\<App\\\\Models\\\\TeamEventVote, App\\\\Models\\\\TeamEvent, \\$this\\(App\\\\Models\\\\Team\\)\\>\\.$#"
			count: 1
			path: app/Models/Team.php

		-
			message: "#^Cannot call method createRecurringEvents\\(\\) on App\\\\Models\\\\TeamEventSeries\\|null\\.$#"
			count: 1
			path: app/Models/TeamEvent.php

		-
			message: "#^Cannot call method isEventWithinAbsenceRange\\(\\) on App\\\\Models\\\\Absence\\|null\\.$#"
			count: 1
			path: app/Models/TeamEvent.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\<App\\\\Models\\\\TeamMember, \\$this\\(App\\\\Models\\\\TeamLedger\\)\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\: TRelatedModel, TIntermediateModel, TDeclaringModel$#"
			count: 2
			path: app/Models/TeamLedger.php

		-
			message: "#^Method App\\\\Models\\\\TeamLedger\\:\\:formerMembers\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\<App\\\\Models\\\\TeamMember, \\$this\\(App\\\\Models\\\\TeamLedger\\)\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\<App\\\\Models\\\\TeamMember, App\\\\Models\\\\Team, \\$this\\(App\\\\Models\\\\TeamLedger\\)\\>\\.$#"
			count: 1
			path: app/Models/TeamLedger.php

		-
			message: "#^Method App\\\\Models\\\\TeamLedger\\:\\:members\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\<App\\\\Models\\\\TeamMember, \\$this\\(App\\\\Models\\\\TeamLedger\\)\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasManyThrough\\<App\\\\Models\\\\TeamMember, App\\\\Models\\\\Team, \\$this\\(App\\\\Models\\\\TeamLedger\\)\\>\\.$#"
			count: 1
			path: app/Models/TeamLedger.php

		-
			message: "#^Parameter \\#1 \\$other of method Cknow\\\\Money\\\\Money\\:\\:greaterThanOrEqual\\(\\) expects Cknow\\\\Money\\\\Money\\|Money\\\\Money, int\\|null given\\.$#"
			count: 1
			path: app/Models/TeamLedger.php

		-
			message: "#^Parameter \\#1 \\.\\.\\.\\$subtrahends of method Cknow\\\\Money\\\\Money\\:\\:subtract\\(\\) expects Cknow\\\\Money\\\\Money\\|Money\\\\Money, int\\|null given\\.$#"
			count: 1
			path: app/Models/TeamLedger.php

		-
			message: "#^Cannot access property \\$amount on App\\\\Models\\\\Abstracts\\\\TeamLedgerClaimable\\|null\\.$#"
			count: 1
			path: app/Models/TeamLedgerClaim.php

		-
			message: "#^Cannot access property \\$item on App\\\\Models\\\\Abstracts\\\\TeamLedgerClaimable\\|null\\.$#"
			count: 1
			path: app/Models/TeamLedgerClaim.php

		-
			message: "#^Cannot access property \\$ledger on App\\\\Models\\\\Abstracts\\\\TeamLedgerClaimable\\|null\\.$#"
			count: 1
			path: app/Models/TeamLedgerClaim.php

		-
			message: "#^Cannot access property \\$title on App\\\\Models\\\\Abstracts\\\\TeamLedgerClaimable\\|null\\.$#"
			count: 1
			path: app/Models/TeamLedgerClaim.php

		-
			message: "#^Cannot call method isBefore\\(\\) on string\\.$#"
			count: 1
			path: app/Models/TeamLedgerClaim.php

		-
			message: "#^Parameter \\#2 \\$dueDate of static method App\\\\Models\\\\TeamLedgerClaim\\:\\:getClaimableForDueDate\\(\\) expects Carbon\\\\Carbon, string given\\.$#"
			count: 1
			path: app/Models/TeamLedgerClaim.php

		-
			message: "#^Property App\\\\Models\\\\TeamLedgerClaim\\:\\:\\$amount \\(int\\|null\\) does not accept Cknow\\\\Money\\\\Money\\|null\\.$#"
			count: 1
			path: app/Models/TeamLedgerClaim.php

		-
			message: "#^Cannot call method isSameDay\\(\\) on string\\.$#"
			count: 1
			path: app/Models/TeamLedgerDues.php

		-
			message: "#^Cannot call method toDateTime\\(\\) on string\\.$#"
			count: 1
			path: app/Models/TeamLedgerDues.php

		-
			message: "#^Cannot call method greaterThan\\(\\) on int\\.$#"
			count: 1
			path: app/Models/TeamLedgerTransaction.php

		-
			message: "#^Cannot call method isPositive\\(\\) on int\\.$#"
			count: 1
			path: app/Models/TeamLedgerTransaction.php

		-
			message: "#^Cannot call method lessThan\\(\\) on int\\<min, \\-1\\>\\|int\\<1, max\\>\\.$#"
			count: 1
			path: app/Models/TeamLedgerTransaction.php

		-
			message: "#^Parameter \\#1 \\.\\.\\.\\$addends of method Cknow\\\\Money\\\\Money\\:\\:add\\(\\) expects Cknow\\\\Money\\\\Money\\|Money\\\\Money, int given\\.$#"
			count: 1
			path: app/Models/TeamLedgerTransaction.php

		-
			message: "#^Cannot call method lessThanOrEqualTo\\(\\) on string\\.$#"
			count: 1
			path: app/Models/TeamMember.php

		-
			message: "#^Method App\\\\Models\\\\TeamRole\\:\\:createRoleIfNotExists\\(\\) should return App\\\\Models\\\\TeamRole but returns App\\\\Models\\\\TeamRole\\|null\\.$#"
			count: 1
			path: app/Models/TeamRole.php

		-
			message: "#^Generic type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Models\\\\RegistrationInformation\\> in PHPDoc tag @return does not specify all template types of class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\: TRelatedModel, TDeclaringModel$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Method App\\\\Models\\\\User\\:\\:registrationInformation\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Models\\\\RegistrationInformation\\> but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<App\\\\Models\\\\RegistrationInformation, \\$this\\(App\\\\Models\\\\User\\)\\>\\.$#"
			count: 1
			path: app/Models/User.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$id\\.$#"
			count: 1
			path: app/Notifications/Channels/RecordingFcmChannel.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$uuid\\.$#"
			count: 1
			path: app/Notifications/Channels/RecordingFcmChannel.php

		-
			message: "#^Cannot access property \\$title on NotificationChannels\\\\Fcm\\\\Resources\\\\Notification\\|null\\.$#"
			count: 1
			path: app/Notifications/Channels/RecordingFcmChannel.php

		-
			message: "#^Parameter \\#1 \\$model of method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphTo\\<Illuminate\\\\Database\\\\Eloquent\\\\Model,App\\\\Models\\\\TrackedNotification\\>\\:\\:associate\\(\\) expects Illuminate\\\\Database\\\\Eloquent\\\\Model\\|null, object given\\.$#"
			count: 1
			path: app/Notifications/Channels/RecordingFcmChannel.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: app/Notifications/Push/UserJoinedTeamNotification.php

		-
			message: "#^Using nullsafe method call on non\\-nullable type App\\\\Models\\\\Person\\. Use \\-\\> instead\\.$#"
			count: 1
			path: app/Notifications/Push/UserJoinedTeamNotification.php

		-
			message: "#^Cannot call method translatedFormat\\(\\) on Carbon\\\\Carbon\\|string\\.$#"
			count: 1
			path: app/Notifications/Push/VoteForUpcomingEventChangedNotification.php

		-
			message: "#^Method App\\\\Notifications\\\\Push\\\\VoteForUpcomingEventChangedNotification\\:\\:constructMessage\\(\\) should return string but returns array\\|string\\.$#"
			count: 2
			path: app/Notifications/Push/VoteForUpcomingEventChangedNotification.php

		-
			message: "#^Access to an undefined property App\\\\Models\\\\TeamMember\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\TeamMember\\>\\:\\:\\$team\\.$#"
			count: 1
			path: app/Policies/InvitePolicy.php

		-
			message: "#^Cannot access constant class on Illuminate\\\\Database\\\\Eloquent\\\\Model\\|null\\.$#"
			count: 3
			path: app/Policies/InvitePolicy.php

		-
			message: "#^Method App\\\\Policies\\\\InvitePolicy\\:\\:create\\(\\) should return bool but returns bool\\|null\\.$#"
			count: 1
			path: app/Policies/InvitePolicy.php

		-
			message: "#^Method App\\\\Policies\\\\InvitePolicy\\:\\:delete\\(\\) should return bool but returns bool\\|null\\.$#"
			count: 1
			path: app/Policies/InvitePolicy.php

		-
			message: "#^Method App\\\\Policies\\\\InvitePolicy\\:\\:getTargetTeamFromRequest\\(\\) should return App\\\\Models\\\\Team\\|null but returns App\\\\Models\\\\Team\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\Team\\>\\.$#"
			count: 1
			path: app/Policies/InvitePolicy.php

		-
			message: "#^Method App\\\\Policies\\\\InvitePolicy\\:\\:update\\(\\) should return bool but returns bool\\|null\\.$#"
			count: 1
			path: app/Policies/InvitePolicy.php

		-
			message: "#^Method App\\\\Policies\\\\InvitePolicy\\:\\:view\\(\\) should return bool but returns bool\\|null\\.$#"
			count: 1
			path: app/Policies/InvitePolicy.php

		-
			message: "#^Parameter \\#1 \\$invitableClass of method App\\\\Policies\\\\InvitePolicy\\:\\:canCreateUpdateOrDelete\\(\\) expects string, class\\-string\\<Illuminate\\\\Database\\\\Eloquent\\\\Model\\>\\|null given\\.$#"
			count: 3
			path: app/Policies/InvitePolicy.php

		-
			message: "#^Parameter \\#1 \\$invitableClass of method App\\\\Policies\\\\InvitePolicy\\:\\:canCreateUpdateOrDelete\\(\\) expects string, string\\|null given\\.$#"
			count: 1
			path: app/Policies/InvitePolicy.php

		-
			message: "#^Parameter \\#3 \\$team of method App\\\\Policies\\\\InvitePolicy\\:\\:canCreateUpdateOrDelete\\(\\) expects App\\\\Models\\\\Team, App\\\\Models\\\\Team\\|null given\\.$#"
			count: 1
			path: app/Policies/InvitePolicy.php

		-
			message: "#^Method App\\\\Policies\\\\TeamEventPolicy\\:\\:getTeamFromRequest\\(\\) should return App\\\\Models\\\\Team but returns App\\\\Models\\\\Team\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\Team\\>\\.$#"
			count: 1
			path: app/Policies/TeamEventPolicy.php

		-
			message: "#^Method App\\\\Policies\\\\TeamEventTaskConfigPolicy\\:\\:getTeamFromRequest\\(\\) should return App\\\\Models\\\\Team but returns App\\\\Models\\\\Team\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\Team\\>\\.$#"
			count: 1
			path: app/Policies/TeamEventTaskConfigPolicy.php

		-
			message: "#^Access to an undefined property App\\\\Models\\\\TeamEvent\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\TeamEvent\\>\\:\\:\\$team\\.$#"
			count: 1
			path: app/Policies/TeamEventTaskPolicy.php

		-
			message: "#^Method App\\\\Policies\\\\TeamEventTaskPolicy\\:\\:getTeamMemberFromRequest\\(\\) should return App\\\\Models\\\\TeamMember\\|null but returns App\\\\Models\\\\TeamMember\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\TeamMember\\>\\.$#"
			count: 1
			path: app/Policies/TeamEventTaskPolicy.php

		-
			message: "#^Method App\\\\Policies\\\\TeamEventVotePolicy\\:\\:getTeamMemberFromRequest\\(\\) should return App\\\\Models\\\\TeamMember but returns App\\\\Models\\\\TeamMember\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\TeamMember\\>\\.$#"
			count: 1
			path: app/Policies/TeamEventVotePolicy.php

		-
			message: "#^Access to an undefined property App\\\\Models\\\\TeamLedger\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\TeamLedger\\>\\:\\:\\$team\\.$#"
			count: 1
			path: app/Policies/TeamLedgerClaimPolicy.php

		-
			message: "#^Access to an undefined property App\\\\Models\\\\TeamLedger\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\TeamLedger\\>\\:\\:\\$team\\.$#"
			count: 1
			path: app/Policies/TeamLedgerDuesPolicy.php

		-
			message: "#^Access to an undefined property App\\\\Models\\\\TeamLedger\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\TeamLedger\\>\\:\\:\\$team\\.$#"
			count: 1
			path: app/Policies/TeamLedgerFinePolicy.php

		-
			message: "#^Method App\\\\Policies\\\\TeamLedgerPolicy\\:\\:getTeamFromRequest\\(\\) should return App\\\\Models\\\\Team but returns App\\\\Models\\\\Team\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\Team\\>\\.$#"
			count: 1
			path: app/Policies/TeamLedgerPolicy.php

		-
			message: "#^Access to an undefined property App\\\\Models\\\\TeamLedger\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\TeamLedger\\>\\:\\:\\$team\\.$#"
			count: 1
			path: app/Policies/TeamLedgerTransactionPolicy.php

		-
			message: "#^Method App\\\\Policies\\\\TeamMemberPolicy\\:\\:getTeamFromRequest\\(\\) should return App\\\\Models\\\\Team but returns App\\\\Models\\\\Team\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\Team\\>\\.$#"
			count: 1
			path: app/Policies/TeamMemberPolicy.php

		-
			message: "#^Method App\\\\Policies\\\\TeamStatsRangePolicy\\:\\:getTeamFromRequest\\(\\) should return App\\\\Models\\\\Team but returns App\\\\Models\\\\Team\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\Team\\>\\.$#"
			count: 1
			path: app/Policies/TeamStatsRangePolicy.php

		-
			message: "#^Parameter \\#2 \\$team of method App\\\\Models\\\\Person\\:\\:hasPermissionForTeam\\(\\) expects App\\\\Models\\\\Team, App\\\\Models\\\\Team\\|null given\\.$#"
			count: 2
			path: app/Policies/TeamStatsRangePolicy.php

		-
			message: "#^Cannot access offset 'host' on array\\{scheme\\?\\: string, host\\?\\: string, port\\?\\: int\\<0, 65535\\>, user\\?\\: string, pass\\?\\: string, path\\?\\: string, query\\?\\: string, fragment\\?\\: string\\}\\|false\\.$#"
			count: 1
			path: app/Providers/FrontendTestServiceProvider.php

		-
			message: "#^Cannot access offset 'host' on array\\{scheme\\?\\: string, host\\?\\: string, port\\?\\: int\\<0, 65535\\>, user\\?\\: string, pass\\?\\: string, path\\?\\: string, query\\?\\: string, fragment\\?\\: string\\}\\|false\\.$#"
			count: 2
			path: app/Providers/RouteServiceProvider.php

		-
			message: "#^Parameter \\#1 \\$column of method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany\\<App\\\\Models\\\\TeamEventVote,App\\\\Models\\\\TeamEvent\\>\\:\\:where\\(\\) expects array\\<int\\|string, mixed\\>\\|\\(Closure\\(Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany\\<App\\\\Models\\\\TeamEventVote, App\\\\Models\\\\TeamEvent\\>\\)\\: mixed\\)\\|Illuminate\\\\Contracts\\\\Database\\\\Query\\\\Expression\\|string, Closure\\(Illuminate\\\\Database\\\\Eloquent\\\\Builder\\)\\: Illuminate\\\\Database\\\\Eloquent\\\\Builder given\\.$#"
			count: 1
			path: app/Services/NumoLiveDemoService.php

		-
			message: "#^Parameter \\#1 \\$column of method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany\\<App\\\\Models\\\\TeamMember,App\\\\Models\\\\Team\\>\\:\\:where\\(\\) expects array\\<int\\|string, mixed\\>\\|\\(Closure\\(Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany\\<App\\\\Models\\\\TeamMember, App\\\\Models\\\\Team\\>\\)\\: mixed\\)\\|Illuminate\\\\Contracts\\\\Database\\\\Query\\\\Expression\\|string, Closure\\(Illuminate\\\\Database\\\\Eloquent\\\\Builder\\)\\: void given\\.$#"
			count: 2
			path: app/Services/NumoLiveDemoService.php

		-
			message: "#^Parameter \\#2 \\$time of method App\\\\Services\\\\PushNotificationService\\:\\:mergeDateTime\\(\\) expects Carbon\\\\Carbon\\|string, string\\|null given\\.$#"
			count: 1
			path: app/Services/PushNotificationService.php

		-
			message: "#^Parameter \\#1 \\$title of static method Illuminate\\\\Support\\\\Str\\:\\:slug\\(\\) expects string, bool\\|string given\\.$#"
			count: 1
			path: config/cache.php

		-
			message: "#^Parameter \\#1 \\$title of static method Illuminate\\\\Support\\\\Str\\:\\:slug\\(\\) expects string, bool\\|string given\\.$#"
			count: 1
			path: config/database.php

		-
			message: "#^Parameter \\#2 \\$string of function explode expects string, bool\\|string given\\.$#"
			count: 1
			path: config/sanctum.php

		-
			message: "#^Parameter \\#1 \\$title of static method Illuminate\\\\Support\\\\Str\\:\\:slug\\(\\) expects string, bool\\|string given\\.$#"
			count: 1
			path: config/session.php

		-
			message: "#^Parameter \\#1 \\$url of function parse_url expects string, bool\\|string\\|null given\\.$#"
			count: 1
			path: config/session.php

		-
			message: "#^Method Database\\\\Factories\\\\UserFactory\\:\\:verified\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Factories\\\\Factory does not specify its types\\: TModel$#"
			count: 1
			path: database/factories/UserFactory.php

		-
			message: "#^Method Database\\\\Factories\\\\UserFactory\\:\\:withPerson\\(\\) return type with generic class Illuminate\\\\Database\\\\Eloquent\\\\Factories\\\\Factory does not specify its types\\: TModel$#"
			count: 1
			path: database/factories/UserFactory.php

		-
			message: "#^Method class@anonymous/database/migrations/2014_10_12_000000_create_users_table\\.php\\:7\\:\\:down\\(\\) has no return type specified\\.$#"
			count: 1
			path: database/migrations/2014_10_12_000000_create_users_table.php

		-
			message: "#^Method class@anonymous/database/migrations/2014_10_12_000000_create_users_table\\.php\\:7\\:\\:up\\(\\) has no return type specified\\.$#"
			count: 1
			path: database/migrations/2014_10_12_000000_create_users_table.php

		-
			message: "#^Method class@anonymous/database/migrations/2014_10_12_100000_create_password_resets_table\\.php\\:7\\:\\:down\\(\\) has no return type specified\\.$#"
			count: 1
			path: database/migrations/2014_10_12_100000_create_password_resets_table.php

		-
			message: "#^Method class@anonymous/database/migrations/2014_10_12_100000_create_password_resets_table\\.php\\:7\\:\\:up\\(\\) has no return type specified\\.$#"
			count: 1
			path: database/migrations/2014_10_12_100000_create_password_resets_table.php

		-
			message: "#^Method class@anonymous/database/migrations/2019_08_19_000000_create_failed_jobs_table\\.php\\:7\\:\\:down\\(\\) has no return type specified\\.$#"
			count: 1
			path: database/migrations/2019_08_19_000000_create_failed_jobs_table.php

		-
			message: "#^Method class@anonymous/database/migrations/2019_08_19_000000_create_failed_jobs_table\\.php\\:7\\:\\:up\\(\\) has no return type specified\\.$#"
			count: 1
			path: database/migrations/2019_08_19_000000_create_failed_jobs_table.php

		-
			message: "#^Method class@anonymous/database/migrations/2019_12_14_000001_create_personal_access_tokens_table\\.php\\:7\\:\\:down\\(\\) has no return type specified\\.$#"
			count: 1
			path: database/migrations/2019_12_14_000001_create_personal_access_tokens_table.php

		-
			message: "#^Method class@anonymous/database/migrations/2019_12_14_000001_create_personal_access_tokens_table\\.php\\:7\\:\\:up\\(\\) has no return type specified\\.$#"
			count: 1
			path: database/migrations/2019_12_14_000001_create_personal_access_tokens_table.php

		-
			message: "#^Method class@anonymous/database/migrations/2022_11_01_000001_create_features_table\\.php\\:7\\:\\:down\\(\\) has no return type specified\\.$#"
			count: 1
			path: database/migrations/2022_11_01_000001_create_features_table.php

		-
			message: "#^Method class@anonymous/database/migrations/2022_11_01_000001_create_features_table\\.php\\:7\\:\\:up\\(\\) has no return type specified\\.$#"
			count: 1
			path: database/migrations/2022_11_01_000001_create_features_table.php

		-
			message: "#^Method class@anonymous/database/migrations/2023_02_07_095725_create_teams_table\\.php\\:7\\:\\:down\\(\\) has no return type specified\\.$#"
			count: 1
			path: database/migrations/2023_02_07_095725_create_teams_table.php

		-
			message: "#^Method class@anonymous/database/migrations/2023_02_07_095725_create_teams_table\\.php\\:7\\:\\:up\\(\\) has no return type specified\\.$#"
			count: 1
			path: database/migrations/2023_02_07_095725_create_teams_table.php

		-
			message: "#^Method class@anonymous/database/migrations/2023_02_11_184618_create_sessions_table\\.php\\:7\\:\\:down\\(\\) has no return type specified\\.$#"
			count: 1
			path: database/migrations/2023_02_11_184618_create_sessions_table.php

		-
			message: "#^Method class@anonymous/database/migrations/2023_02_11_184618_create_sessions_table\\.php\\:7\\:\\:up\\(\\) has no return type specified\\.$#"
			count: 1
			path: database/migrations/2023_02_11_184618_create_sessions_table.php

		-
			message: "#^Property App\\\\Models\\\\User\\:\\:\\$id \\(string\\) does not accept int\\<1, max\\>\\.$#"
			count: 1
			path: database/migrations/2023_03_31_091050_change_user_primary_key_to_uuid.php

		-
			message: "#^Method class@anonymous/database/migrations/2023_04_01_163908_make_team_member_id_unique\\.php\\:7\\:\\:down\\(\\) has no return type specified\\.$#"
			count: 1
			path: database/migrations/2023_04_01_163908_make_team_member_id_unique.php

		-
			message: "#^Method class@anonymous/database/migrations/2023_04_01_163908_make_team_member_id_unique\\.php\\:7\\:\\:up\\(\\) has no return type specified\\.$#"
			count: 1
			path: database/migrations/2023_04_01_163908_make_team_member_id_unique.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$fcm_token\\.$#"
			count: 1
			path: database/migrations/2024_03_23_130206_create_device_info_table.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$installation_id\\.$#"
			count: 1
			path: database/migrations/2024_03_23_130206_create_device_info_table.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$manufacturer\\.$#"
			count: 1
			path: database/migrations/2024_03_23_130206_create_device_info_table.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$model\\.$#"
			count: 1
			path: database/migrations/2024_03_23_130206_create_device_info_table.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$name\\.$#"
			count: 1
			path: database/migrations/2024_03_23_130206_create_device_info_table.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$os\\.$#"
			count: 1
			path: database/migrations/2024_03_23_130206_create_device_info_table.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$platform\\.$#"
			count: 1
			path: database/migrations/2024_03_23_130206_create_device_info_table.php

		-
			message: "#^Method RenameInstallationIdToIdInDeviceInfoTable\\:\\:down\\(\\) has no return type specified\\.$#"
			count: 1
			path: database/migrations/2024_04_07_173256_rename_installation_id_to_id_in_device_info_table.php

		-
			message: "#^Method RenameInstallationIdToIdInDeviceInfoTable\\:\\:up\\(\\) has no return type specified\\.$#"
			count: 1
			path: database/migrations/2024_04_07_173256_rename_installation_id_to_id_in_device_info_table.php

		-
			message: "#^Method CreateFootprintsTable\\:\\:getConnectionName\\(\\) has no return type specified\\.$#"
			count: 1
			path: database/migrations/2024_11_07_130908_create_footprints_table.php

		-
			message: "#^Cannot access property \\$email_verification_token on App\\\\Models\\\\User\\|null\\.$#"
			count: 1
			path: database/seeders/CypressSeeder.php

		-
			message: "#^Cannot access property \\$email_verified_at on App\\\\Models\\\\User\\|null\\.$#"
			count: 1
			path: database/seeders/CypressSeeder.php

		-
			message: "#^Cannot access property \\$person on App\\\\Models\\\\User\\|null\\.$#"
			count: 1
			path: database/seeders/CypressSeeder.php

		-
			message: "#^Cannot call method createMember\\(\\) on App\\\\Models\\\\Team\\|null\\.$#"
			count: 1
			path: database/seeders/CypressSeeder.php

		-
			message: "#^Cannot call method save\\(\\) on App\\\\Models\\\\User\\|null\\.$#"
			count: 1
			path: database/seeders/CypressSeeder.php

		-
			message: "#^Parameter \\#1 \\$model of method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOneOrMany\\<App\\\\Models\\\\TeamMember,App\\\\Models\\\\Person,Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Models\\\\TeamMember\\>\\>\\:\\:save\\(\\) expects App\\\\Models\\\\TeamMember, App\\\\Models\\\\TeamMember\\|null given\\.$#"
			count: 1
			path: database/seeders/CypressSeeder.php

		-
			message: "#^Undefined variable\\: \\$this$#"
			count: 1
			path: routes/console.php

		-
			message: "#^Parameter \\#2 \\$subject of function preg_match expects string, string\\|false given\\.$#"
			count: 2
			path: scripts/dump_models.php
