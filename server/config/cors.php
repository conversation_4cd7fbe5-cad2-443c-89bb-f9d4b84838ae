<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Cross-Origin Resource Sharing (CORS) Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your settings for cross-origin resource sharing
    | or "CORS". This determines what cross-origin operations may execute
    | in web browsers. You are free to adjust these settings as needed.
    |
    | To learn more: https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS
    |
    | blog post with overview and examples: https://www.stackhawk.com/blog/laravel-cors/
    |
    */

    'paths' => ['*'], // , 'sanctum/csrf-cookie'
    'allowed_methods' => ['*'],
    // These origins are finicky and need to be exact. So we just configure them manually here for the moment.
    //'allowed_origins' => ['http://*************:9501', 'https://app.numo-app.com', 'https://app-staging.numo-app.com', 'http://localhost*', 'capacitor://localhost', 'file://'],
    'allowed_origins' => ['*'],
    //'allowed_origins_patterns' => [],
    'allowed_headers' => ['*'],
    'exposed_headers' => [],
    'max_age' => 0,
    'supports_credentials' => true,

];
