includes:
    - vendor/larastan/larastan/extension.neon
    - phpstan-baseline.neon

parameters:
    paths:
        - app/
        - config/
        - database/
        - lang/
        - public/
        - routes/
        - scripts/

    level: 8

#    stubFiles:
#        - _ide_helper_models.php
#        - _ide_helper.php

#    bootstrapFiles:
#        - classAliases.php

#    ignoreErrors:
#        - '#PHPDoc tag @var#'
#
#    excludePaths:
#        - ./*/*/FileToBeExcluded.php
#
#    checkMissingIterableValueType: false
