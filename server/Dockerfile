FROM ubuntu:22.04

RUN apt update; \
    DEBIAN_FRONTEND=noninteractive apt install -y software-properties-common; \
    apt-key adv --keyserver keyserver.ubuntu.com --recv-keys 71DAEAAB4AD4CAB6; \
    add-apt-repository --yes ppa:ondrej/php; \
    apt update; \
    DEBIAN_FRONTEND=noninteractive apt install -y php8.3 unzip php-xml php-curl php8.3-mbstring php8.3-pdo php8.3-pdo-mysql php8.3-bcmath php8.3-intl sshpass curl sudo make zip unzip git mysql-client; \
    curl -sS https://getcomposer.org/installer -o /tmp/composer-setup.php; \
    php /tmp/composer-setup.php --install-dir=/usr/local/bin --filename=composer; \
    sh -c "curl https://rclone.org/install.sh | bash"; \
    curl -sL https://deb.nodesource.com/setup_18.x | bash -; \
    apt-get install -y nodejs; \
    curl -sS https://dl.yarnpkg.com/debian/pubkey.gpg | apt-key add - ; \
    echo "deb https://dl.yarnpkg.com/debian/ stable main" | tee /etc/apt/sources.list.d/yarn.list; \
    apt-get update && apt-get install yarn; \
    DEBIAN_FRONTEND=noninteractive apt-get install -y libgtk2.0-0 libgtk-3-0 libgbm-dev libnotify-dev libnss3 libxss1 libasound2 libxtst6 xauth xvfb;

ENV CI=1 \
# disable shared memory X11 affecting Cypress v4 and Chrome
# https://github.com/cypress-io/cypress-docker-images/issues/270
  QT_X11_NO_MITSHM=1 \
  _X11_NO_MITSHM=1 \
  _MITSHM=0 \
  # point Cypress at the /root/cache no matter what user account is used
  # see https://on.cypress.io/caching
  CYPRESS_CACHE_FOLDER=/root/.cache/Cypress \
  # Allow projects to reference globally installed cypress
  NODE_PATH=/usr/local/lib/node_modules
