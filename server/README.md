# numo (server)

Organize your club

## Useful links

### Project Tools

- Error tracing: https://eltini.sentry.io/

### Lib documentation

#### Frontend

- Quasar: https://quasar.dev/docs
    - Icon Libraries: https://quasar.dev/vue-components/icon
    - Material Icons: https://fonts.google.com/icons?icon.set=Material+Icons
    - Material Design Icons: https://pictogrammers.com/library/mdi/
- Vue.js: https://vuejs.org/api/sfc-script-setup.html
    - Vue Router: https://router.vuejs.org/guide/#javascript
    - Pinia Store: https://pinia.vuejs.org/core-concepts/
        - Pinia ORM ([not in use](https://github.com/eltini/clubmanager/pull/66) at the
          moment): https://pinia-orm.codedredd.de/guide/model/decorators
    - Vue composition utilities: https://vueuse.org/guide/
- TanStack Query: https://tanstack.com/query/latest/docs/vue/overview
- Spraypaint (JSON:API): https://www.graphiti.dev/js/index
- Lodash: https://lodash.com/docs/
- Storybook: https://storybook.js.org/docs/react/get-started/whats-a-story
- RRule: https://github.com/jakubroztocil/rrule
- GSAP (Animation): https://greensock.com/docs/v3
  - FLIP Plugin: https://greensock.com/docs/v3/Plugins/Flip

#### Backend

- Laravel: https://laravel.com/docs
- Laravel Artisan Cheatsheet: https://artisan.page/
- Laravel JSON:API: https://laraveljsonapi.io/docs/3.0/getting-started/
- Laravel Typescript-transformer: https://spatie.be/docs/typescript-transformer/
- Laravel Lang: https://laravel-lang.com/usage/update-locales.html
- RRule: https://github.com/rlanvin/php-rrule
- Clockwork (dev only): https://underground.works/clockwork/#docs-viewing-data
  - Frontend logs: https://server.ddev.site:8443/clockwork/app
- LaraLens: https://github.com/Hi-Folks/lara-lens
  - LaraLens is a Laravel artisan command to show you the current configuration of your application

## Dev Server environment setup with docker managed by DDEV.<br>

### Install php & ddev

#### MacOS

```bash
brew install php composer
brew install drud/ddev/ddev mkcert nss
```

#### Linux / Debian

```bash
# Install keys
curl -fsSL https://apt.fury.io/drud/gpg.key | gpg --dearmor | sudo tee /etc/apt/trusted.gpg.d/ddev.gpg > /dev/null
echo "deb [signed-by=/etc/apt/trusted.gpg.d/ddev.gpg] https://apt.fury.io/drud/ * *" | sudo tee /etc/apt/sources.list.d/ddev.list
# Install ddev and PHP
sudo apt update && sudo apt install -y ddev php
# Install composer
curl -sS https://getcomposer.org/installer -o /tmp/composer-setup.php
sudo php /tmp/composer-setup.php --install-dir=/usr/local/bin --filename=composer
```

#### Windows

See https://ddev.readthedocs.io/en/stable/users/install/ddev-installation/

```bash
# execute these command to create a preconfigured .env file
ddev exec "cat .env.example | sed  -E 's/DB_(HOST|DATABASE|USERNAME|PASSWORD)=(.*)/DB_\1=db/g' > .env"
ddev exec 'sed -i "s#APP_URL=.*#APP_URL=${DDEV_PRIMARY_URL}#g" .env'
ddev exec "php artisan key:generate"
````

### Start the server in development mode (e.g. email capture with MailHog)

```bash
# start /stop server
ddev start
ddev stop

#launch server in browser
ddev launch

# launch mailhog
ddev launch -m

# enable / disable xdebug
ddev xdebug on
ddev xdebug off

# see all services and urls available (web, db, phpmyadmin, mailhog...)
ddev describe
```

### Install the dependencies

```bash
ddev composer install
```

### use artisan for laravel commands

```bash
# basic usage
ddev artisan

# ide helper commands
ddev artisan ide-helper:models --write # generate PHPDocs for models to get ide autocompletion

# typescript types generation, output file will be located in client/src/types
ddev artisan typescript:transform

```

### important files and docs

- config for php version and other server setting, run `ddev start` to reload changes.
    - https://ddev.readthedocs.io/en/stable/users/extend/customization-extendibility/
    - `.ddev/config.yaml`
- XDebug configuration and troubleshooting
    - https://ddev.readthedocs.io/en/stable/users/debugging-profiling/step-debugging/
- PhpStorm Setup and DDEV Plugin
    - https://ddev.readthedocs.io/en/latest/users/install/phpstorm/
- IDE Plugin for Laravel
    - https://laravel-idea.com/
