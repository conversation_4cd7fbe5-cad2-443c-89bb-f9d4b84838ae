<?php

namespace Tests\Helper;

use JsonSerializable;
use Str;

class Relation implements JsonSerializable {

    public function __construct(
        public string $resourceType,
        public string $resourceId,
        public string $method = 'update',
    ) {}

    public function jsonSerialize(): array {
        return [
            $this->resourceType => [
                'data' => [
                    'type'   => Str::plural($this->resourceType),
                    'id'     => $this->resourceId,
                    'method' => $this->method,
                ],
            ],
        ];
    }

    public function toArray(): array {
        return $this->jsonSerialize();
    }
}
