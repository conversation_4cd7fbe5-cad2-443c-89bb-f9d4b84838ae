<?php

namespace Tests\Helper;

use JsonSerializable;

class Relationships implements JsonSerializable {

    /**
     * @var array<Relation>
     */
    public array $relationships = [];

    public function __construct(
        Relation ...$relationships,
    ) {
        $this->relationships = $relationships;
    }

    public function jsonSerialize(): mixed {
        return array_reduce(
            $this->relationships,
            fn($carry, $relationship) => array_merge($carry, $relationship->toArray()),
            []
        );
    }

    public function toArray(): array {
        return $this->jsonSerialize();
    }
}
