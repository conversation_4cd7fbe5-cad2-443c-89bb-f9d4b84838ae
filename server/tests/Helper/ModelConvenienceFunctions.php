<?php

namespace Tests\Helper;

use App\Models\Team;
use App\Models\TeamEvent;
use App\Models\TeamMember;
use App\Models\TeamRole;
use App\Models\User;
use App\Types\EventResponseType;
use App\Types\TeamEventType;
use App\Types\TeamPermissionType;
use App\Types\TeamRoleType;
use Carbon\Carbon;

trait ModelConvenienceFunctions {
    protected function createUser(string $email = '<EMAIL>', string $firstname = 'firstname test'): User {
        $user = User::createWithPerson($email, '12345678', $firstname);
        $user->refresh();
        return $user;
    }

    protected function createUserActingAs(string $email = '<EMAIL>'): User {
        $user = $this->createUser($email);
        $this->actingAs($user);
        return $user;
    }

    protected function createTeam(string $name = 'Test Team', bool $withLedger = true): Team {
        $team = Team::create([
            'name' => $name,
        ]);
        if($withLedger) {
            $team->ledger()->create();
        }
        $team->refresh();
        return $team;
    }

    /**
     * @param TeamRole $role
     * @param array<TeamPermissionType> $permissionNames
     * @return void
     */
    protected function setRolePermissions(TeamRole $role, array $permissionNames): void {
        $rolePermissions = TeamRoleType::getPermissionsForNames($permissionNames);
        $role->permissions()->detach();
        $role->permissions()->saveMany($rolePermissions);
    }

    /**
     * @param string                    $email
     * @param Team                      $team
     * @param TeamRoleType              $roleType
     * @param array<TeamPermissionType> $permissionNames
     *
     * @return TeamMember
     */
    protected function createMemberAndSetRoleWithPermissions(string $email, Team $team, TeamRoleType $roleType = TeamRoleType::MEMBER, array $permissionNames = []): TeamMember {
        $user = $this->createUser($email);
        $member = $team->createMember($email, $roleType, $user);
        $member->refresh();
        $this->setRolePermissions($member->statusRole, $permissionNames);
        return $member;
    }


    /**
     * @param Team $team
     * @param array<TeamPermissionType> $permissionNames
     * @param string $email
     * @return TeamMember
     */
    protected function createMemberWithPermissions(Team $team, array $permissionNames, string $email = 'manager'): TeamMember {
        return $this->createMemberAndSetRoleWithPermissions(
            $email,
            $team,
            TeamRoleType::MANAGER,
            $permissionNames
        );
    }

    protected function createMemberWithoutPermissions(Team $team, string $email = 'member'): TeamMember {
        return $this->createMemberAndSetRoleWithPermissions($email, $team);
    }

    protected function createTeamEvent(
        Team $team,
        TeamEventType $eventType = TeamEventType::TRAINING,
        Carbon $dateTimeBegin = null,
        EventResponseType $responseType = EventResponseType::NONE
    ): TeamEvent {
        $dateTimeBegin ??= Carbon::now();
        return $team->events()->create([
            'title' => 'Event',
            'event_type' => $eventType,
            'response_type' => $responseType,
            'date_begin' => $dateTimeBegin,
            'time_begin' => $dateTimeBegin
        ]);
    }
}
