<?php

namespace Tests\Feature;

use App\Models\Person;
use App\Models\Team;
use App\Models\TeamPermission;
use App\Models\TeamRole;
use App\Types\TeamPermissionType;
use App\Types\TeamRoleType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TeamPermissionTest extends TestCase {
    use RefreshDatabase;

    public function testPermissionTypesAdded() {
        TeamPermission::query()->delete();

        TeamPermissionType::addOrCreateDefaultPermissions();

        $this->assertDatabaseCount(TeamPermission::class, count(TeamPermissionType::cases()));
        $this->assertEquals(TeamPermission::orderBy('id')->first()->name, TeamPermissionType::cases()[0]);
    }

    public function testDefaultRolesAddedOnTeamCreate() {
        $team = Team::create(['name' => 'Test Team 1',]);

        $this->assertCount(1, $team->roles->where('name', TeamRoleType::MANAGER));
    }

    public function testDefaultPermissionsAddedOnTeamCreate() {
        $team = Team::create(['name' => 'Test Team 1',]);

        $defaultRolesAndPermissions = TeamRoleType::getDefaultRolesAndPermissions();
        $firstRoleName = array_keys($defaultRolesAndPermissions)[0];

        $firstPermissionType = $defaultRolesAndPermissions[$firstRoleName][0];
        $firstRoleType = TeamRoleType::from($firstRoleName);

        $role = $team->roles->where('name', $firstRoleType)->first();
        $this->assertEquals($firstRoleName, $role->name->value);
        $this->assertEquals($firstPermissionType, $role->permissions->first()->name);
    }

    public function testRoleCanBeAssignedToTeamMember(): void {
        $team = Team::create(['name' => 'Test Team 1',]);

        TeamPermission::query()->delete();
        TeamRole::query()->delete();

        $managerRole = $team->roles()->create(['name' => TeamRoleType::MANAGER]);
        $memberRole = $team->roles()->create(['name' => TeamRoleType::MEMBER]);

        $teamManager = $team->members()->create(['name' => 'Team Manager']);
        $teamMember = $team->members()->create(['name' => 'Team Member']);
        $teamManagerMember = $team->members()->create(['name' => 'Team Manager Member']);

        $teamManager->roles()->attach($managerRole);
        $teamMember->roles()->attach($memberRole);

        $teamManagerMember->roles()->attach($managerRole);
        $teamManagerMember->roles()->attach($memberRole);

        $this->assertEquals($managerRole->name, $team->roles->where('name', TeamRoleType::MANAGER)->first()->name);
        $this->assertEquals($memberRole->name, $team->roles->where('name', TeamRoleType::MEMBER)->first()->name);

        $this->assertEquals($managerRole->name, $teamManager->roles->where('id', $managerRole->id)->first()->name);
        $this->assertEquals($memberRole->name, $teamMember->roles->where('id', $memberRole->id)->first()->name);

        $this->assertEquals($managerRole->name, $teamManagerMember->roles->where('id', $managerRole->id)->first()->name);
        $this->assertEquals($memberRole->name, $teamManagerMember->roles->where('id', $memberRole->id)->first()->name);

        $this->assertCount(2, $managerRole->teamMembers);
        $this->assertCount(2, $memberRole->teamMembers);
    }

    public function testRolePermissionsAreAvailableForTeamMembers() {
        $team = Team::create(['name' => 'Test Team 1',]);

        TeamPermission::query()->delete();
        TeamRole::query()->delete();

        $createPermission = TeamPermission::create(['name' => TeamPermissionType::TEAM_EVENT_CREATE]);
        $updatePermission = TeamPermission::create(['name' => TeamPermissionType::TEAM_MEMBER_UPDATE]);

        $managerRole = $team->roles()->create(['name' => TeamRoleType::MANAGER]);
        $managerRole->permissions->add($createPermission);
        $managerRole->permissions->add($updatePermission);

        $memberRole = $team->roles()->create(['name' => TeamRoleType::MEMBER]);
        $memberRole->permissions->add($updatePermission);

        $teamManager = $team->members()->create(['name' => 'Team Manager']);
        $teamMember = $team->members()->create(['name' => 'Team Member']);

        $teamManager->statusRole()->associate($managerRole)->save();
        $teamMember->statusRole()->associate($memberRole)->save();

        $this->assertCount(1, $teamMember->permissions());
        $this->assertCount(2, $teamManager->permissions());

        $this->assertContains($updatePermission, $teamMember->permissions());
        $this->assertNotContains($createPermission, $teamMember->permissions());

        $this->assertContains($updatePermission, $teamManager->permissions());
        $this->assertContains($createPermission, $teamManager->permissions());
    }
    
    public function testRoleAndGroupPermissionsAreAvailableForTeamMembers(): void {
        $team = Team::create(['name' => 'Test Team 1',]);

        TeamPermission::query()->delete();
        TeamRole::query()->delete();

        $createPermission = TeamPermission::create(['name' => TeamPermissionType::TEAM_EVENT_CREATE]);
        $updatePermission = TeamPermission::create(['name' => TeamPermissionType::TEAM_MEMBER_UPDATE]);
        $deletePermission = TeamPermission::create(['name' => TeamPermissionType::TEAM_MEMBER_DELETE]);

        $managerRoleCreate = $team->roles()->create(['name' => TeamRoleType::MANAGER]);
        $managerRoleCreate->permissions()->attach($createPermission);
        $managerRoleCreate->permissions()->attach($deletePermission);

        $memberRoleUpdate = $team->roles()->create(['name' => TeamRoleType::MEMBER]);
        $memberRoleUpdate->permissions()->attach($updatePermission);
        $memberRoleUpdate->permissions()->attach($deletePermission);

        $member = $team->members()->create(['name' => 'Team Manager']);

        $member->statusRole()->associate($managerRoleCreate)->save();
        $member->roles()->attach($memberRoleUpdate);
        $member->refresh();

        $this->assertCount(3, $member->permissions());

        $this->assertTrue($member->permissions()->contains(fn (TeamPermission $permission) => $updatePermission->name === $permission->name));
        $this->assertTrue($member->permissions()->contains(fn (TeamPermission $permission) => $createPermission->name === $permission->name));
        $this->assertTrue($member->permissions()->contains(fn (TeamPermission $permission) => $deletePermission->name === $permission->name));
    }
    
    public function testRoleAndGroupPermissionsAreAvailableForPersons(): void {
        $team = Team::create(['name' => 'Test Team 1',]);

        TeamPermission::query()->delete();
        TeamRole::query()->delete();

        $createPermission = TeamPermission::create(['name' => TeamPermissionType::TEAM_EVENT_CREATE]);
        $updatePermission = TeamPermission::create(['name' => TeamPermissionType::TEAM_MEMBER_UPDATE]);
        $deletePermission = TeamPermission::create(['name' => TeamPermissionType::TEAM_MEMBER_DELETE]);

        $managerRoleCreate = $team->roles()->create(['name' => TeamRoleType::MANAGER]);
        $managerRoleCreate->permissions()->attach($createPermission);
        $managerRoleCreate->permissions()->attach($deletePermission);

        $memberRoleUpdate = $team->roles()->create(['name' => TeamRoleType::MEMBER]);
        $memberRoleUpdate->permissions()->attach($updatePermission);
        $memberRoleUpdate->permissions()->attach($deletePermission);

        $member = $team->members()->create(['name' => 'Team Manager']);

        $member->statusRole()->associate($managerRoleCreate)->save();
        $member->roles()->attach($memberRoleUpdate);

        $person = Person::create(['firstname' => 'Test Person A2 B1', 'lastname' => '']);
        $person->teamMembers->add($member);

        $this->assertCount(3, $member->permissions());

        $this->assertTrue($person->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_CREATE, $team));
        $this->assertTrue($person->hasPermissionForTeam(TeamPermissionType::TEAM_MEMBER_UPDATE, $team));
        $this->assertTrue($person->hasPermissionForTeam(TeamPermissionType::TEAM_MEMBER_DELETE, $team));
    }

    public function testTeamPermissionsAreAvailableOnPerson() {
        $teamA = Team::create(['name' => 'Test Team A',]);
        $teamB = Team::create(['name' => 'Test Team B',]);

        TeamPermission::query()->delete();
        TeamRole::query()->delete();

        $createPermission = TeamPermission::create(['name' => TeamPermissionType::TEAM_EVENT_CREATE]);
        $updatePermission = TeamPermission::create(['name' => TeamPermissionType::TEAM_MEMBER_UPDATE]);
        $deletePermission = TeamPermission::create(['name' => TeamPermissionType::TEAM_EVENT_DELETE]);

        $managerRoleTeamA = $teamA->roles()->create(['name' => TeamRoleType::MANAGER]);
        $managerRoleTeamA->permissions()->attach($createPermission);
        $managerRoleTeamA->permissions()->attach($deletePermission);

        $managerRoleTeamB = $teamB->roles()->create(['name' => TeamRoleType::MANAGER]);
        $managerRoleTeamB->permissions()->attach($createPermission);
        $managerRoleTeamB->permissions()->attach($deletePermission);

        $memberRoleTeamA = $teamA->roles()->create(['name' => TeamRoleType::MEMBER]);
        $memberRoleTeamA->permissions()->attach($deletePermission);

        $editorRoleTeamA = $teamA->roles()->create(['name' => TeamRoleType::INACTIVE]);
        $editorRoleTeamA->permissions()->attach($updatePermission);

        $memberRoleTeamB = $teamB->roles()->create(['name' => TeamRoleType::MEMBER]);
        $memberRoleTeamB->permissions()->attach($deletePermission);


        $teamManagerA = $teamA->members()->create(['name' => 'Team Manager A']);
        $teamMemberA1 = $teamA->members()->create(['name' => 'Team Member A1']);
        $teamMemberA2 = $teamA->members()->create(['name' => 'Team Member A2']);
        $teamMemberB1 = $teamB->members()->create(['name' => 'Team Member B1']);

        $teamManagerA->statusRole()->associate($managerRoleTeamA)->save();
        $teamMemberA1->statusRole()->associate($memberRoleTeamA)->save();
        $teamMemberA2->statusRole()->associate($memberRoleTeamA)->save();
        $teamMemberB1->statusRole()->associate($memberRoleTeamB)->save();

        $personMemberA1 = Person::create(['firstname' => 'Test Person A1', 'lastname' => '']);
        $personMemberA1->teamMembers->add($teamMemberA1);

        $personMemberA2_B1 = Person::create(['firstname' => 'Test Person A2 B1', 'lastname' => '']);
        $personMemberA2_B1->teamMembers->add($teamMemberA2);
        $personMemberA2_B1->teamMembers->add($teamMemberB1);

        // $personMemberA1 => $teamA: (count 1) $viewPermission
        $this::assertCount(1, $personMemberA1->permissionNamesPerTeamId()[$teamA->id]);
        $this->assertTrue($personMemberA1->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_DELETE, $teamA));

        // $personMemberA2_B1 => $teamA: (count 1) $viewPermission, $teamB: (count 1) $viewPermission
        $this::assertCount(1, $personMemberA2_B1->permissionNamesPerTeamId()[$teamA->id]);
        $this->assertTrue($personMemberA2_B1->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_DELETE, $teamA));
        $this::assertCount(1, $personMemberA2_B1->permissionNamesPerTeamId()[$teamB->id]);
        $this->assertTrue($personMemberA2_B1->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_DELETE, $teamB));
    }
}
