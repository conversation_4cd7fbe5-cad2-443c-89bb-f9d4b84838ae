<?php

namespace Tests\Feature;

use Illuminate\Support\Facades\DB;
use Tests\TestCase;

/**
 * Prints database connection information, in case there are
 * issues with the local test configuration again.
 */
class DatabaseConnectionTest extends TestCase {
    /** @test */
    public function prints_mysql_connection_info() {
        $connectionName = config('database.default');
        $connectionConfig = config("database.connections.{$connectionName}");

        // Print the MySQL connection details
        echo "\nDatabase Connection Details:\n";
        echo "Connection Name: " . $connectionName . "\n";
        echo "Driver: " . $connectionConfig['driver'] . "\n";
        echo "Host: " . $connectionConfig['host'] . "\n";
        echo "Port: " . $connectionConfig['port'] . "\n";
        echo "Database: " . $connectionConfig['database'] . "\n";
        echo "Username: " . $connectionConfig['username'] . "\n";
        echo "Password: " . $connectionConfig['password'] . "\n";

        try {
            DB::connection()->getPdo();
            echo "Connection status: Successful\n";
        } catch (\Exception $e) {
            echo "Connection status: Failed - " . $e->getMessage() . "\n";
        }

        $this->assertTrue(true);
    }
}
