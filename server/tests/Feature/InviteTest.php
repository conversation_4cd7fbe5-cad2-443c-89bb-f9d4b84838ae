<?php

namespace Tests\Feature;

use App\Models\Invite;
use App\Models\Team;
use App\Models\TeamMember;
use App\Types\TeamRoleType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class InviteTest extends TestCase {
    use RefreshDatabase;

    public function test_invite_can_be_created(): void {
        $team = Team::create([
            'name' => 'Test Team 1',
        ]);

        $teamMember = TeamMember::create([
            'name' => 'Team Member 1',
            'team_id' => $team->id,
        ]);

        $invite = new Invite();
        $invite->invitable()->associate($teamMember);
        $invite->save();

        self::assertInstanceOf(Invite::class, $invite);
        self::assertEquals($teamMember->id, $invite->invitable_id);
        self::assertNotEmpty($invite->token);
    }

    public function test_member_invite_accept(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $user = $this->createUser();
        $member = $team->createMember('Existing Member');
        $invite = $member->invite()->create();

        $this->actingAs($user)
             ->postJson(route('api.invite.accept'), [
                 'token' => $invite->token,
             ])
             ->assertOk()
        ;

        $team->refresh();
        self::assertCount(1, $user->person->teamMembers);
        self::assertEmpty($member->invite);
        self::assertEquals(
            $team->id,
            $user->person->teamMembers()->first()->team->id
        );
    }


    public function test_team_invite_accept_with_name(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $user = $this->createUser();
        $invite = $team->invite()->create();

        $newMemberName = 'New Member';

        $this->actingAs($user)
             ->postJson(route('api.invite.accept'), [
                 'token' => $invite->token,
                 'newMemberName' => $newMemberName,
             ])
             ->assertOk()
        ;

        $team->refresh();
        self::assertCount(1, $user->person->teamMembers);
        self::assertCount(1, $team->members);
        self::assertEquals(
            $team->id,
            $user->person->teamMembers()->first()->team->id
        );
    }

    public function test_team_invite_accept_with_member(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $user = $this->createUser();
        $invite = $team->invite()->create();
        $member = $team->createMember('Existing Member');

        $this->actingAs($user)
             ->postJson(route('api.invite.accept'), [
                 'token' => $invite->token,
                 'memberId' => $member->id,
             ])
             ->assertOk()
        ;

        $team->refresh();
        self::assertCount(1, $user->person->teamMembers);
        self::assertCount(1, $team->members);
        self::assertEquals(
            $team->id,
            $user->person->teamMembers()->first()->team->id
        );
    }

    public function test_team_invite_accept_inactive_member_set_active(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $user = $this->createUser();
        $invite = $team->invite()->create();
        $member = $team->createMember('Existing Member', TeamRoleType::INACTIVE);

        $this->actingAs($user)
             ->postJson(route('api.invite.accept'), [
                 'token' => $invite->token,
                 'memberId' => $member->id,
             ])
             ->assertOk()
        ;

        $member->refresh();
        self::assertEquals(TeamRoleType::MEMBER->value, $member->statusRole->name->value);
    }

    public function test_member_invite_accept_inactive_member_set_active(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $user = $this->createUser();
        $member = $team->createMember('Existing Member', TeamRoleType::INACTIVE);
        $invite = $member->invite()->create();

        $this->actingAs($user)
             ->postJson(route('api.invite.accept'), [
                 'token' => $invite->token
             ])
             ->assertOk()
        ;

        $member->refresh();
        self::assertEquals(TeamRoleType::MEMBER->value, $member->statusRole->name->value);
    }

    public function test_team_invite_accept_fails_without_member_or_name(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $user = $this->createUser();
        $invite = $team->invite()->create();

        $this->actingAs($user)
             ->postJson(route('api.invite.accept'), [
                 'token' => $invite->token
             ])
             ->assertInternalServerError()
        ;

        $team->refresh();
        self::assertCount(0, $user->person->teamMembers);
    }
}
