<?php

use App\Events\AbsenceCreated;
use App\Events\AbsenceUpdated;
use App\Models\Absence;
use App\Models\Team;
use App\Models\TeamEvent;
use App\Models\User;
use App\Types\AbsenceType;
use App\Types\EventResponseType;
use App\Types\TeamEventVoteType;
use App\Types\TeamRoleType;
use Carbon\Carbon;
use Carbon\CarbonInterval;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EventResponseTypeTest extends TestCase {
    use RefreshDatabase;

    private Team $team;
    private User $user;

    private function initiateTeamWithMembers($memberCount = 3, $inactiveMembersCount = 0): void{
        // create Team with 2 members
        $this->team = Team::create(['name' => fake()->name]);
        $this->user = User::createWithPerson('test', 'password');
        for ($i = 0; $i < $memberCount; $i++) {
            $this->team->createMember(fake()->name);
        }
        for ($i = 0; $i < $inactiveMembersCount; $i++) {
            $this->team->createMember(fake()->name, TeamRoleType::INACTIVE);
        }

        // create 1 User
        if($memberCount > 0) {
            $this->team->members()->first()->person()->associate($this->user->person);
        }
    }


    private function addEventToTeam(EventResponseType $responseType, $dateTimeBegin = null): TeamEvent {
        return $this->createTeamEvent(
            team: $this->team,
            dateTimeBegin: $dateTimeBegin,
            responseType: $responseType
        );
    }

    public function test_event_create_set_no_votes() : void {
        $this->initiateTeamWithMembers();
        $event = $this->addEventToTeam(EventResponseType::NONE);
        self::assertCount(0, $event->votes);
    }

    public function test_event_create_with_auto_yes_set_all_votes() : void {
        $this->initiateTeamWithMembers(3);
        $event = $this->addEventToTeam(EventResponseType::AUTO_YES);
        self::assertCount(3, $event->votes);
        self::assertNull($event->votes()->first()->voterPerson);
    }

    public function test_event_update_to_auto_yes_set_missing_votes_only() : void {
        $memberCount = 3;
        $this->initiateTeamWithMembers($memberCount);
        $event = $this->addEventToTeam(EventResponseType::NONE);

        $event->createVote($this->team->members()->first(), TeamEventVoteType::MAYBE, $this->user->person);

        $event->response_type = EventResponseType::AUTO_YES;
        $event->save();

        $maybeVotes = $event->votes()->whereVote(TeamEventVoteType::MAYBE)->get();
        $yesVotes = $event->votes()->whereVote(TeamEventVoteType::YES)->get();

        self::assertCount(1, $maybeVotes);
        self::assertNotNull($maybeVotes->first()->voterPerson);
        self::assertCount($memberCount-1, $yesVotes);
        self::assertNull($yesVotes->first()->voterPerson);
    }

    public function test_event_update_to_none_reset_auto_votes_only() : void {
        $memberCount = 3;
        $this->initiateTeamWithMembers($memberCount);
        $event = $this->addEventToTeam(EventResponseType::AUTO_YES);

        self::assertCount($memberCount, $event->votes);

        $event->createVote($this->team->members()->first(), TeamEventVoteType::MAYBE, $this->user->person);

        $event->response_type = EventResponseType::NONE;
        $event->save();
        $event->refresh();

        $maybeVotes = $event->votes()->whereVote(TeamEventVoteType::MAYBE)->get();
        $yesVotes = $event->votes()->whereVote(TeamEventVoteType::YES)->get();

        self::assertCount(1, $event->votes);
        self::assertCount(1, $maybeVotes);
        self::assertNotNull($maybeVotes->first()->voterPerson);
        self::assertCount(0, $yesVotes);
    }

    public function test_add_member_sets_auto_yes_on_future_events_only(): void {
        $memberCount = 3;
        $this->initiateTeamWithMembers($memberCount);
        $pastEventNone = $this->addEventToTeam(
            EventResponseType::NONE,
            Carbon::now()->sub(CarbonInterval::days(2))
        );
        $pastEventAutoYes = $this->addEventToTeam(
            EventResponseType::AUTO_YES,
            Carbon::now()->sub(CarbonInterval::days(2))
        );
        $futureEventNone = $this->addEventToTeam(
            EventResponseType::NONE,
            Carbon::now()->add(CarbonInterval::days(2))
        );
        $futureEventAutoYes = $this->addEventToTeam(
            EventResponseType::AUTO_YES,
            Carbon::now()->add(CarbonInterval::days(2))
        );

        $this->team->createMember(fake()->name);

        self::assertCount(0, $pastEventNone->votes);
        self::assertCount($memberCount, $pastEventAutoYes->votes);
        self::assertCount(0, $futureEventNone->votes);
        self::assertCount($memberCount+1, $futureEventAutoYes->votes);
    }

    public function test_absence_create_overwrites_auto_yes(): void {
        $this->initiateTeamWithMembers(0);
        $member = $this->team->createMember(user: $this->user);

        $futureEventAutoYes = $this->addEventToTeam(
            EventResponseType::AUTO_YES,
            Carbon::now()->add(CarbonInterval::days(10))
        );
        $futureEventAutoYesWithinAbsence = $this->addEventToTeam(
            EventResponseType::AUTO_YES,
            Carbon::now()->add(CarbonInterval::days(2))
        );

        $absence = Absence::create([
            'date_begin' => Carbon::now()->sub(CarbonInterval::days(1)),
            'date_end' => Carbon::now()->add(CarbonInterval::days(5)),
            'absence_type' => AbsenceType::SICK,
            'author_id' => $member->person->id,
        ]);
        $absence->teamMembers()->attach($member);
        event(new AbsenceCreated($absence));

        self::assertCount(1, $futureEventAutoYes->votes);
        self::assertCount(0, $futureEventAutoYesWithinAbsence->votes()->whereVote(TeamEventVoteType::YES)->get());
        self::assertCount(1, $futureEventAutoYesWithinAbsence->votes()->whereVote(TeamEventVoteType::NO)->get());
    }

    public function test_event_update_to_auto_yes_within_absence_does_not_set_votes(): void {
        $this->initiateTeamWithMembers(0);
        $member = $this->team->createMember(user: $this->user);

        $eventWithoutAbsence = $this->addEventToTeam(
            EventResponseType::NONE,
            Carbon::now()->add(CarbonInterval::days(10))
        );
        $eventWithinAbsence = $this->addEventToTeam(
            EventResponseType::NONE,
            Carbon::now()->add(CarbonInterval::days(2))
        );

        $absence = Absence::create([
            'date_begin' => Carbon::now()->sub(CarbonInterval::days(1)),
            'date_end' => Carbon::now()->add(CarbonInterval::days(5)),
            'absence_type' => AbsenceType::SICK,
            'author_id' => $member->person->id,
        ]);
        $absence->teamMembers()->attach($member);
        event(new AbsenceCreated($absence));

        $eventWithoutAbsence->response_type = EventResponseType::AUTO_YES;
        $eventWithoutAbsence->save();
        $eventWithinAbsence->response_type = EventResponseType::AUTO_YES;
        $eventWithinAbsence->save();

        self::assertCount(1, $eventWithoutAbsence->votes);
        self::assertCount(0, $eventWithinAbsence->votes()->whereVote(TeamEventVoteType::YES)->get());
        self::assertCount(1, $eventWithinAbsence->votes()->whereVote(TeamEventVoteType::NO)->get());
    }

    public function test_event_create_with_auto_yes_set_votes_for_active_members_only(): void {
        $this->initiateTeamWithMembers(3, 2);
        $event = $this->addEventToTeam(EventResponseType::AUTO_YES);
        self::assertCount(3, $event->votes);
        self::assertNull($event->votes()->first()->voterPerson);
    }

    public function test_set_member_inactive_overwrites_auto_yes(): void {
        $this->initiateTeamWithMembers(0);
        $member = $this->team->createMember(user: $this->user);

        $futureEventAutoYes = $this->addEventToTeam(
            EventResponseType::AUTO_YES,
            Carbon::now()->add(CarbonInterval::days(10))
        );

        self::assertCount(1, $futureEventAutoYes->votes);

        $member->statusRole()->associate($member->team->getTeamRole(TeamRoleType::INACTIVE))->save();
        $futureEventAutoYes->refresh();
        self::assertCount(0, $futureEventAutoYes->votes);
    }

    public function test_set_member_active_overwrites_auto_yes(): void {
        $this->initiateTeamWithMembers(0);
        $member = $this->team->createMember(teamRoleType: TeamRoleType::INACTIVE, user: $this->user);

        $futureEventAutoYes = $this->addEventToTeam(
            EventResponseType::AUTO_YES,
            Carbon::now()->add(CarbonInterval::days(10))
        );

        self::assertCount(0, $futureEventAutoYes->votes);

        $member->statusRole()->associate($member->team->getTeamRole(TeamRoleType::MEMBER))->save();
        $futureEventAutoYes->refresh();
        self::assertCount(1, $futureEventAutoYes->votes);
    }

    public function test_absence_delete_sets_auto_yes_for_future_events_only(): void {
        $this->initiateTeamWithMembers(0);
        $member = $this->team->createMember(user: $this->user);

        $futureEventAutoYes = $this->addEventToTeam(
            EventResponseType::AUTO_YES,
            Carbon::now()->add(CarbonInterval::days(10))
        );
        $futureEventAutoYesWithinAbsence = $this->addEventToTeam(
            EventResponseType::AUTO_YES,
            Carbon::now()->add(CarbonInterval::days(5))
        );

        $pastEventAutoYesWithinAbsence = $this->addEventToTeam(
            EventResponseType::AUTO_YES,
            Carbon::now()->add(CarbonInterval::days(1))
        );

        $absence = Absence::create([
            'date_begin' => Carbon::now()->sub(CarbonInterval::days(1)),
            'date_end' => Carbon::now()->add(CarbonInterval::days(10)),
            'absence_type' => AbsenceType::SICK,
            'author_id' => $member->person->id,
        ]);
        $absence->teamMembers()->attach($member);
        event(new AbsenceCreated($absence));

        self::assertCount(1, $futureEventAutoYes->votes);
        self::assertCount(0, $futureEventAutoYesWithinAbsence->votes()->whereVote(TeamEventVoteType::YES)->get());
        self::assertCount(1, $futureEventAutoYesWithinAbsence->votes()->whereVote(TeamEventVoteType::NO)->get());
        // $pastEvent is in future on absence create date
        self::assertCount(0, $pastEventAutoYesWithinAbsence->votes()->whereVote(TeamEventVoteType::YES)->get());
        self::assertCount(1, $pastEventAutoYesWithinAbsence->votes()->whereVote(TeamEventVoteType::NO)->get());

        // travel 3 days into future to have $pastEvent in the past
        Carbon::setTestNow(Carbon::now()->add(CarbonInterval::days(3)));

        $absence->delete();

        self::assertCount(1, $futureEventAutoYes->votes);
        self::assertCount(1, $futureEventAutoYesWithinAbsence->votes()->whereVote(TeamEventVoteType::YES)->get());
        self::assertCount(0, $futureEventAutoYesWithinAbsence->votes()->whereVote(TeamEventVoteType::NO)->get());
        // $pastEvent is in past on absence delete date
        self::assertCount(0, $pastEventAutoYesWithinAbsence->votes()->whereVote(TeamEventVoteType::YES)->get());
        self::assertCount(1, $pastEventAutoYesWithinAbsence->votes()->whereVote(TeamEventVoteType::NO)->get());

    }

    public function test_absence_update_sets_auto_yes_for_future_events_only(): void {
        $this->initiateTeamWithMembers(0);
        $member = $this->team->createMember(user: $this->user);

        $futureEventAutoYes = $this->addEventToTeam(
            EventResponseType::AUTO_YES,
            Carbon::now()->add(CarbonInterval::days(10))
        );
        $futureEventAutoYesWithinAbsence = $this->addEventToTeam(
            EventResponseType::AUTO_YES,
            Carbon::now()->add(CarbonInterval::days(5))
        );

        $pastEventAutoYesWithinAbsence = $this->addEventToTeam(
            EventResponseType::AUTO_YES,
            Carbon::now()->add(CarbonInterval::days(1))
        );

        $absence = Absence::create([
            'date_begin' => Carbon::now()->sub(CarbonInterval::days(1)),
            'date_end' => Carbon::now()->add(CarbonInterval::days(12)),
            'absence_type' => AbsenceType::SICK,
            'author_id' => $member->person->id,
        ]);
        $absenceNewEndDate = Carbon::now()->add(CarbonInterval::days(8));
        $absence->teamMembers()->attach($member);
        event(new AbsenceCreated($absence));

        self::assertCount(0, $futureEventAutoYes->votes()->whereVote(TeamEventVoteType::YES)->get());
        self::assertCount(1, $futureEventAutoYes->votes()->whereVote(TeamEventVoteType::NO)->get());
        self::assertCount(0, $futureEventAutoYesWithinAbsence->votes()->whereVote(TeamEventVoteType::YES)->get());
        self::assertCount(1, $futureEventAutoYesWithinAbsence->votes()->whereVote(TeamEventVoteType::NO)->get());
        // $pastEvent is in future on absence create date
        self::assertCount(0, $pastEventAutoYesWithinAbsence->votes()->whereVote(TeamEventVoteType::YES)->get());
        self::assertCount(1, $pastEventAutoYesWithinAbsence->votes()->whereVote(TeamEventVoteType::NO)->get());

        // travel 3 days into future to have $pastEvent in the past
        Carbon::setTestNow(Carbon::now()->add(CarbonInterval::days(3)));

        $absence->update([
            'date_end' => $absenceNewEndDate
        ]);
        event(new AbsenceUpdated($absence));


        self::assertCount(1, $futureEventAutoYes->votes()->whereVote(TeamEventVoteType::YES)->get());
        self::assertCount(0, $futureEventAutoYes->votes()->whereVote(TeamEventVoteType::NO)->get());
        self::assertCount(0, $futureEventAutoYesWithinAbsence->votes()->whereVote(TeamEventVoteType::YES)->get());
        self::assertCount(1, $futureEventAutoYesWithinAbsence->votes()->whereVote(TeamEventVoteType::NO)->get());
        // $pastEvent is in past on absence delete date
        self::assertCount(0, $pastEventAutoYesWithinAbsence->votes()->whereVote(TeamEventVoteType::YES)->get());
        self::assertCount(1, $pastEventAutoYesWithinAbsence->votes()->whereVote(TeamEventVoteType::NO)->get());

    }
}
