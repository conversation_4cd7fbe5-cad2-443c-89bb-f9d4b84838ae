<?php

use App\Models\Team;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TeamMembershipTest extends TestCase {
    use RefreshDatabase;

    public function test_leaving_team_removes_person_association(): void {
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $team->createMember(user: $user);

        $this->actingAs($user)
             ->postJson(route('api.user.leave-team', [
                 'member' => $member->id
             ]));
        $member->refresh();
        self::assertNull($member->person);
    }

    public function test_leaving_team_sets_role_inactive(): void {
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $team->createMember(user: $user);

        self::assertEquals(\App\Types\TeamRoleType::MEMBER, $member->statusRole->name);

        $this->actingAs($user)
             ->postJson(route('api.user.leave-team', [
                 'member' => $member->id
             ]));
        $member->refresh();
        self::assertEquals(\App\Types\TeamRoleType::INACTIVE, $member->statusRole->name);
    }

    public function test_soft_delete_member_sets_role_inactive(): void {
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $team->createMember(user: $user);

        self::assertEquals(\App\Types\TeamRoleType::MEMBER, $member->statusRole->name);

        $member->delete();
        
        $member->refresh();
        self::assertEquals(\App\Types\TeamRoleType::INACTIVE, $member->statusRole->name);
    }

    public function test_leaving_team_denied_for_not_associated_person(): void {
        $wrongUser = $this->createUser('wrong');
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $team->createMember(user: $user);

        self::assertEquals(\App\Types\TeamRoleType::MEMBER, $member->statusRole->name);

        $this->actingAs($wrongUser)
             ->postJson(route('api.user.leave-team', [
                 'member' => $member->id
             ]))
        ->assertUnauthorized();

        $member->refresh();
        self::assertEquals(\App\Types\TeamRoleType::MEMBER, $member->statusRole->name);
        self::assertEquals($user->person, $member->person);
    }
}
