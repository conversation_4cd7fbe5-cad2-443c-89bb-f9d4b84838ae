<?php

namespace Tests\Feature;

use App\Models\TrackedNotification;
use App\Models\User;

use App\Notifications\Channels\RecordingFcmChannel;
use App\Notifications\Push\UserInitiatedTestNotification;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Notifications\Notification;
use Kreait\Firebase\Exception\FirebaseException;
use Mockery;
use NotificationChannels\Fcm\FcmChannel;
use Str;
use Tests\TestCase;

class RecordingChannelTest extends TestCase {
    use RefreshDatabase;

    /**
     * @throws FirebaseException
     */
    public function testHandlesFirebaseNotificationCorrectly() {
        $logSpy = Log::spy();

        $user = User::factory()->withPerson()->create();
        $notification = new UserInitiatedTestNotification();

        $fcmMock = Mockery::mock(FcmChannel::class);
        $fcmMock->shouldReceive('send')->once()->with($user, $notification)->andReturn(Collection::make());

        (new RecordingFcmChannel($fcmMock))->send($user, $notification);

        $this->assertDatabaseHas('tracked_notifications', [
            'notifiable_id' => $user->id,
            'message_title' => 'Numo - Test Benachrichtigung',
            'message_uuid'  => $notification->uuid(),
            'message_type'  => 'fcm',
        ]);

        $logSpy->shouldNotHaveReceived('warning');
    }

    /**
     * @throws FirebaseException
     */
    public function testLogsUnsupportedNotification() {
        $logSpy = Log::spy();

        $user = User::factory()->withPerson()->create();
        $unsupportedNotification = new Notification();

        $fcmMock = Mockery::mock(FcmChannel::class);
        $fcmMock->shouldReceive('send')->once()->with($user, $unsupportedNotification)->andReturn(Collection::make());

        (new RecordingFcmChannel($fcmMock))->send($user, $unsupportedNotification);

        $logSpy->shouldHaveReceived('warning')
            ->withArgs(function ($message) use ($user, $unsupportedNotification) {
                return str_contains($message, get_class($unsupportedNotification)) &&
                    str_contains($message, $user->id);
            });
    }

    public function testMarksMessageAsReceived(): void {
        $user = User::factory()->withPerson()->create();

        $trackedNotification = TrackedNotification::create([
            'notifiable_id'   => $user->id,
            'notifiable_type' => User::class,
            'message_title'   => 'Test Notification',
            'message_uuid'    => Str::uuid()->toString(),
            'message_type'    => 'fcm',
            'sent_at'         => now(),
        ]);

        $response = $this->actingAs($user)->postJson(route('api.pushreceived'), [
            'message_uuid' => $trackedNotification->message_uuid,
        ]);

        $response->assertOk();

        $updatedNotification = TrackedNotification::find($trackedNotification->id);

        $this->assertNotNull($updatedNotification->received_at);
        $this->assertLessThan(2, now()->diffInMinutes($updatedNotification->received_at), 'The received_at timestamp should be recent.');
    }
}
