<?php

namespace Tests\Feature\API;

use App\JsonApi\V1\Persons\PersonSchema;
use App\Models\RegistrationAllowlist;
use App\Models\Team;
use App\Models\User;
use App\Notifications\VerifyEmail;
use App\Types\TeamRoleType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Illuminate\Testing\Fluent\AssertableJson;
use Symfony\Component\HttpFoundation\Response;

class AuthTest extends JsonApiTestCase {
    use RefreshDatabase;

    public function testRegistrationNotAllowed() {
        $oldValue = config('app.allow_registration');
        config()->set('app.allow_registration', false);
        $response = $this->post(route('api.register'), [
            'firstname' => 'Max',
            'lastname' => 'Muster',
            'email' => '<EMAIL>',
            'password' => '12345678',
            'password_confirmation' => '12345678',
            'device_name' => 'test',
        ]);

        $response
            ->assertStatus(Response::HTTP_BAD_REQUEST)
            ->assertJson(function (AssertableJson $json) {
                $json->has('errors')
                    ->has('message')
                    ->has('errors.not_allowlisted');
            });

        config()->set('app.allow_registration', $oldValue);
    }

    public function testRegistrationAllowed(): void {
        $oldValue = config('app.allow_registration');
        config()->set('app.allow_registration', true);
        Notification::fake();

        $email = '<EMAIL>';
        $firstname = 'Max';


        $response = $this->post(route('api.register'), [
            'firstname' => $firstname,
            'lastname' => 'Muster',
            'email' => $email,
            'password' => '12345678',
            'password_confirmation' => '12345678',
            'device_name' => 'test',
        ]);


        $user = User::whereEmail($email)->first();
        self::assertEquals($firstname, $user->person->firstname);
        self::assertFalse($user->hasVerifiedEmail());
        Notification::assertSentTo($user, VerifyEmail::class);

        $response
            ->assertStatus(Response::HTTP_OK)
            ->assertJson(function (AssertableJson $json) use ($user) {
                $json->has('token')
                    ->has('userId')
                    ->where('userId', $user->id);
            });

        config()->set('app.allow_registration', $oldValue);
    }

    public function testRegistrationAllowedWhenOnWaitlist(): void {
        $oldValue = config('app.allow_registration');
        config()->set('app.allow_registration', false);
        Notification::fake();

        $email = '<EMAIL>';
        $firstname = 'Max';
        $allowlist = RegistrationAllowlist::create([
            'email' => $email,
            'allow_registration' => true,
        ]);

        $this->post(route('api.register'), [
            'firstname' => $firstname,
            'lastname' => 'Muster',
            'email' => $email,
            'password' => '12345678',
            'password_confirmation' => '12345678',
            'device_name' => 'test',
        ]);

        $allowlist->refresh();

        $user = User::find($allowlist->user_id);
        self::assertEquals($firstname, $user->person->firstname);

        config()->set('app.allow_registration', $oldValue);
    }

    public function testRegistrationAllowedWithInvite(): void {
        $oldValue = config('app.allow_registration');
        config()->set('app.allow_registration', false);
        Notification::fake();

        $team = Team::create(['name' => 'Test Team 1',]);
        $invite = $team->invite()->create();

        $email = '<EMAIL>';
        $firstname = 'Max';

        $this->post(route('api.register'), [
            'firstname' => $firstname,
            'lastname' => 'Muster',
            'email' => $email,
            'password' => '12345678',
            'password_confirmation' => '12345678',
            'device_name' => 'test',
            'invite_token' => $invite->token,
        ]);

        $user = User::whereEmail($email)->first();
        self::assertEquals($firstname, $user->person->firstname);

        config()->set('app.allow_registration', $oldValue);
    }

    public function testRegistrationWithoutVerifyMailForProblemProviders(): void {
        $oldValue = config('app.allow_registration');
        config()->set('app.allow_registration', true);
        Notification::fake();

        $email = '<EMAIL>';
        $firstname = 'Max';
        $allowlist = RegistrationAllowlist::create([
            'email' => $email,
            'allow_registration' => true,
        ]);

        $this->post(route('api.register'), [
            'firstname' => $firstname,
            'lastname' => 'Muster',
            'email' => $email,
            'password' => '12345678',
            'password_confirmation' => '12345678',
            'device_name' => 'test',
        ]);

        $allowlist->refresh();

        $user = User::find($allowlist->user_id);
        self::assertTrue($user->hasVerifiedEmail());
        self::assertEquals($firstname, $user->person->firstname);
        Notification::assertNotSentTo($user, VerifyEmail::class);

        config()->set('app.allow_registration', $oldValue);
    }

    public function testLogin() {
        $email = '<EMAIL>';
        $user = User::createWithPerson($email, '12345678');

        $response = $this->post(route('api.login'), [
            'email' => $email,
            'password' => '12345678',
            'device_name' => 'test',
        ]);

        $response
            ->assertStatus(Response::HTTP_OK)
            ->assertJson(function (AssertableJson $json) use ($user) {
                $json->has('token')
                    ->has('userId')
                    ->where('userId', $user->id);
            });
    }

    public function testLogout() {
        $email = '<EMAIL>';
        $user = User::createWithPerson($email, '12345678');
        $token = $user->createToken('test')->plainTextToken;

        $response = $this
            ->actingAs($user)
            ->post(route('api.logout'));

        $response->assertStatus(Response::HTTP_OK);
        self::assertCount(0, $user->tokens()->where(['token' => $token])->get());

    }

    public function testPermissionsIncludedInPersonRequest() {
        $email = '<EMAIL>';
        $firstname = 'firstname test';
        $user = User::createWithPerson($email, '12345678', $firstname);
        $user->refresh();

        $teamA = Team::create(['name' => 'Test Team A',]);
        $teamManager = $teamA->members()->create(['name' => 'Team Manager A']);
        $user->person->teamMembers()->save($teamManager);

        $response = $this
            ->actingAs($user)
            ->jsonApi()
            ->expects('persons')
            ->includePaths('teamMembers.team')
            ->query(['extra_fields['.PersonSchema::type().']' => 'teamPermissions'])
            ->get(route('api.v1.persons.show', [
                'person' => $user->person_id,
            ]));

        $expected = [
            'id' => $user->person_id,
            'attributes' => [
                'firstname' => $firstname,
                'teamPermissions' => $user->person->permissionNamesPerTeamId()
            ],
        ];

        $response
            ->assertFetchedOne($expected)
            ->assertIsIncluded('teamMembers', $teamManager);
    }

    public function testDeleteProfile(): void {
        $email = '<EMAIL>';
        $user = User::createWithPerson($email, '12345678');

        $this
            ->actingAs($user)
            ->post(route('api.user.delete'))
            ->assertOk();

        self::assertCount(0, User::all());
    }

    public function testDeleteProfileDeletesTeamWithSingleAdminAndNoUsers(): void {
        $email = '<EMAIL>';
        $user = User::createWithPerson($email, '12345678');
        $team = Team::create(['name' => 'Test Team 1',]);
        $team->createMember(teamRoleType: TeamRoleType::MANAGER, user: $user);

        $this
            ->actingAs($user)
            ->post(route('api.user.delete'))
            ->assertOk();

        self::assertCount(0, User::all());
        self::assertCount(0, Team::all());
    }

    public function testDeleteProfileNotDeletesTeamWithSecondAdmin(): void {
        $user = $this->createUser('<EMAIL>');
        $user2 = $this->createUser('<EMAIL>');
        $team = Team::create(['name' => 'Test Team 1',]);
        $team->createMember(teamRoleType: TeamRoleType::MANAGER, user: $user);
        $team->createMember(teamRoleType: TeamRoleType::MANAGER, user: $user2);

        $this
            ->actingAs($user)
            ->post(route('api.user.delete'))
            ->assertOk();

        self::assertCount(1, User::all());
        self::assertCount(1, Team::all());
    }
    public function testDeleteProfileFailsWithTeamThatHasUsersButNoSecondAdmin(): void {
        $user = $this->createUser('<EMAIL>');
        $user2 = $this->createUser('<EMAIL>');
        $team = Team::create(['name' => 'Test Team 1',]);
        $team->createMember(teamRoleType: TeamRoleType::MANAGER, user: $user);
        $team->createMember(teamRoleType: TeamRoleType::MEMBER, user: $user2);

        $this
            ->actingAs($user)
            ->post(route('api.user.delete'))
            ->assertForbidden();

        self::assertCount(2, User::all());
        self::assertCount(1, Team::all());
    }
}
