<?php

namespace Tests\Feature\API;


use App\Models\Invite;
use App\Models\Team;
use App\Types\FeatureType;
use App\Types\TeamPermissionType;
use App\Types\TeamRoleType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Laravel\Pennant\Feature;
use function PHPUnit\Framework\assertInstanceOf;

class TeamPermissionTest extends JsonApiTestCase {
    use RefreshDatabase;

    public function testTeamCreateAllowedAndManagerRoleSetToUser(): void {
        Feature::store('array')->define(FeatureType::TEAM_CREATE->value, true);
        $user = $this->createUser();

        $teamName = 'my new team';
        $data = $this->createNewTeamData($teamName);

        $teamId = $this->actingAs($user)
             ->jsonApi('teams')
             ->withData($data)
             ->post(route('api.v1.teams.store'))
             ->assertCreated()
             ->id()
        ;

        $team = Team::find($teamId);
        $user->refresh();
        self::assertEquals($teamName, $team->name);
        self::assertCount(1, $user->person->teamMembers);
        self::assertCount(1, $team->members);
        self::assertEquals($user->person->id, $team->members->first()->person->id);
        self::assertEquals(TeamRoleType::MANAGER, $team->members->first()->statusRole->name);
    }

    public function testTeamCreateNotAllowed(): void {
        Feature::store('array')->define(FeatureType::TEAM_CREATE->value, false);
        $user = $this->createUser();

        $teamName = 'my new team';
        $data = $this->createNewTeamData($teamName);

        $this->actingAs($user)
             ->jsonApi('teams')
             ->withData($data)
             ->post(route('api.v1.teams.store'))
             ->assertForbidden()
        ;

        $user->refresh();
        self::assertCount(0, $user->person->teamMembers);
    }

    public function testMemberCreate(): void {
        $team = Team::create(['name' => 'Test Team 1',]);

        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_MEMBER_CREATE]);
        $member = $this->createMemberWithoutPermissions($team);

        $data = $this->createNewMemberData($team, $team->getTeamRole(TeamRoleType::MEMBER));

        $this->actingAs($manager->person->user)
            ->jsonApi('teamMembers')
            ->withData($data)
            ->post(route('api.v1.teamMembers.store'))
            ->assertCreated();

        $this->actingAs($member->person->user)
            ->jsonApi('teamMembers')
            ->withData($data)
            ->post(route('api.v1.teamMembers.store'))
            ->assertForbidden();
    }

    public function testMemberDelete(): void {
        $team = Team::create(['name' => 'Test Team 1',]);

        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_MEMBER_DELETE]);
        $member = $this->createMemberWithoutPermissions($team);

        $memberToDelete = $this->createMemberWithoutPermissions($team, 'delete');

        $this->actingAs($member->person->user)
            ->jsonApi()
            ->delete(route('api.v1.teamMembers.destroy', [
                'teamMember' => $memberToDelete->id
            ]))
            ->assertForbidden();

        $this->actingAs($manager->person->user)
            ->jsonApi()
            ->delete(route('api.v1.teamMembers.destroy', [
                'teamMember' => $memberToDelete->id
            ]))
            ->assertNoContent();
    }


    public function testMemberUpdate(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $team2 = Team::create(['name' => 'Test Team 2',]);

        $nameInitial = 'initialName';
        $nameUpdated = 'updatedName';

        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_MEMBER_UPDATE]);

        $memberToUpdate = $this->createMemberWithoutPermissions($team, 'update');
        $memberToUpdate->name = $nameInitial;
        $memberToUpdate->save();

        // make team visible for user
        $team2->createMember(user: $memberToUpdate->person->user);

        $data = $this->createMemberUpdateData($memberToUpdate, $nameUpdated);

        // manager can edit
        $this->actingAs($manager->person->user)
            ->jsonApi('teamMembers')
            ->withData($data)
            ->patch(route('api.v1.teamMembers.update', [
                'teamMember' => $memberToUpdate->id
            ]))
            ->assertFetchedOne($memberToUpdate);

        $memberToUpdate->refresh();
        self::assertSame($nameUpdated, $memberToUpdate->name);

        $memberToUpdate->name = $nameInitial;
        $memberToUpdate->save();

        // member can edit own name
        $this->actingAs($memberToUpdate->person->user)
            ->jsonApi('teamMembers')
            ->withData($data)
            ->patch(route('api.v1.teamMembers.update', [
                'teamMember' => $memberToUpdate->id
            ]))
            ->assertFetchedOne($memberToUpdate);

        $memberToUpdate->refresh();
        self::assertSame($nameUpdated, $memberToUpdate->name);

        // member cannot edit own data other than name
        $extraData = $data;
        $extraData['relationships']['team']['data']['id'] = $team2->id;

        $this->actingAs($memberToUpdate->person->user)
            ->jsonApi('teamMembers')
            ->withData($extraData)
            ->patch(route('api.v1.teamMembers.update', [
                'teamMember' => $memberToUpdate->id
            ]))
            ->assertFetchedOne($memberToUpdate);

        $memberToUpdate->refresh();
        self::assertSame($nameUpdated, $memberToUpdate->name);
        self::assertSame($team->id, $memberToUpdate->team_id);

        $memberToUpdate->name = $nameInitial;
        $memberToUpdate->save();

        // other member cannot edit
        $member = $this->createMemberWithoutPermissions($team);
        $this->actingAs($member->person->user)
             ->jsonApi('teamMembers')
             ->withData($data)
             ->patch(route('api.v1.teamMembers.update', [
                 'teamMember' => $memberToUpdate->id
             ]))
             ->assertForbidden();

        $memberToUpdate->refresh();
        self::assertSame($nameInitial, $memberToUpdate->name);


    }

    public function testMemberInviteCreateOther(): void {
        $team = Team::create(['name' => 'Test Team 1',]);

        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_MEMBER_INVITE_CREATE_OTHER]);
        $member = $this->createMemberWithoutPermissions($team);

        $memberToInvite = $this->createMemberWithoutPermissions($team, 'invite');

        $data = $this->createMemberInviteData($memberToInvite);

        $this->actingAs($member->person->user)
            ->jsonApi('invites')
            ->withData($data)
            ->post(route('api.v1.invites.store'))
            ->assertForbidden();

        $memberToInvite->refresh();
        self::assertNull($memberToInvite->invite);


        $this->actingAs($manager->person->user)
            ->jsonApi('invites')
            ->withData($data)
            ->post(route('api.v1.invites.store'))
            ->assertCreated();

        $memberToInvite->refresh();
        assertInstanceOf(Invite::class, $memberToInvite->invite);
    }

    public function testMemberInviteDeleteOther(): void {
        $team = Team::create(['name' => 'Test Team 1',]);

        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_MEMBER_INVITE_CREATE_OTHER]);
        $member = $this->createMemberWithoutPermissions($team);

        $memberToInvite = $this->createMemberWithoutPermissions($team, 'invite');
        $memberToInvite->invite()->create();


        $this->actingAs($member->person->user)
            ->jsonApi()
            ->delete(route('api.v1.invites.destroy', [
                'invite' => $memberToInvite->invite->id
            ]))
            ->assertForbidden();

        $memberToInvite->refresh();
        assertInstanceOf(Invite::class, $memberToInvite->invite);


        $this->actingAs($manager->person->user)
            ->jsonApi()
            ->delete(route('api.v1.invites.destroy', [
                'invite' => $memberToInvite->invite->id
            ]))
            ->assertNoContent();

        $memberToInvite->refresh();
        self::assertNull($memberToInvite->invite);
    }

    public function testTeamInviteCreate(): void {
        $team = Team::create(['name' => 'Test Team 1',]);

        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_INVITE_CREATE]);
        $member = $this->createMemberWithoutPermissions($team);

        $data = $this->createTeamInviteData($team);

        $this->actingAs($member->person->user)
             ->jsonApi('invites')
             ->withData($data)
             ->post(route('api.v1.invites.store'))
             ->assertForbidden();

        $team->refresh();
        self::assertNull($team->invite);


        $this->actingAs($manager->person->user)
             ->jsonApi('invites')
             ->withData($data)
             ->post(route('api.v1.invites.store'))
             ->assertCreated();

        $team->refresh();
        assertInstanceOf(Invite::class, $team->invite);
    }

    public function testTeamInviteDelete(): void {
        $team = Team::create(['name' => 'Test Team 1',]);

        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_INVITE_CREATE]);
        $member = $this->createMemberWithoutPermissions($team);

        $team->invite()->create();

        $this->actingAs($member->person->user)
             ->jsonApi()
             ->delete(route('api.v1.invites.destroy', [
                 'invite' => $team->invite->id
             ]))
             ->assertForbidden();

        $team->refresh();
        assertInstanceOf(Invite::class, $team->invite);


        $this->actingAs($manager->person->user)
             ->jsonApi()
             ->delete(route('api.v1.invites.destroy', [
                 'invite' => $team->invite->id
             ]))
             ->assertNoContent();

        $team->refresh();
        self::assertNull($team->invite);
    }

    public function testTeamUpdate(): void {
        $nameInitial = 'initialName';
        $nameUpdated = 'updatedName';
        $team = Team::create(['name' => $nameInitial,]);

        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_UPDATE]);
        $member = $this->createMemberWithoutPermissions($team);


        $data = $this->createTeamUpdateData($team, $nameUpdated);

        $this->actingAs($member->person->user)
            ->jsonApi('teams')
            ->withData($data)
            ->patch(route('api.v1.teams.update', [
                'team' => $team->id
            ]))
            ->assertForbidden();

        $team->refresh();
        self::assertSame($nameInitial, $team->name);


        $this->actingAs($manager->person->user)
            ->jsonApi('teams')
            ->withData($data)
            ->patch(route('api.v1.teams.update', [
                'team' => $team->id
            ]))
            ->assertFetchedOne($team);

        $team->refresh();
        self::assertSame($nameUpdated, $team->name);
    }

    public function testEventCreate(): void {
        $team = Team::create(['name' => 'Test Team 1',]);

        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_CREATE]);
        $member = $this->createMemberWithoutPermissions($team);

        $data = $this->createNewEventData($team);

        $this->actingAs($manager->person->user)
            ->jsonApi('teamEvents')
            ->withData($data)
            ->post(route('api.v1.teamEvents.store'))
            ->assertCreated();

        $this->actingAs($member->person->user)
            ->jsonApi('teamEvents')
            ->withData($data)
            ->post(route('api.v1.teamEvents.store'))
            ->assertForbidden();
    }

    public function testEventUpdate(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $titleInitial = 'initialName';
        $titleUpdated = 'updatedName';

        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_CREATE]);
        $member = $this->createMemberWithoutPermissions($team);

        $event = $this->createTeamEvent($team);
        $event->title = $titleInitial;
        $event->save();

        $data = $this->createEventUpdateData($event, $titleUpdated);

        $this->actingAs($member->person->user)
            ->jsonApi('teamEvents')
            ->withData($data)
            ->patch(route('api.v1.teamEvents.update', [
                'teamEvent' => $event->id
            ]))
            ->assertForbidden();

        $event->refresh();
        self::assertSame($titleInitial, $event->title);


        $this->actingAs($manager->person->user)
            ->jsonApi('teamEvents')
            ->withData($data)
            ->patch(route('api.v1.teamEvents.update', [
                'teamEvent' => $event->id
            ]))
            ->assertFetchedOne($event);

        $event->refresh();
        self::assertSame($titleUpdated, $event->title);
    }

    public function testEventDelete(): void {
        $team = Team::create(['name' => 'Test Team 1',]);

        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_DELETE]);
        $member = $this->createMemberWithoutPermissions($team);

        $eventToDelete = $this->createTeamEvent($team);

        $this->actingAs($member->person->user)
            ->jsonApi()
            ->delete(route('api.v1.teamEvents.destroy', [
                'teamEvent' => $eventToDelete->id
            ]))
            ->assertForbidden();

        $this->actingAs($manager->person->user)
            ->jsonApi()
            ->delete(route('api.v1.teamEvents.destroy', [
                'teamEvent' => $eventToDelete->id
            ]))
            ->assertNoContent();
    }

    public function testVoteOther(): void {
        $team = Team::create(['name' => 'Test Team 1',]);

        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_VOTE_OTHER]);
        $member = $this->createMemberWithoutPermissions($team);

        $memberToVote = $team->createMember('voter');

        $event = $this->createTeamEvent($team);
        $data = $this->createNewVoteData($event, $memberToVote);

        $this->actingAs($manager->person->user)
            ->jsonApi('teamEventVotes')
            ->withData($data)
            ->post(route('api.v1.teamEventVotes.store'))
            ->assertCreated();

        $this->actingAs($member->person->user)
            ->jsonApi('teamEvents')
            ->withData($data)
            ->post(route('api.v1.teamEventVotes.store'))
            ->assertForbidden();
    }

    public function testVoteSelf(): void {
        // Voting will cause notification to be sent. We'll use a fake here to prevent actual notifications.
        Notification::fake();
        
        $team = Team::create(['name' => 'Test Team 1',]);

        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_VOTE_SELF]);
        $member = $this->createMemberWithoutPermissions($team);

        $event = $this->createTeamEvent($team);
        $managerData = $this->createNewVoteData($event, $manager);
        $memberData = $this->createNewVoteData($event, $member);

        $this->actingAs($manager->person->user)
            ->jsonApi('teamEventVotes')
            ->withData($managerData)
            ->post(route('api.v1.teamEventVotes.store'))
            ->assertCreated();

        $this->actingAs($member->person->user)
            ->jsonApi('teamEvents')
            ->withData($memberData)
            ->post(route('api.v1.teamEventVotes.store'))
            ->assertForbidden();
    }
}
