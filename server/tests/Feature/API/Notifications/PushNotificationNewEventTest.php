<?php

namespace Tests\Feature\API\Notifications;

use App\Models\TeamEvent;
use App\Models\TeamEventSeries;
use App\Notifications\Push\NewTeamEventNotification;
use App\Services\PushNotificationService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use RRule\RRule;
use Tests\Feature\API\JsonApiTestCase;

class PushNotificationNewEventTest extends JsonApiTestCase
{
    use RefreshDatabase;
    use DefaultScenario;

    /**
     * Verify that when a recurring‐parent event is created, the notification’s deep‐link
     * points to the first child event, not to the parent.
     */
    public function test_new_event_series_deep_link_points_to_first_child(): void
    {
        // 1. Freeze time so that child‐generation is deterministic.
        Carbon::setTestNow(Carbon::create(2025, 6, 1, 12, 0, 0));

        // 2. Prepare a team and members without any pre‐existing events.
        $this->prepareDefaultScenario(withoutEvents: true);
        $this->enablePushNotificationsForAll();

        // 3. Create a TeamEventSeries with a daily rule, starting June 10, 2025, count 3.
        $seriesStart = Carbon::create(2025, 6, 10, 10, 0, 0);
        $rruleString = (new RRule([
            'FREQ'    => 'DAILY',
            'DTSTART' => $seriesStart->toDateString(),
            'COUNT'   => 3,
        ]))->rfcString();

        $teamEventSeries = TeamEventSeries::forceCreate([
            'rrule_string' => $rruleString,
        ]);

        // 4. Create the parent event (series_type = RECURRING_PARENT). Using forceCreate triggers
        //    createRecurringEvents(), which generates all child events.
        $team = $this->teamByName('Test Team 1');
        $parentEvent = TeamEvent::forceCreate([
            'team_id'             => $team->id,
            'event_type'          => \App\Types\TeamEventType::TRAINING->value,
            'response_type'       => \App\Types\EventResponseType::NONE->value,
            'title'               => 'Series Parent Event',
            'sub_text'            => 'Parent for series test',
            'date_begin'          => $seriesStart?->toDateString(),
            'time_begin'          => $seriesStart?->toTimeString(),
            'series_type'         => \App\Types\EventSeriesType::RECURRING_PARENT->value,
            'series_id'           => $teamEventSeries->id,
        ]);

        // 5. At this point, createRecurringEvents() has run, so all child events exist.
        //    Find the first child by date.
        $firstChild = TeamEvent::where('series_id', $teamEventSeries->id)
            ->where('series_type', \App\Types\EventSeriesType::RECURRING_CHILD->value)
            ->orderBy('date_begin')
            ->orderBy('time_begin')
            ->first();
        $this->assertNotNull($firstChild, 'Expected at least one child event to be generated');

        // 6. Fake notifications and invoke the PushNotificationService to simulate event‐creation.
        Notification::fake();
        $service = new PushNotificationService();
        $service->handleOnEventCreate($parentEvent, /* $currentUser = */ null);

        // 7. Build the expected deep link. It should point to the first child’s ID.
        $baseUrl = config('app.frontend.url');
        $expectedDeepLink = "{$baseUrl}/events/show/{$firstChild->id}";

        // 8. Assert that every notified user received a NewTeamEventNotification whose deepLink matches.
        $team->members->loadMissing('person.user');
        foreach ($team->members as $member) {
            $recipient = $member->person?->user;
            if (! $recipient) {
                continue;
            }

            Notification::assertSentTo(
                $recipient,
                NewTeamEventNotification::class,
                function ($notification, $channels) use ($expectedDeepLink) {
                    // Use reflection to access the protected $data property on the notification
                    $ref = new \ReflectionClass($notification);
                    if (! $ref->hasProperty('data')) {
                        return false;
                    }
                    $prop = $ref->getProperty('data');
                    $prop->setAccessible(true);
                    $dataObj = $prop->getValue($notification);

                    return isset($dataObj->deepLink)
                        && $dataObj->deepLink === $expectedDeepLink;
                }
            );
        }
    }
}
