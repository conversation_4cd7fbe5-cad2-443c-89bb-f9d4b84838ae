<?php

namespace Tests\Feature\API\Notifications;

use App\Models\Team;
use App\Models\TeamEvent;
use App\Models\TeamEventSeries;
use App\Notifications\Push\VoteReminderNotification;
use App\Rules\RRule;
use App\Types\EventResponseType;
use App\Types\EventSeriesType;
use App\Types\EventVoteReminderType;
use App\Types\TeamEventType;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Feature\API\JsonApiTestCase;

class PushNotificationVoteReminderTest extends JsonApiTestCase {
    use RefreshDatabase;
    use DefaultScenario;

    /**
     * Notes:
     *  - The event always begins 4 hours from now
     *
     * @return array<string, array<int, array{
     *     vote_reminder_type: string,
     *     vote_reminder_hours: int,
     *     setup: \Closure(\Tests\Feature\API\Notifications\PushNotificationVoteReminderTest): void,
     *     modifyTimeFunc: \Closure(\Carbon\Carbon): (\Carbon\Carbon|null),
     *     assertCountFunc: \Closure(\App\Models\Team): int,
     *     assertMarkedAsSent: bool,
     *     assertSentTo: array<string>,
     *     assertVoteReminderType?: string|\App\Types\EventVoteReminderType,
     *     rrule?: string
     * }>>
     */
    public static function dataProvider(): array {
        Carbon::setTestNow(Carbon::create(2024, 12, 31, 15, 0, 0));
        return [
            'Notifications scheduled 2 hours before event (sent and marked as such)' => [[
                'vote_reminder_type'               => 'hours_before_event',
                'vote_reminder_hours'              => 2,
                'setup'                            => function (PushNotificationVoteReminderTest $t): void {},
                'modifyTimeFunc'                   => fn (Carbon $now): Carbon|null => Carbon::make($now)?->addHours(2)->addMinutes(30),
                'assertCountFunc'                  => fn(Team $team): int => $team->members()->whereNot('person_id')->get()->unique('person_id')->count(),
                'assertMarkedAsSent'               => true,
                'assertSentTo'                     => ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
            ]],
            'Notification scheduled 2 hours before event, but artisan execution happened too early' => [[
                'vote_reminder_type'   => 'hours_before_event',
                'vote_reminder_hours'  => 2,
                'setup'                => function (PushNotificationVoteReminderTest $t): void {},
                'modifyTimeFunc'       => fn(Carbon $now): Carbon|null => Carbon::make($now)?->addHours(1),
                'assertCountFunc'      => fn(Team $team): int => 0,
                'assertMarkedAsSent'   => false,
                'assertSentTo'         => [],
            ]],
            // This is essentially the same test as the first one. Just a different purpose.
            'Reminder, person with two team members, no duplicate notifications' => [[
                'vote_reminder_type'               => 'hours_before_event',
                'vote_reminder_hours'              => 2,
                'setup'                            => function (PushNotificationVoteReminderTest $t): void {},
                'modifyTimeFunc'                   => fn(Carbon $now): Carbon|null => Carbon::make($now)?->addHours(2)->addMinutes(30),
                'assertCountFunc'                  => fn(Team $team): int => $team->members()->whereNot('person_id')->get()->unique('person_id')->count(),
                'assertMarkedAsSent'               => false,
                'assertSentTo'                     => ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
            ]],
            'Reminder, person with two team members, one has voted (notification should still be sent)' => [[
                'vote_reminder_type'               => 'hours_before_event',
                'vote_reminder_hours'              => 2,
                'setup'                            => function (PushNotificationVoteReminderTest $t): void {
                    $t->jsonApiVote(
                        $t->eventByTitle('Reminder Event 1')->id,
                        $t->memberByName('With User 1 [a]')->id,
                        $t->userByEmail('<EMAIL>')
                    )->assertCreated();
                },
                'modifyTimeFunc'                   => fn(Carbon $now): Carbon|null => Carbon::make($now)?->addHours(2)->addMinutes(30),
                'assertCountFunc'                  => fn(Team $team): int => $team->members()->whereNot('person_id')->get()->unique('person_id')->count(),
                'assertMarkedAsSent'               => false,
                'assertSentTo'                     => ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
            ]],
            'Reminder, person with two team members, both have voted (notification should not be sent)' => [[
                'vote_reminder_type'               => 'hours_before_event',
                'vote_reminder_hours'              => 2,
                'setup'                            => function (PushNotificationVoteReminderTest $t): void {
                    $t->jsonApiVote(
                        $t->eventByTitle('Reminder Event 1')->id,
                        $t->memberByName('With User 1 [a]')->id,
                        $t->userByEmail('<EMAIL>')
                    )->assertCreated();

                    $t->jsonApiVote(
                        $t->eventByTitle('Reminder Event 1')->id,
                        $t->memberByName('With User 1 [b]')->id,
                        $t->userByEmail('<EMAIL>')
                    )->assertCreated();
                },
                'modifyTimeFunc'                   => fn(Carbon $now): Carbon|null => Carbon::make($now)?->addHours(2)->addMinutes(30),
                'assertCountFunc'                  => fn(Team $team): int => $team->members()->whereNot('person_id')->get()->unique('person_id')->count()-1,
                'assertMarkedAsSent'               => false,
                'assertSentTo'                     => ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
            ]],
            'Reminder, person with one team members, one has voted (notification should not be sent)' => [[
                'vote_reminder_type'               => 'hours_before_event',
                'vote_reminder_hours'              => 2,
                'setup'                            => function (PushNotificationVoteReminderTest $t): void {
                    $t->jsonApiVote(
                        $t->eventByTitle('Reminder Event 1')->id,
                        $t->memberByName('With User 2')->id,
                        $t->userByEmail('<EMAIL>')
                    )->assertCreated();
                },
                'modifyTimeFunc'                   => fn(Carbon $now): Carbon|null => Carbon::make($now)?->addHours(2)->addMinutes(30),
                'assertCountFunc'                  => fn(Team $team): int => $team->members()->whereNot('person_id')->get()->unique('person_id')->count()-1,
                'assertMarkedAsSent'               => false,
                'assertSentTo'                     => ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
            ]],
            'Reminder, person with one team members, one has voted undecided (notification should not be sent)' => [[
                'vote_reminder_type'               => 'hours_before_event',
                'vote_reminder_hours'              => 2,
                'setup'                            => function (PushNotificationVoteReminderTest $t): void {
                    $t->jsonApiVote(
                        $t->eventByTitle('Reminder Event 1')->id,
                        $t->memberByName('With User 2')->id,
                        $t->userByEmail('<EMAIL>'),
                        'maybe'
                    )->assertCreated();
                },
                'modifyTimeFunc'                   => fn(Carbon $now): Carbon|null => Carbon::make($now)?->addHours(2)->addMinutes(30),
                'assertCountFunc'                  => fn(Team $team): int => $team->members()->whereNot('person_id')->get()->unique('person_id')->count()-1,
                'assertMarkedAsSent'               => false,
                'assertSentTo'                     => ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
            ]],
            'Notifications scheduled for before event creation (not sent, type changed to none)' => [[
                'vote_reminder_type'               => 'hours_before_event',
                'vote_reminder_hours'              => 5,
                'setup'                            => function (PushNotificationVoteReminderTest $t): void {},
                'modifyTimeFunc'                   => fn(Carbon $now): Carbon|null => Carbon::make($now)?->addHours(2)->addMinutes(30),
                'assertCountFunc'                  => fn(Team $team): int => $team->members()->whereNot('person_id')->get()->unique('person_id')->count()-1,
                'assertMarkedAsSent'               => false,
                'assertSentTo'                     => [],
                'assertVoteReminderType'           => EventVoteReminderType::NONE,
            ]],
            'Notifications scheduled 1 hour after creation (sent and marked as such)'                                  => [[
                'vote_reminder_type'               => 'hours_after_creation',
                'vote_reminder_hours'              => 1,
                'setup'                            => function (PushNotificationVoteReminderTest $t): void {},
                'modifyTimeFunc'                   => fn (Carbon $now): Carbon|null => Carbon::make($now)?->addHours(2)?->addMinutes(30),
                'assertCountFunc'                  => fn(Team $team): int => $team->members()->whereNot('person_id')->get()->unique('person_id')->count(),
                'assertMarkedAsSent'               => true,
                'assertSentTo'                     => ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
            ]],
            'Notifications scheduled for after event already begun (not sent, type changed to none)' => [[
                'vote_reminder_type'               => 'hours_after_creation',
                'vote_reminder_hours'              => 5,
                'setup'                            => function (PushNotificationVoteReminderTest $t): void {},
                'modifyTimeFunc'                   => fn(Carbon $now): Carbon|null => Carbon::make($now)?->addHours(2)?->addMinutes(30),
                'assertCountFunc'                  => fn(Team $team): int => $team->members()->whereNot('person_id')->get()->unique('person_id')->count()-1,
                'assertMarkedAsSent'               => false,
                'assertSentTo'                     => [],
                'assertVoteReminderType'           => EventVoteReminderType::NONE,
            ]],
            'Notification scheduled 1 hour after event creation, but artisan execution happened too early' => [[
                'vote_reminder_type'   => 'hours_after_creation',
                'vote_reminder_hours'  => 1,
                'setup'                => function (PushNotificationVoteReminderTest $t): void {},
                'modifyTimeFunc'       => fn(Carbon $now): Carbon|null => Carbon::make($now)?->addMinutes(30),
                'assertCountFunc'      => fn(Team $team): int => 0,
                'assertMarkedAsSent'   => false,
                'assertSentTo'         => [],
            ]],
            'Recurring event, hours_after_creation is not supported, type changed to none' => [[
                'vote_reminder_type'               => 'hours_after_creation',
                'vote_reminder_hours'              => 1,
                'setup'                            => function (PushNotificationVoteReminderTest $t): void {},
                'modifyTimeFunc'                   => fn (Carbon $now): Carbon|null => Carbon::make($now)?->addHours(2)?->addMinutes(30),
                'assertCountFunc'                  => fn(Team $team): int => $team->members()->whereNot('person_id')->get()->unique('person_id')->count(),
                'assertMarkedAsSent'               => false,
                'assertVoteReminderType'           => EventVoteReminderType::NONE,
                'assertSentTo'                     => [],
                'rrule'                            => (new \RRule\RRule([
                    'FREQ'    => 'DAILY',
                    'DTSTART' => Carbon::now()->toDateString(),
                    'COUNT'   => 2
                ]))->rfcString(),
            ]],
            'Recurring event, hours_before_event, first occurrence' => [[
                'vote_reminder_type'               => 'hours_before_event',
                'vote_reminder_hours'              => 2,
                'setup'                            => function (PushNotificationVoteReminderTest $t): void {},
                'modifyTimeFunc'                   => fn (Carbon $now): Carbon|null => Carbon::make($now)?->addHours(2)?->addMinutes(30),
                'assertCountFunc'                  => fn(Team $team): int => $team->members()->whereNot('person_id')->get()->unique('person_id')->count(),
                'assertMarkedAsSent'               => true,
                'assertSentTo'                     => ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
                'seriesIndex'                      => 0,
                'rrule'                            => (new \RRule\RRule([
                    'FREQ'    => 'DAILY',
                    'DTSTART' => Carbon::now()->toDateString(),
                    'COUNT'   => 2
                ]))->rfcString(),
            ]],
            'Recurring event, hours_before_event, second occurrence' => [[
                'vote_reminder_type'               => 'hours_before_event',
                'vote_reminder_hours'              => 2,
                'setup'                            => function (PushNotificationVoteReminderTest $t): void {},
                'modifyTimeFunc'                   => fn (Carbon $now): Carbon|null => Carbon::make($now)?->addDay()?->addHours(2)?->addMinutes(30),
                'assertCountFunc'                  => fn(Team $team): int => $team->members()->whereNot('person_id')->get()->unique('person_id')->count(),
                'assertMarkedAsSent'               => false,
                'assertSentTo'                     => [], // This isn't checked. Relies on assertMarkedAsSent.
                'seriesIndex'                      => 1,
                'rrule'                            => (new \RRule\RRule([
                    'FREQ'    => 'DAILY',
                    'DTSTART' => Carbon::now()->toDateString(),
                    'COUNT'   => 2
                ]))->rfcString(),
            ]],
            'Event with auto_yes response type (not sent, type changed to none)' => [[
                'vote_reminder_type'               => 'hours_after_creation',
                'vote_reminder_hours'              => 5,
                'response_type'                    => EventResponseType::AUTO_YES,
                'setup'                            => function (PushNotificationVoteReminderTest $t): void {},
                'modifyTimeFunc'                   => fn(Carbon $now): Carbon|null => Carbon::make($now)?->addHours(2)?->addMinutes(30),
                'assertCountFunc'                  => fn(Team $team): int => $team->members()->whereNot('person_id')->get()->unique('person_id')->count()-1,
                'assertMarkedAsSent'               => false,
                'assertSentTo'                     => [],
                'assertVoteReminderType'           => EventVoteReminderType::NONE,
            ]],
            'Notifications scheduled 2 hours before event (ignored, since cancelled)' => [[
                'vote_reminder_type'               => 'hours_before_event',
                'vote_reminder_hours'              => 2,
                'setup'                            => function (PushNotificationVoteReminderTest $t): void {},
                'modifyTimeFunc'                   => fn (Carbon $now): Carbon|null => Carbon::make($now)?->addHours(2)->addMinutes(30),
                'assertCountFunc'                  => fn(Team $team): int => $team->members()->whereNot('person_id')->get()->unique('person_id')->count(),
                'assertMarkedAsSent'               => false,
                'assertSentTo'                     => [],
                'cancelled' => true,
            ]],
            'Old notifications (over a day ago) should not be sent' => [[
                'vote_reminder_type'               => 'hours_before_event',
                'vote_reminder_hours'              => 2,
                'setup'                            => function (PushNotificationVoteReminderTest $t): void {},
                'modifyTimeFunc'                   => fn (Carbon $now): Carbon|null => Carbon::make($now)?->addDays(2), // Check 2 days after the event
                'assertCountFunc'                  => fn(Team $team): int => 0,
                'assertMarkedAsSent'               => false,
                'assertSentTo'                     => [],
            ]],
        ];
    }

    /**
     * @param array<string, array<int, array{
     *     vote_reminder_type: string,
     *     vote_reminder_hours: int,
     *     setup: \Closure(\Tests\Feature\API\Notifications\PushNotificationVoteReminderTest): void,
     *     modifyTimeFunc: \Closure(\Carbon\Carbon): (\Carbon\Carbon|null),
     *     assertCountFunc: \Closure(\App\Models\Team): int,
     *     assertMarkedAsSent: bool,
     *     assertSentTo: array<string>,
     *     assertVoteReminderType?: string|\App\Types\EventVoteReminderType,
     *     rrule?: string
     * }>> $data
     *
     * @return void
     */
    #[DataProvider('dataProvider')]
    function testPushNotificationVoteReminder(array $data): void {
        Carbon::setTestNow(Carbon::create(2024, 12, 31, 15, 0, 0));
        
        $this->assertDatabaseCount('users', 0);
        $this->prepareDefaultScenario(withoutEvents: true);
        $this->enablePushNotificationsForAll();

        Notification::fake();

        // 1. Create event with reminder
        $team = $this->teamByName('Test Team 1');

        $teamEventSeriesData = [];
        if (isset($data['rrule'])) {
            $teamEventSeries = TeamEventSeries::forceCreate([
                'rrule_string' => $data['rrule'],
            ]);
            $teamEventSeriesData['series_type'] = EventSeriesType::RECURRING_PARENT->value;
            $teamEventSeriesData['series_id'] = $teamEventSeries->id;
        }

        $teamEventCancelledData = [];
        if (isset($data['cancelled'])) {
            $cancelledAt = Carbon::now()->addMinutes(30);
            $teamEventCancelledData['cancelled_at'] = $cancelledAt->getTimestamp();
        }

        // Event that begins in 4 hours from now
        $dateBegin = Carbon::now()->addHours(4);
        $event1 = TeamEvent::forceCreate(array_merge([
            'team_id'             => $team->id,
            'event_type'          => TeamEventType::TRAINING->value,
            'response_type'       => EventResponseType::NONE->value,
            'title'               => 'Reminder Event 1',
            'sub_text'            => 'Reminder Event 1 Description',
            'date_begin'          => $dateBegin->toDateString(),
            'time_begin'          => $dateBegin->toTimeString(),
            'vote_reminder_type'  => $data['vote_reminder_type'],
            'vote_reminder_hours' => $data['vote_reminder_hours'],
        ] + $teamEventSeriesData + $teamEventCancelledData));
        
        $data['setup']($this);

        $currentTime = Carbon::now();
        $modifiedTime = $data['modifyTimeFunc'](Carbon::now());
        Carbon::setTestNow($modifiedTime);

        // print debug info ($modifiedTime, $event1->vote_reminder_date, ...);
        $voteReminderHours = $data['vote_reminder_hours'];
        print "  Current time:       $currentTime\n";
        print "  Event begin:        {$event1->dateTimeBegin()}\n";
        print "  Vote reminder date: $event1->vote_reminder_date (+/- $voteReminderHours hours)\n";
        print "  Modified time:      $modifiedTime\n";
        
        // Print all events in the database
        $this->printEvents();

        // Run votes:send-reminder command (as would be done by the scheduler)
        $this->artisan('notifications:send-vote-reminders');

        // We'd except a reminder to be sent to all members associated with a person (since none have voted yet)
        // -> We use the person association because we can't sent notifications to members without a person
        //    - They don't have a user with an associated fcm token
        //    - They do not have a person to check if the notification feature is enabled
        $assertSentToResolved = array_map(fn($email) => $this->userByEmail($email), $data['assertSentTo']);
        if (sizeof($assertSentToResolved) !== 0) {
            // Using assertSentTo here instead of assertCount because we want to check the exact notification types,
            // otherwise this test is going to be polluted by notifications to e.g. managers about members having voted.
            Notification::assertSentTo(
                $assertSentToResolved, VoteReminderNotification::class
            );
        }

        if ($data['assertMarkedAsSent']) {
            $eventToCheck = $event1;
            if (isset($data['seriesIndex'])) {
                $eventToCheck = $this->seriesEventByTitleAndIndex('Reminder Event 1', $data['seriesIndex']);
            }
            $eventToCheck->refresh();
            $this->printEvents();
            
            print "  Checking vote_reminder_sent flag for event {$eventToCheck->title} ({$eventToCheck->dateTimeBegin()})\n";
            
            $this->assertTrue($eventToCheck->vote_reminder_sent);
        }
        
        // In case of an invalid configuration the vote_reminder_type will be reset to 'none',
        // this block can be used to check such cases.
        if (isset($data['assertVoteReminderType'])) {
            $event1->refresh();
            $this->assertEquals($data['assertVoteReminderType'], $event1->vote_reminder_type);
        }
    }
    
    function printEvents(): void {
        print "  Events in database:\n";
        TeamEvent::all()->each(function ($event) {
            $voteReminderSent = $event->vote_reminder_sent ? 'true' : 'false';
            print "    Event: {$event->title} ({$event->dateTimeBegin()}) [{$event->series_type->value}] [vote_reminder_sent: {$voteReminderSent}]\n";
        });
    }
}
