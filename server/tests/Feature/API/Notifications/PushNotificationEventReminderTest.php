<?php

namespace Tests\Feature\API\Notifications;

use App\Models\Team;
use App\Models\TeamEvent;
use App\Models\TeamEventSeries;
use App\Notifications\Push\EventReminderNotification;
use App\Rules\RRule;
use App\Types\EventResponseType;
use App\Types\EventSeriesType;
use App\Types\EventReminderType;
use App\Types\TeamEventType;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Notification;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\Console\Output\BufferedOutput;
use Tests\Feature\API\JsonApiTestCase;

class PushNotificationEventReminderTest extends JsonApiTestCase {
    use RefreshDatabase;
    use DefaultScenario;

    /**
     * Notes:
     *  - The event always begins 4 hours from now
     *
     * @return array<string, array<int, array{
     *     event_reminder_type: string,
     *     event_reminder_hours: int,
     *     setup: \Closure(\Tests\Feature\API\Notifications\PushNotificationEventReminderTest): void,
     *     modifyTimeFunc: \Closure(Carbon): (Carbon|null),
     *     assertCountFunc: \Closure(Team): int,
     *     assertMarkedAsSent: bool,
     *     assertSentTo: array<string>,
     *     assertNotSentTo?: array<string>,
     *     seriesIndex?: int,
     *     rrule?: string,
     *     cancelled?: bool
     * }>>
     */
    public static function dataProvider(): array {
        Carbon::setTestNow(Carbon::create(2024, 12, 31, 15, 0, 0));
        return [
            'Notifications scheduled 2 hours before event (sent and marked as such)' => [[
                'event_reminder_type'               => 'hours_before_event',
                'event_reminder_hours'              => 2,
                'setup'                            => function (PushNotificationEventReminderTest $t): void {},
                'modifyTimeFunc'                   => fn (Carbon $now): Carbon|null => Carbon::make($now)?->addHours(2)->addMinutes(30),
                'assertCountFunc'                  => fn(Team $team): int => $team->members()->whereNot('person_id')->get()->unique('person_id')->count(),
                'assertMarkedAsSent'               => true,
                'assertSentTo'                     => ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
            ]],
            'Notification scheduled 2 hours before event, but artisan execution happened too early' => [[
                'event_reminder_type'   => 'hours_before_event',
                'event_reminder_hours'  => 2,
                'setup'                => function (PushNotificationEventReminderTest $t): void {},
                'modifyTimeFunc'       => fn(Carbon $now): Carbon|null => Carbon::make($now)?->addHours(0.5),
                'assertCountFunc'      => fn(Team $team): int => 0,
                'assertMarkedAsSent'   => false,
                'assertSentTo'         => [],
            ]],
            'Notification not sent because event already started' => [[
                'event_reminder_type'   => 'hours_before_event', 
                'event_reminder_hours'  => 2,
                'setup'                => function (PushNotificationEventReminderTest $t): void {},
                'modifyTimeFunc'       => fn(Carbon $now): Carbon|null => Carbon::make($now)?->addHours(4)->addMinutes(30), // 30 mins after event start
                'assertCountFunc'      => fn(Team $team): int => 0,
                'assertMarkedAsSent'   => false,
                'assertSentTo'         => [],
            ]],
            'Recurring event, hours_before_event, first occurrence' => [[
                'event_reminder_type'               => 'hours_before_event',
                'event_reminder_hours'              => 2,
                'setup'                            => function (PushNotificationEventReminderTest $t): void {},
                'modifyTimeFunc'                   => fn (Carbon $now): Carbon|null => Carbon::make($now)?->addHours(2)?->addMinutes(30),
                'assertCountFunc'                  => fn(Team $team): int => $team->members()->whereNot('person_id')->get()->unique('person_id')->count(),
                'assertMarkedAsSent'               => true,
                'assertSentTo'                     => ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
                'seriesIndex'                      => 0,
                'rrule'                            => (new \RRule\RRule([
                    'FREQ'    => 'DAILY',
                    'DTSTART' => Carbon::now()->toDateString(),
                    'COUNT'   => 2
                ]))->rfcString(),
            ]],
            'Recurring event, hours_before_event, second occurrence' => [[
                'event_reminder_type'               => 'hours_before_event',
                'event_reminder_hours'              => 2,
                'setup'                            => function (PushNotificationEventReminderTest $t): void {},
                'modifyTimeFunc'                   => fn (Carbon $now): Carbon|null => Carbon::make($now)?->addHours(2)?->addMinutes(30),
                'assertCountFunc'                  => fn(Team $team): int => $team->members()->whereNot('person_id')->get()->unique('person_id')->count(),
                'assertMarkedAsSent'               => false,
                'assertSentTo'                     => [],
                'seriesIndex'                      => 1,
                'rrule'                            => (new \RRule\RRule([
                    'FREQ'    => 'DAILY',
                    'DTSTART' => Carbon::now()->toDateString(),
                    'COUNT'   => 2
                ]))->rfcString(),
            ]],
            'Notifications scheduled 2 hours before event (ignored, since cancelled)' => [[
                'event_reminder_type'               => 'hours_before_event',
                'event_reminder_hours'              => 2,
                'setup'                            => function (PushNotificationEventReminderTest $t): void {},
                'modifyTimeFunc'                   => fn (Carbon $now): Carbon|null => Carbon::make($now)?->addHours(2)->addMinutes(30),
                'assertCountFunc'                  => fn(Team $team): int => $team->members()->whereNot('person_id')->get()->unique('person_id')->count(),
                'assertMarkedAsSent'               => false,
                'assertSentTo'                     => [],
                'cancelled' => true,
            ]],
            'Old notifications (over a day ago) should not be sent' => [[
                'event_reminder_type'               => 'hours_before_event',
                'event_reminder_hours'              => 2,
                'setup'                            => function (PushNotificationEventReminderTest $t): void {},
                'modifyTimeFunc'                   => fn (Carbon $now): Carbon|null => Carbon::make($now)?->addDays(2), // Check 2 days after the event
                'assertCountFunc'                  => fn(Team $team): int => 0,
                'assertMarkedAsSent'               => false,
                'assertSentTo'                     => [],
            ]],
            'Test' => [[
                'event_reminder_type'               => 'hours_before_event',
                'event_reminder_hours'              => 2,
                'setup'                            => function (PushNotificationEventReminderTest $t): void {
                    $t->jsonApiVote(
                        $t->eventByTitle('Reminder Event 1')->id,
                        $t->memberByName('With User 2')->id,
                        $t->userByEmail('<EMAIL>'),
                        'no'
                    )->assertCreated();
                },
                'modifyTimeFunc'                   => fn (Carbon $now): Carbon|null => Carbon::make($now)?->addHours(2)->addMinutes(30),
                'assertCountFunc'                  => fn(Team $team): int => $team->members()->whereNot('person_id')->get()->unique('person_id')->count() - 1,
                'assertMarkedAsSent'               => true,
                'assertSentTo'                     => ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                'assertNotSentTo'                  => ['<EMAIL>'],
            ]],
            'Reminder scheduled before event creation (global time manipulation)' => [[
                'event_reminder_type'  => 'hours_before_event',
                // With an event beginning in 4 hours, a reminder of 5 hours before would be computed as 4 - 5 = -1 hour relative to creation.
                // This means the reminder would be scheduled _before_ the event was created.
                'event_reminder_hours' => 5,
                'setup'                => function (PushNotificationEventReminderTest $t): void {
                    // No extra setup is needed since the saving hook will cancel the reminder automatically.
                },
                // The modified global time is set to when notifications would normally be due,
                // but since the reminder is cancelled, no notifications should be sent.
                'modifyTimeFunc'       => fn (Carbon $now): Carbon|null => Carbon::make($now)?->addHours(2)->addMinutes(30),
                'assertCountFunc'      => fn(Team $team): int => 0,
                'assertMarkedAsSent'   => false,
                'assertSentTo'         => [],
            ]],
            'Reminder scheduled after event begin (global time manipulation)' => [[
                'event_reminder_type'  => 'hours_before_event',
                // With an event beginning 4 hours from creation, a negative value such as -1 results in a reminder at 4 - (-1) = +5 hours,
                // which is after the event start time.
                'event_reminder_hours' => -1,
                'setup'                => function (PushNotificationEventReminderTest $t): void {
                    // No additional setup required.
                },
                // Adjust global time to a moment when the artisan command would normally find a due reminder,
                // but the reminder is cancelled because it’s computed out-of-range.
                'modifyTimeFunc'       => fn (Carbon $now): Carbon|null => Carbon::make($now)?->addHours(3)->addMinutes(30),
                'assertCountFunc'      => fn(Team $team): int => 0,
                'assertMarkedAsSent'   => false,
                'assertSentTo'         => [],
            ]],
        ];
    }
    /**
     * @param array{
     *     event_reminder_type: string,
     *     event_reminder_hours: int,
     *     setup: \Closure(\Tests\Feature\API\Notifications\PushNotificationEventReminderTest): void,
     *     modifyTimeFunc: \Closure(Carbon): (Carbon|null),
     *     assertCountFunc: \Closure(Team): int,
     *     assertMarkedAsSent: bool,
     *     assertSentTo: array<string>,
     *     assertNotSentTo?: array<string>,
     *     seriesIndex?: int,
     *     rrule?: string,
     *     cancelled?: bool
     * } $data
     *
     * @return void
     */
    #[DataProvider('dataProvider')]
    function testPushNotificationEventReminder(array $data): void {
        // Set the current date to a fake data for testing purposes in Carbon / Laravel
        Carbon::setTestNow(Carbon::create(2024, 12, 31, 15, 0, 0));
        
        $this->assertDatabaseCount('users', 0);
        $this->prepareDefaultScenario(withoutEvents: true);
        $this->enablePushNotificationsForAll();

        Notification::fake();

        // 1. Create event with reminder
        $team = $this->teamByName('Test Team 1');

        $teamEventSeriesData = [];
        if (isset($data['rrule'])) {
            print "  Rrule: {$data['rrule']}\n";
            $teamEventSeries = TeamEventSeries::forceCreate([
                'rrule_string' => $data['rrule'],
            ]);
            $teamEventSeriesData['series_type'] = EventSeriesType::RECURRING_PARENT->value;
            $teamEventSeriesData['series_id'] = $teamEventSeries->id;
        }

        $teamEventCancelledData = [];
        if (isset($data['cancelled'])) {
            $cancelledAt = Carbon::now()->addMinutes(30);
            $teamEventCancelledData['cancelled_at'] = $cancelledAt->getTimestamp();
        }

        // Event that begins in 4 hours from now
        $dateBegin = Carbon::now()->addHours(4);
        $event1 = TeamEvent::forceCreate(array_merge([
            'team_id'             => $team->id,
            'event_type'          => TeamEventType::TRAINING->value,
            'response_type'       => EventResponseType::NONE->value,
            'title'               => 'Reminder Event 1',
            'sub_text'            => 'Reminder Event 1 Description',
            'date_begin'          => $dateBegin->toDateString(),
            'time_begin'          => $dateBegin->toTimeString(),
            'event_reminder_type'  => $data['event_reminder_type'],
            'event_reminder_hours' => $data['event_reminder_hours'],
        ] + $teamEventSeriesData + $teamEventCancelledData));
        
        $data['setup']($this);

        $currentTime = Carbon::now();
        $modifiedTime = $data['modifyTimeFunc'](Carbon::now());
        Carbon::setTestNow($modifiedTime);

        // print debug info
        $eventReminderHours = $data['event_reminder_hours'];
        print "  Current time:       $currentTime\n";
        print "  Event begin:        {$event1->dateTimeBegin()}\n";
        print "  Event reminder date: $event1->event_reminder_date (+/- $eventReminderHours hours)\n";
        print "  Modified time:      $modifiedTime\n";
        
        // Print all events in the database
        $this->printEvents();
        $this->withoutMockingConsoleOutput();

        $output = new BufferedOutput();
        Artisan::call('notifications:send-event-reminders', [], $output);
        print PHP_EOL . $output->fetch() . PHP_EOL;
        
        // We'd expect a reminder to be sent to all members associated with a person
        $assertSentToResolved = array_map(fn($email) => $this->userByEmail($email), $data['assertSentTo']);
        if (sizeof($assertSentToResolved) !== 0) {
            // Using assertSentTo here instead of assertCount because we want to check the exact notification types
            Notification::assertSentTo(
                $assertSentToResolved, EventReminderNotification::class
            );
        }

        if (isset($data['assertNotSentTo'])) {
            // We'd expect a reminder to be sent to all members associated with a person
            $assertNotSentToResolved = array_map(fn($email) => $this->userByEmail($email), $data['assertNotSentTo']);
            if (sizeof($assertNotSentToResolved) !== 0) {
                // Using assertSentTo here instead of assertCount because we want to check the exact notification types
                Notification::assertNotSentTo(
                    $assertNotSentToResolved, EventReminderNotification::class
                );
            }
        }
        
        $eventToCheck = $event1;
        if (isset($data['seriesIndex'])) {
            $eventToCheck = $this->seriesEventByTitleAndIndex('Reminder Event 1', $data['seriesIndex']);
        }
        $eventToCheck->refresh();
        $this->printEvents();
        
        print "  Checking event_reminder_sent flag for event {$eventToCheck->title} ({$eventToCheck->dateTimeBegin()})\n";
        
        $this->assertEquals($data['assertMarkedAsSent'], $eventToCheck->event_reminder_sent);
    }
    
    function printEvents(): void {
        print "  Events in database:\n";
        TeamEvent::all()->each(function ($event) {
            $eventReminderSent = $event->event_reminder_sent ? 'true' : 'false';
            print "    Event: {$event->title} ({$event->dateTimeBegin()}) [{$event->series_type->value}] [event_reminder_sent: {$eventReminderSent}, reminder: {$event->event_reminder_date}]\n";
        });
    }
} 
