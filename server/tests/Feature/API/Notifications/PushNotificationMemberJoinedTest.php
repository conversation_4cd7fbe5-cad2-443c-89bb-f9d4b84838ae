<?php

namespace Tests\Feature\API\Notifications;

use App\Models\Invite;
use App\Notifications\Push\UserJoinedTeamNotification;
use App\Notifications\Push\VoteForUpcomingEventChangedNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Feature\API\JsonApiTestCase;
use Tests\Feature\API\Notifications\DefaultScenario;

class PushNotificationMemberJoinedTest extends JsonApiTestCase {
    use RefreshDatabase;
    use DefaultScenario;

    /**
     * @return array<string, array<int, array{
     *     user: string,
     *     member: string,
     *     inviteToken: string,
     *     newMemberName: string,
     *     assertCount: int,
     *     assertSentTo: string[]
     * }>>
     */
    public static function dataProvider(): array {
        return [
            'User accepting invite for member'          => [[
                'user'          => '<EMAIL>',
                'member'        => '',
                'inviteToken'   => 'invite-member-1',
                'newMemberName' => '',
                'assertCount'   => 2,
                'assertSentTo'  => ['<EMAIL>', '<EMAIL>'],
            ]],
            'User accepting team invite for member'     => [[
                'user'          => '<EMAIL>',
                'member'        => 'Without User',
                'inviteToken'   => 'invite-team-1',
                'newMemberName' => '',
                'assertCount'   => 2,
                'assertSentTo'  => ['<EMAIL>', '<EMAIL>'],
            ]],
            'User accepting team invite for new member' => [[
                'user'          => '<EMAIL>',
                'member'        => '',
                'inviteToken'   => 'invite-team-1',
                'newMemberName' => 'This is a new member',
                'assertCount'   => 2,
                'assertSentTo'  => ['<EMAIL>', '<EMAIL>'],
            ]],
        ];
    }

    /**
     * @param array{user:string, member:string, inviteToken:string, assertCount:int, assertSentTo:array<string>} $data
     *
     * @return void
     */
    #[DataProvider('dataProvider')]
    function testPushNotificationMemberJoined(array $data): void {
        $this->assertDatabaseCount('users', 0);
        $this->prepareDefaultScenario();
        $this->prepareInvites();

        $user = $this->userByEmail($data['user']);
        if ($user == null) {
            $this->fail('User not found');
        }

        $assertCount = $data['assertCount'];
        $assertSentToResolved = array_map(fn($email) => $this->userByEmail($email), $data['assertSentTo']);

        $requestData = ['token' => $data['inviteToken']];
        if (isset($data['member']) && $data['member'] !== '') {
            $requestData['memberId'] = $this->memberByName($data['member'])->id;
        }
        if (isset($data['newMemberName']) && $data['newMemberName'] !== '') {
            $requestData['newMemberName'] = $data['newMemberName'];
        }

        Notification::fake();

        $this->actingAs($user)
            ->post(route('api.invite.accept'), $requestData);

        // Check assumptions
        Notification::assertCount($assertCount);
        if (sizeof($assertSentToResolved) !== 0) {
            Notification::assertSentTo($assertSentToResolved, UserJoinedTeamNotification::class);
        }
    }

    /**
     * Prepares invites for the default scenario. One for a specific team member,
     * another one for the entire team.
     */
    function prepareInvites(): void {
        Invite::forceCreate([
            'token'          => 'invite-member-1',
            'invitable_type' => 'App\Models\TeamMember',
            'invitable_id'   => $this->memberByName('Without User')->id,
        ]);
        Invite::forceCreate([
            'token'          => 'invite-team-1',
            'invitable_type' => 'App\Models\Team',
            'invitable_id'   => $this->teamByName('Test Team 1')->id,
        ]);
    }
}
