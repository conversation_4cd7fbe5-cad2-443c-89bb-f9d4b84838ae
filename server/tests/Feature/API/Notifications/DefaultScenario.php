<?php

namespace Tests\Feature\API\Notifications;

use App\Models\DeviceInfo;
use App\Models\Person;
use App\Models\PersonalAccessToken;
use App\Models\Team;
use App\Models\TeamEvent;
use App\Models\TeamMember;
use App\Models\User;
use App\Notifications\Push\VoteReminderNotification;
use App\Types\EventResponseType;
use App\Types\FeatureType;
use App\Types\TeamEventType;
use App\Types\TeamRoleType;
use Carbon\Carbon;
use Illuminate\Notifications\AnonymousNotifiable;
use Illuminate\Support\Facades\Notification;
use Str;
use Tests\Helper\ModelConvenienceFunctions;

trait DefaultScenario {
    use ModelConvenienceFunctions;
    
    function enablePushNotificationsForAll(): void {
        Person::all()->each(function (Person $person) {
            $person->features()->activate(FeatureType::NOTIFICATIONS_ENABLED->value);
        });
    }

    function prepareDefaultScenario(bool $withoutEvents = false): void {
        // Setup a team with different member constellations.
        // - A user managing two members
        // - A user managing one member
        // - A member not assigned to a user
        // - Two users individually assigned to a manager

        $team = Team::create(['name' => 'Test Team 1',]);

        $user1 = $this->createUser('<EMAIL>', 'Test User 1');
        $user2 = $this->createUser('<EMAIL>', 'Test User 2');

        $manager1 = $this->createUser('<EMAIL>', 'Test Manager 1');
        $manager2 = $this->createUser('<EMAIL>', 'Test Manager 2');

        $member1 = $team->createMember("With User 1 [a]", TeamRoleType::MEMBER, $user1);
        $member2 = $team->createMember("With User 1 [b]", TeamRoleType::MEMBER, $user1);
        $member3 = $team->createMember("With User 2", TeamRoleType::MEMBER, $user2);
        $member4 = $team->createMember("Without User", TeamRoleType::MEMBER);

        $member5 = $team->createMember("With Manager 1", TeamRoleType::MANAGER, $manager1);
        $member6 = $team->createMember("With Manager 2", TeamRoleType::MANAGER, $manager2);
        $member7 = $team->createMember("Manager without User", TeamRoleType::MANAGER);

        // Enable push notification feature for managers
        $manager1->person->features()->activate(FeatureType::NOTIFICATIONS_ENABLED->value);
        $manager2->person->features()->activate(FeatureType::NOTIFICATIONS_ENABLED->value);

        if (!$withoutEvents) {
            // Create the event with timestamp now
            $event1 = TeamEvent::forceCreate([
                'team_id'       => $team->id,
                'event_type'    => TeamEventType::TRAINING->value,
                'response_type' => EventResponseType::NONE->value,
                'title'         => 'Test Event 1',
                'sub_text'      => 'Test Event 1 Description',
                'date_begin'    => Carbon::now()->toDateString(),
                'time_begin'    => Carbon::now()->addHour()->toDateTimeString(),
            ]);

            /** @var array<string, Carbon> $eventData */
            foreach ([
                         ['yesterday' => Carbon::now()->subDay()],
                         ['two_hours_ago' => Carbon::now()->subHours()->subMinutes(59)],
                         ['now'           => Carbon::now()],
                         ['tomorrow'      => Carbon::now()->addDay()],
                         ['two_days'      => Carbon::now()->addDays()->addHours(23)->addMinutes(59)],
                         ['three_days'    => Carbon::now()->addDays(3)],
                         ['week'          => Carbon::now()->addDays(7)],
                     ] as $eventData) {
                $name = array_keys($eventData)[0];
                $time = $eventData[$name];

                TeamEvent::forceCreate([
                    'team_id'       => $team->id,
                    'event_type'    => TeamEventType::TRAINING->value,
                    'response_type' => EventResponseType::NONE->value,
                    'title'         => $name,
                    'sub_text'      => $name . ' Description',
                    'date_begin'    => $time->toDateString(),
                    'time_begin'    => $time->toDateTimeString(),
                ]);
            }
        }

        // Create a token and device info entry for each user to enable the push notification logic
        foreach ([$user1, $user2, $manager1, $manager2] as $user) {
            PersonalAccessToken::forceCreate([
                'token'          => 'test-token-' . $user->id,
                'tokenable_id'   => $user->id,
                'tokenable_type' => 'users',
                'name'           => 'test-token-' . $user->id,
            ]);

            DeviceInfo::forceCreate([
                'id'                         => Str::uuid(),
                'user_id'                    => $user->id,
                'fcm_token'                  => 'test-device-token-' . $user->id,
                'push_notifications_enabled' => true,
            ]);
        }
    }

    function eventByTitle(string $title): TeamEvent {
        $event = TeamEvent::where('title', $title)->first();
        if ($event === null) {
            $this->fail('Event not found');
        }
        return $event;
    }

    function seriesEventByTitleAndIndex(string $title, int $index): TeamEvent {
        $event = TeamEvent::where('title', $title)
            ->notSeriesParent()
            ->orderBy('date_begin')
            ->skip($index)
            ->first();
        if ($event === null) {
            $this->fail('Event not found');
        }
        return $event;
    }

    function memberByName(string $name): TeamMember {
        $member = TeamMember::where('name', $name)->first();
        if ($member === null) {
            $this->fail('Member not found');
        }
        return $member;
    }

    function userByEmail(string $email): User {
        $user = User::where('email', $email)->first();
        if ($user === null) {
            $this->fail('User not found');
        }
        return $user;
    }

    function teamByName(string $name): Team {
        $team = Team::where('name', $name)->first();
        if ($team === null) {
            $this->fail('Team not found');
        }
        return $team;
    }
}
