<?php

namespace Tests\Feature\API\Notifications;

use App\Notifications\Push\VoteForUpcomingEventChangedNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Feature\API\JsonApiTestCase;
use Tests\Feature\API\Notifications\DefaultScenario;

class PushNotificationOnVoteChangeTest extends JsonApiTestCase {
    use RefreshDatabase;
    use DefaultScenario;

    /**
     * @return array<string, array<int, array{
     *     user: string,
     *     member: string,
     *     event: string,
     *     assertCount: int,
     *     assertSentTo: string[]
     * }>>
     */
    public static function dataProvider(): array {
        return [
            // Regular user votes for a team member
            'Regular user vote'                                                         => [[
                'user'         => '<EMAIL>',
                'member'       => 'With User 1 [a]',
                'event'        => 'now',
                'assertCount'  => 2,
                'assertSentTo' => ['<EMAIL>', '<EMAIL>'],
            ]],
            'Regular user vote - 1h59m past'                                            => [[
                'user'         => '<EMAIL>',
                'member'       => 'With User 1 [a]',
                'event'        => 'two_hours_ago',
                'assertCount'  => 2,
                'assertSentTo' => ['<EMAIL>', '<EMAIL>'],
            ]],
            'Regular user vote - Tomorrow'                                              => [[
                'user'         => '<EMAIL>',
                'member'       => 'With User 1 [a]',
                'event'        => 'tomorrow',
                'assertCount'  => 2,
                'assertSentTo' => ['<EMAIL>', '<EMAIL>'],
            ]],
            'Regular user vote - Two days in the future'                                => [[
                'user'         => '<EMAIL>',
                'member'       => 'With User 1 [a]',
                'event'        => 'two_days',
                'assertCount'  => 2,
                'assertSentTo' => ['<EMAIL>', '<EMAIL>'],
            ]],
            // If a manager votes for a team member they own, the other managers should be notified
            'Manager vote'                                                              => [[
                'user'         => '<EMAIL>',
                'member'       => 'With Manager 1',
                'event'        => 'now',
                'assertCount'  => 1,
                'assertSentTo' => ['<EMAIL>'],
            ]],
            // If a manager votes for a member they aren't owner of (e.g. syncing votes with WhatsApp), no one should get notified.
            "No notification if user isn't owner of member"                             => [[
                'user'         => '<EMAIL>',
                'member'       => 'With User 1 [a]',
                'event'        => 'now',
                'assertCount'  => 0,
                'assertSentTo' => [],
            ]],
            // Do not send notifications for past events
            'No notification for past event'                                            => [[
                'user'         => '<EMAIL>',
                'member'       => 'With User 1 [a]',
                'event'        => 'yesterday',
                'assertCount'  => 0,
                'assertSentTo' => [],
            ]],
            // Do not send notifications for events more than two days in the future
            'No notifications for events more than two days in the future [three days]' => [[
                'user'         => '<EMAIL>',
                'member'       => 'With User 1 [a]',
                'event'        => 'three_days',
                'assertCount'  => 0,
                'assertSentTo' => [],
            ]],
            // Do not send notifications for events more than two days in the future
            'No notifications for events more than two days in the future [week]'       => [[
                'user'         => '<EMAIL>',
                'member'       => 'With User 1 [a]',
                'event'        => 'week',
                'assertCount'  => 0,
                'assertSentTo' => [],
            ]],
        ];
    }

    /**
     * @param array{user:string, member:string, event:string, assertCount:int, assertSentTo:array<string>} $data
     *
     * @return void
     */
    #[DataProvider('dataProvider')]
    function testPushNotificationOnVoteChange(array $data): void {
        $this->assertDatabaseCount('users', 0);
        $this->prepareDefaultScenario();

        $event = $this->eventByTitle($data['event']);
        $member = $this->memberByName($data['member']);
        $user = $this->userByEmail($data['user']);

        $assertCount = $data['assertCount'];
        $assertSentToResolved = array_map(fn($email) => $this->userByEmail($email), $data['assertSentTo']);

        Notification::fake();

        // Send a vote via Json API
        $this->jsonApiVote($event->id, $member->id, $user)
            ->assertCreated();

        // Check assumptions
        Notification::assertCount($assertCount);
        if (sizeof($assertSentToResolved) !== 0) {
            Notification::assertSentTo(
                $assertSentToResolved, VoteForUpcomingEventChangedNotification::class, function (VoteForUpcomingEventChangedNotification $notification) use ($data) {
                return str_contains($notification->getBody(), $data['member']);
            });
        }
    }
}
