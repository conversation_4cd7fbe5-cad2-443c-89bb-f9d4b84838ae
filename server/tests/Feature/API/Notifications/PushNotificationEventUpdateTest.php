<?php

namespace Feature\API\Notifications;

use App\Models\Team;
use App\Models\TeamEvent;
use App\Models\TeamEventSeries;
use App\Notifications\Push\ChangedTeamEventNotification;
use App\Notifications\Push\EventReminderNotification;
use App\Notifications\Push\NewTeamEventNotification;
use App\Notifications\Push\UserJoinedTeamNotification;
use App\Rules\RRule;
use App\Types\EventResponseType;
use App\Types\EventSeriesType;
use App\Types\EventReminderType;
use App\Types\TeamEventType;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\Feature\API\JsonApiTestCase;
use Tests\Feature\API\Notifications\DefaultScenario;

class PushNotificationEventUpdateTest extends JsonApiTestCase {
    use RefreshDatabase;
    use DefaultScenario;

    /**
     * @return array<string, array<int, array{
     *     attribute: array<string, string>,
     *     value: string,
     *     expectedBody: string,
     *     expectedTitle: string
     * }>>
     */
    public static function dataProvider(): array {
        return [
            'Address changed'                                                         => [[
                'attributes'        => [
                    'addressString' => 'Omas Kueche',
                ],
                'expectedBody'     => 'Der Ort für den Termin Test Event 1 wurde auf Omas Kueche im Team Test Team 1 geändert',
                'expectedTitle'    => 'Termin aktualisiert',
            ]],
            'Begin date changed'                                                         => [[
                'attributes'        => [
                    'dateBegin' => '2024-01-01',
                    // dateBegin has priority and thus timeBegin will not affect the message, but result in a consistent message body
                    'timeBegin' => '23:48',
                ],
                'expectedBody'     => 'Test Event 1 wurde auf 01.01.2024 23:48 Uhr im Team Test Team 1 neu geplant',
                'expectedTitle'    => 'Termin aktualisiert',
            ]],
            'Begin time changed'                                                         => [[
                'attributes'        => [
                    'timeBegin' => '17:00',
                ],
                'expectedBody'     => 'Test Event 1 wurde auf 05.02.2025 17:00 Uhr im Team Test Team 1 neu geplant',
                'expectedTitle'    => 'Termin aktualisiert',
            ]],
            'Begin time and address changed'                                                         => [[
                'attributes'        => [
                    'addressString' => 'Omas Kueche',
                    'timeBegin'     => '17:00',
                ],
                'expectedBody'     => 'Die Uhrzeit für den Termin Test Event 1 wurde auf 05.02.2025 17:00 Uhr und der Ort auf Omas Kueche im Team Test Team 1 geändert',
                'expectedTitle'    => 'Termin aktualisiert',
            ]],
            'Meeting time changed'                                                         => [[
                'attributes'        => [
                    'timeMeet' => '23:59',
                ],
                'expectedBody'     => 'Der Treffzeitpunkt für den Termin Test Event 1 wurde auf 23:59 Uhr im Team Test Team 1 geändert',
                'expectedTitle'    => 'Termin aktualisiert',
            ]],
        ];
    }
    
    /**
     * @param array{attribute:string, value:string, expectedBody:string, expectedTitle:string} $data
     *
     * @return void
     */
    #[DataProvider('dataProvider')]
    function testPushNotificationEventReminder(array $data): void {
        // Set the current date to a fake data for testing purposes in Carbon / Laravel
        Carbon::setTestNow(Carbon::create(2025, 2, 5, 12, 0, 0));
        
        $this->assertDatabaseCount('users', 0);
        $this->prepareDefaultScenario();
        $this->enablePushNotificationsForAll();
        
        $attributes = $data['attributes'];
        $expectedBody = $data['expectedBody'];
        $expectedTitle = $data['expectedTitle']; 

        Notification::fake();

        $event = $this->eventByTitle('Test Event 1');
        $user = $this->userByEmail('<EMAIL>');

        $this->printEvents();

        $attributes['sendChangeNotification'] = true;
        
        $resp = $this->jsonApiEventUpdate($event->id, $user, $attributes);

        $resp->assertSuccessful();

        $this->printEvents();
        
        // Ensure that 4 notifications got sent
        Notification::assertCount(4);

        // Ensure that the notification didn't get sent to the user making the change
        Notification::assertNotSentTo($user, ChangedTeamEventNotification::class);

        // Check if a ChangedTeamEventNotification was sent to another user
        $otherUser = $this->userByEmail('<EMAIL>');
        Notification::assertSentTo(
            $otherUser,
            ChangedTeamEventNotification::class,
            function ($notification, $channels) use ($expectedBody, $expectedTitle) {
                $encodedJson = json_encode($notification);
                if (is_string($encodedJson)) {
                    $jsonMap = json_decode($encodedJson);
                    if (is_object($jsonMap) &&
                        property_exists($jsonMap, 'title') &&
                        property_exists($jsonMap, 'body') &&
                        is_string($jsonMap->title) &&
                        is_string($jsonMap->body)) {
                        return $jsonMap->title === $expectedTitle && $jsonMap->body === $expectedBody;
                    }
                }
            }
        );
        
    }

    /**
     * Test that a notification for a newly created event via JSONAPI
     * has the correct title and body.
     *
     * @return void
     */
    function testPushNotificationForNewlyCreatedEventUsingJsonApi(): void {
        Carbon::setTestNow(Carbon::create(2025, 2, 5, 12, 0, 0));
        $this->prepareDefaultScenario();
        $this->enablePushNotificationsForAll();

        $manager = $this->userByEmail('<EMAIL>');
        $team = $this->teamByName('Test Team 1');

        $begin = Carbon::create(2025, 2, 27, 22, 0, 0);
        $data = $this->createNewEventData($team, $begin, 'Test Event New');
        $data['attributes']['sendChangeNotification'] = true;

        Notification::fake();

        if (!$manager->person->user instanceof \Illuminate\Contracts\Auth\Authenticatable) {
            $this->fail('Invalid user object');
        }
        
        $eventId = $this->actingAs($manager->person->user)
            ->jsonApi('teamEvents')
            ->withData($data)
            ->post(route('api.v1.teamEvents.store'))
            ->id();

        // Test this with another user, since test-manager-1 won't receive the notification themselves 
        $otherUser = $this->userByEmail('<EMAIL>');
        Notification::assertSentTo(
            $otherUser->person->user,
            NewTeamEventNotification::class,
            function ($notification, $channels) {
                $expectedTitle = "Neuer Termin: 27.02.2025 - 22:00 Uhr";
                $expectedBody = "Test Event New wurde im Team Test Team 1 erstellt.";

                $encodedJson = json_encode($notification);
                if (is_string($encodedJson)) {
                    $jsonMap = json_decode($encodedJson);
                    if (is_object($jsonMap) &&
                        property_exists($jsonMap, 'title') &&
                        property_exists($jsonMap, 'body') &&
                        is_string($jsonMap->title) &&
                        is_string($jsonMap->body)) {
                        return $jsonMap->title === $expectedTitle && $jsonMap->body === $expectedBody;
                    }
                }
            }
        );
    }

    /**
     * Test that a notification for a newly created series event via JSONAPI
     * has the correct title and body.
     *
     * @return void
     */
    function testPushNotificationForNewlyCreatedSeriesEventUsingJsonApi(): void {
        Carbon::setTestNow(Carbon::create(2025, 2, 5, 12, 0, 0));
        $this->prepareDefaultScenario();
        $this->enablePushNotificationsForAll();

        $manager = $this->userByEmail('<EMAIL>');
        $team = $this->teamByName('Test Team 1');

        
        // Creating reourring events is a two step process at this time. We first need to create the series metadata
        // and then create an event associated with it. That event will represent the template from which other events are
        // then created.
        
        // We can create the series metadata via Eloquent, but need to create the event itself via jsonApi to trigger the
        // logic within the JsonAPI controller.
        
        $begin = Carbon::create(2025, 2, 27, 22, 0, 0);

        
        $teamEventSeries = TeamEventSeries::forceCreate([
            'rrule_string' => (new \RRule\RRule([
                'FREQ' => 'DAILY',
                'DTSTART' => $begin?->toDateString(),
                'COUNT' => 3
            ]))->rfcString(),
        ]);

        // Debug print to verify series creation
        print "Series created with ID: {$teamEventSeries->id}\n";

        $data = $this->createNewEventData($team, $begin, 'Series Event Test');
        $data['relationships']['series'] = [
            'data' => [
                'type' => 'teamEventSeries',
                'id' => $teamEventSeries->id,
            ]
        ];
        $data['attributes']['sendChangeNotification'] = true;
        $data['attributes']['seriesType'] = EventSeriesType::RECURRING_PARENT->value;
        
        Notification::fake();
        
        // Ideally there would be a single call to set this up, but JsonApi makes it easier not to.
        
        if (!$manager->person->user instanceof \Illuminate\Contracts\Auth\Authenticatable) {
            $this->fail('Invalid user object');
        }
        
        $response = $this->actingAs($manager->person->user)
            ->jsonApi('teamEvents')
            ->withData($data)
            ->post(route('api.v1.teamEvents.store'));

        if ($response->isSuccessful()) {
            $eventId = $response->id();
            print "Event created with ID: {$eventId}\n";
        } else {
            print "Event creation failed with status: {$response->status()}\n";
            print "Response body: " . $response->getContent() . "\n";
        }

        // Test this with another user, since test-manager-1 won't receive the notification themselves 
        $otherUser = $this->userByEmail('<EMAIL>');
        Notification::assertSentTo(
            $otherUser->person->user,
            NewTeamEventNotification::class,
            function ($notification, $channels) {
                $expectedTitle = "Neue Terminserie im Team Test Team 1";
                $expectedBody = "Series Event Test wurde für 22:00 Uhr erstellt: Täglich, ab dem 27.02.2025, 3 Mal insgesamt";

                $encodedJson = json_encode($notification);
                if (is_string($encodedJson)) {
                    $jsonMap = json_decode($encodedJson);
                    if (is_object($jsonMap) && 
                        property_exists($jsonMap, 'title') && 
                        property_exists($jsonMap, 'body') && 
                        is_string($jsonMap->title) && 
                        is_string($jsonMap->body)) {
                        return $jsonMap->title === $expectedTitle && $jsonMap->body === $expectedBody;
                    }
                }
                return false;
            }
        );
    }

    function printEvents(): void {
        print "  Events in database:\n";
        TeamEvent::all()->each(function ($event) {
            $eventReminderSent = $event->event_reminder_sent ? 'true' : 'false';
            print "    Event: {$event->title} ({$event->dateTimeBegin()}) [{$event->series_type->value}] [event_reminder_sent: {$eventReminderSent}]\n";
        });
    }
} 
