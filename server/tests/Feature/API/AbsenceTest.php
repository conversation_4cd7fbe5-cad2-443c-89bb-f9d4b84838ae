<?php

namespace API;

use App\Events\AbsenceCreated;
use App\Events\AbsenceUpdated;
use App\JsonApi\CommonSchema;
use App\Models\Absence;
use App\Models\Team;
use App\Models\TeamEvent;
use App\Types\AbsenceType;
use App\Types\TeamEventVoteType;
use App\Types\TeamPermissionType;
use Carbon\Carbon;
use Carbon\CarbonInterval;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use RRule\RRule;
use Tests\Feature\API\JsonApiTestCase;
use function PHPUnit\Framework\assertCount;
use function PHPUnit\Framework\assertSame;

class AbsenceTest extends JsonApiTestCase {
    use RefreshDatabase;

    public function test_create_absence(): void {
        Event::fake([
            AbsenceCreated::class
        ]);
        $user = $this->createUser();

        $dateBegin = Carbon::create(2023, 6, 12 );
        $absenceData = $this->createNewAbsenceData(
            dateBegin: $dateBegin,
            persons: [$user->person]
        );

        // create absence
        $response = $this->actingAs($user)
             ->jsonApi('absences')
             ->withData($absenceData)
             ->post(route('api.v1.absences.store'))
            ;
        $response->assertCreated();

        $user->person->refresh();
        self::assertCount(1, $user->person->absences);
        Event::assertDispatched(AbsenceCreated::class);

        $absence = Absence::find($response->id());
        self::assertCount(1, $absence->persons);
        self::assertCount(0, $absence->teamMembers);
    }

    public function test_create_recurring_absence(): void {
        $user = $this->createUser();

        $dateBegin = Carbon::create(2023, 6, 12);
        $rrule = new RRule([
            'DTSTART' => $dateBegin,
            'FREQ' => RRule::WEEKLY,
            'BYDAY' => 'MO,TU',
        ]);
        $absenceData = $this->createNewAbsenceData(
            dateBegin: $dateBegin,
            rruleString: $rrule,
            persons: [$user->person]
        );

        // create absence
        $response = $this->actingAs($user)
             ->jsonApi('absences')
             ->withData($absenceData)
             ->post(route('api.v1.absences.store'))
            ;
        $response->assertCreated();

        $user->person->refresh();
        self::assertCount(1, $user->person->absences);
    }

    public function test_create_absence_set_vote_no_single_day(): void {
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $team->createMember(user: $user);
        $member = $team->createMember('test');

        $event1 = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 12, 10, 0, 0 )
        );
        $eventOutOfRange = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 14, 10, 0, 0 )
        );

        $absenceBegin = Carbon::create(2023, 6, 12 );
        $absenceEnd = Carbon::create(2023, 6, 12 );
        $absenceData = $this->createNewAbsenceData(
            dateBegin: $absenceBegin,
            dateEnd: $absenceEnd,
            persons: [$user->person],
            teamMembers: [$member]
        );

        // create absence
        $response = $this->actingAs($user)
                         ->jsonApi('absences')
                         ->withData($absenceData)
                         ->post(route('api.v1.absences.store'))
        ;
        $response->assertCreated();
        $absence = Absence::find($response->id());
        self::assertCount(1, $absence->persons);
        self::assertCount(1, $absence->teamMembers);

        $user->person->refresh();
        $event1->refresh();

        self::assertCount(1, $user->person->absences);

        self::assertCount(2, $event1->votes);
        self::assertSame(TeamEventVoteType::NO, $event1->votes()->first()->vote);
        self::assertSame($user->person->id, $event1->votes()->first()->voterPerson->id);
        self::assertSame($absence->id, $event1->votes()->first()->absence->id);

        self::assertCount(0, $eventOutOfRange->votes);
    }

    public function test_create_absence_set_vote_no_multiple_days(): void {
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $team->createMember(user: $user);
        $event1 = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 12, 10, 0, 0 )
        );
        $event2 = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 20, 10, 0, 0 )
        );
        $eventOutOfRange = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 28,10 , 0, 0 )
        );

        $absenceBegin = Carbon::create(2023, 6, 12 );
        $absenceEnd = Carbon::create(2023, 6, 22 );
        $absenceData = $this->createNewAbsenceData(
            dateBegin: $absenceBegin,
            dateEnd: $absenceEnd,
            persons: [$user->person],
            teamMembers: [$member]
        );

        // create absence
        $response = $this->actingAs($user)
                         ->jsonApi('absences')
                         ->withData($absenceData)
                         ->post(route('api.v1.absences.store'))
        ;
        $response->assertCreated();

        $user->person->refresh();
        $event1->refresh();

        self::assertCount(1, $user->person->absences);

        self::assertCount(1, $event1->votes);
        self::assertSame(TeamEventVoteType::NO, $event1->votes()->first()->vote);

        self::assertCount(1, $event2->votes);
        self::assertSame(TeamEventVoteType::NO, $event2->votes()->first()->vote);

        self::assertCount(0, $eventOutOfRange->votes);
    }
    
    public function test_create_recurring_absence_with_end_date_set_vote_no(): void {
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $team->createMember(user: $user);
        $eventMonday1 = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2025, 5, 5, 10, 0, 0 )
        );
        $eventTuesday1 = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2025, 5, 6, 10, 0, 0 )
        );
        $eventMonday2 = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2025, 5, 12, 10, 0, 0 )
        );
        $eventMonday3 = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2025, 5, 19, 10, 0, 0 )
        );
        $eventOutOfRangeMonday = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2025, 5, 26, 10, 0, 0 )
        );
        $eventOutOfRangeTuesday = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2025, 5, 27, 10, 0, 0 )
        );

        $dateBegin = Carbon::create(2025, 5, 5 );
        $dateEnd = Carbon::create(2025, 5, 20 );
        $rrule = new RRule([
            'DTSTART' => $dateBegin,
            'FREQ' => RRule::WEEKLY,
            'INTERVAL' => 2,
            'BYDAY' => 'MO',
            'UNTIL' => $dateEnd
        ]);
        $absenceData = $this->createNewAbsenceData(
            dateBegin: $dateBegin,
            dateEnd: $dateEnd,
            rruleString: $rrule,
            persons: [$user->person]
        );

        // create absence
        $response = $this->actingAs($user)
                         ->jsonApi('absences')
                         ->withData($absenceData)
                         ->post(route('api.v1.absences.store'))
        ;
        $response->assertCreated();

        self::assertCount(1, $user->person->absences);

        self::assertCount(1, $eventMonday1->votes);
        self::assertSame(TeamEventVoteType::NO, $eventMonday1->votes()->first()->vote);

        self::assertCount(0, $eventMonday2->votes);
        self::assertCount(0, $eventTuesday1->votes);

        self::assertCount(1, $eventMonday3->votes);
        self::assertSame(TeamEventVoteType::NO, $eventMonday3->votes()->first()->vote);

        self::assertCount(0, $eventOutOfRangeMonday->votes);
        self::assertCount(0, $eventOutOfRangeTuesday->votes);
    }

    public function test_create_event_during_absence_range_set_vote_no(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_CREATE]);
        $member = $team->createMember();

        $absenceBegin = Carbon::create(2023, 6, 12 );
        $absenceEnd = Carbon::create(2023, 6, 16 );

        $absence = Absence::create([
            'date_begin' => $absenceBegin,
            'date_end' => $absenceEnd,
            'absence_type' => AbsenceType::VACATION,
            'author_id' => $manager->person->id,
        ]);
        $absence->teamMembers()->attach($member);
        $absence->persons()->attach($manager->person);


        $eventInRangeData = $this->createNewEventData(
            $team,
            Carbon::create(2023, 6, 14, 10 )
        );
        $eventOutOfRangeData = $this->createNewEventData(
            $team,
            Carbon::create(2023, 6, 20, 10 )
        );

        // create event
        $response1 = $this->actingAs($manager->person->user)
                         ->jsonApi('teamEvents')
                         ->withData($eventInRangeData)
                         ->post(route('api.v1.teamEvents.store'))
                         ->assertCreated();

        $teamEventInRange = TeamEvent::find($response1->id());

        // create event
        $response2 = $this->actingAs($manager->person->user)
                         ->jsonApi('teamEvents')
                         ->withData($eventOutOfRangeData)
                         ->post(route('api.v1.teamEvents.store'))
                         ->assertCreated();

        $teamEventOutOfRange = TeamEvent::find($response2->id());


        // should be 2, since there is one member and one person (with manager) added
        assertCount(2, $teamEventInRange->votes);
        assertCount(0, $teamEventOutOfRange->votes);
    }

    public function test_create_event_during_recurring_absence_range_set_vote_no(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_CREATE]);

        $absenceBegin = Carbon::create(2023, 6, 12 );
        $absenceEnd = Carbon::create(2023, 6, 19 );
        $rrule = new RRule([
            'DTSTART' => $absenceBegin,
            'FREQ' => RRule::WEEKLY,
            'BYDAY' => 'MO',
            'UNTIL' => $absenceEnd
        ]);
        $absence = Absence::create([
            'date_begin' => $absenceBegin,
            'date_end' => $absenceEnd,
            'absence_type' => AbsenceType::VACATION,
            'author_id' => $manager->person->id,
            'rrule_string' => $rrule->rfcString()
        ]);
        $absence->teamMembers()->attach($manager);


        $event1MondayData = $this->createNewEventData(
            $team,
            Carbon::create(2023, 6, 12, 10 )
        );
        $event2TuesdayData = $this->createNewEventData(
            $team,
            Carbon::create(2023, 6, 13, 10 )
        );
        $event3MondayData = $this->createNewEventData(
            $team,
            Carbon::create(2023, 6, 19, 10 )
        );
        $eventOutOfRangeMondayData = $this->createNewEventData(
            $team,
            Carbon::create(2023, 6, 26, 10 )
        );
        $eventOutOfRangeTuesdayData = $this->createNewEventData(
            $team,
            Carbon::create(2023, 6, 27, 10 )
        );

        // create events
        $event1Monday = TeamEvent::find(
            $this->actingAs($manager->person->user)
                  ->jsonApi('teamEvents')
                  ->withData($event1MondayData)
                  ->post(route('api.v1.teamEvents.store'))
                  ->assertCreated()
                  ->id()
        );

        $event2Tuesday = TeamEvent::find(
            $this->actingAs($manager->person->user)
                 ->jsonApi('teamEvents')
                 ->withData($event2TuesdayData)
                 ->post(route('api.v1.teamEvents.store'))
                 ->assertCreated()
                 ->id()
        );

        $event3Monday = TeamEvent::find(
            $this->actingAs($manager->person->user)
                 ->jsonApi('teamEvents')
                 ->withData($event3MondayData)
                 ->post(route('api.v1.teamEvents.store'))
                 ->assertCreated()
                 ->id()
        );

        $eventOutOfRangeMonday = TeamEvent::find(
            $this->actingAs($manager->person->user)
                 ->jsonApi('teamEvents')
                 ->withData($eventOutOfRangeMondayData)
                 ->post(route('api.v1.teamEvents.store'))
                 ->assertCreated()
                 ->id()
        );

        $eventOutOfRangeTuesday = TeamEvent::find(
            $this->actingAs($manager->person->user)
                 ->jsonApi('teamEvents')
                 ->withData($eventOutOfRangeTuesdayData)
                 ->post(route('api.v1.teamEvents.store'))
                 ->assertCreated()
                 ->id()
        );

        assertCount(1, $event1Monday->votes);
        assertCount(0, $event2Tuesday->votes);
        assertCount(1, $event3Monday->votes);
        assertCount(0, $eventOutOfRangeMonday->votes);
        assertCount(0, $eventOutOfRangeTuesday->votes);
    }

    public function test_update_event_within_absence_range_does_not_change_absence_related_votes(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_CREATE]);

        $absenceBegin = Carbon::create(2023, 6, 12 );
        $absenceEnd = Carbon::create(2023, 6, 16 );
        $absence = Absence::create([
            'date_begin' => $absenceBegin,
            'date_end' => $absenceEnd,
            'absence_type' => AbsenceType::VACATION,
            'author_id' => $manager->person->id,
        ]);
        $absence->teamMembers()->attach($manager);

        $event = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 13, 10, 0, 0 )
        );

        assertCount(1, $event->votes);
        $vote = $event->votes()->first();
        assertSame($manager->id, $vote->team_member_id);
        assertSame(TeamEventVoteType::NO, $vote->vote);

        $eventUpdateDate = $this->createEventUpdateData(
            $event,
            'changed',
            begin: Carbon::create(2023, 6, 14, 10 )
        );

        // create event
        $this->actingAs($manager->person->user)
                          ->jsonApi('teamEvents')
                          ->withData($eventUpdateDate)
                          ->patch(route('api.v1.teamEvents.update', [
                              'teamEvent' => $event->id
                          ]))
                          ->assertFetchedOne($event);

        $event->refresh();

        assertCount(1, $event->votes);
        $vote = $event->votes()->first();
        assertSame($manager->id, $vote->team_member_id);
        assertSame(TeamEventVoteType::NO, $vote->vote);
    }

    public function test_update_event_into_absence_range_set_vote_no(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_CREATE]);
        $member = $this->createMemberWithoutPermissions($team);

        $absenceBegin = Carbon::create(2023, 6, 12 );
        $absenceEnd = Carbon::create(2023, 6, 16 );
        $absence = Absence::create([
            'date_begin' => $absenceBegin,
            'date_end' => $absenceEnd,
            'absence_type' => AbsenceType::VACATION,
            'author_id' => $member->person->id,
        ]);
        $absence->teamMembers()->attach($manager);

        $event = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 10, 10, 0, 0 )
        );

        assertCount(0, $event->votes);

        $eventUpdateDate = $this->createEventUpdateData(
            $event,
            'changed',
            begin: Carbon::create(2023, 6, 14, 10 )
        );

        // create event
        $this->actingAs($manager->person->user)
             ->jsonApi('teamEvents')
             ->withData($eventUpdateDate)
             ->patch(route('api.v1.teamEvents.update', [
                 'teamEvent' => $event->id
             ]))
             ->assertFetchedOne($event);

        $event->refresh();

        assertCount(1, $event->votes);
        $vote = $event->votes()->first();
        assertSame($manager->id, $vote->team_member_id);
        assertSame(TeamEventVoteType::NO, $vote->vote);
    }

    public function test_update_event_out_of_absence_range_deletes_related_votes(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_CREATE]);
        $member = $this->createMemberWithoutPermissions($team);

        $absenceBegin = Carbon::create(2023, 6, 12 );
        $absenceEnd = Carbon::create(2023, 6, 16 );
        $absence = Absence::create([
            'date_begin' => $absenceBegin,
            'date_end' => $absenceEnd,
            'absence_type' => AbsenceType::VACATION,
            'author_id' => $member->person->id,
        ]);
        $absence->teamMembers()->attach($manager);

        $event = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 13, 10, 0, 0 )
        );

        assertCount(1, $event->votes);

        $eventUpdateDate = $this->createEventUpdateData(
            $event,
            'changed',
            begin: Carbon::create(2023, 6, 10, 10 )
        );

        // create event
        $this->actingAs($manager->person->user)
             ->jsonApi('teamEvents')
             ->withData($eventUpdateDate)
             ->patch(route('api.v1.teamEvents.update', [
                 'teamEvent' => $event->id
             ]))
             ->assertFetchedOne($event);

        $event->refresh();

        assertCount(0, $event->votes);
    }

    public function test_create_absence_overwrite_manual_vote_with_no(): void {
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $team->createMember(user: $user);

        $event1 = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 12, 10, 0, 0 )
        );
        $event1->createVote($member, TeamEventVoteType::YES, $user->person);

        $eventOutOfRange = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 14, 10, 0, 0 )
        );
        $eventOutOfRange->createVote($member, TeamEventVoteType::YES, $user->person);

        $absenceBegin = Carbon::create(2023, 6, 12 );
        $absenceEnd = Carbon::create(2023, 6, 12 );
        $absenceData = $this->createNewAbsenceData(
            dateBegin: $absenceBegin,
            dateEnd: $absenceEnd,
            teamMembers: [$member]
        );

        assertSame(TeamEventVoteType::YES, $event1->votes->first()->vote);
        assertSame(TeamEventVoteType::YES, $eventOutOfRange->votes->first()->vote);

        // create absence
        $this->actingAs($user)
             ->jsonApi('absences')
             ->withData($absenceData)
             ->post(route('api.v1.absences.store'))
             ->assertCreated();

        $event1->refresh();
        $eventOutOfRange->refresh();

        assertSame(TeamEventVoteType::NO, $event1->votes->first()->vote);
        assertSame(TeamEventVoteType::YES, $eventOutOfRange->votes->first()->vote);
    }

    public function test_create_event_series_during_recurring_absence_open_end_set_vote_no(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_CREATE]);
        $member = $this->createMemberWithoutPermissions($team);

        $absenceBegin = Carbon::create(2023, 6, 12 );
        $absenceRrule = new RRule([
            'DTSTART' => $absenceBegin,
            'FREQ' => RRule::WEEKLY,
            'BYDAY' => 'MO',
        ]);
        $absence = Absence::create([
            'date_begin' => $absenceBegin,
            'rrule_string' => $absenceRrule->rfcString(),
            'absence_type' => AbsenceType::VACATION,
            'author_id' => $member->person->id,
        ]);
        $absence->teamMembers()->attach($manager);

        $seriesBegin = Carbon::create(2023, 6, 6, 10, 0, 0 );
        $seriesRrule = new RRule([
            'DTSTART' => $seriesBegin,
            'FREQ' => RRule::WEEKLY,
            'BYDAY' => 'MO,TU',
            'UNTIL' => Carbon::create(2023, 6,25)
        ]);

        $seriesData = $this->createEventSeriesData($seriesRrule);

        // create event series config
        $responseSeries = $this->actingAs($manager->person->user)
                               ->jsonApi('teamEventSeries')
                               ->withData($seriesData)
                               ->post(route('api.v1.teamEventSeries.store'))
        ;
        $responseSeries->assertCreated();

        $seriesId = $responseSeries->id();
        $eventData = $this->createRecurringEventData($team, $seriesBegin, $seriesId);

        // create recurring parent event
        $this->actingAs($manager->person->user)
             ->jsonApi('teamEvents')
             ->withData($eventData)
             ->post(route('api.v1.teamEvents.store'))
             ->assertCreated();

        $expectedNoVotesArray = [
            '2023-06-06' =>  0, // Monday
            '2023-06-07' =>  0, // Tuesday
            '2023-06-12' =>  1, // Monday
            '2023-06-13' =>  0, // Tuesday
            '2023-06-19' =>  1, // Monday
            '2023-06-20' =>  0, // Tuesday
        ];

        foreach ($team->events as $event) {
            $formattedDate = CommonSchema::getIso8601DateFormat($event->date_begin);
            self::assertArrayHasKey($formattedDate, $expectedNoVotesArray);
            $expectedNoVotes = $expectedNoVotesArray[$formattedDate];
            assertCount($expectedNoVotes, $event->votes, $formattedDate);
        }
    }

    public function testUpdateAbsence(): void {
        Event::fake([
            AbsenceUpdated::class
        ]);
        $user = $this->createUser();
        $absenceBegin = Carbon::create(2023, 6, 12 );
        $absenceEnd = Carbon::create(2023, 6, 16 );
        $absence = Absence::create([
            'date_begin' => $absenceBegin,
            'date_end' => $absenceEnd,
            'absence_type' => AbsenceType::VACATION,
            'author_id' => $user->person->id,
        ]);
        $absenceBeginUpdated = Carbon::create(2023, 6, 13 );

        $absenceUpdateData = $this->createAbsenceUpdateData($absence, dateBegin: $absenceBeginUpdated);

        $this->actingAs($user)
             ->jsonApi('absences')
             ->withData($absenceUpdateData)
             ->patch(route('api.v1.absences.update', [
                 'absence' => $absence->id
             ]))
            ->assertFetchedOne($absence);

        $absence->refresh();

        Event::assertDispatched(AbsenceUpdated::class);
        self::assertEquals($absenceBeginUpdated, $absence->date_begin);
    }

    public function test_update_absence_end_date_deletes_out_of_range_votes(): void {
        Carbon::setTestNow(Carbon::create(2023,06,15));
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $team->createMember(user: $user);
        $eventInRange = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 12, 10, 0, 0 )
        );
        $eventOutOfRangeAfterUpdate = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 20, 10, 0, 0 )
        );

        $absenceBegin = Carbon::create(2023, 6, 12);

        $absence = Absence::create([
            'date_begin' => $absenceBegin,
            'absence_type' => AbsenceType::VACATION,
            'author_id' => $member->person->id,
        ]);
        $absence->teamMembers()->attach($member);
        event(new AbsenceCreated($absence));

        assertCount(1, $eventInRange->votes);
        assertCount(1, $eventOutOfRangeAfterUpdate->votes);

        $absenceEndUpdated = Carbon::create(2023, 6, 16 );
        $absenceUpdateData = $this->createAbsenceUpdateData($absence, dateEnd: $absenceEndUpdated);

        $this->actingAs($user)
             ->jsonApi('absences')
             ->withData($absenceUpdateData)
             ->patch(route('api.v1.absences.update', [
                 'absence' => $absence->id
             ]))
             ->assertFetchedOne($absence);

        $eventInRange->refresh();
        $eventOutOfRangeAfterUpdate->refresh();
        assertCount(1, $eventInRange->votes);
        assertCount(0, $eventOutOfRangeAfterUpdate->votes);
    }
    
    public function test_update_recurring_absence_end_date_set_no_votes(): void {
        Carbon::setTestNow(Carbon::create(2023,06,15));
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $team->createMember(user: $user);
        $eventInRange = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 12, 10, 0, 0 )
        );
        $eventOutOfRangeBeforeUpdate = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 19, 10, 0, 0 )
        );

        $absenceBegin = Carbon::create(2023, 6, 12);
        $absenceEnd = Carbon::create(2023, 6, 16 );

        $rrule = new RRule([
            'DTSTART' => $absenceBegin,
            'FREQ' => RRule::WEEKLY,
            'BYDAY' => 'MO',
            'UNTIL' => $absenceEnd
        ]);
        
        $absence = Absence::create([
            'date_begin' => $absenceBegin,
            'date_end' => $absenceEnd,
            'absence_type' => AbsenceType::VACATION,
            'author_id' => $member->person?->id,
            'rrule_string' => $rrule->rfcString()
        ]);
        
        $absence->teamMembers()->attach($member);
        event(new AbsenceCreated($absence));

        assertCount(1, $eventInRange->votes);
        assertCount(0, $eventOutOfRangeBeforeUpdate->votes);

        $absenceEndUpdated = Carbon::create(2023, 6, 22 );

        $rruleUpdated = new RRule([
            'DTSTART' => $absenceBegin,
            'FREQ' => RRule::WEEKLY,
            'BYDAY' => 'MO',
            'UNTIL' => $absenceEndUpdated
        ]);
        
        $absenceUpdateData = $this->createAbsenceUpdateData($absence, dateEnd: $absenceEndUpdated, rruleString: $rruleUpdated);
        
        $this->actingAs($user)
             ->jsonApi('absences')
             ->withData($absenceUpdateData)
             ->patch(route('api.v1.absences.update', [
                 'absence' => $absence->id
             ]))
             ->assertFetchedOne($absence);

        $eventInRange->refresh();
        $eventOutOfRangeBeforeUpdate->refresh();
        assertCount(1, $eventInRange->votes);
        assertCount(1, $eventOutOfRangeBeforeUpdate->votes);
    }

    public function test_update_absence_begin_date_deletes_out_of_range_votes(): void {
        Carbon::setTestNow(Carbon::create(2023,06,10));
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $team->createMember(user: $user);
        $eventOutOfRangeAfterUpdate = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 12, 10, 0, 0 )
        );
        $eventInRange = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 20, 10, 0, 0 )
        );

        $absenceBegin = Carbon::create(2023, 6, 12);
        $absenceBeginUpdated = Carbon::create(2023, 6, 14);

        $absence = Absence::create([
            'date_begin' => $absenceBegin,
            'absence_type' => AbsenceType::VACATION,
            'author_id' => $member->person->id,
        ]);
        $absence->teamMembers()->attach($member);
        event(new AbsenceCreated($absence));

        assertCount(1, $eventInRange->votes);
        assertCount(1, $eventOutOfRangeAfterUpdate->votes);

        $absenceUpdateData = $this->createAbsenceUpdateData($absence, dateBegin: $absenceBeginUpdated);

        $this->actingAs($user)
             ->jsonApi('absences')
             ->withData($absenceUpdateData)
             ->patch(route('api.v1.absences.update', [
                 'absence' => $absence->id
             ]))
             ->assertFetchedOne($absence);

        $eventInRange->refresh();
        $eventOutOfRangeAfterUpdate->refresh();
        assertCount(1, $eventInRange->votes);
        assertCount(0, $eventOutOfRangeAfterUpdate->votes);
    }


    public function test_update_absence_add_recurring_updates_no_votes(): void {
        Carbon::setTestNow(Carbon::create(2023,06,12));
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $team->createMember(user: $user);
        $event1MondayInRange = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 12, 10, 0, 0 )
        );
        $event2TuesdayOutOfRangeAfterUpdate = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 13, 10, 0, 0 )
        );

        $absenceBegin = Carbon::create(2023, 6, 12);
        $absenceEnd = Carbon::create(2023, 6, 16 );

        $rrule = new RRule([
            'DTSTART' => $absenceBegin,
            'FREQ' => RRule::WEEKLY,
            'BYDAY' => 'MO',
            'UNTIL' => $absenceEnd
        ]);

        $absence = Absence::create([
            'date_begin' => $absenceBegin,
            'date_end' => $absenceEnd,
            'absence_type' => AbsenceType::VACATION,
            'author_id' => $member->person->id,
        ]);
        $absence->teamMembers()->attach($member);
        event(new AbsenceCreated($absence));

        assertCount(1, $event1MondayInRange->votes);
        assertCount(1, $event2TuesdayOutOfRangeAfterUpdate->votes);

        $absenceUpdateData = $this->createAbsenceUpdateData($absence, rruleString: $rrule);

        $this->actingAs($user)
             ->jsonApi('absences')
             ->withData($absenceUpdateData)
             ->patch(route('api.v1.absences.update', [
                 'absence' => $absence->id
             ]))
             ->assertFetchedOne($absence);

        $event1MondayInRange->refresh();
        $event2TuesdayOutOfRangeAfterUpdate->refresh();
        assertCount(1, $event1MondayInRange->votes);
        assertCount(0, $event2TuesdayOutOfRangeAfterUpdate->votes);
    }


    public function test_update_absence_remove_recurring_set_no_votes(): void {
        Carbon::setTestNow(Carbon::create(2023,06,12));
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $team->createMember(user: $user);
        $event1MondayInRange = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 12, 10, 0, 0 )
        );
        $event2TuesdayOutOfRangeBeforeUpdate = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 13, 10, 0, 0 )
        );

        $absenceBegin = Carbon::create(2023, 6, 12);
        $absenceEnd = Carbon::create(2023, 6, 16 );

        $rrule = new RRule([
            'DTSTART' => $absenceBegin,
            'FREQ' => RRule::WEEKLY,
            'BYDAY' => 'MO',
            'UNTIL' => $absenceEnd
        ]);

        $absence = Absence::create([
            'date_begin' => $absenceBegin,
            'date_end' => $absenceEnd,
            'rrule_string' => $rrule->rfcString(),
            'absence_type' => AbsenceType::VACATION,
            'author_id' => $member->person->id,
        ]);
        $absence->teamMembers()->attach($member);
        event(new AbsenceCreated($absence));

        assertCount(1, $event1MondayInRange->votes);
        assertCount(0, $event2TuesdayOutOfRangeBeforeUpdate->votes);

        $absenceUpdateData = $this->createAbsenceUpdateData($absence, rruleString: '');

        $this->actingAs($user)
             ->jsonApi('absences')
             ->withData($absenceUpdateData)
             ->patch(route('api.v1.absences.update', [
                 'absence' => $absence->id
             ]))
             ->assertFetchedOne($absence);

        $absence->refresh();
        assertCount(1, $absence->teamMembers);

        $event1MondayInRange->refresh();
        $event2TuesdayOutOfRangeBeforeUpdate->refresh();
        assertCount(1, $event1MondayInRange->votes);
        assertCount(1, $event2TuesdayOutOfRangeBeforeUpdate->votes);
    }

    public function test_delete_absence_deletes_future_votes_only(): void {
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $team->createMember(user: $user);
        $pastEvent = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::now()->add(CarbonInterval::days(1))
        );
        $futureEvent = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::now()->add(CarbonInterval::days(5))
        );

        $absence = Absence::create([
            'date_begin' => Carbon::now()->sub(CarbonInterval::days(1)),
            'absence_type' => AbsenceType::VACATION,
            'author_id' => $member->person->id,
        ]);
        $absence->teamMembers()->attach($member);
        event(new AbsenceCreated($absence));

        // $pastEvent is in future on absence create date
        assertCount(1, $pastEvent->votes);
        assertCount(1, $futureEvent->votes);

        // travel 3 days into future to have $pastEvent in the past
        Carbon::setTestNow(Carbon::now()->add(CarbonInterval::days(3)));

        $this->actingAs($user)
             ->jsonApi('absences')
             ->delete(route('api.v1.absences.destroy', [
                 'absence' => $absence->id
             ]))
             ->assertNoContent();

        $pastEvent->refresh();
        $futureEvent->refresh();
        // $pastEvent is in past on absence delete date
        assertCount(1, $pastEvent->votes);
        assertCount(0, $futureEvent->votes);
    }

    public function test_update_absence_only_touches_future_votes(): void {
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $team->createMember(user: $user);
        $eventInPast = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::now()->sub(CarbonInterval::days(2))
        );
        $eventInFuture = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::now()->add(CarbonInterval::days(7))
        );
        $eventInFutureOutOfRangeAfterUpdate = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::now()->add(CarbonInterval::days(14))
        );

        $absenceBegin = Carbon::now()->sub(CarbonInterval::days(5));
        $absenceEndUpdated = Carbon::now()->add(CarbonInterval::days(10));

        $absence = Absence::create([
            'date_begin' => $absenceBegin,
            'absence_type' => AbsenceType::SICK,
            'author_id' => $member->person->id,
        ]);
        $absence->teamMembers()->attach($member);
        event(new AbsenceCreated($absence));

        assertCount(1, $eventInPast->votes);
        assertCount(1, $eventInFuture->votes);
        assertCount(1, $eventInFutureOutOfRangeAfterUpdate->votes);

        $eventInPast->createVote(
            teamMember: $member,
            vote: TeamEventVoteType::YES,
            voterPerson: $user->person
        );

        $eventInFuture->createVote(
            teamMember: $member,
            vote: TeamEventVoteType::YES,
            voterPerson: $user->person
        );

        $eventInPast->refresh();
        $eventInFuture->refresh();
        assertSame(TeamEventVoteType::YES->value, $eventInPast->votes->first()->vote->value);
        assertSame(TeamEventVoteType::YES->value, $eventInFuture->votes->first()->vote->value);

        $absenceUpdateData = $this->createAbsenceUpdateData($absence, dateEnd: $absenceEndUpdated);

        $this->actingAs($user)
             ->jsonApi('absences')
             ->withData($absenceUpdateData)
             ->patch(route('api.v1.absences.update', [
                 'absence' => $absence->id
             ]))
             ->assertFetchedOne($absence);

        $eventInPast->refresh();
        $eventInFuture->refresh();
        $eventInFutureOutOfRangeAfterUpdate->refresh();
        assertSame(TeamEventVoteType::YES->value, $eventInPast->votes->first()->vote->value);
        assertSame(TeamEventVoteType::NO->value, $eventInFuture->votes->first()->vote->value);
        assertCount(0, $eventInFutureOutOfRangeAfterUpdate->votes);
    }

    public function test_update_absence_checks_other_absences_in_range(): void {
        Carbon::setTestNow(Carbon::create(2023,06,5));
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $team->createMember(user: $user);
        $event = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 15, 10, 0, 0 )
        );

        $absenceBegin = Carbon::create(2023, 6, 10);

        $absence1 = Absence::create([
            'date_begin' => $absenceBegin,
            'absence_type' => AbsenceType::SICK,
            'author_id' => $member->person->id,
        ]);
        $absence1->teamMembers()->attach($member);
        event(new AbsenceCreated($absence1));

        $absenceToChangeOutOfRange = Absence::create([
            'date_begin' => $absenceBegin,
            'absence_type' => AbsenceType::VACATION,
            'author_id' => $member->person->id,
        ]);
        $absenceToChangeOutOfRange->teamMembers()->attach($member);
        event(new AbsenceCreated($absenceToChangeOutOfRange));

        assertCount(1, $event->votes);
        assertSame($absenceToChangeOutOfRange->id, $event->votes->first()->absence_id);

        $absenceEndUpdated = Carbon::create(2023, 6, 11 );
        $absenceUpdateData = $this->createAbsenceUpdateData($absenceToChangeOutOfRange, dateEnd: $absenceEndUpdated);

        $this->actingAs($user)
             ->jsonApi('absences')
             ->withData($absenceUpdateData)
             ->patch(route('api.v1.absences.update', [
                 'absence' => $absenceToChangeOutOfRange->id
             ]))
             ->assertFetchedOne($absenceToChangeOutOfRange);

        $event->refresh();
        assertCount(1, $event->votes);
        assertSame($absence1->id, $event->votes->first()->absence_id);
    }

    public function test_delete_absence_checks_other_absences_in_range(): void {
        Carbon::setTestNow(Carbon::create(2023,06,5));
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $team->createMember(user: $user);
        $event = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 15, 10, 0, 0 )
        );

        $absenceBegin = Carbon::create(2023, 6, 10);

        $absence1 = Absence::create([
            'date_begin' => $absenceBegin,
            'absence_type' => AbsenceType::SICK,
            'author_id' => $member->person->id,
        ]);
        $absence1->teamMembers()->attach($member);
        event(new AbsenceCreated($absence1));

        $absenceToBeDeleted = Absence::create([
            'date_begin' => $absenceBegin,
            'absence_type' => AbsenceType::VACATION,
            'author_id' => $member->person->id,
        ]);
        $absenceToBeDeleted->teamMembers()->attach($member);
        event(new AbsenceCreated($absenceToBeDeleted));

        assertCount(1, $event->votes);
        assertSame($absenceToBeDeleted->id, $event->votes->first()->absence_id);

        $this->actingAs($user)
             ->jsonApi('absences')
             ->delete(route('api.v1.absences.destroy', [
                 'absence' => $absenceToBeDeleted->id
             ]))
             ->assertNoContent();

        $event->refresh();
        assertCount(1, $event->votes);
        assertSame($absence1->id, $event->votes->first()->absence_id);
    }

    public function test_removing_all_members_via_api(): void{
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member1 = $team->createMember(user: $user);
        $member2 = $team->createMember(user: $user);

        $absence = Absence::create([
            'date_begin' =>  Carbon::now(),
            'absence_type' => AbsenceType::SICK,
            'author_id' => $member1->person->id,
        ]);
        $absence->teamMembers()->attach($member1);
        $absence->teamMembers()->attach($member2);
        event(new AbsenceCreated($absence));
        assertCount(2, $absence->teamMembers);

        $absenceUpdateData = $this->createAbsenceUpdateData($absence, teamMembers: []);

        $this->actingAs($member1->person->user)
             ->jsonApi('absences')
             ->withData($absenceUpdateData)
             ->patch(route('api.v1.absences.update', [
                 'absence' => $absence->id
             ]))
             ->assertFetchedOne($absence);

        $absence->refresh();
        assertCount(0, $absence->teamMembers);
    }

    public function test_removing_all_persons_via_api(): void {
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member1 = $team->createMember(user: $user);

        $absence = Absence::create([
            'date_begin' =>  Carbon::now(),
            'absence_type' => AbsenceType::SICK,
            'author_id' => $member1->person->id,
        ]);
        $absence->persons()->attach($user->person);
        event(new AbsenceCreated($absence));
        assertCount(1, $absence->persons);

        $absenceUpdateData = $this->createAbsenceUpdateData($absence, persons: []);

        $this->actingAs($member1->person->user)
             ->jsonApi('absences')
             ->withData($absenceUpdateData)
             ->patch(route('api.v1.absences.update', [
                 'absence' => $absence->id
             ]))
             ->assertFetchedOne($absence);

        $absence->refresh();
        assertCount(0, $absence->persons);
    }

    public function test_adding_member_does_add_votes_for_future_events(): void {
        Carbon::setTestNow(Carbon::create(2023,6,14));
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member1 = $team->createMember(user: $user);
        $member2 = $team->createMember(user: $user);

        $event = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 15, 10, 0, 0 )
        );
        $eventInPast = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 13, 10, 0, 0 )
        );

        $absence = Absence::create([
            'date_begin' =>  Carbon::create(2023, 6, 12, 0, 0, 0 ),
            'absence_type' => AbsenceType::SICK,
            'author_id' => $member1->person->id,
        ]);
        $absence->teamMembers()->attach($member1);
        event(new AbsenceCreated($absence));

        assertCount(1, $event->votes);
        assertCount(1, $eventInPast->votes);

        $absence->teamMembers()->attach($member2);
        $absence->refresh();
        event(new AbsenceUpdated($absence));

        assertCount(2, $event->refresh()->votes);
        assertCount(1, $eventInPast->refresh()->votes);
    }

    public function test_removing_member_does_remove_votes_for_future_events(): void {
        Carbon::setTestNow(Carbon::create(2023,6,14));
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member1 = $team->createMember(user: $user);
        $member2 = $team->createMember(user: $user);

        $event = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 15, 10, 0, 0 )
        );
        $eventInPast = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 13, 10, 0, 0 )
        );

        $absence = Absence::create([
            'date_begin' =>  Carbon::create(2023, 6, 12, 0, 0, 0 ),
            'absence_type' => AbsenceType::SICK,
            'author_id' => $member1->person->id,
        ]);
        $absence->teamMembers()->attach($member1);
        $absence->teamMembers()->attach($member2);
        event(new AbsenceCreated($absence));

        assertCount(2, $event->votes);
        assertCount(2, $eventInPast->votes);

        $absence->teamMembers()->detach($member2);
        $absence->refresh();
        event(new AbsenceUpdated($absence));

        assertCount(1, $event->refresh()->votes);
        assertCount(2, $eventInPast->refresh()->votes);
    }

    public function test_absence_relation_is_removed_on_vote_update(): void {
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $team->createMember(user: $user);

        $event = $this->createTeamEvent($team);

        $absence = Absence::create([
            'date_begin' =>  Carbon::create(2023, 6, 12, 0, 0, 0 ),
            'absence_type' => AbsenceType::SICK,
            'author_id' => $member->person->id,
        ]);
        $absence->teamMembers()->attach($member);
        event(new AbsenceCreated($absence));

        assertCount(1, $event->votes);
        self::assertTrue($event->votes()->first()->hasComment());


        $vote = $event->votes()->first();
        $data = $this->createUpdateVoteData($vote, TeamEventVoteType::YES);

        $this->actingAs($member->person->user)
             ->jsonApi('teamEventVotes')
             ->withData($data)
             ->patch(route('api.v1.teamEventVotes.update', [
                 'teamEventVote' => $vote->id
             ]))
             ->assertFetchedOne($vote);

        self::assertNull($vote->refresh()->absence);
        assertCount(1, $event->refresh()->votes);
        assertCount(0, $absence->refresh()->votes);
        self::assertFalse($event->votes()->first()->hasComment());
    }

    public function test_removing_member_does_remove_manual_votes(): void {
        Carbon::setTestNow(Carbon::create(2023,6,14));
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $member1 = $team->createMember(user: $user);

        $event1 = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 15, 10, 0, 0 )
        );
        $eventManualVote = $this->createTeamEvent(
            team: $team,
            dateTimeBegin:  Carbon::create(2023, 6, 16, 10, 0, 0 )
        );

        $absence = Absence::create([
            'date_begin' =>  Carbon::create(2023, 6, 12, 0, 0, 0 ),
            'absence_type' => AbsenceType::SICK,
            'author_id' => $member1->person->id,
        ]);
        $absence->teamMembers()->attach($member1);
        event(new AbsenceCreated($absence));

        assertCount(1, $event1->votes);
        assertCount(1, $eventManualVote->votes);

        $vote = $eventManualVote->votes()->first();
        $data = $this->createUpdateVoteData($vote, TeamEventVoteType::YES);

        $this->actingAs($member1->person->user)
             ->jsonApi('teamEventVotes')
             ->withData($data)
             ->patch(route('api.v1.teamEventVotes.update', [
                 'teamEventVote' => $vote->id
             ]))
             ->assertFetchedOne($vote);

        $absence->teamMembers()->detach($member1);
        $absence->refresh();
        event(new AbsenceUpdated($absence));

        assertCount(0, $event1->refresh()->votes);
        assertCount(1, $eventManualVote->refresh()->votes);
    }
}
