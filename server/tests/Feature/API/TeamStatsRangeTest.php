<?php

namespace API;

use App\Models\Team;
use App\Models\TeamStatsRange;
use App\Types\TeamPermissionType;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\Feature\API\JsonApiTestCase;

class TeamStatsRangeTest extends JsonApiTestCase {
    use RefreshDatabase;

    public function test_create_team_stats_range(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_STATS_RANGE_CREATE]);

        $name = 'test Range';
        $dateStart = Carbon::create(2024, 1, 1 );
        $dateEnd = Carbon::create(2024, 1, 16 );
        $statsRangeData = $this->createTeamStatsRangeData(
            $team,
            $dateStart,
            $dateEnd,
            $name
        );

        $this->actingAs($manager->person->user)
             ->jsonApi('teamStatsRanges')
             ->withData($statsRangeData)
             ->post(route('api.v1.teamStatsRanges.store'))
             ->assertCreated()
        ;
        $team->refresh();
        self::assertCount(1, $team->statsRanges);
    }

    public function test_create_team_stats_range_without_permissions_fails(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, []);

        $name = 'test Range';
        $dateStart = Carbon::create(2024, 1, 1 );
        $dateEnd = Carbon::create(2024, 1, 16 );
        $statsRangeData = $this->createTeamStatsRangeData(
            $team,
            $dateStart,
            $dateEnd,
            $name
        );

        $this->actingAs($manager->person->user)
             ->jsonApi('teamStatsRanges')
             ->withData($statsRangeData)
             ->post(route('api.v1.teamStatsRanges.store'))
             ->assertForbidden()
        ;
        $team->refresh();
        self::assertCount(0, $team->statsRanges);
    }

    public function test_delete_team_stats_range(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_STATS_RANGE_DELETE]);
        $startsRange = $team->statsRanges()->create([
            'name' => 'test Range',
            'start_date' => Carbon::create(2024, 1, 1 ),
            'end_date' => Carbon::create(2024, 1, 16 ),
        ]);
        self::assertCount(1, $team->statsRanges);

        $this->actingAs($manager->person->user)
             ->jsonApi('teamStatsRanges')
             ->delete(route('api.v1.teamStatsRanges.destroy', [
                 'teamStatsRange' => $startsRange->id
             ]))
             ->assertNoContent()
        ;

        $team->refresh();
        self::assertCount(0, $team->statsRanges);
    }

    public function test_delete_team_stats_range_without_permission_fails(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, []);
        $startsRange = $team->statsRanges()->create([
            'name' => 'test Range',
            'start_date' => Carbon::create(2024, 1, 1 ),
            'end_date' => Carbon::create(2024, 1, 16 ),
        ]);
        self::assertCount(1, $team->statsRanges);

        $this->actingAs($manager->person->user)
             ->jsonApi('teamStatsRanges')
             ->delete(route('api.v1.teamStatsRanges.destroy', [
                 'teamStatsRange' => $startsRange->id
             ]))
             ->assertForbidden()
        ;

        $team->refresh();
        self::assertCount(1, $team->statsRanges);
    }

    public function test_selecting_active_stats_range(): void {
        Carbon::setTestNow(Carbon::create(2024,01,17));

        $team = Team::create(['name' => 'Test Team 1',]);
        $statsRangeWithinDate = $team->statsRanges()->create([
            'name' => 'Range 1',
            'start_date' => Carbon::create(2024, 1, 1 ),
            'end_date' => Carbon::create(2024, 1, 18 ),
        ]);
        $statsRangeOutOfDate = $team->statsRanges()->create([
            'name' => 'Range 2',
            'start_date' => Carbon::create(2024, 1, 1 ),
            'end_date' => Carbon::create(2024, 1, 16 ),
        ]);
        $statsRangeOutOfDate2 = $team->statsRanges()->create([
            'name' => 'Range 3',
            'start_date' => Carbon::create(2024, 1, 18 ),
            'end_date' => Carbon::create(2024, 1, 20 ),
        ]);

        $team->refresh();
        $activeStatsRange = $team->activeStatsRanges()->first();
        self::assertNotNull($activeStatsRange);
        self::assertEquals($statsRangeWithinDate->id, $activeStatsRange->id);
    }

    public function test_selecting_no_active_stats_range(): void {
        Carbon::setTestNow(Carbon::create(2024,01,17));

        $team = Team::create(['name' => 'Test Team 1',]);
        $statsRangeOutOfDate = $team->statsRanges()->create([
            'name' => 'Range 2',
            'start_date' => Carbon::create(2024, 1, 1 ),
            'end_date' => Carbon::create(2024, 1, 16 ),
        ]);
        $statsRangeOutOfDate2 = $team->statsRanges()->create([
            'name' => 'Range 3',
            'start_date' => Carbon::create(2024, 1, 18 ),
            'end_date' => Carbon::create(2024, 1, 20 ),
        ]);

        $team->refresh();
        $activeStatsRange = $team->activeStatsRanges()->first();
        self::assertNull($activeStatsRange);
    }

    public function test_selecting_active_stats_range_with_overlapping_ranges(): void {
        Carbon::setTestNow(Carbon::create(2024,01,17));

        $team = Team::create(['name' => 'Test Team 1',]);
        $statsRangePrimary = $team->statsRanges()->create([
            'name' => 'Range 1',
            'start_date' => Carbon::create(2024, 1, 5 ),
            'end_date' => Carbon::create(2024, 1, 30 ),
        ]);
        $statsRange2 = $team->statsRanges()->create([
            'name' => 'Range 2',
            'start_date' => Carbon::create(2024, 1, 1 ),
            'end_date' => Carbon::create(2024, 1, 18 ),
        ]);
        $statsRange3 = $team->statsRanges()->create([
            'name' => 'Range 3',
            'start_date' => Carbon::create(2024, 1, 10 ),
            'end_date' => Carbon::create(2024, 1, 20 ),
        ]);

        $team->refresh();
        $activeStatsRange = $team->activeStatsRanges()->first();
        self::assertNotNull($activeStatsRange);
        self::assertEquals($statsRangePrimary->id, $activeStatsRange->id);
    }

    public function test_get_team_stats_ranges_on_event_via_api(): void {
        Carbon::setTestNow(Carbon::create(2024,01,10));
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, []);
        $event = $this->createTeamEvent(
            team: $team,
            dateTimeBegin: Carbon::create(2024,01,17)
        );
        $statsRangeWithinDate = $team->statsRanges()->create([
            'name' => 'Range 1',
            'start_date' => Carbon::create(2024, 1, 1 ),
            'end_date' => Carbon::create(2024, 1, 18 ),
        ]);
        $statsRangeOutOfDate = $team->statsRanges()->create([
            'name' => 'Range 2',
            'start_date' => Carbon::create(2024, 1, 1 ),
            'end_date' => Carbon::create(2024, 1, 16 ),
        ]);
        $statsRangeOutOfDate2 = $team->statsRanges()->create([
            'name' => 'Range 3',
            'start_date' => Carbon::create(2024, 1, 18 ),
            'end_date' => Carbon::create(2024, 1, 20 ),
        ]);

        $this->actingAs($manager->person->user)
             ->jsonApi('teamEvents')
            ->includePaths('team.statsRanges')
            ->get(route('api.v1.teamEvents.show', [
                'teamEvent' => $event->id
            ]))
            ->assertIsIncluded('teamStatsRanges', $statsRangeWithinDate)
            ;
    }

    public function test_stats_ranges_default_order(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $statsRange1 = $team->statsRanges()->create([
            'name' => 'Range 1',
            'start_date' => Carbon::create(2024, 1, 5 ),
            'end_date' => Carbon::create(2024, 1, 30 ),
        ]);
        $statsRange2 = $team->statsRanges()->create([
            'name' => 'Range 2',
            'start_date' => Carbon::create(2024, 1, 1 ),
            'end_date' => Carbon::create(2024, 1, 18 ),
        ]);
        $statsRange3 = $team->statsRanges()->create([
            'name' => 'Range 3',
            'start_date' => Carbon::create(2024, 1, 5 ),
            'end_date' => Carbon::create(2024, 1, 20 ),
        ]);
        $statsRange4 = $team->statsRanges()->create([
            'name' => 'Range 3',
            'start_date' => Carbon::create(2024, 1, 10 ),
            'end_date' => Carbon::create(2024, 1, 20 ),
        ]);

        $team->refresh();
        $resultOrder = $team->statsRanges->map(function (TeamStatsRange $statsRange) {
            return $statsRange->id;
        });

        $testOrder = collect([
            $statsRange1,
            $statsRange4,
            $statsRange3,
            $statsRange2,
        ])->map(function (TeamStatsRange $statsRange) {
            return $statsRange->id;
        });

        self::assertEquals($testOrder, $resultOrder);
    }

    public function test_selecting_active_stats_range_on_single_team(): void {
        Carbon::setTestNow(Carbon::create(2024,01,17));

        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $this->createMemberWithoutPermissions($team);
        $statsRangeWithinDate = $team->statsRanges()->create([
            'name' => 'Range 1',
            'start_date' => Carbon::create(2024, 1, 1 ),
            'end_date' => Carbon::create(2024, 1, 18 ),
        ]);
        $statsRangeOutOfDate = $team->statsRanges()->create([
            'name' => 'Range 2',
            'start_date' => Carbon::create(2024, 1, 1 ),
            'end_date' => Carbon::create(2024, 1, 16 ),
        ]);
        $statsRangeOutOfDate2 = $team->statsRanges()->create([
            'name' => 'Range 3',
            'start_date' => Carbon::create(2024, 1, 18 ),
            'end_date' => Carbon::create(2024, 1, 20 ),
        ]);

        $this
            ->actingAs($member->person->user)
            ->jsonApi()
            ->expects('teams')
            ->includePaths('activeStatsRanges')
            ->get(route('api.v1.teams.index'))
            ->assertFetchedMany([
                [
                    'type' => 'teams',
                    'id' => $team->id,
                    'relationships' => [
                        'activeStatsRanges' => [
                            'data' => [
                                [
                                    'type' => 'teamStatsRanges',
                                    'id' => $statsRangeWithinDate->id,
                                ]
                            ]
                        ]
                    ]
                ]
            ]);
    }

    public function test_selecting_stats_ranges_on_two_teams(): void {
        Carbon::setTestNow(Carbon::create(2024,01,17));

        $teamA = Team::create(['name' => 'Test Team A',]);
        $teamB = Team::create(['name' => 'Test Team B',]);
        $member = $this->createMemberWithoutPermissions($teamA);
        $teamB->createMember(user: $member->person->user);

        $statsRangeTeamAWithinDate = $teamA->statsRanges()->create([
            'name' => 'Range A1',
            'start_date' => Carbon::create(2024, 1, 1 ),
            'end_date' => Carbon::create(2024, 1, 18 ),
        ]);

        $statsRangeTeamBWithinDate = $teamB->statsRanges()->create([
            'name' => 'Range B1',
            'start_date' => Carbon::create(2024, 1, 2 ),
            'end_date' => Carbon::create(2024, 1, 19 ),
        ]);

        $this
            ->actingAs($member->person->user)
            ->jsonApi()
            ->expects('teams')
            ->includePaths('statsRanges')
            ->get(route('api.v1.teams.index'))
            ->assertFetchedMany([
                [
                    'type' => 'teams',
                    'id' => $teamA->id,
                    'relationships' => [
                        'statsRanges' => [
                            'data' => [
                                [
                                    'type' => 'teamStatsRanges',
                                    'id' => $statsRangeTeamAWithinDate->id,
                                ]
                            ]
                        ]
                    ]
                ],
                [
                    'type' => 'teams',
                    'id' => $teamB->id,
                    'relationships' => [
                        'statsRanges' => [
                            'data' => [
                                [
                                    'type' => 'teamStatsRanges',
                                    'id' => $statsRangeTeamBWithinDate->id,
                                ]
                            ]
                        ]
                    ]
                ]
            ]);
    }

    public function test_selecting_active_stats_range_on_two_teams(): void {
        Carbon::setTestNow(Carbon::create(2024,01,17));

        $teamA = Team::create(['name' => 'Test Team A',]);
        $teamB = Team::create(['name' => 'Test Team B',]);
        $member = $this->createMemberWithoutPermissions($teamA);
        $teamB->createMember(user: $member->person->user);

        $statsRangeTeamAWithinDate = $teamA->statsRanges()->create([
            'name' => 'Range A1',
            'start_date' => Carbon::create(2024, 1, 1 ),
            'end_date' => Carbon::create(2024, 1, 18 ),
        ]);

        $statsRangeTeamBWithinDate = $teamB->statsRanges()->create([
            'name' => 'Range B1',
            'start_date' => Carbon::create(2024, 1, 2 ),
            'end_date' => Carbon::create(2024, 1, 19 ),
        ]);

        $this
            ->actingAs($member->person->user)
            ->jsonApi()
            ->expects('teams')
            ->includePaths('activeStatsRanges')
            ->get(route('api.v1.teams.index'))
            ->assertFetchedMany([
                [
                    'type' => 'teams',
                    'id' => $teamA->id,
                    'relationships' => [
                        'activeStatsRanges' => [
                            'data' => [
                                [
                                    'type' => 'teamStatsRanges',
                                    'id' => $statsRangeTeamAWithinDate->id,
                                ]
                            ]
                        ]
                    ]
                ],
                [
                    'type' => 'teams',
                    'id' => $teamB->id,
                    'relationships' => [
                        'activeStatsRanges' => [
                            'data' => [
                                [
                                    'type' => 'teamStatsRanges',
                                    'id' => $statsRangeTeamBWithinDate->id,
                                ]
                            ]
                        ]
                    ]
                ]
            ]);
    }
}
