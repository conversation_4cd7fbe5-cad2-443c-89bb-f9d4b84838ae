<?php

namespace Tests\Feature\API;

use App\Models\Absence;
use App\Models\Comment;
use App\Models\Person;
use App\Models\Team;
use App\Models\TeamEvent;
use App\Models\TeamEventTask;
use App\Models\TeamEventTaskConfig;
use App\Models\TeamEventVote;
use App\Models\TeamMember;
use App\Models\TeamRole;
use App\Models\User;
use App\Types\AbsenceType;
use App\Types\EventResponseType;
use App\Types\EventSeriesType;
use App\Types\TeamEventType;
use App\Types\TeamEventVoteType;
use Carbon\Carbon;
use Cknow\Money\Money;
use Illuminate\Database\Eloquent\Model;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Core\Document\ResourceObject;
use Lara<PERSON><PERSON>son<PERSON><PERSON>\Testing\MakesJsonApiRequests;
use Lara<PERSON><PERSON>son<PERSON><PERSON>\Testing\TestResponse;
use Tests\Helper\Relation;
use Tests\Helper\Relationships;
use Tests\TestCase;

abstract class JsonApiTestCase extends TestCase {
    use MakesJsonApiRequests;

    private function createEventData(
        $teamId,
        $title = 'TestEvent',
        $id = null,
        Carbon $begin = null,
        EventSeriesType $seriesType = EventSeriesType::SINGLE,
        string $seriesId = null,
        EventResponseType $responseType = EventResponseType::NONE,
    ): array {
        $eventType = TeamEventType::TRAINING;
        if(!isset($begin)) {
            $now = Carbon::create(null, null, null, null, null, null );
            $begin = $now;
        }

        $data = [
            'type' => 'teamEvents',
            'attributes' => [
                'eventType' => $eventType->value,
                'seriesType' => $seriesType->value,
                'responseType' => $responseType->value,
                'title' => $title,
                'dateBegin' => $begin->format('Y-m-d'),
                'timeBegin' => $begin->format('H:i'),
            ],
            'relationships' => [
                'team' => [
                    'data' => [
                        'type' => 'teams',
                        'id' => $teamId
                    ]
                ],
            ]
        ];
        if(isset($id)) {
            $data['id'] = $id;
        }
        if(isset($seriesId)) {
            $data['relationships']['series'] = [
                'data' => [
                    'type' => 'teamEventSeries',
                    'id' => $seriesId
                ]
            ];
        }
        return $data;
    }


    protected function createNewEventData(Team $team, Carbon $begin = null, string $title = 'TestEvent',
                                          EventResponseType $responseType = EventResponseType::NONE,): array {
        return $this->createEventData($team->id, $title, begin: $begin, responseType: $responseType);
    }

    protected function createEventUpdateData(TeamEvent $teamEvent, $title = null, Carbon $begin = null): array {
        return $this->createEventData($teamEvent->team_id, $title, $teamEvent->id, begin: $begin);
    }

    protected function createEventSeriesData(string $rruleString, string $id = null): array {
        $data = [
            'type' => 'teamEventSeries',
            'attributes' => [
                'rruleString' => $rruleString,
            ],
        ];
        if(isset($id)) {
            $data['id'] = $id;
        }
        return $data;
    }
    protected function createRecurringEventData(Team $team, Carbon $begin, string $seriesId): array {
        return $this->createEventData(
            teamId: $team->id,
            begin: $begin,
            seriesType: EventSeriesType::RECURRING_PARENT,
            seriesId: $seriesId
        );
    }

    protected function createVote(TeamEvent $event, TeamMember $teamMember, TeamEventVoteType $vote, Person $voterPerson = null): TeamEventVote {
        return $event->createVote(
            $teamMember,
            $vote,
            $voterPerson
        );
    }
    private function createVoteData(TeamEvent $teamEvent = null, TeamMember $teamMember = null, TeamEventVoteType $voteType = TeamEventVoteType::YES, $id = null): array {

        $data = [
            'type' => 'teamEventVotes',
            'attributes' => [
                'vote' => $voteType
            ]
        ];
        if(isset($teamEvent, $teamMember)) {
            $data['relationships'] = [
                'teamEvent' => [
                    'data' => [
                        'type' => 'teamEvents',
                        'id' => $teamEvent->id
                    ]
                ],
                'teamMember' => [
                    'data' => [
                        'type' => 'teamMembers',
                        'id' => $teamMember->id
                    ]
                ],
            ];
        }

        if(isset($id)) {
            $data['id'] = $id;
        }
        return $data;
    }

    protected function createNewVoteData(TeamEvent $teamEvent, TeamMember $teamMember, TeamEventVoteType $voteType = TeamEventVoteType::YES): array {
        return $this->createVoteData($teamEvent, $teamMember, $voteType);
    }

    protected function createUpdateVoteData(TeamEventVote $vote, TeamEventVoteType $voteType = TeamEventVoteType::YES): array {
        return $this->createVoteData(voteType: $voteType, id: $vote->id);
    }

    private function createCommentData(string $text, Model $parent = null, string $parentType = null, $id = null): array {
        $data = [
            'type' => 'comments',
            'attributes' => [
                'text' => $text
            ],
        ];
        if(isset($parent, $parentType)) {
            $data['relationships'] = [
                'parent' => [
                    'data' => [
                        'type' => $parentType,
                        'id' => $parent->id
                    ]
                ],
            ];
        }

        if(isset($id)) {
            $data['id'] = $id;
        }
        return $data;
    }

    protected function createNewCommentData(string $text, Model $parent, string $parentType): array {
        return $this->createCommentData($text, $parent, $parentType);
    }

    protected function createCommentUpdateData(Comment $comment, string $text): array {
        return $this->createCommentData($text, id: $comment->id);
    }

    private function createMemberData($roleId, $teamId, $name = 'member', $id = null): array {
        $data = [
            'type' => 'teamMembers',
            'attributes' => [
                'name' => $name
            ],
            'relationships' => [
                'statusRole' => [
                    'data' => [
                        'type' => 'teamRoles',
                        'id' => $roleId
                    ]
                ],
                'team' => [
                    'data' => [
                        'type' => 'teams',
                        'id' => $teamId
                    ]
                ],
            ]
        ];
        if(isset($id)) {
            $data['id'] = $id;
        }
        return $data;
    }


    protected function createNewMemberData(Team $team, TeamRole $role, string $name = 'member'): array {
        return $this->createMemberData($role->id, $team->id, $name);
    }

    protected function createMemberUpdateData(TeamMember $member, $name): array {
        return $this->createMemberData($member->statusRole->id, $member->team->id, $name, $member->id);
    }

    protected function createMemberInviteData(TeamMember $member): array {
        return [
            'type' => 'invites',
            'relationships' => [
                'invitable' => [
                    'data' => [
                        'type' => 'teamMembers',
                        'id' => $member->id
                    ]
                ],
            ]
        ];
    }
    protected function createTeamInviteData(Team $team): array {
        return [
            'type' => 'invites',
            'relationships' => [
                'invitable' => [
                    'data' => [
                        'type' => 'teams',
                        'id' => $team->id
                    ]
                ],
            ]
        ];
    }

    private function createTeamData($name = 'team', $id = null): array {
        $data = [
            'type' => 'teams',
            'attributes' => [
                'name' => $name
            ],
        ];
        if(isset($id)) {
            $data['id'] = $id;
        }
        return $data;
    }

    protected function createNewTeamData(string $name = 'team'): array {
        return $this->createTeamData($name);
    }

    protected function createTeamUpdateData(Team $team, $name): array {
        return $this->createTeamData($name, $team->id);
    }

    protected function createNewLedgerData(Team $team): array {
        return [
            'type' => 'teamLedgers',
            'relationships' => [
                'team' => [
                    'data' => [
                        'type' => 'teams',
                        'id' => $team->id
                    ]
                ]
            ],
        ];
    }
    
    /**
     * @param Carbon       $dateBegin
     * @param Carbon|null  $dateEnd
     * @param string|null  $rruleString
     * @param Person[]     $persons
     * @param TeamMember[] $teamMembers
     * @param AbsenceType  $type
     *
     * @return array
     */
    protected function createAbsenceData(
        Carbon $dateBegin,
        Carbon $dateEnd = null,
        string $rruleString = null,
        array $persons = null,
        array $teamMembers = null,
        AbsenceType $type = AbsenceType::SICK,
        $id = null,
    ): array {

        $data = [
            'type' => 'absences',
            'attributes' => [
                'absenceType' => $type->value,
                'dateBegin' => $dateBegin->format('Y-m-d'),
                'dateEnd' => $dateEnd?->format('Y-m-d'),
                'rruleString' => $rruleString,
            ],

        ];

        if(isset($persons)) {
            if(empty($persons)) {
                $data['attributes']['detachRelationsByName'] = ['persons'];
            } else {
                $data['relationships']['persons']['data'] = array_map(fn(Person $person) =>
                [
                    'type' => 'persons',
                    'id' => $person->id
                ] , $persons);
            }
        }

        if(isset($teamMembers)) {
            if(empty($teamMembers)) {
                $data['attributes']['detachRelationsByName'] = ['teamMembers'];
            } else {
                $data['relationships']['teamMembers']['data'] = array_map(fn(TeamMember $teamMember) =>
                [
                    'type' => 'teamMembers',
                    'id' => $teamMember->id
                ], $teamMembers);
            }
        }
        if(isset($id)) {
            $data['id'] = $id;
        }
        return $data;
    }

    protected function createNewAbsenceData(
        Carbon $dateBegin,
        Carbon $dateEnd = null,
        string $rruleString = null,
        array $persons = [],
        array $teamMembers = [],
        AbsenceType $type = AbsenceType::SICK
    ): array {
        return $this->createAbsenceData(
             dateBegin: $dateBegin,
             dateEnd: $dateEnd,
             rruleString: $rruleString,
             persons: $persons,
             teamMembers: $teamMembers,
             type: $type
        );
    }

    protected function createAbsenceUpdateData(
        Absence $absence,
        Carbon $dateBegin = null,
        Carbon $dateEnd = null,
        string $rruleString = null,
        array $teamMembers = null,
        array $persons = null
    ): array {
        return $this->createAbsenceData(
            dateBegin: $dateBegin ?? $absence->date_begin,
            dateEnd: $dateEnd ?? $absence->date_end,
            rruleString: $rruleString ?? $absence->rrule_string,
            persons: $persons,
            teamMembers: $teamMembers,
            type: $absence->absence_type,
            id: $absence->id
        );
    }


    protected function createTeamEventTaskConfigData(Team $team, string $title = 'My Task'): array {
        $data = [
            'type' => 'teamEventTaskConfigs',
            'attributes' => [
                'title' => $title,
            ],
            'relationships' => [
                'team' => [
                    'data' => [
                        'type' => 'teams',
                        'id' => $team->id
                    ]
                ],
            ]
        ];
//        if(isset($id)) {
//            $data['id'] = $id;
//        }
        return $data;
    }

    protected function createTeamEventTaskData(
        TeamEvent $teamEvent,
        TeamEventTaskConfig $config,
        TeamMember $teamMember = null,
        TeamEventTask $task = null,
    ): array {
        $data = [
            'type' => 'teamEventTasks',
            'relationships' => [
                'teamEvent' => [
                    'data' => [
                        'type' => 'teamEvents',
                        'id' => $teamEvent->id
                    ]
                ],
                'config' => [
                    'data' => [
                        'type' => 'teamEventTaskConfigs',
                        'id' => $config->id
                    ]
                ],
            ]
        ];
        if(isset($teamMember)) {
            $data['relationships']['teamMember'] = [
                'data' => [
                    'type' => 'teamMembers',
                    'id' => $teamMember->id
                ]
            ];
        } else {
            $data['attributes']['detachRelationsByName'] = ['teamMember'];
        }

        if(isset($task)) {
            $data['id'] = $task->id;
        }
        return $data;
    }


    protected function createTeamStatsRangeData(
        Team   $team,
        Carbon $dateStart,
        Carbon $dateEnd,
        string $name = 'My Stats Range'
    ): array {
        $data = [
            'type' => 'teamStatsRanges',
            'attributes' => [
                'name' => $name,
                'startDate' => $dateStart->format('Y-m-d'),
                'endDate' => $dateEnd->format('Y-m-d'),
            ],
            'relationships' => [
                'team' => [
                    'data' => [
                        'type' => 'teams',
                        'id' => $team->id
                    ]
                ],
            ]
        ];
        //        if(isset($id)) {
        //            $data['id'] = $id;
        //        }
        return $data;
    }

    /**
     * @param string|null $eventId
     * @param User|null $actingAs
     * @param array $attributes
     *
     * @return TestResponse
     */
    protected function jsonApiEventUpdate(string|null $eventId, User $actingAs = null, array $attributes): TestResponse {
        if ($eventId === null) {
            $this->fail('Event or team member not found');
        }
        
        $base = $this;
        if ($actingAs !== null) {
            $base = $this->actingAs($actingAs);
        }

        return $base->jsonApi('teamEvents')
            ->withData(new ResourceObject(
                type: 'teamEvents',
                id: $eventId,
                attributes: $attributes,
            ))
            ->patch(route('api.v1.teamEvents.update', $eventId));
    }

    protected function jsonApiVote(string|null $eventId, string|null $teamMemberId, User $actingAs = null, string $voteType = 'yes'): TestResponse {
        if ($eventId === null || $teamMemberId === null) {
            $this->fail('Event or team member not found');
        }
        
        $base = $this;
        if ($actingAs !== null) {
            $base = $this->actingAs($actingAs);
        }

        return $base->jsonApi('teamEventVotes')
            ->withData(new ResourceObject(
                type: 'teamEventVotes',
                id: null,
                attributes: [
                    'vote'            => $voteType,
                    'hasSavedComment' => false,
                ],
                relationships: (new Relationships(
                    new Relation(
                        resourceType: 'teamEvent',
                        resourceId: $eventId,
                    ),
                    new Relation(
                        resourceType: 'teamMember',
                        resourceId: $teamMemberId,
                    ),
                ))->toArray(),
            ))
            ->post(route('api.v1.teamEventVotes.store'));
    }

    protected function createTeamLedgerTransactionData(Team $team, Money $amount, TeamMember $member = null): array {
        return [
            'type' => 'teamLedgerTransactions',
            'attributes' => [
                'title' => 'test',
                'amount' => $amount,
            ],
            'relationships' => [
                'ledger' => [
                    'data' => [
                        'type' => 'teamLedgers',
                        'id' => $team->ledger?->id
                    ]
                ],
                'teamMember' => isset($member) ? [
                    'data' => [
                        'type' => 'teamMembers',
                        'id'   => $member->id,
                    ],
                ] : null,
            ],
        ];
    }
}
