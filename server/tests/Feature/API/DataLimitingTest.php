<?php

namespace Tests\Feature\API;

use App\Models\Absence;
use App\Models\Team;
use App\Types\AbsenceType;
use App\Types\TeamEventVoteType;
use App\Types\TeamPermissionType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Symfony\Component\HttpFoundation\Response;

class DataLimitingTest extends JsonApiTestCase {
    use RefreshDatabase;

    public function testTeamsAreVisibleOnlyWhenUserIsMember() {
        $user = $this->createUser();

        $team1Visible = Team::create(['name' => 'Test Team A',]);
        $teamManagerA = $team1Visible->members()->create(['name' => 'Team Manager A']);
        $user->person->teamMembers()->save($teamManagerA);

        $team2Visible = Team::create(['name' => 'Test Team B',]);
        $teamManagerB = $team2Visible->members()->create(['name' => 'Team Manager B']);
        $user->person->teamMembers()->save($teamManagerB);

        $teamNotVisible = Team::create(['name' => 'Test Team C',]);

        // get all teams
        $responseAll = $this
            ->actingAs($user)
            ->jsonApi()
            ->expects('teams')
            ->get(route('api.v1.teams.index'));

        $responseAll
            ->assertOk()
            ->assertFetchedMany([$team1Visible, $team2Visible]);

        // get visible team
        $responseVisible = $this
            ->actingAs($user)
            ->jsonApi()
            ->expects('teams')
            ->get(route('api.v1.teams.show', [
                'team' => $team1Visible->id
            ]));

        $responseVisible
            ->assertFetchedOne($team1Visible);

        // get not visible team
        $responseNotVisible = $this
            ->actingAs($user)
            ->get(route('api.v1.teams.show', [
                'team' => $teamNotVisible->id
            ]));

        $responseNotVisible
            ->assertStatus(Response::HTTP_NOT_FOUND);
    }

    public function testTeamEventDetailsManagerOnlyVisibleForManager() {
        $team = Team::create(['name' => 'Test Team 1',]);
        $detailsManagerText = 'details - manager';

        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_CREATE]);
        $member = $this->createMemberWithoutPermissions($team);

        $event = $this->createTeamEvent($team);
        $event->details_manager = $detailsManagerText;
        $event->save();

        $this->actingAs($manager->person->user)
             ->jsonApi('teamEvents')
             ->get(route('api.v1.teamEvents.show', [
                 'teamEvent' => $event->id
             ]))
             ->assertFetchedOne($event)
             ->assertJsonPath('data.attributes.detailsManager', $detailsManagerText)
        ;

        $this->actingAs($member->person->user)
             ->jsonApi('teamEvents')
             ->get(route('api.v1.teamEvents.show', [
                 'teamEvent' => $event->id
             ]))
             ->assertFetchedOne($event)
             ->assertJsonMissingPath('data.attributes.detailsManager')
        ;
    }

    public function testVoteCommentsAreOnlyVisibleForSelfOrManager(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $voteMember = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_VOTE_SELF], 'voteMember');

        $event = $this->createTeamEvent($team);
        $vote = $this->createVote($event, $voteMember, TeamEventVoteType::YES, $voteMember->person);
        $text = 'Test Kommentar';
        $vote->comment()->create([
           'text' => $text,
           'author_id' => $voteMember->person->id
        ]);
        $comment = $vote->comment;

        $this->actingAs($voteMember->person->user)
             ->jsonApi('teamEventVotes')
             ->includePaths('comment')
             ->get(route('api.v1.teamEventVotes.show', [
                 'teamEventVote' => $vote->id
             ]))
             ->assertIsIncluded('comments', $comment)
        ;

        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_VOTE_OTHER]);
        $this->actingAs($manager->person->user)
            ->jsonApi('teamEventVotes')
            ->includePaths('comment')
            ->get(route('api.v1.teamEventVotes.show', [
                'teamEventVote' => $vote->id
            ]))
            ->assertIsIncluded('comments', $comment)
        ;

        $otherMember = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_VOTE_SELF], 'otherMember');
        $this->actingAs($otherMember->person->user)
            ->jsonApi('teamEventVotes')
            ->includePaths('comment')
            ->get(route('api.v1.teamEventVotes.show', [
                'teamEventVote' => $vote->id
            ]))
            ->assertDoesntHaveIncluded()
        ;
    }

    public function testTeamIsVisibleForNonMembersOnInvite(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $invite = $team->invite()->create();

        $user = $this->createUser();

        $this->actingAs($user)
             ->jsonApi('invites')
             ->includePaths('invitable')
             ->filter(['token' => $invite->token])
             ->get(route('api.v1.invites.index'))
             ->assertIsIncluded('teams', $team)
            ;
    }

    public function testTeamMemberIsVisibleForNonMembersOnInvite(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $team->createMember();
        $invite = $member->invite()->create();

        $user = $this->createUser();

        $this->actingAs($user)
             ->jsonApi('invites')
             ->includePaths('invitable')
             ->filter(['token' => $invite->token])
             ->get(route('api.v1.invites.index'))
             ->assertIsIncluded('teamMembers', $member)
            ;
    }

    public function testNoMembersAreVisibleWithoutTokenOnInvite(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $team->createMember();
        $invite = $member->invite()->create();

        $user = $this->createUser();

        $this->actingAs($user)
             ->jsonApi('invites')
             ->includePaths('invitable')
             ->get(route('api.v1.invites.index'))
             ->assertBadRequest()
            ;
    }

    public function testAbsencesAreOnlyVisibleToAuthors(): void {
        $author = $this->createUser('author');
        $other = $this->createUser('other');

        $absenceAuthor1 = Absence::create([
            'date_begin' => Carbon::create(2023, 6, 12),
            'absence_type' => AbsenceType::VACATION,
            'author_id' => $author->person->id,
        ]);

        $absenceOther = Absence::create([
            'date_begin' => Carbon::create(2023, 6, 12),
            'absence_type' => AbsenceType::VACATION,
            'author_id' => $other->person->id,
        ]);

        $absenceAuthor2 = Absence::create([
            'date_begin' => Carbon::create(2023, 6, 12),
            'absence_type' => AbsenceType::VACATION,
            'author_id' => $author->person->id,
        ]);

        $this->actingAs($author)
             ->jsonApi('absences')
             ->get(route('api.v1.absences.index'))
             ->assertJsonCount(2, 'data')
             ->assertFetchedMany(new Collection([
                 $absenceAuthor1,
                 $absenceAuthor2
             ]))
        ;
    }
}
