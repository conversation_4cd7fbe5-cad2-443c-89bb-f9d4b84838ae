<?php

namespace Tests\Feature\API;

use App\Models\Team;
use App\Models\TeamEvent;
use App\Types\EventResponseType;
use App\Types\TeamEventVoteType;
use App\Types\TeamPermissionType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;

class VotingTest extends JsonApiTestCase {
    use RefreshDatabase;

    protected function setUp(): void {
        parent::setUp();
        Notification::fake();
    }

    public function testVoterPersonIsSetOnVote(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_VOTE_SELF]);
        $event = $this->createTeamEvent($team);

        $data = $this->createNewVoteData($event, $member);

        $this->actingAs($member->person->user)
             ->jsonApi('teamEventVotes')
             ->withData($data)
             ->post(route('api.v1.teamEventVotes.store'))
             ->assertCreated()
        ;
        self::assertSame($member->person->id, $event->votes()->first()->voter_person_id);
    }

    public function testVoterPersonIsNotSetOnAutoYesVotes(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_CREATE]);
        $data = $this->createNewEventData($team, responseType: EventResponseType::AUTO_YES);

        $eventId = $this->actingAs($manager->person->user)
             ->jsonApi('teamEvents')
             ->withData($data)
             ->post(route('api.v1.teamEvents.store'))
             ->id();

        $event = TeamEvent::find($eventId);

        self::assertCount(1, $event->votes);
        self::assertNull($event->votes()->first()->voterPerson);
    }

    public function testVoterPersonIsChangedOnVoteUpdate(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_VOTE_OTHER]);
        $member = $this->createMemberAndSetRoleWithPermissions(email: 'member', team: $team, permissionNames: [TeamPermissionType::TEAM_EVENT_VOTE_SELF]);
        $event = $this->createTeamEvent($team);

        $vote = $this->createVote($event, $member, TeamEventVoteType::YES, $manager->person);
        self::assertSame($manager->person->id, $vote->voter_person_id);

        $data = $this->createUpdateVoteData($vote, TeamEventVoteType::NO);

        $this->actingAs($member->person->user)
             ->jsonApi('teamEventVotes')
             ->withData($data)
             ->patch(route('api.v1.teamEventVotes.update', [
                 'teamEventVote' => $vote->id
             ]))
             ->assertFetchedOne($vote);

        $vote->refresh();
        self::assertSame($member->person->id, $vote->voter_person_id);
    }

    public function testVotingCreate() {
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $teamMember = $team->members()->create(['name' => 'Team Member']);
        $user->person->teamMembers()->save($teamMember);

        $teamEvent = $this->createTeamEvent($team);
        $data = $this->createNewVoteData($teamEvent, $teamMember);

        $this->actingAs($user)
            ->jsonApi('teamEventVotes')
            ->withData($data)
            ->post(route('api.v1.teamEventVotes.store'))
            ->assertCreated()
        ;

        $response = $this->actingAs($user)
            ->jsonApi('teamEvents')
            ->includePaths('votes')
            ->get(route('api.v1.teamEvents.show', [
                'teamEvent' => $teamEvent->id
            ]))
        ;

        $response
            ->assertFetchedOne($teamEvent)
            ->assertIsIncluded('teamEventVotes', $teamEvent->votes()->first());
    }

    public function testVotingUpdate(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $voteMember = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_VOTE_SELF], 'voteMember');
        $event = $this->createTeamEvent($team);

        $voteInitial = TeamEventVoteType::YES;
        $voteUpdated = TeamEventVoteType::NO;

        $vote = $this->createVote($event, $voteMember, $voteInitial, $voteMember->person);

        $data = $this->createUpdateVoteData($vote, $voteUpdated);

        $this->actingAs($voteMember->person->user)
             ->jsonApi('teamEventVotes')
             ->withData($data)
             ->patch(route('api.v1.teamEventVotes.update', [
                 'teamEventVote' => $vote->id
             ]))
             ->assertFetchedOne($vote);
        $vote->refresh();
        self::assertSame($voteUpdated, $vote->vote);
    }

    public function testCreateSameVoteOverrideExistingVote() {
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $teamMember = $team->members()->create(['name' => 'Team Member']);
        $user->person->teamMembers()->save($teamMember);

        $teamEvent = $this->createTeamEvent($team);
        $data = $this->createNewVoteData($teamEvent, $teamMember);


        $this->actingAs($user)
            ->jsonApi('teamEventVotes')
            ->withData($data)
            ->post(route('api.v1.teamEventVotes.store'))
            ->assertCreated()
        ;

        $vote = $teamEvent->votes()->first();

        $this->actingAs($user)
            ->jsonApi('teamEventVotes')
            ->withData($data)
            ->post(route('api.v1.teamEventVotes.store'))
            ->assertCreated()
        ;

        self::assertCount(1, $teamEvent->votes()->get());

        self::assertNotSame($vote->id, $teamEvent->votes()->first()->id);
    }

    public function testCreateDifferentVoteOverrideExistingVote() {
        $user = $this->createUser();
        $team = Team::create(['name' => 'Test Team 1',]);
        $teamMember = $team->members()->create(['name' => 'Team Member']);
        $user->person->teamMembers()->save($teamMember);

        $teamEvent = $this->createTeamEvent($team);


        $this->actingAs($user)
            ->jsonApi('teamEventVotes')
            ->withData($this->createNewVoteData($teamEvent, $teamMember))
            ->post(route('api.v1.teamEventVotes.store'))
            ->assertCreated()
        ;

        $vote = $teamEvent->votes()->first();

        $this->actingAs($user)
            ->jsonApi('teamEventVotes')
            ->withData($this->createNewVoteData($teamEvent, $teamMember, TeamEventVoteType::NO))
            ->post(route('api.v1.teamEventVotes.store'))
            ->assertCreated()
        ;

        self::assertCount(1, $teamEvent->votes()->get());

        self::assertSame(TeamEventVoteType::NO, $teamEvent->votes()->first()->vote);
        self::assertNotSame($vote->id, $teamEvent->votes()->first()->id);
    }

    public function testIgnoreVotesFromOtherTeamsAfterChangingTeam() {
        $user = $this->createUser();
        $teamA = Team::create(['name' => 'Test Team A',]);
        $teamB = Team::create(['name' => 'Test Team B',]);
        $teamMemberA1 = $teamA->members()->create(['name' => 'Team Member A1']);
        $teamMemberB1 = $teamB->members()->create(['name' => 'Team Member B1']);
        $user->person->teamMembers()->save($teamMemberA1);
        $user->person->teamMembers()->save($teamMemberB1);

        $teamEvent = $this->createTeamEvent($teamA);
        $data = $this->createNewVoteData($teamEvent, $teamMemberA1);

        $this->actingAs($user)
            ->jsonApi('teamEventVotes')
            ->withData($data)
            ->post(route('api.v1.teamEventVotes.store'))
            ->assertCreated()
        ;

        $teamEvent->team()->associate($teamB);
        $teamEvent->save();

        $response = $this->actingAs($user)
            ->jsonApi('teamEvents')
            ->includePaths('votes')
            ->get(route('api.v1.teamEvents.show', [
                'teamEvent' => $teamEvent->id
            ]))
        ;

        self::assertEmpty($teamEvent->votes()->get());

//        dd($response);
        $response
            ->assertFetchedOne($teamEvent)
            ->assertDoesntHaveIncluded();
    }

    public function testVotesFromNonTeamMembersAreNotAllowed() {
        $user = $this->createUser();
        $teamA = Team::create(['name' => 'Test Team A',]);
        $teamB = Team::create(['name' => 'Test Team B',]);
        $teamMemberA1 = $teamA->members()->create(['name' => 'Team Member A1']);
        $teamMemberB1 = $teamB->members()->create(['name' => 'Team Member B1']);
        $user->person->teamMembers()->save($teamMemberA1);
        $user->person->teamMembers()->save($teamMemberB1);

        $teamEventB = $this->createTeamEvent($teamB);
        $data = $this->createNewVoteData($teamEventB, $teamMemberA1);

        $this->actingAs($user)
            ->jsonApi('teamEventVotes')
            ->withData($data)
            ->post(route('api.v1.teamEventVotes.store'))
            ->assertForbidden()
        ;

        self::assertEmpty($teamEventB->votes()->get());
    }


    // won't do this, maybe later when events can be related to multiple teams
//    public function testRestoreVotesFromOriginalTeamAfterResettingTeam() {
//        $user = $this->createUser();
//        $teamA = Team::create(['name' => 'Test Team A',]);
//        $teamB = Team::create(['name' => 'Test Team B',]);
//        $teamMemberA1 = $teamA->members()->create(['name' => 'Team Member A1']);
//        $teamMemberA2 = $teamA->members()->create(['name' => 'Team Member A2']);
//        $teamMemberB1 = $teamB->members()->create(['name' => 'Team Member B1']);
//        $user->person->teamMembers()->save($teamMemberA1);
//        $user->person->teamMembers()->save($teamMemberA2);
//        $user->person->teamMembers()->save($teamMemberB1);
//
//        $teamEvent = $this->createTeamEvent($teamA);
//
//        $this->actingAs($user)
//            ->jsonApi('teamEventVotes')
//            ->withData($this->createVoteData($teamEvent, $teamMemberA1))
//            ->post(route('api.v1.teamEventVotes.store'))
//            ->assertCreated()
//        ;
//        $this->actingAs($user)
//            ->jsonApi('teamEventVotes')
//            ->withData($this->createVoteData($teamEvent, $teamMemberA2))
//            ->post(route('api.v1.teamEventVotes.store'))
//            ->assertCreated()
//        ;
//
//        self::assertCount(2, $teamEvent->votes()->get());
//        self::assertCount(1,  $teamEvent->votes()->where('team_member_id', $teamMemberA1->id)->get());
//
//        $teamEvent->team()->associate($teamB);
//        $teamEvent->refresh();
//
//        $this->actingAs($user)
//            ->jsonApi('teamEventVotes')
//            ->withData($this->createVoteData($teamEvent, $teamMemberB1))
//            ->post(route('api.v1.teamEventVotes.store'))
//            ->assertCreated()
//        ;
//
//        self::assertCount(1, $teamEvent->votes()->get());
//        self::assertCount(0,  $teamEvent->votes()->where('team_member_id', $teamMemberA1->id)->get());
//        self::assertCount(1,  $teamEvent->votes()->where('team_member_id', $teamMemberB1->id)->get());
//
//        $teamEvent->team()->associate($teamA);
//        $teamEvent->refresh();
//
//        self::assertCount(2, $teamEvent->votes()->get());
//        self::assertCount(1,  $teamEvent->votes()->where('team_member_id', $teamMemberA1->id)->get());
//        self::assertCount(0,  $teamEvent->votes()->where('team_member_id', $teamMemberB1->id)->get());
//    }
}
