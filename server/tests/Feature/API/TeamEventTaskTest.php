<?php

namespace API;

use App\Models\Team;
use App\Types\TeamPermissionType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\Feature\API\JsonApiTestCase;

class TeamEventTaskTest extends JsonApiTestCase {
    use RefreshDatabase;

    public function test_new_task_config_visible_on_team(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_TASK_CREATE]);

        $configData = $this->createTeamEventTaskConfigData($team);

        // create task config
        $this->actingAs($manager->person->user)
             ->json<PERSON><PERSON>('teamEventTaskConfigs')
             ->withData($configData)
             ->post(route('api.v1.teamEventTaskConfigs.store'))
             ->assertCreated()
        ;

        $team->refresh();
        self::assertCount(1, $team->eventTaskConfigs);
    }

    public function test_new_task_config_without_permissions_fails(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, []);

        $configData = $this->createTeamEventTaskConfigData($team);

        // create task config
        $this->actingAs($manager->person->user)
             ->jsonApi('teamEventTaskConfigs')
             ->withData($configData)
             ->post(route('api.v1.teamEventTaskConfigs.store'))
             ->assertForbidden()
        ;

        $team->refresh();
        self::assertCount(0, $team->eventTaskConfigs);
    }


    public function test_new_task_config_without_being_team_member_fails(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $team2 = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_TASK_CREATE]);

        $configData = $this->createTeamEventTaskConfigData($team2);

        // create task config
        $this->actingAs($manager->person->user)
             ->jsonApi('teamEventTaskConfigs')
             ->withData($configData)
             ->post(route('api.v1.teamEventTaskConfigs.store'))
             ->assertNotFound()
        ;

        $team2->refresh();
        self::assertCount(0, $team2->eventTaskConfigs);
    }

    public function test_new_task_without_member_visible_on_team_event(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_TASK_CREATE]);
        $event = $this->createTeamEvent($team);

        $taskConfig = $team->eventTaskConfigs()->create([
           'title' => 'My task'
        ]);

        $taskData = $this->createTeamEventTaskData($event, $taskConfig);

        // create task
        $this->actingAs($manager->person->user)
             ->jsonApi('teamEventTasks')
             ->withData($taskData)
             ->post(route('api.v1.teamEventTasks.store'))
             ->assertCreated()
        ;

        $event->refresh();
        self::assertCount(1, $event->tasks);
    }

    public function test_new_task_with_self_member_visible_on_team_event(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [
            TeamPermissionType::TEAM_EVENT_TASK_CREATE,
        ]);
        $event = $this->createTeamEvent($team);

        $taskConfig = $team->eventTaskConfigs()->create([
           'title' => 'My task'
        ]);

        $taskData = $this->createTeamEventTaskData($event, $taskConfig, $manager);

        // create task
        $this->actingAs($manager->person->user)
             ->jsonApi('teamEventTasks')
             ->withData($taskData)
             ->post(route('api.v1.teamEventTasks.store'))
             ->assertCreated()
        ;

        $event->refresh();
        self::assertCount(1, $event->tasks);
        self::assertEquals($manager->id, $event->tasks()->first()->team_member_id);
    }

    public function test_new_task_with_member_visible_on_team_event(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [
            TeamPermissionType::TEAM_EVENT_TASK_CREATE,
        ]);
        $member = $this->createMemberWithoutPermissions($team);
        $event = $this->createTeamEvent($team);

        $taskConfig = $team->eventTaskConfigs()->create([
           'title' => 'My task'
        ]);

        $taskData = $this->createTeamEventTaskData($event, $taskConfig, $member);

        // create task
        $this->actingAs($manager->person->user)
             ->jsonApi('teamEventTasks')
             ->withData($taskData)
             ->post(route('api.v1.teamEventTasks.store'))
             ->assertCreated()
        ;

        $event->refresh();
        self::assertCount(1, $event->tasks);
        self::assertEquals($member->id, $event->tasks()->first()->team_member_id);
    }

    public function test_new_task_without_permissions_fails(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_TASK_ASSIGN_OTHER]);
        $member = $this->createMemberWithoutPermissions($team);
        $event = $this->createTeamEvent($team);

        $taskConfig = $team->eventTaskConfigs()->create([
           'title' => 'My task'
        ]);

        $taskData = $this->createTeamEventTaskData($event, $taskConfig, $member);

        // create task
        $this->actingAs($manager->person->user)
             ->jsonApi('teamEventTasks')
             ->withData($taskData)
             ->post(route('api.v1.teamEventTasks.store'))
             ->assertForbidden()
        ;

        $event->refresh();
        self::assertCount(0, $event->tasks);
    }

    public function test_update_task_with_self_member(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [
            TeamPermissionType::TEAM_EVENT_TASK_ASSIGN_SELF,
        ]);
        $event = $this->createTeamEvent($team);

        $taskConfig = $team->eventTaskConfigs()->create([
            'title' => 'My task'
        ]);

        $task = $event->tasks()->create([
            'config_id' => $taskConfig->id,
        ]);

        self::assertNull($task->team_member_id);

        $taskData = $this->createTeamEventTaskData($event, $taskConfig, $manager, $task);

        // update task
        $this->actingAs($manager->person->user)
             ->jsonApi('teamEventTasks')
             ->withData($taskData)
             ->patch(route('api.v1.teamEventTasks.update', [
                 'teamEventTask' => $task->id
             ]))
             ->assertFetchedOne($task)
        ;

        $task->refresh();
        self::assertEquals($manager->id, $task->refresh()->team_member_id);
    }

    public function test_update_task_with_other_member(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [
            TeamPermissionType::TEAM_EVENT_TASK_ASSIGN_OTHER,
        ]);
        $member = $this->createMemberWithoutPermissions($team);
        $event = $this->createTeamEvent($team);

        $taskConfig = $team->eventTaskConfigs()->create([
            'title' => 'My task'
        ]);

        $task = $event->tasks()->create([
            'config_id' => $taskConfig->id,
        ]);

        self::assertNull($task->team_member_id);

        $taskData = $this->createTeamEventTaskData($event, $taskConfig, $member, $task);

        // update task
        $this->actingAs($manager->person->user)
             ->jsonApi('teamEventTasks')
             ->withData($taskData)
             ->patch(route('api.v1.teamEventTasks.update', [
                 'teamEventTask' => $task->id
             ]))
             ->assertFetchedOne($task)
        ;

        $task->refresh();
        self::assertEquals($member->id, $task->refresh()->team_member_id);
    }

    public function test_update_task_with_self_member_without_permissions_fails(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithoutPermissions($team);
        $event = $this->createTeamEvent($team);

        $taskConfig = $team->eventTaskConfigs()->create([
            'title' => 'My task'
        ]);

        $task = $event->tasks()->create([
            'config_id' => $taskConfig->id,
        ]);

        self::assertNull($task->team_member_id);

        $taskData = $this->createTeamEventTaskData($event, $taskConfig, $manager, $task);

        // update task
        $this->actingAs($manager->person->user)
             ->jsonApi('teamEventTasks')
             ->withData($taskData)
             ->patch(route('api.v1.teamEventTasks.update', [
                 'teamEventTask' => $task->id
             ]))
             ->assertForbidden()
        ;

        $task->refresh();
        self::assertNull($task->team_member_id);
    }

    public function test_update_task_with_other_member_without_permissions_fails(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithoutPermissions($team, 'manager');
        $member = $this->createMemberWithoutPermissions($team);
        $event = $this->createTeamEvent($team);

        $taskConfig = $team->eventTaskConfigs()->create([
            'title' => 'My task'
        ]);

        $task = $event->tasks()->create([
            'config_id' => $taskConfig->id,
        ]);

        self::assertNull($task->team_member_id);

        $taskData = $this->createTeamEventTaskData($event, $taskConfig, $member, $task);

        // update task
        $this->actingAs($manager->person->user)
             ->jsonApi('teamEventTasks')
             ->withData($taskData)
             ->patch(route('api.v1.teamEventTasks.update', [
                 'teamEventTask' => $task->id
             ]))
             ->assertForbidden()
        ;

        $task->refresh();
        self::assertNull($task->team_member_id);
    }

    public function test_update_task_without_member(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_TASK_CREATE]);
        $event = $this->createTeamEvent($team);

        $taskConfig = $team->eventTaskConfigs()->create([
            'title' => 'My task'
        ]);

        $task = $event->tasks()->create([
            'config_id' => $taskConfig->id,
            'team_member_id' => $manager->id
        ]);

        self::assertEquals($manager->id, $task->refresh()->team_member_id);

        $taskData = $this->createTeamEventTaskData($event, $taskConfig, null, $task);

        // update task
        $this->actingAs($manager->person->user)
             ->jsonApi('teamEventTasks')
             ->withData($taskData)
             ->patch(route('api.v1.teamEventTasks.update', [
                 'teamEventTask' => $task->id
             ]))
             ->assertFetchedOne($task)
        ;

        $task->refresh();
        self::assertNull($task->team_member_id);
    }

    public function test_delete_task(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_TASK_DELETE]);
        $event = $this->createTeamEvent($team);

        $taskConfig = $team->eventTaskConfigs()->create([
           'title' => 'My task'
        ]);
        $task = $event->tasks()->create([
            'config_id' => $taskConfig->id
        ]);

        self::assertCount(1, $event->tasks);

        // delete task
        $this->actingAs($manager->person->user)
             ->jsonApi('teamEventTasks')
             ->delete(route('api.v1.teamEventTasks.destroy', [
                 'teamEventTask' => $task->id
             ]))
             ->assertNoContent()
        ;

        $event->refresh();
        self::assertCount(0, $event->tasks);
    }
}
