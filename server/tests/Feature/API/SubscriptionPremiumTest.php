<?php

namespace API;

use App\Models\Team;
use App\Types\TeamPermissionType;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\Feature\API\JsonApiTestCase;

class SubscriptionPremiumTest extends JsonApiTestCase {
    use RefreshDatabase;

    protected function setUp(): void {
        parent::setUp();
        // Reset any test time that might have been set
        Carbon::setTestNow();
    }
    
    private function createPremiumTeam(): Team {
        return Team::create(['name' => 'Premium Team']);
    }
    
    private function createNonPremiumTeam(): Team {
        $team = Team::create(['name' => 'Non Premium Team']);
        $team->trial_ends_at = Carbon::now()->subDay();
        $team->save();
        return $team;
    }
    
    public function test_premium_team_has_premium_features(): void {
        $team = $this->createPremiumTeam();
        self::assertTrue($team->isPremium());
    }
    
    public function test_non_premium_team_does_not_have_premium_features(): void {
        $team = $this->createNonPremiumTeam();
        self::assertFalse($team->isPremium());
    }

    public function test_event_creation_disabled_for_non_premium_teams(): void {
        $team = $this->createNonPremiumTeam();
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_CREATE]);
        $data = $this->createNewEventData($team);
        
        $this->actingAs($manager->person->user)
            ->jsonApi('teamEvents')
            ->withData($data)
            ->post(route('api.v1.teamEvents.store'))
            ->assertForbidden();
    }
    
    public function test_event_update_disabled_for_non_premium_teams(): void {
        $team = $this->createNonPremiumTeam();
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_CREATE]);
        $event = $this->createTeamEvent($team);
        $oldTitle = $event->title;
        $data = $this->createEventUpdateData($event, 'new title');
        
        $this->actingAs($manager->person->user)
            ->jsonApi('teamEvents')
            ->withData($data)
            ->patch(route('api.v1.teamEvents.update', [
                'teamEvent' => $event->id
            ]))
            ->assertForbidden();
        
        $event->refresh();
        self::assertSame($oldTitle, $event->title);
    }
    
    public function test_event_delete_disabled_for_non_premium_teams(): void {
        $team = $this->createNonPremiumTeam();
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_DELETE]);
        $event = $this->createTeamEvent($team);
        
        $this->actingAs($manager->person->user)
            ->jsonApi('teamEvents')
            ->delete(route('api.v1.teamEvents.destroy', [
                'teamEvent' => $event->id
            ]))
            ->assertForbidden();
        
        self::assertTrue($event->exists);
    }

    public function test_event_task_config_creation_disabled_for_non_premium_teams(): void {
        $team = $this->createNonPremiumTeam();
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_TASK_CREATE]);
        $data = $this->createTeamEventTaskConfigData($team);
        
        $this->actingAs($manager->person->user)
            ->jsonApi('teamEventTaskConfigs')
            ->withData($data)
            ->post(route('api.v1.teamEventTaskConfigs.store'))
            ->assertForbidden();
    }

    public function test_event_task_creation_disabled_for_non_premium_teams(): void {
        $team = $this->createNonPremiumTeam();
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_TASK_CREATE]);
        $event = $this->createTeamEvent($team);
        $taskConfig = $team->eventTaskConfigs()->create([
            'title' => 'My task'
        ]);
        $data = $this->createTeamEventTaskData($event, $taskConfig);
        
        $this->actingAs($manager->person->user)
            ->jsonApi('teamEventTasks')
            ->withData($data)
            ->post(route('api.v1.teamEventTasks.store'))
            ->assertForbidden();
    }

    public function test_event_task_update_disabled_for_non_premium_teams(): void {
        $team = $this->createNonPremiumTeam();
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_TASK_CREATE]);
        $event = $this->createTeamEvent($team);
        $taskConfig = $team->eventTaskConfigs()->create([
            'title' => 'My task'
        ]);
        $task = $event->tasks()->create([
            'config_id' => $taskConfig->id
        ]);
        $data = $this->createTeamEventTaskData($event, $taskConfig, null, $task);
        
        $this->actingAs($manager->person->user)
            ->jsonApi('teamEventTasks')
            ->withData($data)
            ->patch(route('api.v1.teamEventTasks.update', [
                'teamEventTask' => $task->id
            ]))
            ->assertForbidden();
    }


    public function test_event_task_update_with_member_enabled_for_non_premium_teams(): void {
        $team = $this->createNonPremiumTeam();
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_TASK_ASSIGN_SELF]);
        $event = $this->createTeamEvent($team);
        $taskConfig = $team->eventTaskConfigs()->create([
            'title' => 'My task'
        ]);
        $task = $event->tasks()->create([
            'config_id' => $taskConfig->id
        ]);
        $data = $this->createTeamEventTaskData($event, $taskConfig, $manager, $task);
        
        $this->actingAs($manager->person->user)
            ->jsonApi('teamEventTasks')
            ->withData($data)
            ->patch(route('api.v1.teamEventTasks.update', [
                'teamEventTask' => $task->id
            ]))
            ->assertFetchedOne($task);
    }

    public function test_team_ledger_transaction_creation_disabled_for_non_premium_teams():void {
        $team = $this->createNonPremiumTeam();
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_LEDGER_MANAGE]);
        $amount = money(10);
        $data = $this->createTeamLedgerTransactionData($team, $amount, $manager);
        
        $this->actingAs($manager->person->user)
            ->jsonApi('teamLedgerTransactions')
            ->withData($data)
            ->post(route('api.v1.teamLedgerTransactions.store'))
            ->assertForbidden();
        
    }
}
