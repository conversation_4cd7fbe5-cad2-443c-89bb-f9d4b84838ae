<?php

namespace Tests\Feature\API;

use App\Models\Team;
use App\Types\TeamEventVoteType;
use App\Types\TeamPermissionType;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CommentsTest extends JsonApiTestCase {
    use RefreshDatabase;

    public function testVoteCommentCreate(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $member = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_CREATE]);
        $event = $this->createTeamEvent($team);
        $vote = $this->createVote($event, $member, TeamEventVoteType::YES, $member->person);

        $text = 'Test Kommentar';
        $data = $this->createNewCommentData($text, $vote, 'teamEventVotes');


        $this->actingAs($member->person->user)
             ->jsonApi('comments')
             ->withData($data)
             ->post(route('api.v1.comments.store'))
             ->assertCreated();

        $vote->refresh();
        $comment = $vote->comment;

        self::assertEquals($comment->text, $text);
        self::assertEquals($comment->author->id, $member->person->id);
    }

    public function testVoteCommentUpdate(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $voteMember = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_VOTE_SELF], 'voteMember');

        $event = $this->createTeamEvent($team);
        $vote = $this->createVote($event, $voteMember, TeamEventVoteType::YES, $voteMember->person);
        $textInitial = 'Test Kommentar';
        $textUpdated = 'Comment changed';
        $vote->comment()->create([
            'text' => $textInitial,
            'author_id' => $voteMember->person->id
        ]);
        $comment = $vote->comment;

        $data = $this->createCommentUpdateData($comment, $textUpdated);

        $this->actingAs($voteMember->person->user)
             ->jsonApi('comments')
             ->withData($data)
             ->patch(route('api.v1.comments.update', [
                 'comment' => $comment->id
             ]))
            ->assertFetchedOne($comment);

        $comment->refresh();
        self::assertSame($textUpdated, $comment->text);

        $comment->text = $textInitial;
        $comment->save();

        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_VOTE_OTHER]);
        $this->actingAs($manager->person->user)
            ->jsonApi('comments')
            ->withData($data)
            ->patch(route('api.v1.comments.update', [
                'comment' => $comment->id
            ]))
            ->assertFetchedOne($comment);

        $comment->refresh();
        self::assertSame($textUpdated, $comment->text);

        $comment->text = $textInitial;
        $comment->save();

        $otherMember = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_VOTE_SELF], 'otherMember');
        $this->actingAs($otherMember->person->user)
            ->jsonApi('comments')
            ->withData($data)
            ->patch(route('api.v1.comments.update', [
                'comment' => $comment->id
            ]))
            ->assertForbidden();

        $comment->refresh();
        self::assertSame($textInitial, $comment->text);
    }

}
