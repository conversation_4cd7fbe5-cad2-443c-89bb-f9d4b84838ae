<?php

namespace Tests\Feature\API;

use App\Models\Team;
use App\Models\TeamEventSeries;
use App\Types\EventSeriesType;
use App\Types\TeamPermissionType;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use RRule\RRule;
use Symfony\Component\HttpFoundation\Response;
use function PHPUnit\Framework\assertCount;

class TeamEventSeriesTest extends JsonApiTestCase {
    use RefreshDatabase;

    public function test_new_recurring_event_create_series(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_CREATE]);

        $begin = Carbon::create(2023, 6, 12, 10, 0, 0 );
        $rrule = new RRule([
            'DTSTART' => $begin,
            'FREQ' => RRule::WEEKLY,
            'BYDAY' => 'MO,TU',
            'UNTIL' => Carbon::create(2023, 6,25)
        ]);
        // DTSTART:20230612T100000Z\nRRULE:FREQ=WEEKLY;BYDAY=MO,TU;UNTIL=20230625T000000Z
        $rruleString = $rrule->rfcString();

        $expectedCreatedEvents = [
            ['attributes' => ['dateBegin' => '2023-06-12', 'seriesType' => EventSeriesType::RECURRING_CHILD->value]],
            ['attributes' => ['dateBegin' => '2023-06-13', 'seriesType' => EventSeriesType::RECURRING_CHILD->value]],
            ['attributes' => ['dateBegin' => '2023-06-19', 'seriesType' => EventSeriesType::RECURRING_CHILD->value]],
            ['attributes' => ['dateBegin' => '2023-06-20', 'seriesType' => EventSeriesType::RECURRING_CHILD->value]],
        ];

        $seriesData = $this->createEventSeriesData($rruleString);

        // create event series config
        $responseSeries = $this->actingAs($manager->person->user)
             ->jsonApi('teamEventSeries')
             ->withData($seriesData)
             ->post(route('api.v1.teamEventSeries.store'))
            ;
        $responseSeries->assertCreated();

        $seriesId = $responseSeries->json('data.id');
        $eventData = $this->createRecurringEventData($team, $begin, $seriesId);

        // create recurring parent event
        $this->actingAs($manager->person->user)
             ->jsonApi('teamEvents')
             ->withData($eventData)
             ->post(route('api.v1.teamEvents.store'))
             ->assertCreated();

        // check recurring children have been created
        $this->actingAs($manager->person->user)
             ->jsonApi('teamEvents')
             ->filter(['events' => 'true'])
             ->get(route('api.v1.teamEvents.index'))
             ->assertFetchedManyInOrder($expectedCreatedEvents)
            ;
    }

    public function test_creating_infinite_series_not_allowed(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_CREATE]);

        $seriesDataUNTIL = $this->createEventSeriesData('DTSTART:20230612T100000Z
        RRULE:FREQ=WEEKLY;BYDAY=MO,TU;UNTIL=20230625T000000Z');
        $seriesDataCOUNT = $this->createEventSeriesData('DTSTART:20230613T100000Z
        RRULE:FREQ=WEEKLY;BYDAY=MO,TU;COUNT=5');
        $seriesDataINFINITE = $this->createEventSeriesData('DTSTART:20230614T100000Z
        RRULE:FREQ=WEEKLY;BYDAY=MO,TU');

        $this->actingAs($manager->person->user)
             ->jsonApi('teamEventSeries')
             ->withData($seriesDataUNTIL)
             ->post(route('api.v1.teamEventSeries.store'))
             ->assertCreated();

        $this->actingAs($manager->person->user)
             ->jsonApi('teamEventSeries')
             ->withData($seriesDataCOUNT)
             ->post(route('api.v1.teamEventSeries.store'))
             ->assertCreated();

        $this->actingAs($manager->person->user)
             ->jsonApi('teamEventSeries')
             ->withData($seriesDataINFINITE)
             ->post(route('api.v1.teamEventSeries.store'))
             ->assertHasError(Response::HTTP_BAD_REQUEST);
    }

    public function test_delete_single_event_of_series(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_CREATE, TeamPermissionType::TEAM_EVENT_DELETE]);

        $begin = Carbon::create(2023, 6, 12, 10, 0, 0 );
        $rrule = new RRule([
            'DTSTART' => $begin,
            'FREQ' => RRule::WEEKLY,
            'BYDAY' => 'MO,TU',
            'UNTIL' => Carbon::create(2023, 6,25)
        ]);
        $rruleString = $rrule->rfcString();
        $eventSeries = TeamEventSeries::create([
           'rrule_string' => $rruleString
        ]);

        // create recurring parent event
        $eventData = $this->createRecurringEventData($team, $begin, $eventSeries->id);
        $this->actingAs($manager->person->user)
             ->jsonApi('teamEvents')
             ->withData($eventData)
             ->post(route('api.v1.teamEvents.store'));

        $eventSeries->refresh();
        assertCount(4, $eventSeries->events);

        // delete first event
        $this->actingAs($manager->person->user)
             ->jsonApi()
             ->delete(route('api.v1.teamEvents.destroy', [
                 'teamEvent' => $eventSeries->events->first()->id
             ]))
            ->assertNoContent()
        ;

        $eventSeries->refresh();
        assertCount(3, $eventSeries->events);
    }

    public function test_update_series_end_date_deletes_event_out_of_range(): void {
        $team = Team::create(['name' => 'Test Team 1',]);
        $manager = $this->createMemberWithPermissions($team, [TeamPermissionType::TEAM_EVENT_CREATE]);

        $begin = Carbon::create(2023, 6, 12, 10, 0, 0 );
        $rrule = new RRule([
            'DTSTART' => $begin->startOfDay(),
            'FREQ' => RRule::WEEKLY,
            'BYDAY' => 'MO,TU',
            'UNTIL' => Carbon::create(2023, 6,25)->startOfDay()
        ]);
        $rruleString = $rrule->rfcString();

        $rruleUpdated = new RRule([
            'DTSTART' => $begin->startOfDay(),
            'FREQ' => RRule::WEEKLY,
            'BYDAY' => 'MO,TU',
            'UNTIL' => Carbon::create(2023, 6,18)->startOfDay()
        ]);
        $rruleStringUpdated = $rruleUpdated->rfcString();
        $eventSeries = TeamEventSeries::create([
            'rrule_string' => $rruleString
        ]);

        // create recurring parent event
        $eventData = $this->createRecurringEventData($team, $begin, $eventSeries->id);
        $this->actingAs($manager->person->user)
             ->jsonApi('teamEvents')
             ->withData($eventData)
             ->post(route('api.v1.teamEvents.store'));

        $eventSeries->refresh();
        assertCount(4, $eventSeries->events);

        // updating end date of series
        $seriesData = $this->createEventSeriesData($rruleStringUpdated, $eventSeries->id);
        $this->actingAs($manager->person->user)
             ->jsonApi('teamEventSeries')
             ->withData($seriesData)
             ->patch(route('api.v1.teamEventSeries.update', [
                 'teamEventSeries' => $eventSeries->id
             ]))
        ;

        $eventSeries->refresh();
        assertCount(2, $eventSeries->events);
    }
}
