<?php

namespace Tests\Feature;

use App\Models\TeamLedger;
use App\Models\TeamLedgerClaim;
use App\Models\TeamLedgerDues;
use Illuminate\Support\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use RRule\RRule;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Tests\TestCase;
use function PHPUnit\Framework\assertContains;
use function PHPUnit\Framework\assertCount;
use function PHPUnit\Framework\assertEquals;
use function PHPUnit\Framework\assertFalse;
use function PHPUnit\Framework\assertTrue;

class TeamLedgerTest extends TestCase {
    use RefreshDatabase;

    public function test_team_has_a_ledger(): void {
        $team = $this->createTeam();
        self::assertInstanceOf(TeamLedger::class, $team->ledger);
    }

    public function test_transaction_sets_author() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $transaction = $team->ledger->transactions()->create([
            'title' => 'test',
            'amount' => money(100)
        ]);

        self::assertEquals($transaction->author, $user->person);
    }

    public function test_transactions_resolve_in_balance() : void {
        $team = $this->createTeam();
        $this->createUserActingAs();

        $amount1 = money(100);
        $amount2 = money(50);
        $amount3 = money(-10);
        $expected = money_sum($amount1, $amount2, $amount3);
        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amount1],
            ['title' => 'test', 'amount' => $amount2],
            ['title' => 'test', 'amount' => $amount3],
        ]);
        self::assertEquals($expected, $team->ledger->getBalance());
    }

    public function test_money_claims_resolve_in_claim_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amount1 = money(100);
        $amount2 = money(50);
        $amount3 = money(-10);
        $expected = money_sum($amount1, $amount2, $amount3);
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amount1],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amount2],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amount3],
        ]);
        self::assertEquals($expected, $team->ledger->getClaimBalance());
    }
    
    public function test_credit_reduces_claim_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountClaim = money(100);
        $amountCredit = money(30);
        $expected = $amountClaim->subtract($amountCredit);
        $team->ledger->claims()->create([
            'title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amountClaim,
        ]);
        
        $team->ledger->transactions()->create(['team_member_id' => $member->id, 'amount' => $amountCredit, 'title' => 'paid',]);
        self::assertEquals($expected, $team->ledger->getClaimBalance());
    }
    
    public function test_credit_reduces_future_claim_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountClaim = money(100);
        $amountCredit = money(30);
        $expected = $amountClaim->subtract($amountCredit);
        $team->ledger->claims()->create([
            'title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now()->addDay(), 'amount' => $amountClaim,
        ]);
        
        $team->ledger->transactions()->create(['team_member_id' => $member->id, 'amount' => $amountCredit, 'title' => 'paid',]);
        self::assertEquals($expected, $team->ledger->getFutureClaimBalance());
    }
    
    public function test_credit_that_reduces_claim_balance_does_not_reduce_future_claim_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountClaim = money(100);
        $amountCredit = money(30);
        $expectedClaimBalance = $amountClaim->subtract($amountCredit);
        $expectedFutureClaimBalance = $amountClaim;
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amountClaim,],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now()->addDay(), 'amount' => $amountClaim,],
        ]);
        
        $team->ledger->transactions()->create(['team_member_id' => $member->id, 'amount' => $amountCredit, 'title' => 'paid',]);
        self::assertEquals($expectedClaimBalance, $team->ledger->getClaimBalance());
        self::assertEquals($expectedFutureClaimBalance, $team->ledger->getFutureClaimBalance());
    }

    public function test_item_claims_do_not_resolve_in_claim_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amount1 = money(100);
        $amount2 = money(50);
        $expected = money_sum($amount1, $amount2);
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amount1],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amount2],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'item' => 'Kuchen'],
        ]);
        self::assertEquals($expected, $team->ledger->getClaimBalance());
    }


    public function test_exempt_money_claims_do_not_resolve_in_claim_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amount1 = money(100);
        $amount2 = money(50);
        $amountExempt = money(-10);
        $expected = money_sum($amount1, $amount2);
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amount1],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amount2],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amountExempt, 'exempt' => true],
        ]);
        self::assertEquals($expected, $team->ledger->getClaimBalance());
    }

    public function test_future_money_claims_do_not_resolve_in_claim_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amount1 = money(100);
        $amount2 = money(50);
        $amountFuture = money(-10);
        $expected = money_sum($amount1, $amount2);
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::today(), 'amount' => $amount1],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::today(), 'amount' => $amount2],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now()->addDays(), 'amount' => $amountFuture],
        ]);
        self::assertEquals($expected, $team->ledger->getClaimBalance());
    }

    public function test_future_money_claims_resolve_in_future_claim_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amount1 = money(100);
        $amount2 = money(50);
        $amount3 = money(-10);
        $expected = money_sum($amount1, $amount2, $amount3);
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now()->addDays(), 'amount' => $amount1],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now()->addDays(), 'amount' => $amount2],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now()->addDays(), 'amount' => $amount3],
        ]);
        self::assertEquals($expected, $team->ledger->getFutureClaimBalance());
    }

    public function test_due_money_claims_do_not_resolve_in_future_claim_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amount1 = money(100);
        $amount2 = money(50);
        $amountDue = money(-10);
        $expected = money_sum($amount1, $amount2);
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now()->addDays(), 'amount' => $amount1],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now()->addDays(), 'amount' => $amount2],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amountDue],
        ]);
        self::assertEquals($expected, $team->ledger->getFutureClaimBalance());
    }

    public function test_item_claims_do_resolve_in_item_list() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $item1 = 'Kuchen';
        $item2 = '1 Kiste Bier';
        $expected = [$item1, $item2];
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'item' => $item1],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'item' => $item2],
        ]);
        self::assertEquals(
            $expected,
            $team->ledger->dueItemClaims()->get()->map(fn(TeamLedgerClaim $itemClaim) => $itemClaim->item)->toArray()
        );
    }

    public function test_exempt_item_claims_do_not_resolve_in_item_list() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $item1 = 'Kuchen';
        $item2 = '1 Kiste Bier';
        $expected = [$item1];
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'item' => $item1],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'item' => $item2, 'exempt' => true],
        ]);
        self::assertEquals(
            $expected,
            $team->ledger->getDueItemClaims()->get()->map(fn(TeamLedgerClaim $itemClaim) => $itemClaim->item)->toArray()
        );
    }

    public function test_fulfilled_item_claims_do_not_resolve_in_item_list() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $item1 = 'Kuchen';
        $item2 = '1 Kiste Bier';
        $expected = [$item1];
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'item' => $item1],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'item' => $item2, 'fulfilled_at' => Carbon::today()],
        ]);
        self::assertEquals(
            $expected,
            $team->ledger->getDueItemClaims()->get()->map(fn(TeamLedgerClaim $itemClaim) => $itemClaim->item)->toArray()
        );
    }

    public function test_money_claims_do_not_resolve_in_item_list() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $item1 = 'Kuchen';
        $item2 = '1 Kiste Bier';
        $amount1 = money(100);
        $expected = [$item1, $item2];
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amount1],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'item' => $item1],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'item' => $item2],
        ]);
        self::assertEquals(
            $expected,
            $team->ledger->getDueItemClaims()->get()->map(fn(TeamLedgerClaim $itemClaim) => $itemClaim->item)->toArray()
        );
    }

    public function test_future_item_claims_do_not_resolve_in_item_list() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $item1 = 'Kuchen';
        $item2 = '1 Kiste Bier';
        $itemFuture = '1 Kiste Cola';
        $expected = [$item1, $item2];
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'item' => $item1],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'item' => $item2],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now()->addDays(), 'item' => $itemFuture],
        ]);
        self::assertEquals(
            $expected,
            $team->ledger->getDueItemClaims()->get()->map(fn(TeamLedgerClaim $itemClaim) => $itemClaim->item)->toArray()
        );
    }

    public function test_future_item_claims_resolve_in_future_item_list() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $item1 = 'Kuchen';
        $item2 = '1 Kiste Bier';
        $expected = [$item1, $item2];
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now()->addDays(), 'item' => $item1],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now()->addDays(), 'item' => $item2],
        ]);
        self::assertEquals(
            $expected,
            $team->ledger->getFutureItemClaims()->get()->map(fn(TeamLedgerClaim $itemClaim) => $itemClaim->item)->toArray()
        );
    }


    public function test_due_future_money_claims_do_not_resolve_in_future_item_list() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $item1 = 'Kuchen';
        $item2 = '1 Kiste Bier';
        $itemDue = '1 Kiste Cola';
        $expected = [$item1, $item2];
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now()->addDays(), 'item' => $item1],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now()->addDays(), 'item' => $item2],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'item' => $itemDue],
        ]);
        self::assertEquals(
            $expected,
            $team->ledger->getFutureItemClaims()->get()->map(fn(TeamLedgerClaim $itemClaim) => $itemClaim->item)->toArray()
        );
    }

    public function test_fulfilled_money_claims_do_not_resolve_in_claim_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amount1 = money(100);
        $amount2 = money(50);
        $amount3 = money(-10);
        $expected = money_sum($amount2, $amount3);
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amount1, 'fulfilled_at' => Carbon::now()],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amount2],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amount3],
        ]);
        // create transaction with same amount of claim
        $team->ledger->transactions()->create(['team_member_id' => $member->id, 'amount' => $amount1, 'title' => 'paid',]);
        self::assertEquals($expected, $team->ledger->getClaimBalance());
    }

    public function test_fulfilled_money_claims_do_not_resolve_in_future_claim_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amount1 = money(100);
        $amount2 = money(50);
        $amount3 = money(-10);
        $expected = money_sum($amount2, $amount3);
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now()->addDays(), 'amount' => $amount1, 'fulfilled_at' => Carbon::now()],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now()->addDays(), 'amount' => $amount2],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now()->addDays(), 'amount' => $amount3],
        ]);
        // create transaction with same amount of claim
        $team->ledger->transactions()->create(['team_member_id' => $member->id, 'amount' => $amount1, 'title' => 'paid',]);
        
        self::assertEquals($expected, $team->ledger->getFutureClaimBalance());
    }

    public function test_transaction_from_member_resolve_in_credit_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amount1 = money(100);
        $amount2 = money(50);
        $amount3 = money(-10);
        $expected = money_sum($amount1, $amount2, $amount3);
        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amount1, 'team_member_id' => $member->id],
            ['title' => 'test', 'amount' => $amount2, 'team_member_id' => $member->id],
            ['title' => 'test', 'amount' => $amount3, 'team_member_id' => $member->id],
        ]);
        self::assertEquals($expected, $team->ledger->getCreditBalance());
    }

    public function test_transaction_from_member_sets_title() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountIn = money(100);
        $amountOut = money(-50);
        $expectedIn = 'Einzahlung';
        $expectedOut = 'Auszahlung';
        $transactionIn = $team->ledger->transactions()->create(['amount' => $amountIn, 'team_member_id' => $member->id]);
        $transactionOut = $team->ledger->transactions()->create(['amount' => $amountOut, 'team_member_id' => $member->id]);
        self::assertEquals($expectedIn, $transactionIn->title);
        self::assertEquals($expectedOut, $transactionOut->title);
    }

    public function test_transaction_from_non_member_resolve_in_non_member_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amount1 = money(100);
        $amount2 = money(50);
        $amountMember = money(10);
        $expected = money_sum($amount1, $amount2);
        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amount1],
            ['title' => 'test', 'amount' => $amount2],
            ['title' => 'test', 'amount' => $amountMember, 'team_member_id' => $member->id],
        ]);
        self::assertEquals($expected, $team->ledger->getBalanceFromNonMembers());
    }

    public function test_transaction_from_non_member_do_not_resolve_in_credit_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amount1 = money(100);
        $amount2 = money(50);
        $amountNonMember = money(30);
        $expected = money_sum($amount1, $amount2);
        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amount1, 'team_member_id' => $member->id],
            ['title' => 'test', 'amount' => $amount2, 'team_member_id' => $member->id],
            ['title' => 'test', 'amount' => $amountNonMember],
        ]);
        self::assertEquals($expected, $team->ledger->getCreditBalance());
    }

    public function test_fulfilled_claims_reduces_credit_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amount1 = money(100);
        $amount2 = money(50);
        $amount3 = money(-10);
        $expected = money_sum($amount2, $amount3);
        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amount1, 'team_member_id' => $member->id],
            ['title' => 'test', 'amount' => $amount2, 'team_member_id' => $member->id],
            ['title' => 'test', 'amount' => $amount3, 'team_member_id' => $member->id],
        ]);
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amount1, 'fulfilled_at' => Carbon::now()],
        ]);

        self::assertEquals($expected, $team->ledger->getCreditBalance());
    }

    public function test_credit_from_member_do_not_resolve_in_available_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amount1 = money(100);
        $amount2 = money(50);
        $expected = money($amount2);
        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amount1, 'team_member_id' => $member->id],
            ['title' => 'test', 'amount' => $amount2],
        ]);

        self::assertEquals($expected, $team->ledger->getAvailableBalance());
    }

    public function test_transactions_resolve_in_member_balance() : void {
        $team = $this->createTeam();
        $this->createUserActingAs();

        $member1 = $team->createMember();
        $member2 = $team->createMember();
        $member3 = $team->createMember();

        $amount1 = money(100);
        $amount2 = money(50);
        $amount3 = money(10);
        $expected = money_sum($amount1->multiply(2), $amount2, $amount3);
        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amount1, 'team_member_id' => $member1->id],
            ['title' => 'test', 'amount' => $amount1, 'team_member_id' => $member1->id],
            ['title' => 'test', 'amount' => $amount2, 'team_member_id' => $member2->id],
            ['title' => 'test', 'amount' => $amount3, 'team_member_id' => $member3->id],
        ]);
        self::assertEquals($amount1->multiply(2), $team->ledger->getBalance($member1));
        self::assertEquals($amount2, $team->ledger->getBalance($member2));
        self::assertEquals($amount3, $team->ledger->getBalance($member3));
        self::assertEquals($expected, $team->ledger->getBalance());
    }

    public function test_money_claims_resolve_in_member_claim_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $member1 = $team->createMember();
        $member2 = $team->createMember();
        $member3 = $team->createMember();

        $amount1 = money(100);
        $amount2 = money(50);
        $amount3 = money(-10);
        $expected = money_sum($amount1->multiply(2), $amount2, $amount3);
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member1->id, 'due_date' => Carbon::now(), 'amount' => $amount1],
            ['title' => 'test', 'team_member_id' => $member1->id, 'due_date' => Carbon::now(), 'amount' => $amount1],
            ['title' => 'test', 'team_member_id' => $member2->id, 'due_date' => Carbon::now(), 'amount' => $amount2],
            ['title' => 'test', 'team_member_id' => $member3->id, 'due_date' => Carbon::now(), 'amount' => $amount3],
        ]);
        self::assertEquals($amount1->multiply(2), $team->ledger->getClaimBalance($member1));
        self::assertEquals($amount2, $team->ledger->getClaimBalance($member2));
        self::assertEquals($amount3, $team->ledger->getClaimBalance($member3));
        self::assertEquals($expected, $team->ledger->getClaimBalance());
    }

    public function test_exempt_money_claims_do_not_resolve_in_member_claim_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $member1 = $team->createMember();
        $member2 = $team->createMember();
        $member3 = $team->createMember();

        $amount1 = money(100);
        $amount2 = money(50);
        $amount3 = money(30);
        $amount3Exempt = money(-10);
        $expected = money_sum($amount1, $amount2, $amount3);
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member1->id, 'due_date' => Carbon::now(), 'amount' => $amount1],
            ['title' => 'test', 'team_member_id' => $member2->id, 'due_date' => Carbon::now(), 'amount' => $amount2],
            ['title' => 'test', 'team_member_id' => $member3->id, 'due_date' => Carbon::now(), 'amount' => $amount3],
            ['title' => 'test', 'team_member_id' => $member3->id, 'due_date' => Carbon::now(), 'amount' => $amount3Exempt, 'exempt' => true],
        ]);
        self::assertEquals($amount1, $team->ledger->getClaimBalance($member1));
        self::assertEquals($amount2, $team->ledger->getClaimBalance($member2));
        self::assertEquals($amount3, $team->ledger->getClaimBalance($member3));
        self::assertEquals($expected, $team->ledger->getClaimBalance());
    }

    public function test_future_money_claims_do_not_resolve_in_member_claim_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $member1 = $team->createMember();
        $member2 = $team->createMember();
        $member3 = $team->createMember();

        $amount1 = money(100);
        $amount2 = money(50);
        $amount3 = money(30);
        $amountFuture = money(-10);
        $expected = money_sum($amount1, $amount2, $amount3);
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member1->id, 'due_date' => Carbon::now(), 'amount' => $amount1],
            ['title' => 'test', 'team_member_id' => $member2->id, 'due_date' => Carbon::now(), 'amount' => $amount2],
            ['title' => 'test', 'team_member_id' => $member3->id, 'due_date' => Carbon::now(), 'amount' => $amount3],
            ['title' => 'test', 'team_member_id' => $member3->id, 'due_date' => Carbon::now()->addDays(), 'amount' => $amountFuture],
        ]);
        self::assertEquals($amount1, $team->ledger->getClaimBalance($member1));
        self::assertEquals($amount2, $team->ledger->getClaimBalance($member2));
        self::assertEquals($amount3, $team->ledger->getClaimBalance($member3));
        self::assertEquals($expected, $team->ledger->getClaimBalance());
    }

    public function test_future_money_claims_resolve_in_future_member_claim_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $member1 = $team->createMember();
        $member2 = $team->createMember();
        $member3 = $team->createMember();

        $amount1 = money(100);
        $amount2 = money(50);
        $amount3 = money(30);
        $expected = money_sum($amount1, $amount2, $amount3);
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member1->id, 'due_date' => Carbon::now()->addDays(), 'amount' => $amount1],
            ['title' => 'test', 'team_member_id' => $member2->id, 'due_date' => Carbon::now()->addDays(), 'amount' => $amount2],
            ['title' => 'test', 'team_member_id' => $member3->id, 'due_date' => Carbon::now()->addDays(), 'amount' => $amount3],
        ]);
        self::assertEquals($amount1, $team->ledger->getFutureClaimBalance($member1));
        self::assertEquals($amount2, $team->ledger->getFutureClaimBalance($member2));
        self::assertEquals($amount3, $team->ledger->getFutureClaimBalance($member3));
        self::assertEquals($expected, $team->ledger->getFutureClaimBalance());
    }

    public function test_due_money_claims_do_not_resolve_in_future_member_claim_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $member1 = $team->createMember();
        $member2 = $team->createMember();
        $member3 = $team->createMember();

        $amount1 = money(100);
        $amount2 = money(50);
        $amountDue = money(-10);
        $expected = money_sum($amount1, $amount2);
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member1->id, 'due_date' => Carbon::now()->addDays(), 'amount' => $amount1],
            ['title' => 'test', 'team_member_id' => $member2->id, 'due_date' => Carbon::now()->addDays(), 'amount' => $amount2],
            ['title' => 'test', 'team_member_id' => $member3->id, 'due_date' => Carbon::now(), 'amount' => $amountDue],
        ]);
        self::assertEquals($amount1, $team->ledger->getFutureClaimBalance($member1));
        self::assertEquals($amount2, $team->ledger->getFutureClaimBalance($member2));
        self::assertEquals(money(0), $team->ledger->getFutureClaimBalance($member3));
        self::assertEquals($expected, $team->ledger->getFutureClaimBalance());
    }

    public function test_item_claims_do_resolve_in_member_item_list() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $member1 = $team->createMember();
        $member2 = $team->createMember();

        $item1 = 'Kuchen';
        $item2 = '1 Kiste Bier';
        $expectedMember1 = [$item1, $item2];
        $expectedMember2 = [$item2];
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member1->id, 'due_date' => Carbon::now(), 'item' => $item1],
            ['title' => 'test', 'team_member_id' => $member1->id, 'due_date' => Carbon::now(), 'item' => $item2],
            ['title' => 'test', 'team_member_id' => $member2->id, 'due_date' => Carbon::now(), 'item' => $item2],
        ]);
        self::assertEquals(
            $expectedMember1,
            $team->ledger->getDueItemClaims($member1)->get()->map(fn(TeamLedgerClaim $itemClaim) => $itemClaim->item)->toArray()
        );
        self::assertEquals(
            $expectedMember2,
            $team->ledger->getDueItemClaims($member2)->get()->map(fn(TeamLedgerClaim $itemClaim) => $itemClaim->item)->toArray()
        );
    }

    public function test_money_claims_do_not_resolve_in_member_item_list() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $member1 = $team->createMember();
        $member2 = $team->createMember();

        $item1 = 'Kuchen';
        $item2 = '1 Kiste Bier';
        $amount1 = money(100);
        $expectedMember1 = [$item1, $item2];
        $expectedMember2 = [$item2];
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member1->id, 'due_date' => Carbon::now(), 'amount' => $amount1],
            ['title' => 'test', 'team_member_id' => $member1->id, 'due_date' => Carbon::now(), 'item' => $item1],
            ['title' => 'test', 'team_member_id' => $member1->id, 'due_date' => Carbon::now(), 'item' => $item2],
            ['title' => 'test', 'team_member_id' => $member2->id, 'due_date' => Carbon::now(), 'item' => $item2],
        ]);
        self::assertEquals(
            $expectedMember1,
            $team->ledger->getDueItemClaims($member1)->get()->map(fn(TeamLedgerClaim $itemClaim) => $itemClaim->item)->toArray()
        );
        self::assertEquals(
            $expectedMember2,
            $team->ledger->getDueItemClaims($member2)->get()->map(fn(TeamLedgerClaim $itemClaim) => $itemClaim->item)->toArray()
        );
    }

    public function test_future_item_claims_do_not_resolve_in_member_item_list() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $member1 = $team->createMember();
        $member2 = $team->createMember();

        $item1 = 'Kuchen';
        $item2 = '1 Kiste Bier';
        $itemFuture = '1 Kiste Cola';
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member1->id, 'due_date' => Carbon::now(), 'item' => $item1],
            ['title' => 'test', 'team_member_id' => $member2->id, 'due_date' => Carbon::now(), 'item' => $item2],
            ['title' => 'test', 'team_member_id' => $member2->id, 'due_date' => Carbon::now()->addDays(), 'item' => $itemFuture],
        ]);
        self::assertEquals(
            [$item1],
            $team->ledger->getDueItemClaims($member1)->get()->map(fn(TeamLedgerClaim $itemClaim) => $itemClaim->item)->toArray()
        );
        self::assertEquals(
            [$item2],
            $team->ledger->getDueItemClaims($member2)->get()->map(fn(TeamLedgerClaim $itemClaim) => $itemClaim->item)->toArray()
        );
    }

    public function test_future_item_claims_resolve_in_future_member_item_list() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $member1 = $team->createMember();
        $member2 = $team->createMember();

        $item1 = 'Kuchen';
        $item2 = '1 Kiste Bier';
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member1->id, 'due_date' => Carbon::now()->addDays(), 'item' => $item1],
            ['title' => 'test', 'team_member_id' => $member2->id, 'due_date' => Carbon::now()->addDays(), 'item' => $item2],
        ]);
        self::assertEquals(
            [$item1],
            $team->ledger->getFutureItemClaims($member1)->get()->map(fn(TeamLedgerClaim $itemClaim) => $itemClaim->item)->toArray()
        );
        self::assertEquals(
            [$item2],
            $team->ledger->getFutureItemClaims($member2)->get()->map(fn(TeamLedgerClaim $itemClaim) => $itemClaim->item)->toArray()
        );
    }

    public function test_fulfilled_money_claims_do_not_resolve_in_member_claim_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $member1 = $team->createMember();
        $member2 = $team->createMember();

        $amount1 = money(100);
        $amount2 = money(50);
        $amount3 = money(-10);
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member1->id, 'due_date' => Carbon::now(), 'amount' => $amount1, 'fulfilled_at' => Carbon::now()],
            ['title' => 'test', 'team_member_id' => $member1->id, 'due_date' => Carbon::now(), 'amount' => $amount2],
            ['title' => 'test', 'team_member_id' => $member2->id, 'due_date' => Carbon::now(), 'amount' => $amount3],
        ]);
        // create transaction with same amount of claim
        $team->ledger->transactions()->create(['team_member_id' => $member1->id, 'amount' => $amount1, 'title' => 'paid',]);
        
        self::assertEquals($amount2, $team->ledger->getClaimBalance($member1));
        self::assertEquals($amount3, $team->ledger->getClaimBalance($member2));
        self::assertEquals(money_sum($amount2, $amount3), $team->ledger->getClaimBalance());
    }

    public function test_transaction_from_member_resolve_in_member_credit_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $member1 = $team->createMember();
        $member2 = $team->createMember();

        $amount1 = money(100);
        $amount2 = money(50);
        $amount3 = money(10);
        $expected = money_sum($amount1, $amount2, $amount3);
        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amount1, 'team_member_id' => $member1->id],
            ['title' => 'test', 'amount' => $amount2, 'team_member_id' => $member1->id],
            ['title' => 'test', 'amount' => $amount3, 'team_member_id' => $member2->id],
        ]);
        self::assertEquals(money_sum($amount1, $amount2), $team->ledger->getCreditBalance($member1));
        self::assertEquals($amount3, $team->ledger->getCreditBalance($member2));
        self::assertEquals($expected, $team->ledger->getCreditBalance());
    }

    public function test_prevent_negative_transaction_from_member_without_credit_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $member1 = $team->createMember();

        $amountIn = money(0);
        $amountOut = money(-100);
        $this->assertThrows(function () use ($team, $amountOut, $member1) {
            $team->ledger->transactions()->create([
                'title' => 'test', 'amount' => $amountOut, 'team_member_id' => $member1->id
            ]);
        }, \InvalidArgumentException::class);
        self::assertEquals($amountIn, $team->ledger->getCreditBalance($member1));
    }

    public function test_prevent_negative_transaction_from_member_with_lower_credit_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $member1 = $team->createMember();

        $amountIn = money(100);
        $amountOut = money(-200);
        $team->ledger->transactions()->create([
            'title' => 'test', 'amount' => $amountIn, 'team_member_id' => $member1->id
        ]);
        $this->assertThrows(function () use ($team, $amountOut, $member1) {
            $team->ledger->transactions()->create([
                'title' => 'test', 'amount' => $amountOut, 'team_member_id' => $member1->id
            ]);
        }, \InvalidArgumentException::class);
        self::assertEquals($amountIn, $team->ledger->getCreditBalance($member1));
    }

    public function test_allow_negative_transaction_from_member_with_higher_credit_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $member1 = $team->createMember();

        $amountIn = money(300);
        $amountOut = money(-200);
        $expected = money_sum($amountIn, $amountOut);
        $team->ledger->transactions()->create([
            'title' => 'test', 'amount' => $amountIn, 'team_member_id' => $member1->id
        ]);
        $team->ledger->transactions()->create([
            'title' => 'test', 'amount' => $amountOut, 'team_member_id' => $member1->id
        ]);
        self::assertEquals($expected, $team->ledger->getCreditBalance($member1));
    }

    public function test_allow_negative_transaction_from_member_with_equal_credit_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $member1 = $team->createMember();

        $amountIn = money(200);
        $amountOut = money(-200);
        $expected = money_sum($amountIn, $amountOut);
        $team->ledger->transactions()->create([
            'title' => 'test', 'amount' => $amountIn, 'team_member_id' => $member1->id
        ]);
        $team->ledger->transactions()->create([
            'title' => 'test', 'amount' => $amountOut, 'team_member_id' => $member1->id
        ]);
        self::assertEquals($expected, $team->ledger->getCreditBalance($member1));
    }

    public function test_transaction_from_non_member_do_not_resolve_in_member_credit_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $member = $team->createMember();

        $amount1 = money(100);
        $amount2 = money(50);
        $amountNonMember = money(30);
        $expected = money_sum($amount1, $amount2);
        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amount1, 'team_member_id' => $member->id],
            ['title' => 'test', 'amount' => $amount2, 'team_member_id' => $member->id],
            ['title' => 'test', 'amount' => $amountNonMember],
        ]);
        self::assertEquals($expected, $team->ledger->getCreditBalance($member));
    }

    public function test_fulfilled_claims_reduces_member_credit_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $member = $team->createMember();

        $amount1 = money(100);
        $amount2 = money(50);
        $amount3 = money(-10);
        $expected = money_sum($amount2, $amount3);
        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amount1, 'team_member_id' => $member->id],
            ['title' => 'test', 'amount' => $amount2, 'team_member_id' => $member->id],
            ['title' => 'test', 'amount' => $amount3, 'team_member_id' => $member->id],
        ]);
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amount1, 'fulfilled_at' => Carbon::now()],
        ]);

        self::assertEquals($expected, $team->ledger->getCreditBalance($member));
    }

    public function test_deposit_transaction_from_member_fulfills_claim() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amount1 = money(100);
        $expectedClaimBalance = money(0);
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amount1],
        ]);

        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amount1, 'team_member_id' => $member->id],
        ]);

        self::assertEquals($expectedClaimBalance, $team->ledger->getClaimBalance());
        self::assertEquals($amount1, $team->ledger->getBalance());
    }

    public function test_deposit_transaction_from_member_fulfills_own_claims_only() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);
        $member2 = $team->createMember();

        $amount1 = money(100);
        $amountOtherMember = money(30);
        $expectedClaimBalance = $amountOtherMember;
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amount1],
            ['title' => 'test', 'team_member_id' => $member2->id, 'due_date' => Carbon::now(), 'amount' => $amountOtherMember],
        ]);

        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amount1, 'team_member_id' => $member->id],
        ]);

        self::assertEquals($expectedClaimBalance, $team->ledger->getClaimBalance());
        self::assertEquals($amount1, $team->ledger->getBalance());
    }

    public function test_deposit_transaction_from_non_member_fulfills_no_claims() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amount1 = money(100);
        $amount2 = money(30);
        $amountDeposit = money(150);
        $expectedClaimBalance = money_sum($amount1, $amount2);
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amount1],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amount2],
        ]);

        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amountDeposit],
        ]);

        self::assertEquals($expectedClaimBalance, $team->ledger->getClaimBalance());
        self::assertEquals($amountDeposit, $team->ledger->getBalance());
        self::assertEquals($amountDeposit, $team->ledger->getAvailableBalance());
        self::assertEquals(money(0), $team->ledger->getCreditBalance());
    }


    public function test_deposit_transaction_from_member_fulfills_claim_and_has_credit() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountClaim1 = money(100);
        $amountClaim2 = money(30);
        $amountTransaction = money(150);
        $expectedClaimBalance = money(0);
        $expectedCredit = money(20);
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amountClaim1],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amountClaim2],
        ]);

        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amountTransaction, 'team_member_id' => $member->id],
        ]);

        self::assertEquals($expectedClaimBalance, $team->ledger->getClaimBalance());
        self::assertEquals($expectedCredit, $team->ledger->getCreditBalance());
    }

    public function test_deposit_transaction_from_member_ignores_exempt_claim() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amount1 = money(100);
        $amountExempt = money(30);
        $amountTransaction = money(150);
        $expectedClaimBalance = money(0);
        $expectedCredit = money(50);
        $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amount1],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amountExempt, 'exempt' => true],
        ]);
        $claimExempt = $team->ledger->claims()->notExempt()->first();

        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amountTransaction, 'team_member_id' => $member->id],
        ]);

        self::assertFalse($claimExempt->isFulfilled());
        self::assertEquals($expectedClaimBalance, $team->ledger->getClaimBalance());
        self::assertEquals($expectedCredit, $team->ledger->getCreditBalance());
    }

    public function test_deposit_transaction_fulfills_future_claims() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountDeposit = money(100);
        $amountPerClaim = money(10); // 3x
        $countClaims = $team->ledger->claims()->createMany([
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now()->subDays(2), 'amount' => $amountPerClaim],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now(), 'amount' => $amountPerClaim],
            ['title' => 'test', 'team_member_id' => $member->id, 'due_date' => Carbon::now()->addDays(2), 'amount' => $amountPerClaim],
        ])->count();

        self::assertEquals($amountPerClaim->multiply(2), $team->ledger->getClaimBalance());
        self::assertEquals($amountPerClaim->multiply(1), $team->ledger->getFutureClaimBalance());

        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amountDeposit, 'team_member_id' => $member->id],
        ]);

        self::assertEquals(money(0), $team->ledger->getClaimBalance());

        self::assertEquals($amountPerClaim->multiply($countClaims), $team->ledger->getFulfilledClaimBalance());
        self::assertEquals($amountDeposit, $team->ledger->getBalance());
        self::assertEquals($amountDeposit->subtract($amountPerClaim->multiply($countClaims)), $team->ledger->getCreditBalance());
    }

    public function test_add_child_fine_soft_deletes_parent() : void {
        $team = $this->createTeam();

        $parent = $team->ledger->fines()->create([
            'title' => 'Parent',
            'date_begin' => Carbon::now(),
        ]);
        $child = $team->ledger->fines()->create([
            'title' => 'Child',
            'date_begin' => Carbon::now(),
            'parent_id' => $parent->id,
        ]);

        $this->assertSoftDeleted($parent);
        $this->assertNotSoftDeleted($child);
    }

    public function test_create_claim_from_fine_adds_ledger_id() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);


        $fine = $team->ledger->fines()->create([
            'title' => 'Fine',
            'date_begin' => Carbon::now(),
            ]);
        $claim = $fine->claims()->create([
            'title' => 'Fine',
            'team_member_id' => $member->id,
            'due_date' => Carbon::now(),
            ]);

        $this->assertEquals($fine->ledger, $claim->ledger);
    }

    public function test_create_claim_fulfills_from_credit() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountCredit = money(100);
        $amountClaim = money(30);
        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amountCredit, 'team_member_id' => $member->id],
        ]);

        self::assertEquals($amountCredit, $team->ledger->getCreditBalance());

        $claim = $team->ledger->claims()->create([
            'title' => 'Fine',
            'team_member_id' => $member->id,
            'due_date' => Carbon::now(),
            'amount' => $amountClaim,
            ]);

        $claim->refresh();

        self::assertEquals($amountCredit->subtract($amountClaim), $team->ledger->getCreditBalance());
        self::assertTrue($claim->isFulfilled());
    }
    
    public function test_delete_fulfilled_claim_resolve_in_credit_balance() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountClaim = money(30);

        $claim = $team->ledger->claims()->create([
            'title' => 'Fine',
            'team_member_id' => $member->id,
            'due_date' => Carbon::now(),
            'amount' => $amountClaim,
        ]);

        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amountClaim, 'team_member_id' => $member->id],
        ]);        
        
        self::assertEquals(money(0), $team->ledger->getCreditBalance());
        
        $claim->delete();
        self::assertEquals($amountClaim, $team->ledger->getCreditBalance());
    }
    
    public function test_delete_fulfilled_claim_processes_money_claims_to_fulfill() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountClaim = money(30);

        $claim1 = $team->ledger?->claims()->create([
            'title' => 'Fine',
            'team_member_id' => $member->id,
            'due_date' => Carbon::now()->subDay(),
            'amount' => $amountClaim,
        ]);
        $claim2 = $team->ledger?->claims()->create([
            'title' => 'Fine',
            'team_member_id' => $member->id,
            'due_date' => Carbon::now(),
            'amount' => $amountClaim,
        ]);

        $team->ledger?->transactions()->createMany([
            ['title' => 'test', 'amount' => $amountClaim, 'team_member_id' => $member->id],
        ]);        
        
        self::assertEquals(money(0), $team->ledger?->getCreditBalance());
        $claim1->refresh();
        $claim2->refresh();
        self::assertTrue($claim1->isFulfilled());
        self::assertFalse($claim2->isFulfilled());
        
        $claim1->delete();
        $claim2->refresh();
        $team->ledger?->refresh();
        self::assertTrue($claim2->isFulfilled());
        self::assertEquals(money(0), $team->ledger?->getCreditBalance());
    }

    public function test_add_child_fine_adjust_reference_and_amount_title_for_claims_in_range() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountOld = money(50);
        $amountNew = money(100);

        $oldFineMoney = $team->ledger->fines()->create([
            'title' => 'Parent Money',
            'amount' => $amountOld,
            'date_begin' => Carbon::now()->subDays(5)
        ]);

        $claim1InRange = $oldFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()]);
        $claim2InRange = $oldFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()->subDays(2)]);
        $claimOutOfRange = $oldFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()->subDays(4)]);

        $newFineMoney = $team->ledger->fines()->create([
            'title' => 'Child Money',
            'amount' => $amountNew,
            'date_begin' => Carbon::now()->subDays(2),
            'parent_id' => $oldFineMoney->id,
        ]);

        $claim1InRange->refresh();
        $claim2InRange->refresh();
        $claimOutOfRange->refresh();
        $oldFineMoney->refresh();
        $newFineMoney->refresh();

        $this->assertEquals($amountOld, $claimOutOfRange->amount);
        $this->assertEquals($oldFineMoney->id, $claimOutOfRange->claimable->id);
        $this->assertEquals($oldFineMoney->title, $claimOutOfRange->title);

        $this->assertEquals($amountNew, $claim1InRange->amount);
        $this->assertEquals($newFineMoney->id, $claim1InRange->claimable->id);
        $this->assertEquals($newFineMoney->title, $claim1InRange->title);

        $this->assertEquals($amountNew, $claim2InRange->amount);
        $this->assertEquals($newFineMoney->id, $claim2InRange->claimable->id);
        $this->assertEquals($newFineMoney->title, $claim2InRange->title);
    }
    
    public function test_adjust_fine_adjust_amount_title_for_related_claims() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountOld = money(50);
        $amountNew = money(100);
        
        $titleOld = 'Money Fine';
        $titleNew = 'Money Fine Updated';

        $oldFineMoney = $team->ledger->fines()->create([
            'title' => $titleOld,
            'amount' => $amountOld,
            'date_begin' => Carbon::now()->subDays(5)
        ]);

        $claim1 = $oldFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()]);
        $claim2 = $oldFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()->subDays(2)]);

        $oldFineMoney->update([
            'title' => $titleNew,
            'amount' => $amountNew,
        ]);

        $claim1->refresh();
        $claim2->refresh();

        $this->assertEquals($amountNew, $claim1->amount);
        $this->assertEquals($titleNew, $claim1->title);

        $this->assertEquals($amountNew, $claim2->amount);
        $this->assertEquals($titleNew, $claim2->title);
    }
    
    public function test_adjust_fine_amount_processes_related_claims_to_fulfill() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountCredit = money(100);
        $amountOld = money(200);
        $amountNew = money(50);
        
        $titleOld = 'Money Fine';
        $titleNew = 'Money Fine Updated';

        $oldFineMoney = $team->ledger->fines()->create([
            'title' => $titleOld,
            'amount' => $amountOld,
            'date_begin' => Carbon::now()->subDays(5)
        ]);

        $claim1 = $oldFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()]);
        $claim2 = $oldFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()->subDays(2)]);

        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amountCredit, 'team_member_id' => $member->id],
        ]);

        $claim1->refresh();
        $claim2->refresh();
        
        self::assertFalse($claim1->isFulfilled());
        self::assertFalse($claim2->isFulfilled());
        
        $oldFineMoney->update([
            'title' => $titleNew,
            'amount' => $amountNew,
        ]);

        $claim1->refresh();
        $claim2->refresh();
        
        self::assertTrue($claim1->isFulfilled());
        self::assertTrue($claim2->isFulfilled());
    }

    public function test_adjust_fine_amount_processes_related_claims_to_unfulfilled() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountCredit = money(100);
        $amountOld = money(50);
        $amountNew = money(200);

        $titleOld = 'Money Fine';
        $titleNew = 'Money Fine Updated';

        $oldFineMoney = $team->ledger->fines()->create([
            'title' => $titleOld,
            'amount' => $amountOld,
            'date_begin' => Carbon::now()->subDays(5)
        ]);

        $claim1 = $oldFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()]);
        $claim2 = $oldFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()->subDays(2)]);

        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amountCredit, 'team_member_id' => $member->id],
        ]);

        $claim1->refresh();
        $claim2->refresh();

        self::assertTrue($claim1->isFulfilled());
        self::assertTrue($claim2->isFulfilled());

        $oldFineMoney->update([
            'title' => $titleNew,
            'amount' => $amountNew,
        ]);

        $claim1->refresh();
        $claim2->refresh();

        self::assertFalse($claim1->isFulfilled());
        self::assertFalse($claim2->isFulfilled());
    }
    
    public function test_add_claim_with_older_due_date_references_parent_fine() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountParent2 = money(50);
        $amountParent1 = money(70);
        $amountCurrent = money(100);

        $parent2FineMoney = $team->ledger->fines()->create([
            'title' => 'Parent 2 Money',
            'amount' => $amountParent2,
            'date_begin' => Carbon::now()->subDays(10)
        ]);

        $parent1FineMoney = $team->ledger->fines()->create([
            'title' => 'Parent 1 Money',
            'amount' => $amountParent1,
            'date_begin' => Carbon::now()->subDays(5),
            'parent_id' => $parent2FineMoney->id,
        ]);

        $currentFineMoney = $team->ledger->fines()->create([
            'title' => 'Child Money',
            'amount' => $amountCurrent,
            'date_begin' => Carbon::now()->subDays(1),
            'parent_id' => $parent1FineMoney->id,
        ]);

        $currentFineMoney->refresh();

        $claimParent2 = $currentFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()->subDays(8)]);
        $claimParent1 = $currentFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()->subDays(4)]);
        $claimCurrent = $currentFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()]);

        $claimParent2->refresh();
        $claimParent1->refresh();
        $claimCurrent->refresh();

        $this->assertEquals($amountParent2, $claimParent2->amount);
        $this->assertEquals($parent2FineMoney->id, $claimParent2->claimable->id);
        $this->assertEquals($parent2FineMoney->title, $claimParent2->title);

        $this->assertEquals($amountParent1, $claimParent1->amount);
        $this->assertEquals($parent1FineMoney->id, $claimParent1->claimable->id);
        $this->assertEquals($parent1FineMoney->title, $claimParent1->title);

        $this->assertEquals($amountCurrent, $claimCurrent->amount);
        $this->assertEquals($currentFineMoney->id, $claimCurrent->claimable->id);
        $this->assertEquals($currentFineMoney->title, $claimCurrent->title);
    }
      
    public function test_update_fine_claim_due_date_references_active_fine() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountParent2 = money(50);
        $amountParent1 = money(70);
        $amountCurrent = money(100);

        $parent2FineMoney = $team->ledger->fines()->create([
            'title' => 'Parent 2 Money',
            'amount' => $amountParent2,
            'date_begin' => Carbon::now()->subDays(10)
        ]);

        $parent1FineMoney = $team->ledger->fines()->create([
            'title' => 'Parent 1 Money',
            'amount' => $amountParent1,
            'date_begin' => Carbon::now()->subDays(5),
            'parent_id' => $parent2FineMoney->id,
        ]);

        $currentFineMoney = $team->ledger->fines()->create([
            'title' => 'Child Money',
            'amount' => $amountCurrent,
            'date_begin' => Carbon::now()->subDays(1),
            'parent_id' => $parent1FineMoney->id,
        ]);

        $currentFineMoney->refresh();
        $parent1FineMoney->refresh();
        $parent2FineMoney->refresh();

        $claimParent2ToCurrent = $currentFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()->subDays(8)]);
        $claimParent1ToParent2 = $currentFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()->subDays(4)]);
        $claimCurrentToParent2 = $currentFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()]);

        $claimParent2ToCurrent->refresh();
        $claimParent1ToParent2->refresh();
        $claimCurrentToParent2->refresh();
        
        $claimParent2ToCurrent->update(['due_date' => Carbon::now()]);
        $claimParent1ToParent2->update(['due_date' => Carbon::now()->subDays(8)]);
        $claimCurrentToParent2->update(['due_date' => Carbon::now()->subDays(8)]);

        $claimParent2ToCurrent->refresh();
        $claimParent1ToParent2->refresh();
        $claimCurrentToParent2->refresh();
        
        $this->assertEquals($amountCurrent, $claimParent2ToCurrent->amount);
        $this->assertEquals($currentFineMoney->id, $claimParent2ToCurrent->claimable->id);
        $this->assertEquals($currentFineMoney->title, $claimParent2ToCurrent->title);

        $this->assertEquals($amountParent2, $claimParent1ToParent2->amount);
        $this->assertEquals($parent2FineMoney->id, $claimParent1ToParent2->claimable->id);
        $this->assertEquals($parent2FineMoney->title, $claimParent1ToParent2->title);

        $this->assertEquals($amountParent2, $claimCurrentToParent2->amount);
        $this->assertEquals($parent2FineMoney->id, $claimCurrentToParent2->claimable->id);
        $this->assertEquals($parent2FineMoney->title, $claimCurrentToParent2->title);
    } 
    
    public function test_update_fine_claim_due_date_fulfills_when_fine_changes() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountParent1 = money(50);
        $amountCurrent = money(100);
        $amountCredit = money(150);

        $parent1FineMoney = $team->ledger->fines()->create([
            'title' => 'Parent 1 Money',
            'amount' => $amountParent1,
            'date_begin' => Carbon::now()->subDays(5),
        ]);

        $currentFineMoney = $team->ledger->fines()->create([
            'title' => 'Child Money',
            'amount' => $amountCurrent,
            'date_begin' => Carbon::now()->subDays(2),
            'parent_id' => $parent1FineMoney->id,
        ]);

        $currentFineMoney->refresh();
        $parent1FineMoney->refresh();

        $claimCurrentToParent1 = $currentFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()]);
        $claimCurrent = $currentFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()->subDays(1)]);

        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amountCredit, 'team_member_id' => $member->id],
        ]);

        $claimCurrentToParent1->refresh();
        $claimCurrent->refresh();
        
        $this->assertNotTrue($claimCurrentToParent1->isFulfilled());
        $this->assertTrue($claimCurrent->isFulfilled());
        
        $claimCurrentToParent1->update(['due_date' => Carbon::now()->subDays(4)]);

        $claimCurrentToParent1->refresh();
        $claimCurrent->refresh();
        
        $this->assertTrue($claimCurrentToParent1->isFulfilled());
        $this->assertTrue($claimCurrent->isFulfilled());
    }
    
    public function test_add_child_fine_adjust_reference_and_item_title_for_unfulfilled_claims_in_range_only() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $itemOld = 'old Kuchen';
        $itemNew = 'new Muffins';

        $oldFineItem = $team->ledger->fines()->create([
            'title' => 'Parent Item',
            'item' => $itemOld,
            'date_begin' => Carbon::now()->subDays(5)
        ]);

        $claim1InRange = $oldFineItem->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()]);
        $claim2InRange = $oldFineItem->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()->subDays(2)]);
        $claimOutOfRange = $oldFineItem->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()->subDays(4)]);
        $claimFulfilledInRange = $oldFineItem->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()]);
        $claimFulfilledInRange->setFulfilled();

        $newFineItem = $team->ledger->fines()->create([
            'title' => 'Child Item',
            'item' => $itemNew,
            'date_begin' => Carbon::now()->subDays(2),
            'parent_id' => $oldFineItem->id,
        ]);

        $claim1InRange->refresh();
        $claim2InRange->refresh();
        $claimOutOfRange->refresh();
        $claimFulfilledInRange->refresh();
        $oldFineItem->refresh();
        $newFineItem->refresh();

        $this->assertEquals($itemOld, $claimOutOfRange->item);
        $this->assertEquals($oldFineItem->id, $claimOutOfRange->claimable->id);
        $this->assertEquals($oldFineItem->title, $claimOutOfRange->title);

        $this->assertEquals($itemNew, $claim1InRange->item);
        $this->assertEquals($newFineItem->id, $claim1InRange->claimable->id);
        $this->assertEquals($newFineItem->title, $claim1InRange->title);

        $this->assertEquals($itemNew, $claim2InRange->item);
        $this->assertEquals($newFineItem->id, $claim2InRange->claimable->id);
        $this->assertEquals($newFineItem->title, $claim2InRange->title);

        $this->assertEquals($itemOld, $claimFulfilledInRange->item);
        $this->assertEquals($oldFineItem->id, $claimFulfilledInRange->claimable->id);
        $this->assertEquals($oldFineItem->title, $claimFulfilledInRange->title);
    }

    public function test_add_child_fine_mark_money_claims_as_unfulfilled_for_claims_in_range() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountOld = money(10);
        $amountNew = money(100);

        $oldFineMoney = $team->ledger->fines()->create([
            'title' => 'Parent Money',
            'amount' => $amountOld,
            'date_begin' => Carbon::now()->subDays(5)
        ]);

        $claim1FulfilledInRange = $oldFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()->subDays(2)]);
        $claim2FulfilledInRange = $oldFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()]);
        $claimFulfilledOutOfRange = $oldFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()->subDays(4)]);

        $claim1FulfilledInRange->setFulfilled();
        $claim2FulfilledInRange->setFulfilled();
        $claimFulfilledOutOfRange->setFulfilled();

        $team->ledger->fines()->create([
            'title' => 'Child Money',
            'amount' => $amountNew,
            'date_begin' => Carbon::now()->subDays(2),
            'parent_id' => $oldFineMoney->id,
        ]);

        $claim1FulfilledInRange->refresh();
        $claim2FulfilledInRange->refresh();
        $claimFulfilledOutOfRange->refresh();

        $this->assertTrue($claimFulfilledOutOfRange->isFulfilled());
        $this->assertFalse($claim1FulfilledInRange->isFulfilled());
        $this->assertFalse($claim2FulfilledInRange->isFulfilled());
    }

    public function test_add_child_fine_processes_money_claims_to_fulfill_for_claims_in_range() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountCredit = money(100);
        $amountPerClaimOld = money(40); // 3x claims = 120
        $amountPerClaimNew = money(30); // 1x old, 2x new amount = 100

        $oldFineMoney = $team->ledger->fines()->create([
            'title' => 'Parent Money',
            'amount' => $amountPerClaimOld,
            'date_begin' => Carbon::now()->subDays(5)
        ]);

        $claim1FulfilledInRange = $oldFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()->subDays(2)]);
        $claim2UnfulfilledInRange = $oldFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()]);
        $claimFulfilledOutOfRange = $oldFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now()->subDays(4)]);

        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amountCredit, 'team_member_id' => $member->id],
        ]);

        $claim1FulfilledInRange->refresh();
        $claim2UnfulfilledInRange->refresh();
        $claimFulfilledOutOfRange->refresh();

        self::assertEquals($amountPerClaimOld->multiply(3)->subtract($amountCredit), $team->ledger->getCreditBalance());
        self::assertTrue($claimFulfilledOutOfRange->isFulfilled());
        self::assertTrue($claim1FulfilledInRange->isFulfilled());
        self::assertFalse($claim2UnfulfilledInRange->isFulfilled());

        $team->ledger->fines()->create([
            'title' => 'Child Money',
            'amount' => $amountPerClaimNew,
            'date_begin' => Carbon::now()->subDays(2),
            'parent_id' => $oldFineMoney->id,
        ]);

        $claim1FulfilledInRange->refresh();
        $claim2UnfulfilledInRange->refresh();
        $claimFulfilledOutOfRange->refresh();

        self::assertEquals(money(0), $team->ledger->getCreditBalance());
        self::assertTrue($claimFulfilledOutOfRange->isFulfilled());
        self::assertTrue($claim1FulfilledInRange->isFulfilled());
        self::assertTrue($claim2UnfulfilledInRange->isFulfilled());
    }

    public function test_create_dues_creates_claims_per_member_and_adds_ledger_id() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member1 = $team->createMember(user: $user);
        $member2 = $team->createMember(user: $user);


        $dues = $team->ledger->dues()->create([
            'title' => 'Dues',
            'date_begin' => Carbon::now(),
        ]);

        $this->assertEquals($dues->ledger->id, $dues->claims()->first()->ledger->id);
        $this->assertTrue($dues->claims()->whereTeamMemberId($member1->id)->exists());
        $this->assertTrue($dues->claims()->whereTeamMemberId($member2->id)->exists());
    }

    public function test_adjust_dues_adjust_amount_title_for_related_claims() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountOld = money(50);
        $amountNew = money(100);

        $titleOld = 'Money Dues';
        $titleNew = 'Money Dues Updated';

        $oldDuesMoney = $team->ledger->dues()->create([
            'title' => $titleOld,
            'amount' => $amountOld,
            'date_begin' => Carbon::now()
        ]);

        $claim1 = $oldDuesMoney->claims()->whereTeamMemberId($member->id)->first();
        $this->assertEquals($amountOld, $claim1->amount);
        $this->assertEquals($titleOld, $claim1->title);

        $oldDuesMoney->update([
            'title' => $titleNew,
            'amount' => $amountNew,
        ]);

        $claim1->refresh();
        $this->assertEquals($amountNew, $claim1->amount);
        $this->assertEquals($titleNew, $claim1->title);
    }
    
    public function test_adjust_dues_adjust_item_for_related_claims() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);


        $itemOld = 'Money Dues';
        $itemNew = 'Money Dues Updated';

        $oldDuesMoney = $team->ledger->dues()->create([
            'title' => 'Title',
            'item' => $itemOld,
            'date_begin' => Carbon::now()
        ]);

        $claim1 = $oldDuesMoney->claims()->whereTeamMemberId($member->id)->first();
        $this->assertEquals($itemOld, $claim1->item);

        $oldDuesMoney->update([
            'item' => $itemNew,
        ]);

        $claim1->refresh();
        $this->assertEquals($itemNew, $claim1->item);
    }

    public function test_create_recurring_dues_creates_claims_per_member() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member1 = $team->createMember(user: $user);
        $member2 = $team->createMember(user: $user);

        $claimsPerMember = 3;
        $dateBegin = Carbon::now();
        $rrule = new RRule([
            'DTSTART' => $dateBegin,
            'FREQ' => RRule::DAILY,
            'UNTIL' => $dateBegin->copy()->addDays($claimsPerMember-1)
        ]);

        $dues = $team->ledger->dues()->create([
            'title' => 'Dues',
            'date_begin' => $dateBegin,
            'rrule_string' => $rrule
        ]);

        $claimsMember1 =  $dues->claims()->whereTeamMemberId($member1->id)->get();
        $claimsMember2 =  $dues->claims()->whereTeamMemberId($member2->id)->get();
        $this->assertCount($claimsPerMember, $claimsMember1);
        $this->assertCount($claimsPerMember, $claimsMember2);

        self::assertTrue($claimsMember1[0]->due_date->isSameDay($dateBegin));
        self::assertTrue($claimsMember1[1]->due_date->isSameDay($dateBegin->copy()->addDays(1)));
        self::assertTrue($claimsMember1[2]->due_date->isSameDay($dateBegin->copy()->addDays(2)));
    }

    public function test_create_member_creates_claims_per_due() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $dues = $team->ledger->dues()->create([
            'title' => 'Dues',
            'date_begin' => Carbon::now(),
        ]);

        self::assertEmpty($team->ledger->claims);
        $member1 = $team->createMember(user: $user);
        $member2 = $team->createMember(user: $user);

        $this->assertTrue($dues->claims()->whereTeamMemberId($member1->id)->exists());
        $this->assertTrue($dues->claims()->whereTeamMemberId($member2->id)->exists());
    }

    public function test_create_member_creates_claims_for_recurring_due_only_after_join_date() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $dateBegin = Carbon::now()->subDays(2);
        $rrule = new RRule([
            'DTSTART' => $dateBegin,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()->addDays(1)
        ]);

        $dues = $team->ledger->dues()->create([
            'title' => 'Dues',
            'date_begin' => $dateBegin,
            'rrule_string' => $rrule
        ]);

        $member1 = $team->createMember(user: $user, joinDate: Carbon::now()->subDays(2));
        $member2 = $team->createMember(user: $user, joinDate: Carbon::now()->subDays(1));
        $member3 = $team->createMember(user: $user);

        $claimsMember1 =  $dues->claims()->whereTeamMemberId($member1->id)->get();
        $claimsMember2 =  $dues->claims()->whereTeamMemberId($member2->id)->get();
        $claimsMember3 =  $dues->claims()->whereTeamMemberId($member3->id)->get();
        $this->assertCount(4, $claimsMember1);
        $this->assertCount(3, $claimsMember2);
        $this->assertCount(2, $claimsMember3);
    }
    
    public function test_create_claims_for_recurring_due_only_before_deleted_date() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $dateBegin = Carbon::now()->subDays(2);
        $member1 = $team->createMember(user: $user, joinDate: $dateBegin);
        $member1->delete();
        
        $rrule = new RRule([
            'DTSTART' => $dateBegin,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()->addDays(2)
        ]);

        $dues = $team->ledger->dues()->create([
            'title' => 'Dues',
            'date_begin' => $dateBegin,
            'rrule_string' => $rrule
        ]);
        
        $claimsMember1 =  $dues->claims()->whereTeamMemberId($member1->id)->get();
        $this->assertCount(2, $claimsMember1);
    }

    public function test_update_dues_dateBegin_updates_claims_dueDate() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $dateOld = Carbon::now();
        $dateNew = Carbon::now()->addDays(2);

        $dues = $team->ledger->dues()->create([
            'title' => 'Dues',
            'date_begin' => $dateOld,
        ]);

        $claim = $dues->claims()->first();
        $dues->refresh();
        assertTrue($claim->due_date->isSameDay($dateOld));
        self::assertCount(1, $dues->claims);

        $dues->update([
            'date_begin' => $dateNew
        ]);

        $claim->refresh();
        $dues->refresh();
        assertTrue($claim->due_date->isSameDay($dateNew));
        self::assertCount(1, $dues->claims, 'duplicates were created instead moving dueDate');
    }

    public function test_add_child_dues_soft_deletes_parent() : void {
        $team = $this->createTeam();

        $parent = $team->ledger->dues()->create([
            'title' => 'Parent',
            'date_begin' => Carbon::now(),
        ]);
        $child = $team->ledger->dues()->create([
            'title' => 'Child',
            'date_begin' => Carbon::now(),
            'parent_id' => $parent->id,
        ]);

        $this->assertSoftDeleted($parent);
        $this->assertNotSoftDeleted($child);
    }

    public function test_add_child_dues_adjust_parent_date_begin_if_same() : void {
        $team = $this->createTeam();

        $parent = $team->ledger->dues()->create([
            'title' => 'Parent',
            'date_begin' => Carbon::now(),
        ]);
        $child = $team->ledger->dues()->create([
            'title' => 'Child',
            'date_begin' => Carbon::now(),
            'parent_id' => $parent->id,
        ]);

        $parent->refresh();
        self::assertTrue($parent->date_begin->isSameDay($child->date_begin->copy()->subDay()));
    }

    public function test_add_child_dues_does_not_adjust_parent_date_begin_if_not_same() : void {
        $team = $this->createTeam();

        $dateBegin = Carbon::now()->subDays(2);
        $parent = $team->ledger->dues()->create([
            'title' => 'Parent',
            'date_begin' => $dateBegin,
        ]);
        $child = $team->ledger->dues()->create([
            'title' => 'Child',
            'date_begin' => Carbon::now(),
            'parent_id' => $parent->id,
        ]);

        $parent->refresh();
        self::assertTrue($parent->date_begin->isSameDay($dateBegin));
    }
    
    public function test_add_child_dues_does_throws_exception_when_when_date_begin_before_parent() : void {
        $team = $this->createTeam();

        $dateBegin = Carbon::now();
        $parent = $team->ledger->dues()->create([
            'title' => 'Parent',
            'date_begin' => $dateBegin,
        ]);

        $this->assertThrows(function () use ($team, $dateBegin, $parent) {
            $child = $team->ledger->dues()->create([
                'title' => 'Child',
                'date_begin' => $dateBegin->copy()->subDays(),
                'parent_id' => $parent->id,
            ]);
        }, \InvalidArgumentException::class);
    }

    public function test_update_child_due_date_begin_adjust_parent_date_begin_if_same() : void {
        $team = $this->createTeam();

        $parent = $team->ledger->dues()->create([
            'title' => 'Parent',
            'date_begin' => Carbon::now(),
        ]);
        $child = $team->ledger->dues()->create([
            'title' => 'Child',
            'date_begin' => Carbon::now(),
            'parent_id' => $parent->id,
        ]);

        $parent->refresh();
        self::assertTrue($parent->date_begin->isSameDay($child->date_begin->copy()->subDay()));
    }

    public function test_create_recurring_due_cannot_be_infinite(): void {
        $team = $this->createTeam();

        $dateBegin = Carbon::now()->subDays(3);

        $rruleInfinite = new RRule([
            'DTSTART' => $dateBegin,
            'FREQ' => RRule::DAILY,
        ]);

        $rruleFinite = new RRule([
            'DTSTART' => $dateBegin,
            'FREQ' => RRule::DAILY,
            'UNTIL' => $dateBegin->copy()->addDays(2)
        ]);

        $this->assertThrows(function () use ($team, $rruleInfinite, $dateBegin) {
            $team->ledger->dues()->create([
                'title' => 'Parent',
                'date_begin' => $dateBegin,
                'rrule_string' => $rruleInfinite
            ]);
        }, HttpException::class);

        $team->ledger->dues()->create([
            'title' => 'Parent',
            'date_begin' => $dateBegin,
            'rrule_string' => $rruleFinite
        ]);

    }

    public function test_recurring_add_child_due_sets_recurring_end_date_to_parent() : void {
        $team = $this->createTeam();
        $dateBeginParent = Carbon::today()->subDays(3);
        $dateBeginChild = Carbon::today();
        $rruleParent = new RRule([
            'DTSTART' => $dateBeginParent,
            'FREQ' => RRule::DAILY,
            'UNTIL' => $dateBeginChild
        ]);
        $rruleChild = new RRule([
            'DTSTART' => $dateBeginChild,
            'FREQ' => RRule::DAILY,
            'UNTIL' => $dateBeginChild->copy()->addDays(2)
        ]);

        $parent = $team->ledger->dues()->create([
            'title' => 'Parent',
            'date_begin' => $dateBeginParent,
            'rrule_string' => $rruleParent
        ]);
        $child = $team->ledger->dues()->create([
            'title' => 'Child',
            'date_begin' => $dateBeginChild,
            'rrule_string' => $rruleChild,
            'parent_id' => $parent->id,
        ]);

        $parent->refresh();

        $parentDateEnd = Carbon::create($parent->getRRule()->getRule()['UNTIL'])->setTimezone(config('app.timezone'));
        self::assertTrue($parentDateEnd->isSameDay($dateBeginChild->copy()->subDay()));
    }

    public function test_update_recurring_dues_dateBegin_updates_parent_recurring_end_date() : void {
        $team = $this->createTeam();
        $dateBeginParent = Carbon::today()->subDays(3);
        $dateBeginChild = Carbon::today();
        $dateBeginChildUpdated = Carbon::today()->addDays(2);
        $rruleParent = new RRule([
            'DTSTART' => $dateBeginParent,
            'FREQ' => RRule::DAILY,
            'UNTIL' => $dateBeginChild
        ]);
        $rruleChild = new RRule([
            'DTSTART' => $dateBeginChild,
            'FREQ' => RRule::DAILY,
            'UNTIL' => $dateBeginChild->copy()->addDays(2)
        ]);

        $parent = $team->ledger->dues()->create([
            'title' => 'Parent',
            'date_begin' => $dateBeginParent,
            'rrule_string' => $rruleParent
        ]);
        $child = $team->ledger->dues()->create([
            'title' => 'Child',
            'date_begin' => $dateBeginChild,
            'rrule_string' => $rruleChild,
            'parent_id' => $parent->id,
        ]);

        $child->update([
            'date_begin' => $dateBeginChildUpdated
        ]);

        $parent->refresh();

        $parentDateEnd = Carbon::create($parent->getRRule()->getRule()['UNTIL'])->setTimezone(config('app.timezone'));
        self::assertTrue($parentDateEnd->isSameDay($dateBeginChildUpdated->copy()->subDay()));
    }

    public function test_add_child_dues_adjust_reference_and_amount_title_for_claims_in_range() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $dateBeginOld = Carbon::now()->subDays(5)->startOfDay();
        $member = $team->createMember(user: $user, joinDate: $dateBeginOld);

        $amountOld = money(50);
        $amountNew = money(100);

        $rruleOldDues = new RRule([
            'DTSTART' => $dateBeginOld,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()
        ]);

        $oldDuesMoney = $team->ledger->dues()->create([
            'title' => 'Parent Money',
            'amount' => $amountOld,
            'date_begin' => $dateBeginOld,
            'rrule_string' => $rruleOldDues
        ]);

        $claim1InRange = $oldDuesMoney->claims()->whereTeamMemberId($member->id)->whereDate('due_date', '=', Carbon::now())->first();
        $claim2InRange = $oldDuesMoney->claims()->whereTeamMemberId($member->id)->whereDate('due_date', '=', Carbon::now()->subDays(2))->first();
        $claimOutOfRange = $oldDuesMoney->claims()->whereTeamMemberId($member->id)->whereDate('due_date', '=', Carbon::now()->subDays(4))->first();

        $dateBeginNew = Carbon::now()->subDays(2)->startOfDay();
        $rruleNewDues = new RRule([
            'DTSTART' => $dateBeginNew,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()->addDays(2)
        ]);

        $newDuesMoney = $team->ledger->dues()->create([
            'title' => 'Child Money',
            'amount' => $amountNew,
            'parent_id' => $oldDuesMoney->id,
            'date_begin' => $dateBeginNew,
            'rrule_string' => $rruleNewDues
        ]);

        $claim1InRange->refresh();
        $claim2InRange->refresh();
        $claimOutOfRange->refresh();
        $oldDuesMoney->refresh();
        $newDuesMoney->refresh();

        $this->assertEquals($amountOld, $claimOutOfRange->amount);
        $this->assertEquals($oldDuesMoney->id, $claimOutOfRange->claimable->id);
        $this->assertEquals($oldDuesMoney->title, $claimOutOfRange->title);

        $this->assertEquals($amountNew, $claim1InRange->amount);
        $this->assertEquals($newDuesMoney->id, $claim1InRange->claimable->id);
        $this->assertEquals($newDuesMoney->title, $claim1InRange->title);

        $this->assertEquals($amountNew, $claim2InRange->amount);
        $this->assertEquals($newDuesMoney->id, $claim2InRange->claimable->id);
        $this->assertEquals($newDuesMoney->title, $claim2InRange->title);
    }

    public function test_add_child_dues_adjust_reference_and_item_title_for_unfulfilled_claims_in_range_only() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $dateBeginOld = Carbon::now()->subDays(5)->startOfDay();
        $member = $team->createMember(user: $user, joinDate: $dateBeginOld);

        $itemOld = 'old Kuchen';
        $itemNew = 'new Muffins';

        $rruleOldDues = new RRule([
            'DTSTART' => $dateBeginOld,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()
        ]);

        $oldDuesItem = $team->ledger->dues()->create([
            'title' => 'Parent Item',
            'item' => $itemOld,
            'date_begin' => $dateBeginOld,
            'rrule_string' => $rruleOldDues
        ]);

        $claim1InRange = $oldDuesItem->claims()->whereTeamMemberId($member->id)->whereDate('due_date', '=', Carbon::now())->first();
        $claim2InRange = $oldDuesItem->claims()->whereTeamMemberId($member->id)->whereDate('due_date', '=', Carbon::now()->subDays(2))->first();
        $claimOutOfRange = $oldDuesItem->claims()->whereTeamMemberId($member->id)->whereDate('due_date', '=', Carbon::now()->subDays(4))->first();
        $claimFulfilledInRange = $oldDuesItem->claims()->whereTeamMemberId($member->id)->whereDate('due_date', '=', Carbon::now()->subDays(1))->first();
        $claimFulfilledInRange->setFulfilled();

        $dateBeginNew = Carbon::now()->subDays(2)->startOfDay();
        $rruleNewDues = new RRule([
            'DTSTART' => $dateBeginNew,
            'FREQ' => RRule::DAILY,
            'UNTIL' => $dateBeginNew->copy()->addDays(4)
        ]);
        $newDuesItem = $team->ledger->dues()->create([
            'title' => 'Child Item',
            'item' => $itemNew,
            'date_begin' => $dateBeginNew,
            'parent_id' => $oldDuesItem->id,
            'rrule_string' => $rruleNewDues
        ]);

        $claim1InRange->refresh();
        $claim2InRange->refresh();
        $claimOutOfRange->refresh();
        $claimFulfilledInRange->refresh();
        $oldDuesItem->refresh();
        $newDuesItem->refresh();

        $this->assertEquals($itemOld, $claimOutOfRange->item);
        $this->assertEquals($oldDuesItem->id, $claimOutOfRange->claimable->id);
        $this->assertEquals($oldDuesItem->title, $claimOutOfRange->title);

        $this->assertEquals($itemNew, $claim1InRange->item);
        $this->assertEquals($newDuesItem->id, $claim1InRange->claimable->id);
        $this->assertEquals($newDuesItem->title, $claim1InRange->title);

        $this->assertEquals($itemNew, $claim2InRange->item);
        $this->assertEquals($newDuesItem->id, $claim2InRange->claimable->id);
        $this->assertEquals($newDuesItem->title, $claim2InRange->title);

        $this->assertEquals($itemOld, $claimFulfilledInRange->item);
        $this->assertEquals($oldDuesItem->id, $claimFulfilledInRange->claimable->id);
        $this->assertEquals($oldDuesItem->title, $claimFulfilledInRange->title);
    }

    public function test_add_child_dues_mark_money_claims_as_unfulfilled_for_claims_in_range() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $dateBeginOld = Carbon::now()->subDays(5)->startOfDay();
        $member = $team->createMember(user: $user, joinDate: $dateBeginOld);

        $amountOld = money(10);
        $amountNew = money(100);

        $rruleOldDues = new RRule([
            'DTSTART' => $dateBeginOld,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()
        ]);
        $oldDuesMoney = $team->ledger->dues()->create([
            'title' => 'Parent Money',
            'amount' => $amountOld,
            'date_begin' => $dateBeginOld,
            'rrule_string' => $rruleOldDues
        ]);

        $claim1FulfilledInRange = $oldDuesMoney->claims()->whereTeamMemberId($member->id)->whereDate('due_date', '=', Carbon::now()->subDays(2))->first();
        $claim2FulfilledInRange = $oldDuesMoney->claims()->whereTeamMemberId($member->id)->whereDate('due_date', '=', Carbon::now())->first();
        $claimFulfilledOutOfRange = $oldDuesMoney->claims()->whereTeamMemberId($member->id)->whereDate('due_date', '=', Carbon::now()->subDays(4))->first();

        $claim1FulfilledInRange->setFulfilled();
        $claim2FulfilledInRange->setFulfilled();
        $claimFulfilledOutOfRange->setFulfilled();

        $dateBeginNew = Carbon::now()->subDays(2)->startOfDay();
        $rruleNewDues = new RRule([
            'DTSTART' => $dateBeginNew,
            'FREQ' => RRule::DAILY,
            'UNTIL' => $dateBeginNew->copy()->addDays(4)
        ]);
        $team->ledger->dues()->create([
            'title' => 'Child Money',
            'amount' => $amountNew,
            'parent_id' => $oldDuesMoney->id,
            'date_begin' => $dateBeginNew,
            'rrule_string' => $rruleNewDues
        ]);

        $claim1FulfilledInRange->refresh();
        $claim2FulfilledInRange->refresh();
        $claimFulfilledOutOfRange->refresh();

        $this->assertTrue($claimFulfilledOutOfRange->isFulfilled());
        $this->assertFalse($claim1FulfilledInRange->isFulfilled());
        $this->assertFalse($claim2FulfilledInRange->isFulfilled());
    }

    public function test_add_child_dues_processes_money_claims_to_fulfill_for_claims_in_range() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $dateBeginOld = Carbon::today()->subDays(2);
        $member = $team->createMember(user: $user, joinDate: $dateBeginOld);

        $amountCredit = money(100);
        $amountPerClaimOld = money(40); // 3x claims = 120
        $amountPerClaimNew = money(30); // 1x old, 2x new amount = 100

        $rruleOldDues = new RRule([
            'DTSTART' => $dateBeginOld,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::today()
        ]);
        $oldDuesMoney = $team->ledger->dues()->create([
            'title' => 'Parent Money',
            'amount' => $amountPerClaimOld,
            'date_begin' => $dateBeginOld,
            'rrule_string' => $rruleOldDues
        ]);

        $claim1FulfilledInRange = $oldDuesMoney->claims()->whereTeamMemberId($member->id)->whereDate('due_date', '=', Carbon::today()->subDays(1))->first();
        $claim2UnfulfilledInRange = $oldDuesMoney->claims()->whereTeamMemberId($member->id)->whereDate('due_date', '=', Carbon::today())->first();
        $claimFulfilledOutOfRange = $oldDuesMoney->claims()->whereTeamMemberId($member->id)->whereDate('due_date', '=', Carbon::today()->subDays(2))->first();

        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amountCredit, 'team_member_id' => $member->id],
        ]);

        $claim1FulfilledInRange->refresh();
        $claim2UnfulfilledInRange->refresh();
        $claimFulfilledOutOfRange->refresh();

        self::assertEquals($amountPerClaimOld->multiply(3)->subtract($amountCredit), $team->ledger->getCreditBalance());
        self::assertTrue($claimFulfilledOutOfRange->isFulfilled());
        self::assertTrue($claim1FulfilledInRange->isFulfilled());
        self::assertFalse($claim2UnfulfilledInRange->isFulfilled());

        $dateBeginNew = Carbon::today()->subDays(1);
        $rruleNewDues = new RRule([
            'DTSTART' => $dateBeginNew,
            'FREQ' => RRule::DAILY,
            'UNTIL' => $dateBeginNew->copy()->addDays(4)
        ]);
        $team->ledger->dues()->create([
            'title' => 'Child Money',
            'amount' => $amountPerClaimNew,
            'parent_id' => $oldDuesMoney->id,
            'date_begin' => $dateBeginNew,
            'rrule_string' => $rruleNewDues
        ]);

        $claim1FulfilledInRange->refresh();
        $claim2UnfulfilledInRange->refresh();
        $claimFulfilledOutOfRange->refresh();

        self::assertEquals(money(0), $team->ledger->getCreditBalance());
        self::assertTrue($claimFulfilledOutOfRange->isFulfilled());
        self::assertTrue($claim1FulfilledInRange->isFulfilled());
        self::assertTrue($claim2UnfulfilledInRange->isFulfilled());
    }

    public function test_update_dues_dateBegin_to_later_adjust_parents_claims_reference_and_process_claims() : void {
        // TODO split into multiple smaller tests?
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $dateBeginOld = Carbon::now()->subDays(2)->startOfDay();
        $member = $team->createMember(user: $user, joinDate: $dateBeginOld);

        $amountCredit = money(110);
        $amountPerClaimOld = money(40); // 3x claims = 120
        $amountPerClaimNew = money(30);

        $rruleOldDues = new RRule([
            'DTSTART' => $dateBeginOld,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()
        ]);
        $oldDuesMoney = $team->ledger->dues()->create([
            'title' => 'Parent Money',
            'amount' => $amountPerClaimOld,
            'date_begin' => $dateBeginOld,
            'rrule_string' => $rruleOldDues
        ]);

        $claimFulfilledOutOfRange = $oldDuesMoney->claims()->whereTeamMemberId($member->id)->whereDate('due_date', '=', Carbon::now()->subDays(2))->first();
        $claimFulfilledInRange = $oldDuesMoney->claims()->whereTeamMemberId($member->id)->whereDate('due_date', '=', Carbon::now()->subDays(1))->first();
        $claimUnfulfilledInRange = $oldDuesMoney->claims()->whereTeamMemberId($member->id)->whereDate('due_date', '=', Carbon::now())->first();

        $team->ledger->transactions()->createMany([
            ['title' => 'test', 'amount' => $amountCredit, 'team_member_id' => $member->id],
        ]);

        $claimFulfilledOutOfRange->refresh();
        $claimFulfilledInRange->refresh();
        $claimUnfulfilledInRange->refresh();

        // 2x old amount = 80 => 30 credit
        self::assertEquals(money(30), $team->ledger->getCreditBalance());
        self::assertTrue($claimFulfilledOutOfRange->isFulfilled());
        self::assertTrue($claimFulfilledInRange->isFulfilled());
        self::assertFalse($claimUnfulfilledInRange->isFulfilled());

        $dateBeginNew = Carbon::now()->subDays(1)->startOfDay();
        $rruleNewDues = new RRule([
            'DTSTART' => $dateBeginNew,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()
        ]);
        $newDuesMoney = $team->ledger->dues()->create([
            'title' => 'Child Money',
            'amount' => $amountPerClaimNew,
            'parent_id' => $oldDuesMoney->id,
            'date_begin' => $dateBeginNew,
            'rrule_string' => $rruleNewDues
        ]);

        $claimFulfilledOutOfRange->refresh();
        $claimFulfilledInRange->refresh();
        $claimUnfulfilledInRange->refresh();
        $team->ledger->refresh();


        // 1x old, 2x new amount = 100 => 10 credit
        self::assertEquals(money(10), $team->ledger->getCreditBalance());
        self::assertTrue($claimFulfilledOutOfRange->isFulfilled());
        self::assertTrue($claimFulfilledInRange->isFulfilled());
        self::assertTrue($claimUnfulfilledInRange->isFulfilled());
        self::assertEquals($oldDuesMoney->id, $claimFulfilledOutOfRange->claimable_id);
        self::assertEquals($newDuesMoney->id, $claimFulfilledInRange->claimable_id);
        self::assertEquals($newDuesMoney->id, $claimUnfulfilledInRange->claimable_id);

        // update
        $dateBeginUpdated = Carbon::now()->startOfDay();
        $rruleUpdatedDues = new RRule([
            'DTSTART' => $dateBeginUpdated,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()
        ]);
        $newDuesMoney->update([
            'date_begin' => $dateBeginUpdated,
            'rrule_string' => $rruleUpdatedDues
        ]);

        $claimFulfilledOutOfRange->refresh();
        $claimFulfilledInRange->refresh();
        $claimUnfulfilledInRange->refresh();
        $team->ledger->refresh();

        // 2x old, 1x new amount = 110 => 0 credit
        self::assertEquals(money(0), $team->ledger->getCreditBalance());
        self::assertTrue($claimFulfilledOutOfRange->isFulfilled());
        self::assertTrue($claimFulfilledInRange->isFulfilled());
        self::assertTrue($claimUnfulfilledInRange->isFulfilled());
        self::assertEquals($oldDuesMoney->id, $claimFulfilledOutOfRange->claimable_id);
        self::assertEquals($oldDuesMoney->id, $claimFulfilledInRange->claimable_id);
        self::assertEquals($newDuesMoney->id, $claimUnfulfilledInRange->claimable_id);
    }

    public function test_update_recurring_dues_dateBegin_creates_missing_claims() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $dateBeginUpdated = Carbon::now()->startOfMonth()->subMonths(1);
        $member1 = $team->createMember(user: $user, joinDate: $dateBeginUpdated);
        $member2 = $team->createMember(user: $user, joinDate: $dateBeginUpdated);

        $dateBeginStart = Carbon::now()->startOfMonth();
        $rruleStartDues = new RRule([
            'DTSTART' => $dateBeginStart,
            'FREQ' => RRule::MONTHLY,
            'BYMONTHDAY' => 1,
            'UNTIL' => Carbon::now()->endOfMonth()->addMonths(2)
        ]);
        
        // DTSTART:20250201T000000Z
        //RRULE:FREQ=MONTHLY;BYMONTHDAY=1;UNTIL=20251231T000000Z
        
        $newDues = $team->ledger->dues()->create([
            'title' => 'Child',
            'date_begin' => $dateBeginStart,
            'rrule_string' => $rruleStartDues
        ]);

        self::assertCount(6, $newDues->claims()->get()); // 3 month x 2 member

        $rruleUpdatedDues = new RRule([
            'DTSTART' => $dateBeginUpdated,
            'FREQ' => RRule::MONTHLY,
            'BYMONTHDAY' => 1,
            'UNTIL' => Carbon::now()->endOfMonth()->addMonths(2)
        ]);
        $newDues->update([
            'date_begin' => $dateBeginUpdated,
            'rrule_string' => $rruleUpdatedDues
        ]);

        self::assertCount(8, $newDues->claims()->get()); // 4 month x 2 member
    }

    public function test_update_recurring_dues_dateBegin_removes_claims() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $dateBeginStart = Carbon::now()->subDays(1);
        $member1 = $team->createMember(user: $user, joinDate: $dateBeginStart);
        $member2 = $team->createMember(user: $user, joinDate: $dateBeginStart);

        $rruleStartDues = new RRule([
            'DTSTART' => $dateBeginStart,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()->addDays(1)
        ]);
        $newDues = $team->ledger->dues()->create([
            'title' => 'Child',
            'date_begin' => $dateBeginStart,
            'rrule_string' => $rruleStartDues
        ]);

        self::assertCount(6, $newDues->claims()->get()); // 3 days x 2 member

        $dateBeginUpdated = Carbon::now();
        $rruleUpdatedDues = new RRule([
            'DTSTART' => $dateBeginUpdated,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()
        ]);
        $newDues->update([
            'date_begin' => $dateBeginUpdated,
            'rrule_string' => $rruleUpdatedDues
        ]);

        self::assertCount(2, $newDues->claims()->get()); // 1 day x 2 member
    }

    public function test_update_recurring_dues_dateEnd_removes_claims() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $dateBeginStart = Carbon::now()->subDays(1);
        $dateEndStart = Carbon::now()->addDays(1);
        $member1 = $team->createMember(user: $user, joinDate: $dateBeginStart);
        $member2 = $team->createMember(user: $user, joinDate: $dateBeginStart);

        $rruleStartDues = new RRule([
            'DTSTART' => $dateBeginStart,
            'FREQ' => RRule::DAILY,
            'UNTIL' => $dateEndStart
        ]);
        $newDues = $team->ledger->dues()->create([
            'title' => 'Child',
            'date_begin' => $dateBeginStart,
            'rrule_string' => $rruleStartDues
        ]);

        self::assertCount(6, $newDues->claims()->get()); // 3 days x 2 member

        $dateBeginUpdated = Carbon::now();
        $rruleUpdatedDues = new RRule([
            'DTSTART' => $dateBeginStart,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()
        ]);
        $newDues->update([
            'rrule_string' => $rruleUpdatedDues
        ]);

        self::assertCount(4, $newDues->claims()->get()); // 2 day x 2 member
    }

    public function test_add_recurring_child_dues_creates_missing_claims_only() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $dateBeginOld = Carbon::now()->subDays(2)->startOfDay();
        $member1 = $team->createMember(user: $user, joinDate: $dateBeginOld);
        $member2 = $team->createMember(user: $user, joinDate: $dateBeginOld);

        $rruleOldDues = new RRule([
            'DTSTART' => $dateBeginOld,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()
        ]);
        $oldDues = $team->ledger->dues()->create([
            'title' => 'Parent',
            'date_begin' => $dateBeginOld,
            'rrule_string' => $rruleOldDues
        ]);

        self::assertCount(6, $oldDues->claims()->get()); // 3 days x 2 member

        $dateBeginNew = Carbon::now()->subDays(1)->startOfDay();
        $rruleNewDues = new RRule([
            'DTSTART' => $dateBeginNew,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()->addDays(1)
        ]);
        $newDues = $team->ledger->dues()->create([
            'title' => 'Child',
            'parent_id' => $oldDues->id,
            'date_begin' => $dateBeginNew,
            'rrule_string' => $rruleNewDues
        ]);
        self::assertCount(2, $oldDues->claims()->get()); // 1 day x 2 member
        self::assertCount(6, $newDues->claims()->get()); // 3 days x 2 member
    }

    public function test_update_recurring_child_dues_dateBegin_creates_missing_claims_only() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $dateBeginOld = Carbon::now()->subDays(2)->startOfDay();
        $member1 = $team->createMember(user: $user, joinDate: $dateBeginOld);
        $member2 = $team->createMember(user: $user, joinDate: $dateBeginOld);

        $rruleOldDues = new RRule([
            'DTSTART' => $dateBeginOld,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()
        ]);
        $oldDues = $team->ledger->dues()->create([
            'title' => 'Parent',
            'date_begin' => $dateBeginOld,
            'rrule_string' => $rruleOldDues
        ]);

        self::assertCount(6, $oldDues->claims()->get()); // 3 days x 2 member

        $dateBeginNew = Carbon::now()->startOfDay();
        $rruleNewDues = new RRule([
            'DTSTART' => $dateBeginNew,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()->addDays(1)
        ]);
        $newDues = $team->ledger->dues()->create([
            'title' => 'Child',
            'parent_id' => $oldDues->id,
            'date_begin' => $dateBeginNew,
            'rrule_string' => $rruleNewDues
        ]);

        self::assertCount(4, $oldDues->claims()->get()); // 2 days x 2 member
        self::assertCount(4, $newDues->claims()->get()); // 2 days x 2 member

        $dateBeginUpdated = Carbon::now()->subDays(1)->startOfDay();
        $rruleUpdatedDues = new RRule([
            'DTSTART' => $dateBeginUpdated,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()->addDays(1)
        ]);
        $newDues->update([
            'date_begin' => $dateBeginUpdated,
            'rrule_string' => $rruleUpdatedDues
        ]);

        self::assertCount(2, $oldDues->claims()->get()); // 1 days x 2 member
        self::assertCount(6, $newDues->claims()->get()); // 3 days x 2 member
    }

    public function test_add_child_claimable_changes_exempt_claims_but_preserve_exempt_state() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountOld = money(40);
        $amountNew = money(30);

        $itemOld = 'old Kuchen';
        $itemNew = 'new Muffins';

        $oldDuesMoney = $team->ledger->dues()->create(['title' => 'Parent Money', 'amount' => $amountOld, 'date_begin' => Carbon::now()]);
        $oldFineMoney = $team->ledger->fines()->create(['title' => 'Parent Money', 'amount' => $amountOld, 'date_begin' => Carbon::now()]);
        $oldDuesItem = $team->ledger->dues()->create(['title' => 'Parent Item', 'item' => $itemOld, 'date_begin' => Carbon::now()]);
        $oldFineItem = $team->ledger->fines()->create(['title' => 'Parent Item', 'item' => $itemOld, 'date_begin' => Carbon::now()]);

//        self::fail('do not create claims for dues manually');
        $claimDuesMoney = $oldDuesMoney->claims()->first()->setExempt();
        $claimDuesItem = $oldDuesItem->claims()->first()->setExempt();

        $claimFineMoney = $oldFineMoney->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now(), 'exempt' => true]);
        $claimFineItem = $oldFineItem->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now(), 'exempt' => true]);

        $newDuesMoney = $team->ledger->dues()->create(['title' => 'Child Money', 'amount' => $amountNew, 'date_begin' => Carbon::now(), 'parent_id' => $oldDuesMoney->id,]);
        $newFineMoney = $team->ledger->fines()->create(['title' => 'Child Money', 'amount' => $amountNew, 'date_begin' => Carbon::now(), 'parent_id' => $oldFineMoney->id,]);
        $newDuesItem = $team->ledger->dues()->create(['title' => 'Child Item', 'item' => $itemNew, 'date_begin' => Carbon::now(), 'parent_id' => $oldDuesItem->id,]);
        $newFineItem = $team->ledger->fines()->create(['title' => 'Child Item', 'item' => $itemNew, 'date_begin' => Carbon::now(), 'parent_id' => $oldFineItem->id,]);

        $claimDuesMoney->refresh();
        $claimFineMoney->refresh();
        $claimDuesItem->refresh();
        $claimFineItem->refresh();

        self::assertTrue($claimDuesMoney->exempt);
        self::assertEquals($amountNew, $claimDuesMoney->amount);

        self::assertTrue($claimFineMoney->exempt);
        self::assertEquals($amountNew, $claimFineMoney->amount);

        self::assertTrue($claimDuesItem->exempt);
        self::assertEquals($itemNew, $claimDuesItem->item);

        self::assertTrue($claimFineItem->exempt);
        self::assertEquals($itemNew, $claimFineItem->item);
    }

    public function test_add_recurring_child_due_to_non_recurring_parent_fails(): void {
        $team = $this->createTeam();
        $oldDues = $team->ledger->dues()->create([
            'title' => 'Parent Money',
            'date_begin' => Carbon::now(),
        ]);

        $dateBeginNew = Carbon::now();
        $rruleNewDues = new RRule([
            'DTSTART' => $dateBeginNew,
            'FREQ' => RRule::WEEKLY,
            'UNTIL' => $dateBeginNew->copy()->addDays(4)
        ]);

        $this->assertThrows(function () use ($rruleNewDues, $dateBeginNew, $oldDues, $team) {
            $team->ledger->dues()->create([
                'title' => 'Child Money',
                'parent_id' => $oldDues->id,
                'date_begin' => $dateBeginNew,
                'rrule_string' => $rruleNewDues
            ]);
        }, \InvalidArgumentException::class);
    }

    public function test_update_recurring_child_due_to_non_recurring_parent_fails(): void {
        $team = $this->createTeam();
        $oldDues = $team->ledger->dues()->create([
            'title' => 'Parent Money',
            'date_begin' => Carbon::now(),
        ]);

        $dateBeginNew = Carbon::now();
        $rruleNewDues = new RRule([
            'DTSTART' => $dateBeginNew,
            'FREQ' => RRule::WEEKLY,
            'UNTIL' => $dateBeginNew->copy()->addDays(4)
        ]);

        $newDues = $team->ledger->dues()->create([
            'title' => 'Child Money',
            'parent_id' => $oldDues->id,
            'date_begin' => $dateBeginNew,
        ]);

        $this->assertThrows(function () use ($newDues, $rruleNewDues) {
            $newDues->update([
                'rrule_string' => $rruleNewDues
            ]);
        }, \InvalidArgumentException::class);
    }

    public function test_add_non_recurring_child_due_to_recurring_parent_fails(): void {
        $team = $this->createTeam();
        $dateBegin = Carbon::now();
        $rruleOldDues = new RRule([
            'DTSTART' => $dateBegin,
            'FREQ' => RRule::WEEKLY,
            'UNTIL' => $dateBegin->copy()->addDays(4)
        ]);
        $oldDues = $team->ledger->dues()->create([
            'title' => 'Parent Money',
            'date_begin' => $dateBegin,
            'rrule_string' => $rruleOldDues,
        ]);

        $this->assertThrows(function () use ($dateBegin, $oldDues, $team) {
            $team->ledger->dues()->create([
                'title' => 'Child Money',
                'parent_id' => $oldDues->id,
                'date_begin' => $dateBegin,
            ]);
        }, \InvalidArgumentException::class);
    }

    public function test_update_non_recurring_child_due_to_recurring_parent_fails(): void {
        $team = $this->createTeam();
        $dateBegin = Carbon::now();
        $rruleOldDues = new RRule([
            'DTSTART' => $dateBegin,
            'FREQ' => RRule::WEEKLY,
            'UNTIL' => $dateBegin->copy()->addDays(4)
        ]);
        $oldDues = $team->ledger->dues()->create([
            'title' => 'Parent Money',
            'date_begin' => $dateBegin,
            'rrule_string' => $rruleOldDues,
        ]);

        $newDues = $team->ledger->dues()->create([
            'title' => 'Child Money',
            'parent_id' => $oldDues->id,
            'date_begin' => $dateBegin,
            'rrule_string' => $rruleOldDues,
        ]);

        $this->assertThrows(function () use ($newDues, $dateBegin, $oldDues, $team) {
            $newDues->update([
                'rrule_string' => null,
            ]);
        }, \InvalidArgumentException::class);
    }

    public function test_add_child_dues_with_overlapping_occurrences_have_matching_recurring_rule(): void {
        $team = $this->createTeam();
        $dateBeginOld = Carbon::now()->subDays(3);
        $rruleOldDues = new RRule([
            'DTSTART' => $dateBeginOld,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()
        ]);
        $oldDues = $team->ledger->dues()->create([
            'title' => 'Parent Money',
            'date_begin' => $dateBeginOld,
            'rrule_string' => $rruleOldDues
        ]);

        $dateBeginNew = Carbon::now()->subDays(1);
        $rruleNewDues = new RRule([
            'DTSTART' => $dateBeginNew,
            'FREQ' => RRule::DAILY,
            'UNTIL' => $dateBeginNew->copy()->addDays(4)
        ]);

        $newDues = $team->ledger->dues()->create([
                'title' => 'Child Money',
                'parent_id' => $oldDues->id,
                'date_begin' => $dateBeginNew,
                'rrule_string' => $rruleNewDues
            ]);

        self::assertInstanceOf(TeamLedgerDues::class, $newDues);
    }

    public function test_add_child_dues_with_overlapping_occurrences_fails_without_matching_recurring_rule(): void {
        $team = $this->createTeam();
        $dateBeginOld = Carbon::now()->subDays(3);
        $rruleOldDues = new RRule([
            'DTSTART' => $dateBeginOld,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()
        ]);
        $oldDues = $team->ledger->dues()->create([
            'title' => 'Parent Money',
            'date_begin' => $dateBeginOld,
            'rrule_string' => $rruleOldDues
        ]);

        $dateBeginNew = Carbon::now()->subDays(1);
        $rruleNewDues = new RRule([
            'DTSTART' => $dateBeginNew,
            'FREQ' => RRule::WEEKLY,
            'UNTIL' => $dateBeginNew->copy()->addDays(4)
        ]);

        $this->assertThrows(function () use ($rruleNewDues, $dateBeginNew, $oldDues, $team) {
            $team->ledger->dues()->create([
                'title' => 'Child Money',
                'parent_id' => $oldDues->id,
                'date_begin' => $dateBeginNew,
                'rrule_string' => $rruleNewDues
            ]);
        }, \InvalidArgumentException::class);
    }


    public function test_soft_delete_fine_or_duee_does_not_delete_claims() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amount = money(10);
        $fine = $team->ledger->fines()->create(['title' => 'Fine', 'date_begin' => Carbon::now(), 'amount' => $amount]);
        $dues = $team->ledger->dues()->create(['title' => 'Due', 'date_begin' => Carbon::now(), 'amount' => $amount]);
        $claimFine = $fine->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now(),]);
        $claimDue = $dues->claims()->first();

        assertEquals($amount->multiply(2), $team->ledger->getClaimBalance());

        $fine->delete();
        $dues->delete();

        $claimFine->refresh();
        $claimDue->refresh();
        $team->ledger->refresh();

        self::assertTrue($claimFine->exists);
        self::assertTrue($claimDue->exists);

        assertEquals($amount->multiply(2), $team->ledger->getClaimBalance());
    }
    
    public function test_soft_delete_recurring_due_does_sets_end_date_and_delete_future_claims() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $claimCountOriginal = 10;
        $claimCountNew = 4;
        
        $dateEnd = Carbon::now()->addDays($claimCountNew); // -1 since begin and end date are included in claim count

        $rrule = new RRule([
            'DTSTART' => Carbon::now()->addDays(1)->startOfDay(),
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()->addDays($claimCountOriginal)
        ]);
        
        $dues = $team->ledger->dues()->create([
            'title' => 'Due', 
            'date_begin' => Carbon::now()->addDays(1),
            'rrule_string' => $rrule
        ]);

        $dues->refresh();
        assertCount($claimCountOriginal, $dues->claims);

        Carbon::setTestNow($dateEnd);
        $dues->delete();
        
        $dues->refresh();
        assertTrue($dateEnd->isSameDay($dues->getRRule()->getRule()['UNTIL']));
        assertCount($claimCountNew, $dues->claims);
    }

    public function test_hard_delete_fine_or_due_does_delete_claims() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amount = money(10); // x2
        $amountDeposit = money(50);
        $fine = $team->ledger->fines()->create(['title' => 'Fine', 'date_begin' => Carbon::now(), 'amount' => $amount]);
        $due = $team->ledger->dues()->create(['title' => 'Due', 'date_begin' => Carbon::now(), 'amount' => $amount]);
        $claimFine = $fine->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now(),]);

        assertEquals($amount->multiply(2), $team->ledger->getClaimBalance());

        $transaction = $team->ledger->transactions()->create(['title' => 'test', 'amount' => $amountDeposit, 'team_member_id' => $member->id]);

        assertEquals(2, $team->ledger->claims->count());
        assertEquals(money(0), $team->ledger->getClaimBalance());
        assertEquals($amountDeposit, $team->ledger->getBalance());
        assertEquals($amountDeposit->subtract($amount->multiply(2)), $team->ledger->getCreditBalance());

        $fine->forceDelete();
        $due->forceDelete();

        $team->ledger->refresh();
        assertEquals(0, $team->ledger->claims->count());
        assertEquals(money(0), $team->ledger->getClaimBalance());
        assertEquals($amountDeposit, $team->ledger->getBalance());
        assertEquals($amountDeposit, $team->ledger->getCreditBalance());
    }
    
    public function test_hard_delete_fine_moves_claims_to_parent() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);

        $amountParent = money(15);
        $amountChild = money(10);
        $amountDeposit = money(12);

        $fineParent = $team->ledger->fines()->create(['title' => 'Fine Parent', 'date_begin' => Carbon::now()->subDays(2), 'amount' => $amountParent]);
        $fine = $team->ledger->fines()->create(['title' => 'Fine Child', 'date_begin' => Carbon::now(), 'amount' => $amountChild, 'parent_id' => $fineParent->id]);
        
        $claimFine = $fine->claims()->create(['team_member_id' => $member->id, 'due_date' => Carbon::now(),]);

        assertEquals($amountChild, $team->ledger->getClaimBalance());

        $transaction = $team->ledger->transactions()->create(['title' => 'test', 'amount' => $amountDeposit, 'team_member_id' => $member->id]);

        $claimFine->refresh();
        assertEquals($fine->id, $claimFine->claimable_id);
        assertTrue($claimFine->isFulfilled());
        assertEquals(money(0), $team->ledger->getClaimBalance());
        assertEquals($amountDeposit, $team->ledger->getBalance());
        assertEquals($amountDeposit->subtract($amountChild), $team->ledger->getCreditBalance());

        $fine->forceDelete();

        $team->ledger->refresh();
        $claimFine->refresh();
        
        assertEquals(1, $team->ledger->claims->count());
        assertEquals($fineParent->id, $claimFine->claimable_id);
        assertEquals($amountParent, $claimFine->amount);
        assertFalse($claimFine->isFulfilled());
        // credit balance from members with unfulfilled claims is subtracted from claim balance
        assertEquals($amountParent->subtract($amountDeposit), $team->ledger->getClaimBalance());
        assertEquals($amountDeposit, $team->ledger->getBalance()); 
        assertEquals($amountDeposit, $team->ledger->getCreditBalance());
    }
    
    public function test_join_date_is_set_on_new_member() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);
        assertTrue($member->join_date->isSameDay(Carbon::now()));
    }

    public function test_update_join_date_on_member_removes_and_creates_claims() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();

        $member1 = $team->createMember(user: $user, joinDate: Carbon::now()->subDays(2));
        $member2 = $team->createMember(user: $user);

        $dateBegin = Carbon::now()->subDays(2);
        $rrule = new RRule([
            'DTSTART' => $dateBegin,
            'FREQ' => RRule::DAILY,
            'UNTIL' => Carbon::now()->addDays(1)
        ]);

        $dues = $team->ledger->dues()->create([
            'title' => 'Dues',
            'date_begin' => $dateBegin,
            'rrule_string' => $rrule
        ]);

        $claimsMember1 =  $dues->claims()->whereTeamMemberId($member1->id)->get();
        $claimsMember2 =  $dues->claims()->whereTeamMemberId($member2->id)->get();
        $this->assertCount(4, $claimsMember1);
        $this->assertCount(2, $claimsMember2);

        $member1->update(['join_date' => Carbon::now()->subDays(1)]);
        $member2->update(['join_date' => Carbon::now()->subDays(2)]);


        $claimsMember1 =  $dues->claims()->whereTeamMemberId($member1->id)->get();
        $claimsMember2 =  $dues->claims()->whereTeamMemberId($member2->id)->get();
        $this->assertCount(3, $claimsMember1);
        $this->assertCount(4, $claimsMember2);
    }

    public function test_soft_delete_member_preserve_claim_and_transactions() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);
        $amountTransaction = money(10);
        $amountClaim = money(5);
        $claim = $team->ledger->claims()->create([
            'title' => 'Test',
            'team_member_id' => $member->id,
            'due_date' => Carbon::now(),
            'amount' => $amountClaim
        ]);
        $transaction = $team->ledger->transactions()->create(['title' => 'test', 'amount' => $amountTransaction, 'team_member_id' => $member->id]);
        assertEquals($amountTransaction, $team->ledger->getBalance());
        assertEquals($amountClaim, $team->ledger->getCreditBalance());

        $member->delete();

        $team->ledger->refresh();
        $claim->refresh();
        $transaction->refresh();
        assertTrue($claim->exists);
        assertTrue($transaction->exists);
        assertEquals($amountTransaction, $team->ledger->getBalance());
        assertEquals($amountClaim, $team->ledger->getCreditBalance());

        self::assertNotNull($claim->teamMember);
        self::assertNotNull($transaction->teamMember);
    }
    
    public function test_soft_deleted_members_with_transactions_or_claims_appear_in_former_member_list() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $memberActive = $team->createMember(user: $user);
        $memberDeletedWithTransaction = $team->createMember(user: $user);
        $memberDeletedWithClaim = $team->createMember(user: $user);
        $memberDeletedWithoutTransactions = $team->createMember(user: $user);

        $team->ledger->transactions()->create(['title' => 'test', 'amount' => money(1), 'team_member_id' => $memberDeletedWithTransaction->id]);
        $team->ledger->claims()->create([
            'title' => 'Test',
            'team_member_id' => $memberDeletedWithClaim->id,
            'due_date' => Carbon::now(),
            'amount' => money(1)
        ]);
        $memberDeletedWithTransaction->delete();
        $memberDeletedWithClaim->delete();
        $memberDeletedWithoutTransactions->delete();

        $team->ledger->refresh();
        
        $memberStatus = $team->ledger->getMemberStatus();
        assertEquals($memberActive->id, $memberStatus->get('active')->keys()->first());
        assertContains($memberDeletedWithTransaction->id, $memberStatus->get('former')->keys());
        assertContains($memberDeletedWithClaim->id, $memberStatus->get('former')->keys());
        self::assertNotContains($memberDeletedWithoutTransactions->id, $memberStatus->get('former')->keys());
    }
    
    
    
    public function test_soft_delete_member_deletes_future_claims() : void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        $member = $team->createMember(user: $user);
        $amountClaim = money(5);
        $claimPresent = $team->ledger->claims()->create([
            'title' => 'Test',
            'team_member_id' => $member->id,
            'due_date' => Carbon::now(),
            'amount' => $amountClaim
        ]);
        $claimFuture = $team->ledger->claims()->create([
            'title' => 'Test',
            'team_member_id' => $member->id,
            'due_date' => Carbon::now()->addDays(1),
            'amount' => $amountClaim
        ]);
        $team->ledger->refresh();
        assertCount(2, $team->ledger->claims);

        $member->delete();
        $team->ledger->refresh();
    
        assertTrue($team->ledger->claims()->whereId($claimPresent->id)->exists());
        assertFalse($team->ledger->claims()->whereId($claimFuture->id)->exists());
        assertCount(1, $team->ledger->claims);
    }

}
