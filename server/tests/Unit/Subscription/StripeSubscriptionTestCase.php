<?php

namespace Tests\Unit\Subscription;

use App\Listeners\StripeEventHandledListener;
use App\Mail\InvoicePaidEmail;
use App\Models\Subscription;
use App\Models\Team;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Lara<PERSON>\Cashier\Cashier;
use Laravel\Cashier\Events\WebhookHandled;
use Mockery;
use Mockery\MockInterface;
use Stripe\StripeClient;
use Tests\TestCase;

abstract class StripeSubscriptionTestCase extends TestCase {
    use RefreshDatabase;
    
    protected function useStripeClientMock(MockInterface $mockStripe): void {
        $this->app->bind(StripeClient::class, function () use ($mockStripe) {
            return $mockStripe;
        });
    }

}
