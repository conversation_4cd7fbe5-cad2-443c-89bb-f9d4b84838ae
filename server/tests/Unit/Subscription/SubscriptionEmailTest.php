<?php

namespace Tests\Unit\Subscription;

use App\Mail\InvoicePaidEmail;
use App\Mail\TeamCreatedWelcomeEmail;
use App\Mail\TrialEndingReminderEmail;
use App\Models\Team;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use <PERSON><PERSON>\Cashier\Invoice as CashierInvoice;
use Stripe\Invoice as StripeInvoice;
use Mockery;

class SubscriptionEmailTest extends StripeSubscriptionTestCase {
    use RefreshDatabase;

    public function test_welcome_email_has_correct_content(): void {
        // Create a user and team
        $user = $this->createUser('<EMAIL>', 'John');
        $team = Team::create(['name' => 'Test Team']);
        
        // Create the email
        $email = new TeamCreatedWelcomeEmail($user, $team);
        
        // Check that the email has the correct subject
        $this->assertEquals(
            'Willkommen bei Numo - Dein Team "Test Team" wurde erstellt',
            $email->envelope()->subject
        );
        
        // Check that the email renders without errors and contains correct data
        $rendered = strip_tags($email->render());
        $trialEndString = Team::getTrialEndsAtForNewTeam()?->format('d.m.Y');
        $this->assertNotEmpty($rendered);
        $this->assertStringContainsString('Test Team', $rendered);
        $this->assertStringContainsString('John', $rendered);
        $this->assertStringContainsString('bis zum '.$trialEndString.' kostenlos', $rendered);
    }

    public function test_trial_ending_email_has_correct_content(): void {
        // Create a user and team
        $user = $this->createUser('<EMAIL>', 'John');
        $team = Team::create(['name' => 'Test Team']);

        // Create the email
        $email = new TrialEndingReminderEmail($user, $team);

        // Check that the email has the correct subject
        $this->assertEquals(
            'Dein Testzeitraum für "Test Team" endet bald',
            $email->envelope()->subject
        );

        // Check that the email renders without errors and contains correct data
        $rendered = strip_tags($email->render());
        $trialEndString = Team::getTrialEndsAtForNewTeam()?->format('d.m.Y');
        $this->assertNotEmpty($rendered);
        $this->assertStringContainsString('Test Team', $rendered);
        $this->assertStringContainsString('John', $rendered);
        $this->assertStringContainsString('endet am '.$trialEndString, $rendered);
    }

    public function test_invoice_paid_email_has_correct_content_and_attachment(): void {
        // Create a user and team
        $user = $this->createUser('<EMAIL>', 'John');
        $team = Team::create(['name' => 'Test Team']);

        // Create a mock Stripe invoice
        $stripeInvoice = new StripeInvoice('inv_123');
        $stripeInvoice->number = 'INV-123';
        $stripeInvoice->created = time();
        $stripeInvoice->amount_paid = 5000;
        $stripeInvoice->currency = 'eur';
        $stripeInvoice->invoice_pdf = 'https://example.com/invoice.pdf';

        // Create a mock Cashier invoice
        $cashierInvoice = Mockery::mock(CashierInvoice::class);
        $cashierInvoice->shouldReceive('asStripeInvoice')
            ->andReturn($stripeInvoice);
        $cashierInvoice->shouldReceive('owner')
            ->andReturn($user);

        // Mock the HTTP facade
        Http::fake([
            'https://example.com/invoice.pdf' => Http::response('PDF content', 200),
        ]);
        
        // Create the email
        $email = new InvoicePaidEmail($cashierInvoice, $team);

        // Check that the email has the correct subject
        $this->assertEquals(
            'Rechnung für Dein Numo Abonnement (Test Team)',
            $email->envelope()->subject
        );

        // Check that the email has the correct content
        $rendered = strip_tags($email->render());

        $this->assertNotEmpty($rendered);
        $this->assertStringContainsString('Test Team', $rendered);
        $this->assertStringContainsString('John', $rendered);
        $this->assertStringContainsString('INV-123', $rendered);
        $this->assertStringContainsString('50,00 €', $rendered);
        $this->assertStringContainsString(date('d.m.Y'), $rendered);

        // Check attachment
        $attachments = $email->attachments();
        $this->assertCount(1, $attachments);
        $this->assertEquals('Invoice-INV-123.pdf', $attachments[0]->as);
        $this->assertEquals('application/pdf', $attachments[0]->mime);
    }
}
