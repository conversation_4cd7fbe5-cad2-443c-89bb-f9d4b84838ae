<?php

namespace Subscription;

use App\Models\Subscription;
use App\Models\Team;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SubscriptionTest extends TestCase {
    use RefreshDatabase;

    protected function setUp(): void {
        parent::setUp();
        // Reset any test time that might have been set
        Carbon::setTestNow();
    }

    public function test_stripe_status_values(): void {
        self::assertEquals([
                'active',
                'canceled',
                'incomplete',
                'incomplete_expired',
                'past_due',
                'paused',
                'trialing',
                'unpaid',
            ],
            array_column(\App\Types\SubscriptionStatusType::cases(), 'value'),
            'Stripe subscription status changed within Cashier, update SubscriptionStatusType and related code'
        );
    }

    public function test_new_team_has_trial(): void {
        $team = Team::create(['name' => 'Test Team']);
        self::assertTrue($team->isOnTrial());
    }

    public function test_new_team_has_trial_until_minimum_trial_end_date(): void {
        Carbon::setTestNow(Carbon::create(2024,06,25));
        $team = Team::create(['name' => 'Test Team']);
        self::assertEquals($team->trial_ends_at, Team::getMinimumTrialEndDate());
    }

    public function test_new_team_has_configured_trial_days(): void {
        Carbon::setTestNow(Carbon::create(2024,12,25));
        $team = Team::create(['name' => 'Test Team']);
        self::assertEquals($team->trial_ends_at, now()->endOfDay()->addRealDays(config('cashier.trial_days.team')));
    }

    private function addSubscriptionToTeamWithDirectPayment(User $user, Team $team): void {
        $subscription = $user->newSubscription('team', 'price_1P5x35Ru6ItLrT66X6HGlhpH')
            ->create(
                paymentMethod: 'pm_card_visa',
                customerOptions: [
                    'address' => [
                        'city' => 'Nettetal',
                        'country' => 'DE',
                        'line1' => 'Hinsbecker Str. 32',
                        'postal_code' => '41334',
                    ],
                ]
            );

        // Associate the subscription with the team
//        $subscription->team()->associate($team);
//        $subscription->save();
    }

    public function test_subscribe(): void {
        self::markTestSkipped('can be reactivated with https://github.com/eltini/clubmanager/issues/96');
        $user = $this->createUser('<EMAIL>');
        $team = Team::create(['name' => 'Test Team 1',]);
        $team->createMember(teamRoleType: \App\Types\TeamRoleType::MANAGER, user: $user);

        $this->addSubscriptionToTeamWithDirectPayment($user, $team);

        self::assertTrue($team->isSubscribed());
    }

    public function test_team_with_active_subscription_is_subscribed(): void {
        $user = $this->createUser();
        $team = $this->createTeam();

        // Create a subscription that is not on grace period
        Subscription::create([
            'user_id' => $user->id,
            'type' => 'team',
            'stripe_id' => 'sub_123',
            'stripe_status' => 'active',
            'stripe_price' => 'price_123',
            'quantity' => 1,
            'team_id' => $team->id,
        ]);
        
        $this->assertTrue($team->isSubscribed());
    }

    public function test_team_with_canceled_subscription_on_grace_period_is_subscribed(): void {
        $user = $this->createUser();
        $team = $this->createTeam();

        // Create a subscription that is on grace period
        Subscription::create([
            'user_id' => $user->id,
            'type' => 'team',
            'stripe_id' => 'sub_123',
            'stripe_status' => 'active',
            'stripe_price' => 'price_123',
            'quantity' => 1,
            'ends_at' => now()->addDays(5), // On grace period
            'team_id' => $team->id,
        ]);

        $this->assertTrue($team->isSubscribed());
        $this->assertTrue($team->getActiveSubscription()?->onGracePeriod());
    }

    public function test_team_with_ended_subscription_is_not_subscribed(): void {
        $user = $this->createUser();
        $team = $this->createTeam();

        // Create a subscription that has ended
        Subscription::create([
            'user_id' => $user->id,
            'type' => 'team',
            'stripe_id' => 'sub_123',
            'stripe_status' => 'canceled',
            'stripe_price' => 'price_123',
            'quantity' => 1,
            'ends_at' => now()->subDay(), // Already ended
            'team_id' => $team->id,
        ]);

        $this->assertFalse($team->isSubscribed());
    }
}
