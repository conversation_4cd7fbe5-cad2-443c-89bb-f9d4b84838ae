<?php

namespace Tests\Unit\Subscription;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use <PERSON><PERSON>\Cashier\Events\WebhookHandled;
use <PERSON><PERSON>\Cashier\Events\WebhookReceived;
use <PERSON><PERSON>\Cashier\Http\Middleware\VerifyWebhookSignature;

class StripeWebhookControllerTest extends StripeSubscriptionTestCase {
    use RefreshDatabase;

    public function test_handle_invoice_payment_succeeded(): void {
        $this->withoutMiddleware(VerifyWebhookSignature::class);
        Event::fake();

        // Create a sample payload
        $payload = [
            'type' => 'invoice.payment_succeeded',
            'data' => [
                'object' => [
                    'id' => 'inv_123',
                    'customer' => 'cus_123',
                    'subscription' => 'sub_123',
                ]
            ]
        ];
        
        $response = $this->postJson(route('stripe.webhook', [], false), $payload);
        
        // Verify the response
        Event::assertDispatched(WebhookReceived::class);
        Event::assertDispatched(WebhookHandled::class);
        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_handle_checkout_session_completed(): void {
        $this->withoutMiddleware(VerifyWebhookSignature::class);
        Event::fake();
        
        // Create a sample payload
        $payload = [
            'type' => 'checkout.session.completed',
            'data' => [
                'object' => [
                    'id' => 'cs_123',
                    'customer' => 'cus_123',
                    'mode' => 'setup',
                ]
            ]
        ];

        $response = $this->postJson(route('stripe.webhook', [], false), $payload);
        
        // Verify the response
        Event::assertDispatched(WebhookReceived::class);
        Event::assertDispatched(WebhookHandled::class);
        $this->assertEquals(200, $response->getStatusCode());
    }
}
