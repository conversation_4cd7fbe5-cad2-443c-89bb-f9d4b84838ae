<?php

namespace Tests\Unit\Subscription;

use App\Listeners\StripeEventHandledListener;
use App\Mail\InvoicePaidEmail;
use App\Models\Subscription;
use App\Models\Team;
use App\Models\User;
use App\Types\SubscriptionPriceType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Laravel\Cashier\Cashier;
use Laravel\Cashier\Events\WebhookHandled;
use Mockery;
use Mockery\MockInterface;
use Stripe\StripeClient;

class StripeEventHandledListenerTest extends StripeSubscriptionTestCase {
    use RefreshDatabase;

    protected MockInterface $logMock;
    
    protected function setUp(): void {
        parent::setUp();
        
        // Mock the Log facade for all tests
        $this->logMock = Log::spy();
    }

    public function test_subscription_created_with_no_team_id(): void {
        // Create the event with no team_id in metadata
        $event = new WebhookHandled([
            'type' => 'customer.subscription.created',
            'data' => [
                'object' => [
                    'id'       => 'sub_123',
                    'customer' => 'cus_123',
                    'metadata' => [
                        'price_type' => SubscriptionPriceType::TEAM_YEARLY->value
                    ]
                ]
            ]
        ]);

        $listener = new StripeEventHandledListener();
        $listener->handle($event);

        // Verify that an error was logged
        $this->logMock->shouldHaveReceived('error')
            ->with('No team_id found in subscription metadata', Mockery::any());
    }
    
    public function test_subscription_created_with_no_price_type(): void {
        // Create the event with no team_id in metadata
        $event = new WebhookHandled([
            'type' => 'customer.subscription.created',
            'data' => [
                'object' => [
                    'id'       => 'sub_123',
                    'customer' => 'cus_123',
                    'metadata' => []
                ]
            ]
        ]);

        $listener = new StripeEventHandledListener();
        $listener->handle($event);

        // Verify that an error was logged
        $this->logMock->shouldHaveReceived('error')
            ->with('No price_type found in subscription metadata', Mockery::any());
    }

    public function test_subscription_created_with_nonexistent_team(): void {
        // Create the event with a non-existent team_id
        $event = new WebhookHandled([
            'type' => 'customer.subscription.created',
            'data' => [
                'object' => [
                    'id'       => 'sub_123',
                    'customer' => 'cus_123',
                    'metadata' => [
                        'team_id'    => 'non_existent_team_id',
                        'price_type' => SubscriptionPriceType::TEAM_YEARLY->value
                    ]
                ]
            ]
        ]);

        $listener = new StripeEventHandledListener();
        $listener->handle($event);

        // Verify that an error was logged
        $this->logMock->shouldHaveReceived('error')
            ->with('Team not found for subscription', Mockery::any());
    }

    public function test_subscription_created_with_existing_team_subscription_cancel_and_refund(): void {
        $team = $this->createTeam();
        $user = $this->createUser();
        
        // Create an existing subscription for the team
        Subscription::create([
            'user_id'       => $user->id,
            'type'          => 'team',
            'stripe_id'     => 'sub_existing',
            'stripe_status' => 'active',
            'stripe_price'  => 'price_123',
            'quantity'      => 1,
            'team_id'       => $team->id
        ]);
        
        // Create the event with the team_id
        $event = new WebhookHandled([
            'type' => 'customer.subscription.created',
            'data' => [
                'object' => [
                    'id'       => 'sub_new',
                    'customer' => 'cus_123',
                    'metadata' => [
                        'team_id'    => $team->id,
                        'price_type' => SubscriptionPriceType::TEAM_YEARLY->value
                    ]
                ]
            ]
        ]);
        
        // Mock Stripe client
        $mockStripe = Mockery::mock(StripeClient::class);
        
        // Set up the subscriptions mock
        $mockStripe->subscriptions = Mockery::mock();
        $mockStripe->subscriptions->shouldReceive('cancel')
            ->once()
            ->with('sub_new', Mockery::any());
            
        // Set up the invoices mock
        $mockStripe->invoices = Mockery::mock();
        $mockStripe->invoices->shouldReceive('all')
            ->once()
            ->andReturn((object)['data' => []]);
            
        // Replace the Stripe client with our mock
        $this->useStripeClientMock($mockStripe);

        // Create the listener
        $listener = new StripeEventHandledListener();

        // Call the handle method
        $listener->handle($event);

        // Verify that a warning was logged
        $this->logMock->shouldHaveReceived('warning')
            ->with('Team already has an active subscription. Cancelling new subscription.', Mockery::any());
    }

    public function test_subscription_created_with_nonexistent_user(): void {
        // Create a team
        $team = $this->createTeam();
        
        // Create the event with the team_id
        $event = new WebhookHandled([
            'type' => 'customer.subscription.created',
            'data' => [
                'object' => [
                    'id'       => 'sub_123',
                    'customer' => 'cus_nonexistent',
                    'metadata' => [
                        'team_id'    => $team->id,
                        'price_type' => SubscriptionPriceType::TEAM_YEARLY->value
                    ]
                ]
            ]
        ]);

        // Create the listener
        $listener = new StripeEventHandledListener();

        // Call the handle method
        $listener->handle($event);

        // Verify that an error was logged
        $this->logMock->shouldHaveReceived('error')
            ->with('User not found for Stripe customer', Mockery::any());
    }

    public function test_subscription_created_with_nonexistent_subscription(): void {
        // Create a team
        $team = $this->createTeam();
        
        // Create a user with a Stripe ID
        $user = $this->createUser();
        $user->stripe_id = 'cus_123';
        $user->saveQuietly();
        
        // Create the event with the team_id
        $event = new WebhookHandled([
            'type' => 'customer.subscription.created',
            'data' => [
                'object' => [
                    'id'       => 'sub_nonexistent',
                    'customer' => 'cus_123',
                    'metadata' => [
                        'team_id'    => $team->id,
                        'price_type' => SubscriptionPriceType::TEAM_YEARLY->value
                    ]
                ]
            ]
        ]);

        // Create the listener
        $listener = new StripeEventHandledListener();

        // Call the handle method
        $listener->handle($event);

        // Verify that an error was logged
        $this->logMock->shouldHaveReceived('error')
            ->with('Subscription not found in database', Mockery::any());
    }

    public function test_subscription_created_successfully(): void {
        // Create a team
        $team = $this->createTeam();
        
        // Create a user with a Stripe ID
        $user = $this->createUser();
        $user->stripe_id = 'cus_123';
        $user->saveQuietly();
        
        // Create a subscription without a team
        $subscription = Subscription::create([
            'user_id'       => $user->id,
            'type'          => 'team',
            'stripe_id'     => 'sub_123',
            'stripe_status' => 'active',
            'stripe_price'  => 'price_123',
            'quantity'      => 1
        ]);
        
        // Create the event with the team_id
        $event = new WebhookHandled([
            'type' => 'customer.subscription.created',
            'data' => [
                'object' => [
                    'id'       => 'sub_123',
                    'customer' => 'cus_123',
                    'metadata' => [
                        'team_id'    => $team->id,
                        'price_type' => SubscriptionPriceType::TEAM_YEARLY->value
                    ]
                ]
            ]
        ]);

        // Create the listener
        $listener = new StripeEventHandledListener();

        // Call the handle method
        $listener->handle($event);

        // Refresh the subscription
        $subscription->refresh();
        
        // Verify that the subscription is now associated with the team
        $this->assertEquals($team->id, $subscription->team_id);
        
        // Verify that a success message was logged
        $this->logMock->shouldHaveReceived('info')
            ->with('Subscription associated with team', Mockery::any());
    }

    public function test_invoice_payment_succeeded_with_zero_amount(): void {
        // Create the event with a zero amount
        $event = new WebhookHandled([
            'type' => 'invoice.payment_succeeded',
            'data' => [
                'object' => [
                    'id'                   => 'inv_123',
                    'customer'             => 'cus_123',
                    'subscription'         => 'sub_123',
                    'subscription_details' => [
                        'metadata' => [
                            'team_id' => 'team_123'
                        ]
                    ],
                    'amount_paid' => 0
                ]
            ]
        ]);

        // Create the listener
        $listener = new StripeEventHandledListener();

        // Call the handle method
        $listener->handle($event);

        // Verify that an info message was logged
        $this->logMock->shouldHaveReceived('info')
            ->with('Skipping zero-amount invoice', Mockery::any());
    }

    public function test_invoice_payment_succeeded_with_nonexistent_user(): void {
        // Create the event
        $event = new WebhookHandled([
            'type' => 'invoice.payment_succeeded',
            'data' => [
                'object' => [
                    'id'                   => 'inv_123',
                    'customer'             => 'cus_nonexistent',
                    'subscription'         => 'sub_123',
                    'subscription_details' => [
                        'metadata' => [
                            'team_id' => 'team_123'
                        ]
                    ],
                    'amount_paid' => 5000
                ]
            ]
        ]);

        // Create the listener
        $listener = new StripeEventHandledListener();

        // Call the handle method
        $listener->handle($event);

        // Verify that an error was logged
        $this->logMock->shouldHaveReceived('error')
            ->with('User not found for Stripe customer', Mockery::any());
    }

    public function test_invoice_payment_succeeded_with_nonexistent_team(): void {
        // Create a user with a Stripe ID
        $user = $this->createUser();
        $user->stripe_id = 'cus_123';
        $user->saveQuietly();
        
        // Create the event
        $event = new WebhookHandled([
            'type' => 'invoice.payment_succeeded',
            'data' => [
                'object' => [
                    'id'                   => 'inv_123',
                    'customer'             => 'cus_123',
                    'subscription'         => 'sub_123',
                    'subscription_details' => [
                        'metadata' => [
                            'team_id' => 'team_nonexistent'
                        ]
                    ],
                    'amount_paid' => 5000
                ]
            ]
        ]);

        // Create the listener
        $listener = new StripeEventHandledListener();

        // Call the handle method
        $listener->handle($event);

        // Verify that an error was logged
        $this->logMock->shouldHaveReceived('error')
            ->with('Team not found for subscription', Mockery::any());
    }

    public function test_invoice_payment_succeeded_successfully(): void {
        // Create a team
        $team = $this->createTeam();
        
        // Create a user with a Stripe ID
        $user = $this->createUser();
        $user->stripe_id = 'cus_123';
        $user->saveQuietly();
        
        // Mock the Mail facade
        Mail::fake();
        
        // Mock Stripe client
        $mockStripe = Mockery::mock(StripeClient::class);
        
        // Set up the invoices mock
        $mockStripe->invoices = Mockery::mock();
        $mockStripe->invoices->shouldReceive('update')
            ->once()
            ->with('inv_123', Mockery::any());
            
        // Replace the Stripe client with our mock
        $this->useStripeClientMock($mockStripe);
        
        // Create the event
        $event = new WebhookHandled([
            'type' => 'invoice.payment_succeeded',
            'data' => [
                'object' => [
                    'id'                   => 'inv_123',
                    'customer'             => 'cus_123',
                    'subscription'         => 'sub_123',
                    'subscription_details' => [
                        'metadata' => [
                            'team_id' => $team->id
                        ]
                    ],
                    'amount_paid' => 5000,
                    'currency'    => 'eur',
                    'lines'       => [
                        'data' => [
                            [
                                'price' => [
                                    'lookup_key' => 'team_yearly'
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ]);

        // Create the listener
        $listener = new StripeEventHandledListener();

        // Call the handle method
        $listener->handle($event);

        // Verify that the email was sent
        Mail::assertSent(InvoicePaidEmail::class, function ($mail) use ($user, $team) {
            return $mail->hasTo($user->email) &&
                   $mail->team->id === $team->id;
        });
        
        // Verify that a success message was logged
        $this->logMock->shouldHaveReceived('info')
            ->with('Invoice email sent', Mockery::any());
    }

    public function test_checkout_session_completed_with_nonexistent_user(): void {
        // Create the event
        $event = new WebhookHandled([
            'type' => 'checkout.session.completed',
            'data' => [
                'object' => [
                    'mode'     => 'setup',
                    'customer' => 'cus_nonexistent',
                    'metadata' => [
                        'team_id' => 'team_123'
                    ],
                    'setup_intent' => 'seti_123'
                ]
            ]
        ]);

        // Create the listener
        $listener = new StripeEventHandledListener();

        // Call the handle method
        $listener->handle($event);

        // Verify that an error was logged
        $this->logMock->shouldHaveReceived('error')
            ->with('User not found for Stripe customer', Mockery::any());
    }

    public function test_checkout_session_completed_with_nonexistent_team(): void {
        // Create a user with a Stripe ID
        $user = $this->createUser();
        $user->stripe_id = 'cus_123';
        $user->saveQuietly();
        
        // Create the event
        $event = new WebhookHandled([
            'type' => 'checkout.session.completed',
            'data' => [
                'object' => [
                    'mode'     => 'setup',
                    'customer' => 'cus_123',
                    'metadata' => [
                        'team_id' => 'team_nonexistent'
                    ],
                    'setup_intent' => 'seti_123'
                ]
            ]
        ]);

        // Create the listener
        $listener = new StripeEventHandledListener();

        // Call the handle method
        $listener->handle($event);

        // Verify that an error was logged
        $this->logMock->shouldHaveReceived('error')
            ->with('Team not found for subscription', Mockery::any());
    }

    public function test_checkout_session_completed_with_no_old_subscription(): void {
        self::markTestSkipped('can be reactivated with https://github.com/eltini/clubmanager/issues/946');

        // Create a team
        $team = $this->createTeam();
        
        // Create a user with a Stripe ID
        $user = $this->createUser();
        $user->stripe_id = 'cus_123';
        $user->saveQuietly();
        
        // Create the event
        $event = new WebhookHandled([
            'type' => 'checkout.session.completed',
            'data' => [
                'object' => [
                    'mode'     => 'setup',
                    'customer' => 'cus_123',
                    'metadata' => [
                        'team_id' => $team->id
                    ],
                    'setup_intent' => 'seti_123'
                ]
            ]
        ]);
        
        // Mock Stripe client
        $mockStripe = Mockery::mock(StripeClient::class);
        
        // Set up the setupIntents mock
        $mockStripe->setupIntents = Mockery::mock();
        $mockStripe->setupIntents->shouldReceive('retrieve')
            ->once()
            ->with('seti_123')
            ->andReturn((object)['payment_method' => 'pm_123']);
            
        // Replace the Stripe client with our mock
        $this->useStripeClientMock($mockStripe);
        
        // Mock the user's updateDefaultPaymentMethod method
        $userMock = $this->getMockBuilder(User::class)
            ->disableOriginalConstructor()
            ->onlyMethods(['updateDefaultPaymentMethod'])
            ->getMock();
        
        $userMock->expects($this->once())
            ->method('updateDefaultPaymentMethod')
            ->with('pm_123');
        $userMock->stripe_id = 'cus_123';
            
        // Mock the User::where method to return our mock user
        $userSpy = $this->spy(User::class);
        $userSpy->shouldReceive('where')
            ->once()
            ->with('stripe_id', 'cus_123')
            ->andReturnSelf();

        $userSpy->shouldReceive('first')
            ->once()
            ->andReturn($user);

        // Create the listener
        $listener = new StripeEventHandledListener();

        // Call the handle method
        $listener->handle($event);

        // Verify that an error was logged
        $this->logMock->shouldHaveReceived('error')
            ->with('Team has no old subscription', Mockery::any());
    }

    public function test_checkout_session_completed_with_subscription_not_on_grace_period(): void {
        self::markTestSkipped('can be reactivated with https://github.com/eltini/clubmanager/issues/946');
        
        // Create a team
        $team = $this->createTeam();
        
        // Create a user with a Stripe ID
        $user = $this->createUser();
        $user->stripe_id = 'cus_123';
        $user->save();
        
        // Create a subscription for the team (not on grace period)
        $subscription = Subscription::create([
            'user_id'       => $user->id,
            'type'          => 'team',
            'stripe_id'     => 'sub_123',
            'stripe_status' => 'active',
            'stripe_price'  => 'price_123',
            'quantity'      => 1,
            'team_id'       => $team->id
        ]);
        
        // Create the event
        $event = new WebhookHandled([
            'type' => 'checkout.session.completed',
            'data' => [
                'object' => [
                    'mode'     => 'setup',
                    'customer' => 'cus_123',
                    'metadata' => [
                        'team_id' => $team->id
                    ],
                    'setup_intent' => 'seti_123'
                ]
            ]
        ]);
        
        // Mock Stripe client
        $mockStripe = Mockery::mock(StripeClient::class);
        
        // Set up the setupIntents mock
        $mockStripe->setupIntents = Mockery::mock();
        $mockStripe->setupIntents->shouldReceive('retrieve')
            ->once()
            ->with('seti_123')
            ->andReturn((object)['payment_method' => 'pm_123']);
            
        // Replace the Stripe client with our mock
        Cashier::$stripeClient = $mockStripe;
        
        // Mock the user's updateDefaultPaymentMethod method
        $user = Mockery::mock(User::class)->makePartial();
        $user->shouldReceive('updateDefaultPaymentMethod')
            ->once()
            ->with('pm_123');
            
        // Mock the User::where method to return our mock user
        User::shouldReceive('where')
            ->once()
            ->with('stripe_id', 'cus_123')
            ->andReturnSelf();
            
        User::shouldReceive('first')
            ->once()
            ->andReturn($user);
            
        // Mock the subscription's onGracePeriod method
        $mockSubscription = Mockery::mock(Subscription::class)->makePartial();
        $mockSubscription->shouldReceive('onGracePeriod')
            ->once()
            ->andReturn(false);
            
        // Mock the team's getActiveSubscription method
        $mockTeam = Mockery::mock(Team::class)->makePartial();
        $mockTeam->shouldReceive('getActiveSubscription')
            ->once()
            ->andReturn($mockSubscription);
            
        // Mock the Team::find method to return our mock team
        Team::shouldReceive('find')
            ->once()
            ->with($team->id)
            ->andReturn($mockTeam);

        // Create the listener
        $listener = new StripeEventHandledListener();

        // Call the handle method
        $listener->handle($event);

        // Verify that an error was logged
        Log::shouldHaveReceived('error')
            ->with('old subscription is not on grace period', Mockery::any());
    }

    public function test_checkout_session_completed_successfully(): void {
        self::markTestSkipped('can be reactivated with https://github.com/eltini/clubmanager/issues/946');

        // Create a team
        $team = $this->createTeam();
        
        // Create a user with a Stripe ID
        $user = $this->createUser();
        $user->stripe_id = 'cus_123';
        $user->save();
        
        // Create a subscription for the team (on grace period)
        $subscription = Subscription::create([
            'user_id'       => $user->id,
            'type'          => 'team',
            'stripe_id'     => 'sub_123',
            'stripe_status' => 'active',
            'stripe_price'  => 'price_123',
            'quantity'      => 1,
            'ends_at'       => now()->addDays(5), // On grace period
            'team_id'       => $team->id
        ]);
        
        // Create the event
        $event = new WebhookHandled([
            'type' => 'checkout.session.completed',
            'data' => [
                'object' => [
                    'mode'     => 'setup',
                    'customer' => 'cus_123',
                    'metadata' => [
                        'team_id' => $team->id
                    ],
                    'setup_intent' => 'seti_123'
                ]
            ]
        ]);
        
        // Mock Stripe client
        $mockStripe = Mockery::mock(StripeClient::class);
        
        // Set up the setupIntents mock
        $mockStripe->setupIntents = Mockery::mock();
        $mockStripe->setupIntents->shouldReceive('retrieve')
            ->once()
            ->with('seti_123')
            ->andReturn((object)['payment_method' => 'pm_123']);
            
        // Set up the subscriptionSchedules mock
        $mockStripe->subscriptionSchedules = Mockery::mock();
        $mockStripe->subscriptionSchedules->shouldReceive('create')
            ->once()
            ->andReturn((object)['id' => 'sub_sched_123']);
            
        // Replace the Stripe client with our mock
        Cashier::$stripeClient = $mockStripe;
        
        // Configure the cashier.lookup_keys.product.team config
        config(['cashier.lookup_keys.product.team' => 'team']);
        
        // Mock the user's updateDefaultPaymentMethod method
        $user = Mockery::mock(User::class)->makePartial();
        $user->shouldReceive('updateDefaultPaymentMethod')
            ->once()
            ->with('pm_123');
        $user->stripe_id = 'cus_123';
            
        // Mock the User::where method to return our mock user
        User::shouldReceive('where')
            ->once()
            ->with('stripe_id', 'cus_123')
            ->andReturnSelf();
            
        User::shouldReceive('first')
            ->once()
            ->andReturn($user);
            
        // Mock the subscription's onGracePeriod method
        $mockSubscription = Mockery::mock(Subscription::class)->makePartial();
        $mockSubscription->shouldReceive('onGracePeriod')
            ->once()
            ->andReturn(true);
        $mockSubscription->stripe_id = 'sub_123';
        $mockSubscription->stripe_price = 'price_123';
        $mockSubscription->ends_at = now()->addDays(5);
            
        // Mock the team's getActiveSubscription method
        $mockTeam = Mockery::mock(Team::class)->makePartial();
        $mockTeam->shouldReceive('getActiveSubscription')
            ->once()
            ->andReturn($mockSubscription);
        $mockTeam->id = $team->id;
            
        // Mock the Team::find method to return our mock team
        Team::shouldReceive('find')
            ->once()
            ->with($team->id)
            ->andReturn($mockTeam);

        // Create the listener
        $listener = new StripeEventHandledListener();

        // Call the handle method
        $listener->handle($event);

        // Verify that a success message was logged
        Log::shouldHaveReceived('info')
            ->with('Scheduled subscription for transfer created', Mockery::any());
    }
}
