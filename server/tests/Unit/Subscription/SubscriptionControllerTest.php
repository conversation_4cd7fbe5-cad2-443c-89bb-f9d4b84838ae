<?php

namespace Tests\Unit\Subscription;

use App\Models\Subscription;
use App\Types\SubscriptionPriceType;
use App\Types\TeamRoleType;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Stripe\StripeClient;

class SubscriptionControllerTest extends StripeSubscriptionTestCase {
    use RefreshDatabase;

    protected function setUp(): void {
        parent::setUp();
        // Reset any test time that might have been set
        Carbon::setTestNow();
    }

    public function test_create_checkout_session_requires_authentication(): void {
        $team = $this->createTeam();

        $response = $this->postJson(route('api.subscription.createCheckoutSession', [
            'team'      => $team->id,
            'priceType' => SubscriptionPriceType::TEAM_YEARLY->value
        ]));

        $response->assertStatus(401);
    }
    
    public function test_cancel_subscription_requires_authentication(): void {
        $team = $this->createTeam();

        $response = $this->postJson(route('api.subscription.cancel', ['team' => $team->id]));

        $response->assertStatus(401);
    }

    public function test_cancel_subscription_requires_authorization(): void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        
        // User is not a member of the team, so they shouldn't be authorized
        $response = $this->postJson(route('api.subscription.cancel', ['team' => $team->id]));

        $response->assertStatus(403);
    }

    public function test_cancel_subscription_with_no_active_subscription(): void {
        $user = $this->createUserActingAs();
        $team = $this->createTeam();
        
        $response = $this->postJson(route('api.subscription.cancel', ['team' => $team->id]));
        $response->assertStatus(400)
            ->assertJson([
                'message' => 'Team does not have an active subscription'
            ]);
    }

    public function test_cancel_subscription_on_trial(): void {
        $user = $this->createUserActingAs();
        $team = $this->createTeam();
        
        // Create a subscription
        $subscription = Subscription::create([
            'user_id'       => $user->id,
            'type'          => 'team',
            'stripe_id'     => 'sub_123',
            'stripe_status' => 'active',
            'stripe_price'  => 'price_123',
            'quantity'      => 1,
            'team_id'       => $team->id,
        ]);

        // Mock stripe client
        $mockStripe = Mockery::mock(StripeClient::class);
        $mockStripe->subscriptions = Mockery::mock();
        $mockStripe->subscriptions->shouldReceive('cancel')
            ->once()
            ->andReturn((object)['data' => []]);
        $this->useStripeClientMock($mockStripe);
        
        $response = $this->postJson(route('api.subscription.cancel', ['team' => $team->id]));
        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Subscription canceled successfully'
            ]);
    }

    public function test_cancel_subscription_not_on_trial(): void {
        $user = $this->createUserActingAs();
        $team = $this->createTeam();
        $team->trial_ends_at = now()->subDays(5);
        $team->save();
        
        // Create a subscription
        $subscription = Subscription::create([
            'user_id'       => $user->id,
            'type'          => 'team',
            'stripe_id'     => 'sub_123',
            'stripe_status' => 'active',
            'stripe_price'  => 'price_123',
            'quantity'      => 1,
            'team_id'       => $team->id,
        ]);
        
        // Mock stripe client
        $mockStripe = Mockery::mock(StripeClient::class);
        $mockStripe->subscriptions = Mockery::mock();
        $mockStripe->subscriptions->shouldReceive('update')
            ->once()
            ->andReturn((object)[
                'status'             => 'canceled',
                'current_period_end' => now()->addDays(5)->timestamp
            ]);
        $this->useStripeClientMock($mockStripe);
        
        $response = $this->postJson(route('api.subscription.cancel', ['team' => $team->id]));
        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Subscription canceled successfully'
            ]);
    }

    public function test_resume_subscription_requires_authentication(): void {
        $team = $this->createTeam();

        $response = $this->postJson(route('api.subscription.resume', ['team' => $team->id]));

        $response->assertStatus(401);
    }

    public function test_resume_subscription_requires_authorization(): void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        
        // User is not a member of the team, so they shouldn't be authorized
        $response = $this->postJson(route('api.subscription.resume', ['team' => $team->id]));

        $response->assertStatus(403);
    }

    public function test_resume_subscription_with_no_subscription_on_grace_period(): void {
        $user = $this->createUserActingAs();
        $team = $this->createTeam();
                
        $response = $this->postJson(route('api.subscription.resume', ['team' => $team->id]));
        $response->assertStatus(400);
    }

    public function test_resume_subscription_with_active_subscription_not_on_grace_period(): void {
        $user = $this->createUserActingAs();
        $team = $this->createTeam();
        $team->createMember(teamRoleType: TeamRoleType::MANAGER, user: $user);
        
        // Create a subscription
        $subscription = Subscription::create([
            'user_id'       => $user->id,
            'type'          => 'team',
            'stripe_id'     => 'sub_123',
            'stripe_status' => 'active',
            'stripe_price'  => 'price_123',
            'quantity'      => 1,
            'team_id'       => $team->id,
        ]);
        
        $response = $this->postJson(route('api.subscription.resume', ['team' => $team->id]));

        $response->assertStatus(400);
    }

    public function test_resume_subscription_successfully(): void {
        $user = $this->createUserActingAs();
        $team = $this->createTeam();
        $team->createMember(teamRoleType: TeamRoleType::MANAGER, user: $user);
        
        // Create a subscription on grace period
        $subscription = Subscription::create([
            'user_id'       => $user->id,
            'type'          => 'team',
            'stripe_id'     => 'sub_123',
            'stripe_status' => 'active',
            'stripe_price'  => 'price_123',
            'quantity'      => 1,
            'ends_at'       => now()->addDays(5), // On grace period
            'team_id'       => $team->id,
        ]);

        // Mock stripe client
        $mockStripe = Mockery::mock(StripeClient::class);
        $mockStripe->subscriptions = Mockery::mock();
        $mockStripe->subscriptions->shouldReceive('update')
            ->once()
            ->andReturn((object)[
                'status' => 'active',
            ]);
        $this->useStripeClientMock($mockStripe);
        
        $response = $this->postJson(route('api.subscription.resume', ['team' => $team->id]));

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Subscription resumed successfully'
            ]);
    }

    public function test_get_invoices_requires_authentication(): void {
        $team = $this->createTeam();

        $response = $this->getJson(route('api.subscription.getInvoices', ['team' => $team->id]));

        $response->assertStatus(401);
    }

    public function test_get_invoices_requires_authorization(): void {
        $team = $this->createTeam();
        $user = $this->createUserActingAs();
        
        // User is not a member of the team, so they shouldn't be authorized
        $response = $this->getJson(route('api.subscription.getInvoices', ['team' => $team->id]));

        $response->assertStatus(403);
    }
}
