<?php

namespace Tests\Unit;

use App\Models\Team;
use App\Models\TeamEventSeries;
use App\Types\EventResponseType;
use App\Types\EventSeriesType;
use App\Types\EventVoteReminderType;
use Carbon\Carbon;
use Mockery;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\TestCase;
use App\Models\TeamEvent;

class TeamEventTest extends TestCase {
    public function tearDown(): void {
        Mockery::close();
        parent::tearDown();
    }
    
    /**
     * @return array<string, array{
     *     string,
     *     string,
     *     EventVoteReminderType,
     *     int,
     *     string|null,
     *     bool|null,
     *     bool|null
     * }>
     */
    public static function voteReminderDataProvider(): array {
        // Note: The code here will assume the inputs to be in the timezone of $team->getTimezone(), which is currently hardcoded to Europe/Berlin.

        $convertToBerlin = function (string $utcDate): string {
            $t = Carbon::createFromFormat('Y-m-d H:i:s', $utcDate, 'UTC');
            if ($t != null) {
                return $t->setTimezone('Europe/Berlin')
                    ->format('Y-m-d H:i:s');
            }
            return $utcDate;
        };

        return [
            '5 hours_before_event' => ['2024-09-10 14:00:00', $convertToBerlin('2024-09-15 14:00:00'), EventVoteReminderType::HOURS_BEFORE_EVENT, 5, '2024-09-15 09:00:00', null, null],
            '3 hours_before_event' => ['2024-09-10 14:00:00', $convertToBerlin('2024-09-15 14:00:00'), EventVoteReminderType::HOURS_BEFORE_EVENT, 3, '2024-09-15 11:00:00', null, null],
            '7 hours_before_event' => ['2024-09-10 14:00:00', $convertToBerlin('2024-09-15 14:00:00'), EventVoteReminderType::HOURS_BEFORE_EVENT, 7, '2024-09-15 07:00:00', null, null],

            '24 hours_after_creation' => ['2024-09-10 14:00:00', $convertToBerlin('2024-09-15 14:00:00'), EventVoteReminderType::HOURS_AFTER_CREATION, 1 * 24, '2024-09-11 14:00:00', null, null],
            '48 hours_after_creation' => ['2024-09-10 14:00:00', $convertToBerlin('2024-09-15 14:00:00'), EventVoteReminderType::HOURS_AFTER_CREATION, 2 * 24, '2024-09-12 14:00:00', null, null],
            '5 hours_after_creation'  => ['2024-09-10 14:00:00', $convertToBerlin('2024-09-15 14:00:00'), EventVoteReminderType::HOURS_AFTER_CREATION, 5, '2024-09-10 19:00:00', null, null],

            '0 none reminder' => ['2024-09-10 14:00:00', $convertToBerlin('2024-09-15 14:00:00'), EventVoteReminderType::NONE, 0 * 24, null, null, null],
            '5 none reminder' => ['2024-09-10 14:00:00', $convertToBerlin('2024-09-15 14:00:00'), EventVoteReminderType::NONE, 5 * 24, null, null, null],

            'out of bounds before event'   => ['2024-09-10 14:00:00', $convertToBerlin('2024-09-15 14:00:00'), EventVoteReminderType::HOURS_BEFORE_EVENT, 6 * 24, null, null, null],
            'out of bounds after creation' => ['2024-09-10 14:00:00', $convertToBerlin('2024-09-15 14:00:00'), EventVoteReminderType::HOURS_AFTER_CREATION, 6 * 24, null, null, null],

            'recurring parent event and after creation' => ['2024-09-10 14:00:00', $convertToBerlin('2024-09-15 14:00:00'), EventVoteReminderType::HOURS_AFTER_CREATION, 1 * 24, null, true, null],

            'auto yes' => ['2024-09-10 14:00:00', $convertToBerlin('2024-09-15 14:00:00'), EventVoteReminderType::HOURS_AFTER_CREATION, 1 * 24, null, false, true],
        ];
    }

    #[DataProvider('voteReminderDataProvider')]
    public function test_team_event_reminder(
        string                $createdAt,
        string                $dateBegin,
        EventVoteReminderType $reminderType,
        int                   $reminderHours,
        string|null           $expectedReminderDate,
        bool|null             $makeParent = false,
        bool|null             $autoYes = false
    ): void {
        $createdAtParsed = Carbon::createFromFormat('Y-m-d H:i:s', $createdAt);
        assert($createdAtParsed !== null);

        $dateBeginParsed = Carbon::createFromFormat('Y-m-d H:i:s', $dateBegin);
        assert($dateBeginParsed !== null);

        /** @var TeamEvent $teamEventMock */
        $teamEventMock = Mockery::mock(TeamEvent::class)->makePartial();

        $teamEventMock->created_at = $createdAtParsed;
        $teamEventMock->date_begin = $dateBeginParsed;
        $teamEventMock->time_begin = $dateBeginParsed->format('H:i:s');
        $teamEventMock->vote_reminder_type = $reminderType;
        $teamEventMock->vote_reminder_hours = $reminderHours;
        
        /** @var Team $teamMock */
        $teamMock = Mockery::mock('App\Models\Team')->makePartial();
        $teamEventMock->team = $teamMock;

        if ($makeParent) {
            $teamEventMock->series_type = EventSeriesType::RECURRING_PARENT;
            $teamEventMock->series = Mockery::mock(TeamEventSeries::class)->makePartial();
            $teamEventMock->series->rrule_string = 'FREQ=DAILY;INTERVAL=1;COUNT=5';
        }

        if ($autoYes) {
            $teamEventMock->response_type = EventResponseType::AUTO_YES;
        }

        $teamEventMock->calculateAndUpdateVoteReminderDate();

        if ($reminderType === EventVoteReminderType::NONE) {
            $this->assertNull($teamEventMock->vote_reminder_date);
        } else {
            $this->assertEquals($expectedReminderDate, $teamEventMock->vote_reminder_date?->format('Y-m-d H:i:s'));
        }
    }
}
