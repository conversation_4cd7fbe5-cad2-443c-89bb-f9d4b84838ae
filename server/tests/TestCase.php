<?php

namespace Tests;

use Tests\Helper\ModelConvenienceFunctions;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Support\Facades\DB;

abstract class TestCase extends BaseTestCase {
    use CreatesApplication;
    use ModelConvenienceFunctions;

    /**
     * dumpDatabase retrieves all table names from the current database and
     * prints the entire content of these tables (except the 'migrations' table)/
     *
     * Can be used for some manual debugging if the expected database state has been created.
     */
    function dumpDatabase(): void {
        // Get all table names from the database
        $tables = DB::select('SHOW TABLES');

        foreach ($tables as $table) {
            $tableName = array_values((array)$table)[0];

            if ($tableName === 'migrations') {
                continue;
            }

            $records = DB::table($tableName)->get();

            if ($records->isNotEmpty()) {
                echo "Contents of {$tableName} table:\n";
                echo json_encode($records, JSON_PRETTY_PRINT);
                echo "\n------------------------\n";
            }
        }
    }
}
