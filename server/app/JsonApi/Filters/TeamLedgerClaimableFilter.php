<?php

namespace App\JsonApi\Filters;

use App\Models\TeamLedgerClaim;
use Illuminate\Database\Eloquent\Builder;
use LaravelJsonA<PERSON>\Core\Facades\JsonApi;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Contracts\Filter;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Filters\Concerns\IsSingular;

class TeamLedgerClaimableFilter implements Filter
{
    use IsSingular;

    /**
     * Create a new filter.
     *
     * @param string $name
     * @return TeamLedgerClaimableFilter
     */
    public static function make(string $name): self
    {
        return new self($name);
    }

    /**
     * TeamLedgerClaimableFilter constructor.
     *
     * @param string $name
     */
    public function __construct(private readonly string $name) {}

    /**
     * Get the key for the filter.
     *
     * @return string
     */
    public function key(): string
    {
        return $this->name;
    }

    /**
     * Apply the filter to the query.
     *
     * @param Builder|TeamLedgerClaim $query
     * @param string $value
     * @return Builder|TeamLedgerClaim
     */
    public function apply($query, $value): TeamLedgerClaim|Builder {
        $server = JsonApi::server();
        $schema = $server->schemas()->schemaFor($value);
        $claimableType = $schema::model();
        return $query->whereClaimableType($claimableType);
    }
}
