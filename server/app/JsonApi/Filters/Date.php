<?php

namespace App\JsonApi\Filters;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Laravel<PERSON>sonA<PERSON>\Eloquent\Filters\Where;

class Date extends Where {
    public function __construct(string $name, string $column = null) {
        $this->deserializeUsing(
            static fn($value) => Carbon::parse($value)
        );
        parent::__construct($name, $column);
    }

    /**
     * @param mixed $value
     *
     * @return Carbon
     */
    protected function deserialize($value): Carbon {
        return parent::deserialize($value);
    }

    /**
     * Apply the filter to the query.
     *
     * @param Builder<Model> $query
     * @param mixed          $value
     *
     * @return Builder<Model>
     */
    public function apply($query, $value) {
        return $query->where(
            $query->getModel()->qualifyColumn($this->column()),
            $this->operator(),
            $this->deserialize($value)
        );
    }
}
