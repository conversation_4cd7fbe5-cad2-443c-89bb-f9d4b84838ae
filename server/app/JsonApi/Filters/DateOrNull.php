<?php

namespace App\JsonApi\Filters;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class DateOrNull extends Date {

    /**
     * Apply the filter to the query.
     *
     * @param Builder<Model> $query
     * @param mixed          $value
     *
     * @return Builder<Model>
     */
    public function apply($query, $value) {
        return $query->where(function (Builder $query) use ($value) {
            $query->where(
                $query->getModel()->qualifyColumn($this->column()),
                $this->operator(),
                $this->deserialize($value)
            )->orWhereNull($query->getModel()->qualifyColumn($this->column()),
            );
        });
    }
}
