<?php

namespace App\<PERSON>son<PERSON><PERSON>\V1\Subscriptions;

use App\Json<PERSON><PERSON>\CommonSchema;
use App\Models\Subscription;
use Lara<PERSON><PERSON>sonA<PERSON>\Contracts\Schema\Field;
use LaravelJsonApi\Contracts\Schema\Filter;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Contracts\Paginator;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\ID;
use Lara<PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Str;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Filters\WhereIdIn;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Pagination\PagePagination;

class SubscriptionSchema extends CommonSchema
{

    /**
     * The model the schema corresponds to.
     *
     * @var string
     */
    public static string $model = Subscription::class;

    /**
     * Get the resource fields.
     *
     * @return array<int, Field>
     */
    public function fields(): array
    {
        return self::withTimestamps([
            ID::make()->uuid(),
            Str::make('userId')->readOnly(),
            Str::make('sponsorName')->extractUsing(
                static fn (Subscription $model) => $model->user?->person->getFullName()
            )->readOnly()
        ]);
    }

    /**
     * Get the resource filters.
     *
     * @return array<int, Filter>
     */
    public function filters(): array
    {
        return [
            WhereIdIn::make($this),
        ];
    }

    /**
     * Get the resource paginator.
     *
     * @return Paginator|null
     */
    public function pagination(): ?Paginator
    {
        return PagePagination::make();
    }

}
