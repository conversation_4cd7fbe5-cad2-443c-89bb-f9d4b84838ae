<?php

namespace App\JsonApi\V1;

use App\JsonApi\V1\Absences\AbsenceSchema;
use App\JsonApi\V1\AdminStatsTeams\AdminStatsTeamSchema;
use App\JsonApi\V1\Comments\CommentSchema;
use App\JsonApi\V1\DeviceInfo\DeviceInfoSchema;
use App\JsonApi\V1\ICalendars\ICalendarSchema;
use App\JsonApi\V1\Invites\InviteSchema;
use App\JsonApi\V1\Persons\PersonSchema;
use App\JsonApi\V1\Subscriptions\SubscriptionSchema;
use App\JsonApi\V1\TeamEvents\TeamEventSchema;
use App\JsonApi\V1\TeamEventSeries\TeamEventSeriesSchema;
use App\JsonApi\V1\TeamEventTaskConfigs\TeamEventTaskConfigSchema;
use App\JsonApi\V1\TeamEventTasks\TeamEventTaskSchema;
use App\JsonApi\V1\TeamEventVotes\TeamEventVoteSchema;
use App\JsonApi\V1\TeamLedgerClaims\TeamLedgerClaimSchema;
use App\JsonApi\V1\TeamLedgerDues\TeamLedgerDuesSchema;
use App\JsonApi\V1\TeamLedgerFines\TeamLedgerFineSchema;
use App\JsonApi\V1\TeamLedgers\TeamLedgerSchema;
use App\JsonApi\V1\TeamLedgerTransactions\TeamLedgerTransactionSchema;
use App\JsonApi\V1\TeamMembers\TeamMemberSchema;
use App\JsonApi\V1\TeamPermissions\TeamPermissionSchema;
use App\JsonApi\V1\TeamRoles\TeamRoleSchema;
use App\JsonApi\V1\Teams\TeamSchema;
use App\JsonApi\V1\TeamStatsRanges\TeamStatsRangeSchema;
use App\JsonApi\V1\Users\UserSchema;
use App\Models\Absence;
use App\Models\DeviceInfo;
use App\Models\ICalendar;
use App\Models\Scopes\LimitToCurrentUserAbsencesScope;
use App\Models\Scopes\LimitToCurrentUserICalendarsScope;
use App\Models\Scopes\LimitToCurrentUserTeamEventTaskConfigScope;
use App\Models\Scopes\LimitToCurrentUserTeamRelationScope;
use App\Models\Scopes\LimitToCurrentUserTeamsScope;
use App\Models\Team;
use App\Models\TeamEvent;
use App\Models\TeamEventTask;
use App\Models\TeamEventTaskConfig;
use App\Models\TeamLedger;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use LaravelJsonApi\Core\Server\Server as BaseServer;

class Server extends BaseServer {

    /**
     * The base URI namespace for this server.
     *
     * @return string
     */
    protected function baseUri(): string {
        return config('app.api_url') . '/v1';
    }

    /**
     * Bootstrap the server when it is handling an HTTP request.
     *
     * @return void
     */
    public function serving(): void {
        Auth::shouldUse('sanctum');

        Team::addGlobalScope(new LimitToCurrentUserTeamsScope());
        TeamEvent::addGlobalScope(new LimitToCurrentUserTeamRelationScope());
        TeamLedger::addGlobalScope(new LimitToCurrentUserTeamRelationScope());
        TeamEventTaskConfig::addGlobalScope(new LimitToCurrentUserTeamRelationScope());
        TeamEventTask::addGlobalScope(new LimitToCurrentUserTeamEventTaskConfigScope());
        Absence::addGlobalScope(new LimitToCurrentUserAbsencesScope());
        ICalendar::addGlobalScope(new LimitToCurrentUserICalendarsScope());

        // Ensure that users can only ever read their own device information
        DeviceInfo::addGlobalScope('user', fn(Builder $builder) => $builder->where('user_id', Auth::user()->id));
    }

    /**
     * Get the server's list of schemas.
     *
     * @return array<class-string>
     */
    protected function allSchemas(): array {
        return [
            AdminStatsTeamSchema::class,
            TeamSchema::class,
            InviteSchema::class,
            TeamMemberSchema::class,
            PersonSchema::class,
            UserSchema::class,
            TeamEventSchema::class,
            TeamEventVoteSchema::class,
            TeamRoleSchema::class,
            TeamPermissionSchema::class,
            TeamEventSeriesSchema::class,
            CommentSchema::class,
            AbsenceSchema::class,
            TeamEventTaskConfigSchema::class,
            TeamEventTaskSchema::class,
            TeamStatsRangeSchema::class,
            DeviceInfoSchema::class,
            TeamLedgerSchema::class,
            TeamLedgerTransactionSchema::class,
            TeamLedgerClaimSchema::class,
            TeamLedgerFineSchema::class,
            TeamLedgerDuesSchema::class,
            ICalendarSchema::class,
            SubscriptionSchema::class,
        ];
    }
}
