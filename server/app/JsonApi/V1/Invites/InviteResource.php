<?php

namespace App\JsonApi\V1\Invites;

use App\Http\Controllers\AuthController;
use App\JsonApi\CommonRessource;
use App\Models\Invite;
use Illuminate\Http\Request;
use LaravelJsonApi\Core\Resources\ConditionalFields;

/**
 * @extends CommonRessource<Invite>
 */
class InviteResource extends CommonRessource {

    private function getInvite(): Invite {
        return $this->resource;
    }

    private function isAuthorizedUser(): bool {
        return AuthController::getCurrentUser() !== null;
    }

    /**
     * Get the resource's attributes.
     *
     * @param Request|null $request
     *
     * @return array<int, ConditionalFields>
     */
    public function attributes($request): iterable {
        return [
            $this->mergeWhen($this->isAuthorizedUser(), [
                'token'     => $this->getInvite()->token,
                'team'      => $this->getInvite()->getRelatedTeam(),
                'createdAt' => $this->getInvite()->created_at,
                'updatedAt' => $this->getInvite()->updated_at,
            ]),
        ];
    }

    /**
     * Get the resource's relationships.
     *
     * @param Request|null $request
     *
     * @return array<int, ConditionalFields>
     */
    public function relationships($request): iterable {
        return [
            $this->mergeWhen($this->isAuthorizedUser(), [
                $this->relation('invitable'),
            ]),
        ];
    }

}
