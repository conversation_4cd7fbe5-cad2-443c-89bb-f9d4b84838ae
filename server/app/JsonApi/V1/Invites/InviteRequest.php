<?php

namespace App\JsonApi\V1\Invites;

use Illuminate\Validation\Rule;
use LaravelJsonApi\Laravel\Http\Requests\ResourceRequest;
use LaravelJsonApi\Validation\Rule as JsonApiRule;

class InviteRequest extends ResourceRequest {

    /**
     * Get the validation rules for the resource.
     *
     * @return array<string, array<int, Rule|string>>
     */
    public function rules(): array {
        return [
            'invitable' => ['required', JsonApiRule::toOne()],
            'token' => ['prohibited'],
        ];
    }
}
