<?php

namespace App\JsonApi\V1\Invites;

use Illuminate\Contracts\Validation\Rule;
use Laravel<PERSON>sonA<PERSON>\Laravel\Http\Requests\ResourceQuery;
use LaravelJsonApi\Validation\Rule as JsonApiRule;

class InviteCollectionQuery extends ResourceQuery {

    /**
     * Get the validation rules that apply to the request query parameters.
     *
     * @return array<string, array<int, Rule|string>>
     */
    public function rules(): array {
        return [
            'fields'       => [
                'nullable',
                'array',
                JsonApiRule::fieldSets(),
            ],
            'filter'       => [
                'nullable',
                'array',
                JsonApiRule::filter(),
            ],
            'filter.token' => [
                'required',
            ],
            'include'      => [
                'nullable',
                'string',
                JsonApiRule::includePaths([
                    'invitable',
                    'invitable.roles',
                    'invitable.members.statusRole',
                    'invitable.members.person',
                    'invitable.members.invite',
                ]),
            ],
            'page'         => [
                'nullable',
                'array',
                JsonApiRule::page(),
            ],
            'sort'         => [
                'nullable',
                'string',
                JsonApiRule::sort(),
            ],
            'withCount'    => [
                'nullable',
                'string',
                JsonApiRule::countable(),
            ],
        ];
    }
}
