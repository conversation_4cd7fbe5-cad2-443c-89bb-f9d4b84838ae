<?php

namespace App\Json<PERSON><PERSON>\V1\Invites;

use App\Http\Controllers\AuthController;
use App\<PERSON>sonA<PERSON>\CommonSchema;
use App\JsonApi\V1\TeamMembers\TeamMemberSchema;
use App\JsonApi\V1\Teams\TeamSchema;
use App\Models\Invite;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Contracts\Schema\Field;
use Lara<PERSON><PERSON>sonA<PERSON>\Contracts\Schema\Filter;
use Lara<PERSON><PERSON>sonA<PERSON>\Eloquent\Contracts\Paginator;
use Lara<PERSON><PERSON>sonA<PERSON>\Eloquent\Fields\ID;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Relations\MorphTo;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Str;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Filters\Where;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Filters\WhereIdIn;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Pagination\PagePagination;

class InviteSchema extends CommonSchema {
    public static string $model = Invite::class;

    public function authorizable(): bool {
        if (AuthController::getCurrentUser() === null) {
            // default authorization will NOT run
            return false;
        }

        // default authorization will run...
        return true;
    }

    /**
     * Get the resource fields.
     *
     * @return array<int, Field>
     */
    public function fields(): array {
        return self::withTimestamps([
            ID::make()->uuid(),
            Str::make('token'),

            // Relations
            MorphTo::make('invitable')->types(
                TeamMemberSchema::type(),
                TeamSchema::type(),
            ),
            Str::make('team')->readOnly(),
        ]);
    }

    /**
     * Get the resource filters.
     *
     * @return array<int, Filter>
     */
    public function filters(): array {
        return [
            WhereIdIn::make($this),

            // Allow directly retrieving invites by token
            Where::make('token', 'token'),
        ];
    }

    public function pagination(): ?Paginator {
        return PagePagination::make();
    }

}
