<?php

namespace App\JsonApi\V1\Teams;

use Illuminate\Contracts\Validation\Rule;
use Laravel<PERSON>sonA<PERSON>\Laravel\Http\Requests\ResourceQuery;
use LaravelJsonApi\Validation\Rule as JsonApiRule;

class TeamCollectionQuery extends ResourceQuery {

    /**
     * Get the validation rules that apply to the request query parameters.
     *
     * @return array<string, array<int, Rule|string>>
     */
    public function rules(): array {
        return [
            'fields'    => [
                'nullable',
                'array',
                JsonApiRule::fieldSets(),
            ],
            'filter'    => [
                'nullable',
                'array',
                JsonApiRule::filter(),
            ],
            'include'   => [
                'nullable',
                'string',
                JsonApiRule::includePaths([
                    'roles',
                    'members.statusRole',
                    'members.person',
                    'activeStatsRanges',
                    'statsRanges',
                    'ledger',
                    'ledger.dueItemClaims',
                ]),
            ],
            'page'      => [
                'nullable',
                'array',
                JsonApiRule::page(),
            ],
            'sort'      => [
                'nullable',
                'string',
                JsonApiRule::sort(),
            ],
            'withCount' => [
                'nullable',
                'string',
                JsonApiRule::countable(),
            ],
        ];
    }
}
