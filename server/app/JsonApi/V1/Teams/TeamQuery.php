<?php

namespace App\JsonApi\V1\Teams;

use Illuminate\Contracts\Validation\Rule;
use Laravel<PERSON>sonA<PERSON>\Laravel\Http\Requests\ResourceQuery;
use LaravelJsonApi\Validation\Rule as JsonApiRule;

class TeamQuery extends ResourceQuery {

    /**
     * Get the validation rules that apply to the request query parameters.
     *
     * @return array<string, array<int, Rule|string>>
     */
    public function rules(): array {
        return [
            'fields' => [
                'nullable',
                'array',
                JsonApiRule::fieldSets(),
            ],
            'filter' => [
                'nullable',
                'array',
                JsonApiRule::filter()->forget('id'),
            ],
            'include' => [
                'nullable',
                'string',
                JsonApiRule::includePaths([
                    'invite',
                    'roles',
                    'members.statusRole',
                    'members.roles',
                    'members.person',
                    'members.invite',
                    'activeStatsRanges',
                    'statsRanges',
                    'ledger',
                    'ledger.formerMembers',
                    'ledger.fines',
                    'ledger.dueItemClaims',
                    'ledger.dueItemClaims.claimable',
                    'ledger.dueItemClaims.teamMember',
                    'activeSubscription',
                ]),
            ],
            'page' => JsonApiRule::notSupported(),
            'sort' => JsonApiRule::notSupported(),
            'withCount' => [
                'nullable',
                'string',
                JsonApiRule::countable(),
            ],
        ];
    }
}
