<?php

namespace App\JsonApi\V1\Teams;

use App\JsonApi\CommonSchema;
use App\JsonApi\V1\Invites\InviteSchema;
use App\JsonApi\V1\Subscriptions\SubscriptionSchema;
use App\JsonApi\V1\TeamEvents\TeamEventSchema;
use App\JsonApi\V1\TeamEventTaskConfigs\TeamEventTaskConfigSchema;
use App\JsonApi\V1\TeamLedgers\TeamLedgerSchema;
use App\JsonApi\V1\TeamMembers\TeamMemberSchema;
use App\JsonApi\V1\TeamRoles\TeamRoleSchema;
use App\JsonApi\V1\TeamStatsRanges\TeamStatsRangeSchema;
use App\Models\Team;
use LaravelJsonApi\Contracts\Schema\Field;
use Laravel<PERSON>sonApi\Contracts\Schema\Filter;
use LaravelJsonApi\Eloquent\Contracts\Paginator;
use Lara<PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Boolean;
use Lara<PERSON><PERSON>sonApi\Eloquent\Fields\DateTime;
use LaravelJsonApi\Eloquent\Fields\ID;
use LaravelJsonApi\Eloquent\Fields\Number;
use Lara<PERSON><PERSON>sonApi\Eloquent\Fields\Relations\HasMany;
use LaravelJsonApi\Eloquent\Fields\Relations\HasOne;
use LaravelJsonApi\Eloquent\Fields\Str;
use LaravelJsonApi\Eloquent\Filters\WhereIdIn;
use LaravelJsonApi\Eloquent\Pagination\PagePagination;

class TeamSchema extends CommonSchema {

    public static string $model = Team::class;

    protected $defaultSort = 'name';

    /**
     * @return array<int, Field>
     */
    public function fields(): array {
        return self::withTimestamps([
            ID::make()->uuid(),
            Str::make('name')->sortable(),
            Str::make('icon_name'),
            Number::make('membersCount'),
            Number::make('registeredMembersCount'),
            Boolean::make('isSubscribed')->readOnly(),
            Boolean::make('isPremium')->readOnly(),
            DateTime::make('subscriptionEndsAt')->readOnly(),
            Str::make('subscriptionPriceType'),
            Boolean::make('isOnTrial')->readOnly(),
            DateTime::make('trialEndsAt')->readOnly(),

            // Relations
            HasMany::make('members')->type(TeamMemberSchema::type()),
            HasMany::make('events')->type(TeamEventSchema::type()),
            HasMany::make('roles')->type(TeamRoleSchema::type()),
            HasOne::make('invite')->type(InviteSchema::type()),
            HasMany::make('eventTaskConfigs')->type(TeamEventTaskConfigSchema::type()),
            HasMany::make('statsRanges')->type(TeamStatsRangeSchema::type()),
            HasMany::make('activeStatsRanges')->type(TeamStatsRangeSchema::type())->readOnly(),
            HasOne::make('ledger')->type(TeamLedgerSchema::type())->readOnly(),
            HasOne::make('activeSubscription')->type(SubscriptionSchema::type())->readOnly(),
        ]);
    }

    /**
     * @return array<int, Filter>
     */
    public function filters(): array {
        return [
            WhereIdIn::make($this),
        ];
    }

    public function pagination(): ?Paginator {
        return PagePagination::make();
    }

}
