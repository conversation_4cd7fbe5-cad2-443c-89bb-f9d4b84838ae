<?php

namespace App\JsonApi\V1\Teams;

use Illuminate\Contracts\Validation\Rule;
use LaravelJsonApi\Laravel\Http\Requests\ResourceRequest;

class TeamRequest extends ResourceRequest {

    /**
     * @return array<string, array<int, Rule|string>>
     */
    public function rules(): array {
        return [
            'name' => ['required', 'string'],
            'iconName' => ['nullable'],
        ];
    }

}
