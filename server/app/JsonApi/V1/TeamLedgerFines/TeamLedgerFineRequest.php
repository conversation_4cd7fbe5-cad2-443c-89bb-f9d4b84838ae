<?php

namespace App\JsonApi\V1\TeamLedgerFines;

use App\Rules\Money;
use LaravelJsonApi\Laravel\Http\Requests\ResourceRequest;
use LaravelJsonApi\Validation\Rule as JsonApiRule;

class TeamLedgerFineRequest extends ResourceRequest
{

    /**
     * Get the validation rules for the resource.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'title' => ['required', 'string'],
            'amount' => ['nullable', new Money()],
            'item' => ['nullable', 'string'],
            'dateBegin' => ['required', 'date_format:Y-m-d'],
            'ledger' => ['required', JsonApiRule::toOne()],
            'parent' => ['nullable', JsonApiRule::toOne()],
            'deletedAt' => ['nullable', JsonApiRule::dateTime()],
        ];
    }

}
