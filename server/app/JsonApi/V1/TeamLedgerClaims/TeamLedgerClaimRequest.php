<?php

namespace App\JsonApi\V1\TeamLedgerClaims;

use App\Rules\Money;
use Laravel<PERSON>sonA<PERSON>\Laravel\Http\Requests\ResourceRequest;
use LaravelJsonApi\Validation\Rule as JsonApiRule;

class TeamLedgerClaimRequest extends ResourceRequest
{

    /**
     * Get the validation rules for the resource.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'exempt' => JsonApiRule::boolean(),
            'title' => ['nullable', 'string'],
            'dueDate' => ['required', 'date_format:Y-m-d'],
            'amount' => ['nullable', new Money()],
            'item' => ['nullable', 'string'],
            'fulfilledAt' => ['nullable', 'date_format:Y-m-d'],
            'teamMember' => ['required', JsonApiRule::toOne()],
            'ledger' => ['required', JsonApiRule::toOne()],
            'claimable' => ['nullable', JsonApiRule::toOne()],
        ];
    }

}
