<?php

namespace App\JsonApi\V1\TeamLedgerClaims;

use App\JsonApi\CommonSchema;
use App\<PERSON>sonA<PERSON>\Fields\Money;
use App\JsonApi\Filters\TeamLedgerClaimableFilter;
use App\JsonApi\V1\Persons\PersonSchema;
use App\JsonApi\V1\TeamLedgerDues\TeamLedgerDuesSchema;
use App\JsonApi\V1\TeamLedgerFines\TeamLedgerFineSchema;
use App\JsonApi\V1\TeamLedgers\TeamLedgerSchema;
use App\JsonApi\V1\TeamMembers\TeamMemberSchema;
use App\Models\TeamLedgerClaim;
use Laravel<PERSON>sonApi\Eloquent\Contracts\Paginator;
use <PERSON><PERSON><PERSON>sonA<PERSON>\Eloquent\Fields\Boolean;
use LaravelJsonApi\Eloquent\Fields\DateTime;
use LaravelJsonApi\Eloquent\Fields\ID;
use LaravelJsonApi\Eloquent\Fields\Relations\BelongsTo;
use Lara<PERSON><PERSON>sonApi\Eloquent\Fields\Relations\MorphTo;
use <PERSON><PERSON><PERSON>sonApi\Eloquent\Fields\Str;
use Lara<PERSON>JsonA<PERSON>\Eloquent\Filters\Where;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Filters\WhereIdIn;
use LaravelJsonApi\Eloquent\Pagination\PagePagination;

class TeamLedgerClaimSchema extends CommonSchema
{

    /**
     * The model the schema corresponds to.
     *
     * @var string
     */
    public static string $model = TeamLedgerClaim::class;

    /**
     * Get the resource fields.
     *
     * @return array
     */
    public function fields(): array
    {
        return self::withTimestamps([
            ID::make()->uuid(),
            Boolean::make('exempt'),
            Str::make('title'),
            DateTime::make('dueDate')->sortable()->serializeUsing($this::iso8601DateFormat()),
            Money::make('amount'),
            Str::make('item'),
            DateTime::make('fulfilledAt')->serializeUsing($this::iso8601DateFormat()),
            Str::make('status')->readOnly()->extractUsing(fn(TeamLedgerClaim $model) => $model->getStatus()),

            // Relations
            BelongsTo::make('teamMember')->type(TeamMemberSchema::type()),
            BelongsTo::make('author')->type(PersonSchema::type())->readOnly(),
            BelongsTo::make('ledger')->type(TeamLedgerSchema::type()),
            MorphTo::make('claimable')->types(
                TeamLedgerFineSchema::type(),
                TeamLedgerDuesSchema::type(),
            ),
        ]);
    }

    /**
     * Get the resource filters.
     *
     * @return array
     */
    public function filters(): array
    {
        return [
            WhereIdIn::make($this),
            Where::make('ledgerId', 'team_ledger_id'),
            Where::make('claimableId', 'claimable_id'),
            TeamLedgerClaimableFilter::make('claimableType')
        ];
    }

    /**
     * Get the resource paginator.
     *
     * @return Paginator|null
     */
    public function pagination(): ?Paginator
    {
        return PagePagination::make();
    }

}
