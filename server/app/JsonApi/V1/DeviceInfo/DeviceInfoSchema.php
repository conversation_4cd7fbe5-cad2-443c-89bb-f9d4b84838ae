<?php

namespace App\<PERSON>son<PERSON><PERSON>\V1\DeviceInfo;

use App\Json<PERSON><PERSON>\CommonSchema;
use App\Models\DeviceInfo;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Contracts\Schema\Field;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Contracts\Schema\Filter;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Contracts\Paginator;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Fields\Boolean;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Fields\DateTime;
use Lara<PERSON><PERSON>sonA<PERSON>\Eloquent\Fields\ID;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Str;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Filters\WhereIdIn;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Pagination\PagePagination;

class DeviceInfoSchema extends CommonSchema {

    public static string $model = DeviceInfo::class;

    /**
     * Get the resource fields.
     *
     * @return array<int, Field>
     */
    public function fields(): array {
        return self::withTimestamps([
            ID::make()->uuid(),
            Str::make('name'),
            Str::make('displayModel')->extractUsing(fn(DeviceInfo $model) => $model->getDisplayModel()),
            Boolean::make('pushNotificationsEnabled'),
            DateTime::make('lastSeen'),
        ]);
    }

    /**
     * Get the resource filters.
     *
     * @return array<int, Filter>
     */
    public function filters(): array {
        return [
            WhereIdIn::make($this),
        ];
    }

    public function pagination(): ?Paginator {
        return PagePagination::make();
    }
}
