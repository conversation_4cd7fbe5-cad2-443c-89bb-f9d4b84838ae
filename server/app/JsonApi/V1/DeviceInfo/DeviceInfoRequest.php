<?php

namespace App\<PERSON>sonApi\V1\DeviceInfo;

use Illuminate\Validation\Rule;
use Laravel<PERSON>son<PERSON><PERSON>\Laravel\Http\Requests\ResourceRequest;
use LaravelJsonApi\Validation\Rule as JsonApiRule;

class DeviceInfoRequest extends ResourceRequest {

    /**
     * Fields that should be modified as part of a create or update request MUST be listed here. Otherwise, they will be filtered out.
     *
     * @return array<string, array<int, Rule|string>>
     */
    public function rules(): array {
        return [
            'pushNotificationsEnabled' => ['nullable', 'boolean'],
        ];
    }
}
