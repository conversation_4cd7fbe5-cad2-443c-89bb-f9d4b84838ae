<?php

namespace App\JsonApi\V1\Comments;

use Illuminate\Validation\Rule;
use LaravelJsonApi\Laravel\Http\Requests\ResourceRequest;
use LaravelJsonApi\Validation\Rule as JsonApiRule;

class CommentRequest extends ResourceRequest {

    /**
     * Fields that should be modified as part of a create or update request MUST be listed here. Otherwise, they will be filtered out.
     *
     * @return array<string, array<int, Rule|string>>
     */
    public function rules(): array {
        return [
            'text'   => ['required', 'string'],
            'parent' => ['required', JsonApiRule::toOne()],
        ];
    }

}
