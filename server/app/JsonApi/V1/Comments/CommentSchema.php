<?php

namespace App\<PERSON>son<PERSON><PERSON>\V1\Comments;

use App\<PERSON>son<PERSON><PERSON>\CommonSchema;
use App\<PERSON>son<PERSON><PERSON>\V1\Persons\PersonSchema;
use App\JsonApi\V1\TeamEvents\TeamEventSchema;
use App\JsonApi\V1\TeamEventVotes\TeamEventVoteSchema;
use App\Models\Comment;
use Lara<PERSON><PERSON>son<PERSON><PERSON>\Contracts\Schema\Field;
use Lara<PERSON><PERSON>son<PERSON><PERSON>\Contracts\Schema\Filter;
use Lara<PERSON><PERSON><PERSON>A<PERSON>\Eloquent\Contracts\Paginator;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\ID;
use LaravelJsonApi\Eloquent\Fields\Relations\BelongsTo;
use <PERSON><PERSON><PERSON>son<PERSON>pi\Eloquent\Fields\Relations\MorphTo;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Fields\Str;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Filters\WhereIdIn;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Pagination\PagePagination;

class CommentSchema extends CommonSchema {

    /**
     * The model the schema corresponds to.
     *
     * @var string
     */
    public static string $model = Comment::class;

    /**
     * Get the resource fields.
     *
     * @return array<int, Field>
     */
    public function fields(): array {
        return self::withTimestamps([
            ID::make()->uuid(),
            Str::make('text'),

            // Relations
            BelongsTo::make('author')->type(PersonSchema::type()),
            MorphTo::make('parent')->types(
                TeamEventVoteSchema::type(),
                TeamEventSchema::type(), // TODO there have to be two arguments, so replace later when comments are used on another model
            ),
        ]);
    }

    /**
     * Get the resource filters.
     *
     * @return array<int, Filter>
     */
    public function filters(): array {
        return [
            WhereIdIn::make($this),
        ];
    }

    /**
     * Get the resource paginator.
     *
     * @return Paginator|null
     */
    public function pagination(): ?Paginator {
        return PagePagination::make();
    }

}
