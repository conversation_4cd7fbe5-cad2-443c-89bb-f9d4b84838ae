<?php

namespace App\<PERSON>son<PERSON><PERSON>\V1\Absences;

use App\<PERSON>son<PERSON><PERSON>\CommonSchema;
use App\<PERSON>son<PERSON><PERSON>\V1\Persons\PersonSchema;
use App\JsonApi\V1\TeamMembers\TeamMemberSchema;
use App\Models\Absence;
use Illuminate\Database\Eloquent\Builder;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Contracts\Schema\Field;
use Lara<PERSON><PERSON>son<PERSON><PERSON>\Contracts\Schema\Filter;
use Lara<PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Contracts\Paginator;
use Lara<PERSON><PERSON>sonA<PERSON>\Eloquent\Fields\ArrayHash;
use <PERSON><PERSON><PERSON>sonA<PERSON>\Eloquent\Fields\DateTime;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\ID;
use Lara<PERSON><PERSON>sonApi\Eloquent\Fields\Relations\BelongsTo;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Fields\Relations\BelongsToMany;
use <PERSON><PERSON><PERSON>sonA<PERSON>\Eloquent\Fields\Str;
use <PERSON><PERSON><PERSON>sonA<PERSON>\Eloquent\Filters\WhereIdIn;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Pagination\PagePagination;

class AbsenceSchema extends CommonSchema {

    /**
     * The model the schema corresponds to.
     *
     * @var string
     */
    public static string $model = Absence::class;

    protected $defaultSort = ['-dateBegin'];
    
    /**
     * Get the resource fields.
     *
     * @return array<int, Field>
     */
    public function fields(): array {
        return self::withTimestamps([
            ID::make()->uuid(),
            Str::make('name'),
            Str::make('absenceType'),
            DateTime::make('dateBegin')->sortable()->serializeUsing($this::iso8601DateFormat()),
            DateTime::make('dateEnd')->sortable()->serializeUsing($this::iso8601DateFormat()),
            Str::make('rruleString'),
            ArrayHash::make('targetTeamMemberStrings')->readOnly()->extractUsing(
                static function (Absence $model, $column, $value) {
                    return $model->getUniqueTargetTeamMembers()
                        ->map(fn($member) => $member->name . ' (' . $member->team->name . ')');
                }
            ),

            // Relations
            BelongsTo::make('author')->type(PersonSchema::type()),
            BelongsToMany::make('teamMembers')->type(TeamMemberSchema::type()),
            BelongsToMany::make('persons')->type(PersonSchema::type()),
        ]);
    }

    /**
     * Get the resource filters.
     *
     * @return array<int, Filter>
     */
    public function filters(): array {
        return [
            WhereIdIn::make($this),
        ];
    }

    /**
     * Get the resource paginator.
     *
     * @return Paginator|null
     */
    public function pagination(): ?Paginator {
        return PagePagination::make();
    }

}
