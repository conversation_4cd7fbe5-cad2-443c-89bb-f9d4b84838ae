<?php

namespace App\<PERSON>son<PERSON>pi\V1\Absences;

use App\<PERSON>sonApi\CommonRessourceRequest;
use App\Rules\RRule;
use Lara<PERSON><PERSON>sonA<PERSON>\Validation\Rule as JsonApiRule;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Validation\Rules\HasMany;

class AbsenceRequest extends CommonRessourceRequest {

    /**
     * @return array<string, array<int, RRule|string>|HasMany>
     */
    public function rules(): array {
        return [
            'name'        => ['nullable', 'string'],
            'absenceType' => ['required', 'string'],
            'dateBegin'   => ['required', 'date_format:Y-m-d'],
            'dateEnd'     => ['nullable', 'date_format:Y-m-d'],
            'rruleString' => ['nullable', 'string', new RRule()],
            'teamMembers' => JsonApiRule::toMany(),
            'persons'     => JsonApiRule::toMany(),
        ];
    }
}
