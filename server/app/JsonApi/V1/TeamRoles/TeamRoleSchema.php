<?php

namespace App\<PERSON>son<PERSON><PERSON>\V1\TeamRoles;

use App\<PERSON>son<PERSON><PERSON>\CommonSchema;
use App\JsonA<PERSON>\V1\TeamPermissions\TeamPermissionSchema;
use App\Models\TeamRole;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Contracts\Schema\Field;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Contracts\Schema\Filter;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Contracts\Paginator;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\ID;
use Lara<PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Relations\BelongsToMany;
use <PERSON><PERSON><PERSON>sonApi\Eloquent\Fields\Str;
use Lara<PERSON>JsonApi\Eloquent\Filters\WhereIdIn;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Pagination\PagePagination;

class TeamRoleSchema extends CommonSchema {

    public static string $model = TeamRole::class;

    /**
     * Get the resource fields.
     *
     * @return array<int, Field>
     */
    public function fields(): array {
        return [
            ID::make()->uuid(),
            Str::make('name'),

            // Relations
            BelongsToMany::make('permissions')->type(TeamPermissionSchema::type()),
        ];
    }

    /**
     * Get the resource filters.
     *
     * @return array<int, Filter>
     */
    public function filters(): array {
        return [
            WhereIdIn::make($this),
        ];
    }

    public function pagination(): ?Paginator {
        return PagePagination::make();
    }

}
