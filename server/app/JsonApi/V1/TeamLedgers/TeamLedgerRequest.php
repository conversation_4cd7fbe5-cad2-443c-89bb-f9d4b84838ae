<?php

namespace App\<PERSON>sonApi\V1\TeamLedgers;

use Lara<PERSON><PERSON>sonA<PERSON>\Laravel\Http\Requests\ResourceRequest;
use LaravelJsonApi\Validation\Rule as JsonApiRule;

class TeamLedgerRequest extends ResourceRequest
{

    /**
     * Get the validation rules for the resource.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'team' => ['required', JsonApiRule::toOne()],
        ];
    }

}
