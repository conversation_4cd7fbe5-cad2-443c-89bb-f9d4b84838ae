<?php

namespace App\<PERSON>son<PERSON><PERSON>\V1\TeamLedgers;

use App\JsonA<PERSON>\CommonSchema;
use App\<PERSON>sonA<PERSON>\Fields\Money;
use App\JsonApi\V1\TeamLedgerClaims\TeamLedgerClaimSchema;
use App\JsonApi\V1\TeamLedgerFines\TeamLedgerFineSchema;
use App\JsonApi\V1\TeamMembers\TeamMemberSchema;
use App\JsonApi\V1\Teams\TeamSchema;
use App\Models\TeamLedger;
use LaravelJsonApi\Eloquent\Contracts\Paginator;
use LaravelJsonApi\Eloquent\Fields\ArrayHash;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\ID;
use Lara<PERSON><PERSON>sonApi\Eloquent\Fields\Relations\BelongsTo;
use <PERSON><PERSON><PERSON>sonA<PERSON>\Eloquent\Fields\Relations\HasMany;
use <PERSON><PERSON><PERSON>sonApi\Eloquent\Filters\WhereIdIn;
use LaravelJsonApi\Eloquent\Pagination\PagePagination;

class TeamLedgerSchema extends CommonSchema
{

    /**
     * The model the schema corresponds to.
     *
     * @var string
     */
    public static string $model = TeamLedger::class;

    /**
     * Get the resource fields.
     *
     * @return array
     */
    public function fields(): array
    {
        return self::withTimestamps([
            ID::make()->uuid(),

            Money::make('totalClaimBalance')->readOnly(),
            Money::make('availableBalance')->readOnly(),
            Money::make('availableBalanceFromMembers')->readOnly(),
            Money::make('claimBalance')->readOnly(),
            Money::make('futureClaimBalance')->readOnly(),
            Money::make('balance')->readOnly(),
            Money::make('creditBalance')->readOnly(),
            Money::make('nonMemberBalance')->readOnly(),
            Money::make('nonMemberSumIn')->readOnly(),
            Money::make('nonMemberSumOut')->readOnly(),
            ArrayHash::make('memberStatus')->readOnly(),

            // Relations
            BelongsTo::make('team')->type(TeamSchema::type()),
            HasMany::make('fines')->type(TeamLedgerFineSchema::type()),
            HasMany::make('dueItemClaims')->type(TeamLedgerClaimSchema::type()),
            HasMany::make('formerMembers')->type(TeamMemberSchema::type())->readOnly(),
        ]);
    }

    /**
     * Get the resource filters.
     *
     * @return array
     */
    public function filters(): array
    {
        return [
            WhereIdIn::make($this),
        ];
    }

    /**
     * Get the resource paginator.
     *
     * @return Paginator|null
     */
    public function pagination(): ?Paginator
    {
        return PagePagination::make();
    }

}
