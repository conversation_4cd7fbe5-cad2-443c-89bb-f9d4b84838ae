<?php

namespace App\JsonApi\V1\TeamLedgers;

use App\JsonApi\CommonRessource;
use App\Models\TeamLedger;
use Illuminate\Http\Request;

/**
 * @extends CommonRessource<TeamLedger>
 */
class TeamLedgerResource extends CommonRessource
{

    private function getLedger(): TeamLedger {
        return $this->resource;
    }

    /**
     * Get the resource's attributes.
     *
     * @param Request|null $request
     * @return iterable
     */
    public function attributes($request): iterable
    {
        return [
            'totalClaimBalance' => $this->getLedger()->getTotalClaimBalance(),
            'availableBalance' => $this->getLedger()->getAvailableBalance(),
            'availableBalanceFromMembers' => $this->getLedger()->getAvailableBalanceFromMembers(),
            'claimBalance' => $this->getLedger()->getClaimBalance(),
            'futureClaimBalance' => $this->getLedger()->getFutureClaimBalance(),
            'balance' => $this->getLedger()->getBalance(),
            'creditBalance' => $this->getLedger()->getCreditBalance(),
            'nonMemberBalance' => $this->getLedger()->getBalanceFromNonMembers(),
            'nonMemberSumIn' => $this->getLedger()->getSumInFromNonMembers(),
            'nonMemberSumOut' => $this->getLedger()->getSumOutFromNonMembers(),
            'memberStatus' => $this->when($this->hasFieldRequested('memberStatus'),
                fn() => $this->getLedger()->getMemberStatus()),

            'createdAt' => $this->getLedger()->created_at,
            'updatedAt' => $this->getLedger()->updated_at,
        ];
    }

    /**
     * Get the resource's relationships.
     *
     * @param Request|null $request
     * @return iterable
     */
    public function relationships($request): iterable
    {
        return [
            $this->relation('team'),
            $this->relation('fines'),
            $this->relation('dueItemClaims'),
            $this->relation('formerMembers'),
        ];
    }

}
