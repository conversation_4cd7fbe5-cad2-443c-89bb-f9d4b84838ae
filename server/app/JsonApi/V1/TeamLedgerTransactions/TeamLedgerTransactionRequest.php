<?php

namespace App\JsonApi\V1\TeamLedgerTransactions;

use App\Rules\Money;
use Laravel<PERSON>sonApi\Laravel\Http\Requests\ResourceRequest;
use LaravelJsonApi\Validation\Rule as JsonApiRule;

class TeamLedgerTransactionRequest extends ResourceRequest
{

    /**
     * Get the validation rules for the resource.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'title' => ['nullable', 'string'],
            'amount' => ['required', new Money()],
            'teamMember' => ['nullable', JsonApiRule::toOne()],
            'ledger' => ['required', JsonApiRule::toOne()],
        ];
    }

}
