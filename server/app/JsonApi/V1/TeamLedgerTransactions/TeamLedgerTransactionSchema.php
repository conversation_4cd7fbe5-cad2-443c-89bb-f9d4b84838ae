<?php

namespace App\<PERSON>son<PERSON><PERSON>\V1\TeamLedgerTransactions;

use App\JsonApi\CommonSchema;
use App\<PERSON>sonApi\Fields\Money;
use App\<PERSON>sonApi\V1\Persons\PersonSchema;
use App\JsonApi\V1\TeamLedgers\TeamLedgerSchema;
use App\JsonApi\V1\TeamMembers\TeamMemberSchema;
use App\Models\TeamLedgerTransaction;
use Laravel<PERSON>sonApi\Eloquent\Contracts\Paginator;
use Lara<PERSON><PERSON>sonA<PERSON>\Eloquent\Fields\ID;
use <PERSON><PERSON><PERSON>sonA<PERSON>\Eloquent\Fields\Relations\BelongsTo;
use Lara<PERSON><PERSON>sonApi\Eloquent\Fields\Str;
use Lara<PERSON><PERSON>sonApi\Eloquent\Filters\Where;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Filters\WhereIdIn;
use <PERSON><PERSON><PERSON>sonA<PERSON>\Eloquent\Pagination\PagePagination;

class TeamLedgerTransactionSchema extends CommonSchema
{

    /**
     * The model the schema corresponds to.
     *
     * @var string
     */
    public static string $model = TeamLedgerTransaction::class;

    /**
     * Get the resource fields.
     *
     * @return array
     */
    public function fields(): array
    {
        return self::withTimestamps([
            ID::make()->uuid(),
            Str::make('title'),
            Money::make('amount')->sortable(),

            // Relations
            BelongsTo::make('teamMember')->type(TeamMemberSchema::type()),
            BelongsTo::make('author')->type(PersonSchema::type())->readOnly(),
            BelongsTo::make('ledger')->type(TeamLedgerSchema::type()),
        ]);
    }

    /**
     * Get the resource filters.
     *
     * @return array
     */
    public function filters(): array
    {
        return [
            WhereIdIn::make($this),
            Where::make('ledgerId', 'team_ledger_id'),
        ];
    }

    /**
     * Get the resource paginator.
     *
     * @return Paginator|null
     */
    public function pagination(): ?Paginator
    {
        return PagePagination::make();
    }

}
