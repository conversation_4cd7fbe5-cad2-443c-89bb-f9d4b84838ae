<?php

namespace App\<PERSON>sonApi\V1\ICalendars;

use App\JsonApi\CommonRessourceRequest;
use LaravelJsonApi\Validation\Rule as JsonApiRule;
use LaravelJsonApi\Validation\Rules\HasMany;

class ICalendarRequest extends CommonRessourceRequest
{

    /**
     * Get the validation rules for the resource.
     *
     * @return array<string, array<int,string>|HasMany>
     */
    public function rules(): array
    {
        return [
            'internalName' => ['nullable', 'string'],
            'teamMembers'  => JsonApiRule::toMany(),
            'persons'      => JsonApiRule::toMany(),
        ];
    }

}
