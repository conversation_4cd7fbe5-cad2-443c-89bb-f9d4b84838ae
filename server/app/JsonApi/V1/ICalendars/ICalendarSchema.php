<?php

namespace App\<PERSON>son<PERSON><PERSON>\V1\ICalendars;

use App\<PERSON>son<PERSON><PERSON>\CommonSchema;
use App\<PERSON>sonApi\V1\Persons\PersonSchema;
use App\JsonApi\V1\TeamMembers\TeamMemberSchema;
use App\Models\ICalendar;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Contracts\Schema\Field;
use Lara<PERSON><PERSON>son<PERSON><PERSON>\Contracts\Schema\Filter;
use Lara<PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Contracts\Paginator;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Fields\ArrayHash;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\ID;
use LaravelJsonApi\Eloquent\Fields\Relations\BelongsTo;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Relations\BelongsToMany;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Fields\Str;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Filters\WhereIdIn;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Pagination\PagePagination;

class ICalendarSchema extends CommonSchema
{

    /**
     * The model the schema corresponds to.
     *
     * @var string
     */
    public static string $model = ICalendar::class;

    /**
     * Get the resource fields.
     *
     * @return array<int, Field>
     */
    public function fields(): array
    {
        return self::withTimestamps([
            ID::make()->uuid(),
            Str::make('internalName'),
            Str::make('publicLink')->extractUsing(
                static fn (ICalendar $model) => $model->getPublicLink()
            ),
            Str::make('name')->extractUsing(
                static fn (ICalendar $model) => $model->getDefaultName()
            ),
            ArrayHash::make('targetTeamMemberStrings')->readOnly()->extractUsing(
                static function (ICalendar $model, $column, $value) {
                    return $model->getUniqueTargetTeamMembers()
                        ->map(fn($member) => $member->name . ' (' . $member->team->name . ')');
                }
            ),

            // Relations
            BelongsTo::make('author')->type(PersonSchema::type()),
            BelongsToMany::make('teamMembers')->type(TeamMemberSchema::type()),
            BelongsToMany::make('persons')->type(PersonSchema::type()),
        ]);
    }

    /**
     * Get the resource filters.
     *
     * @return array<int, Filter>
     */
    public function filters(): array
    {
        return [
            WhereIdIn::make($this),
        ];
    }

    /**
     * Get the resource paginator.
     *
     * @return Paginator|null
     */
    public function pagination(): ?Paginator
    {
        return PagePagination::make();
    }

}
