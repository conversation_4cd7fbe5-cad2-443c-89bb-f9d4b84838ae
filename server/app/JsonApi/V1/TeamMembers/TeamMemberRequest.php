<?php

namespace App\JsonApi\V1\TeamMembers;

use App\Http\Controllers\AuthController;
use App\JsonApi\CommonRessourceRequest;
use App\Models\TeamMember;
use App\Types\TeamPermissionType;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Validation\Rule as JsonApiRule;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Validation\Rules\DateTimeIso8601;
use Lara<PERSON><PERSON>sonApi\Validation\Rules\HasOne;

class TeamMemberRequest extends CommonRessourceRequest {

    public function model(): ?TeamMember {
        /** @var TeamMember */
        return parent::model();
    }

    /**
     * Get the validation rules for the resource.
     *
     * @return array<string, array<int, DateTimeIso8601|HasOne|string>>
     */
    public function rules(): array {
        $user = AuthController::getCurrentUser();
        $model = $this->model();
        $canOnlyUpdateSelf = $user && $model instanceof TeamMember
            && !$user->person->hasPermissionForTeam(TeamPermissionType::TEAM_MEMBER_UPDATE, $model->team);

        $updatableBySelf = [
            'name' => ['required'],
        ];

        if($canOnlyUpdateSelf) {
            return $updatableBySelf;
        }

        return [
            ...$updatableBySelf,
            'team' => ['required', JsonApiRule::toOne()],
            'statusRole' => [JsonApiRule::toOne()],
            'roles' => [JsonApiRule::toMany()],
            'person' => [JsonApiRule::toOne()],
            'deletedAt' => ['nullable', JsonApiRule::dateTime()],
        ];
    }
}
