<?php

namespace App\<PERSON>son<PERSON><PERSON>\V1\TeamMembers;

use App\<PERSON>son<PERSON><PERSON>\CommonSchema;
use App\<PERSON>son<PERSON><PERSON>\V1\Invites\InviteSchema;
use App\JsonApi\V1\Persons\PersonSchema;
use App\JsonApi\V1\TeamRoles\TeamRoleSchema;
use App\JsonApi\V1\Teams\TeamSchema;
use App\Models\TeamMember;
use Lara<PERSON><PERSON>sonA<PERSON>\Contracts\Schema\Field;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Contracts\Schema\Filter;
use Lara<PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Contracts\Paginator;
use LaravelJsonApi\Eloquent\Fields\Boolean;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\ID;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Relations\BelongsTo;
use <PERSON><PERSON><PERSON>sonA<PERSON>\Eloquent\Fields\Relations\BelongsToMany;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Relations\HasOne;
use LaravelJsonApi\Eloquent\Fields\SoftDelete;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Fields\Str;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Filters\WhereIdIn;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Pagination\PagePagination;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\SoftDeletes;

class TeamMemberSchema extends CommonSchema {

    use SoftDeletes;
    
    public static string $model = TeamMember::class;

    protected $defaultSort = 'name';

    /**
     * @var string[]
     */
    protected array $with = [
        'person.user', // is accessed for field "isRegistered"
    ];

    /**
     * Get the resource fields.
     *
     * @return array<int, Field>
     */
    public function fields(): array {

        return self::withTimestamps([
            ID::make()->uuid(),
            SoftDelete::make('deletedAt'),
            Str::make('name')->sortable(),
            Boolean::make('isRegistered')->readOnly(),

            // Relations
            BelongsTo::make('team')->type(TeamSchema::type())->notSparseField(),
            BelongsTo::make('person')->type(PersonSchema::type()),
            BelongsTo::make('statusRole')->type(TeamRoleSchema::type()),
            BelongsToMany::make('roles')->type(TeamRoleSchema::type()),
            HasOne::make('invite')->type(InviteSchema::type()),
        ]);
    }

    /**
     * Get the resource filters.
     *
     * @return array<int, Filter>
     */
    public function filters(): array {
        return [
            WhereIdIn::make($this),
        ];
    }

    public function pagination(): ?Paginator {
        return PagePagination::make();
    }

}
