<?php

namespace App\JsonApi\V1\TeamMembers;

use App\Http\Controllers\AuthController;
use App\JsonApi\CommonRessource;
use App\Models\TeamMember;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Core\Resources\ConditionalFields;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Core\Resources\Relation;

/**
 * @extends CommonRessource<TeamMember>
 */
class TeamMemberResource extends CommonRessource {

    private function getTeamMember(): TeamMember {
        return $this->resource;
    }

    /**
     * Get the resource's attributes.
     *
     * @param Request|null $request
     *
     * @return iterable<string, string|int|bool|Carbon|null>
     */
    public function attributes($request): iterable {
        $isRegistered = $this->getTeamMember()->person !== null && $this->getTeamMember()->person->user !== null;

        return [
            'name'         => $this->getTeamMember()->name,
            'isRegistered' => $isRegistered,
            'createdAt'    => $this->getTeamMember()->created_at,
            'updatedAt'    => $this->getTeamMember()->updated_at,
            'deletedAt'    => $this->getTeamMember()->deleted_at,

        ];
    }

    /**
     * Get the resource's relationships.
     *
     * @param Request|null $request
     *
     * @return iterable<Relation|ConditionalFields>
     */
    public function relationships($request): iterable {
        $user = AuthController::getCurrentUser();
        return [
            $this->relation('team'),
            $this->relation('person'),
            $this->relation('statusRole'),
            $this->relation('roles'),
            $this->mergeWhen($this->isRelationRequested('invite') && $user && $user->can('view', $this->getTeamMember()->invite), [
                $this->relation('invite'),
            ]),
        ];
    }

}
