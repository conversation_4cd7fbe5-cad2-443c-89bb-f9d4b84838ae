<?php

namespace App\<PERSON>son<PERSON>pi\V1\TeamEvents;

use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Laravel\Http\Requests\ResourceRequest;
use LaravelJsonApi\Validation\Rule;

class TeamEventRequest extends ResourceRequest {

    /**
     * Get the validation rules for the resource.
     *
     * @return array<string, array<int, \Illuminate\Validation\Rule|string>>
     */
    public function rules(): array {
        return [
            'eventType' => ['required', 'string'],
            'title' => ['nullable', 'string'],
            'subText' => ['nullable', 'string'],
            'details' => ['nullable', 'string'],
            'detailsManager' => ['nullable', 'string'],
            'dateBegin' => ['required', 'date_format:Y-m-d'],
            'timeBegin' => ['required', 'date_format:H:i'],
            'timeEnd' => ['nullable', 'date_format:H:i'],
            'timeMeet' => ['nullable', 'date_format:H:i'],
            'seriesType' => ['required', 'string'],
            'responseType' => ['required', 'string'],
            'cancelledAt' => ['nullable', Rule::dateTime()],
            'addressString' => ['nullable', 'string'],
            'matchType' => ['nullable', 'string'],
            'matchOpponentName' => ['nullable', 'string'],
            'voteReminderType' => ['nullable', 'string'],
            'voteReminderHours' => ['nullable', 'integer'],
            'eventReminderType' => ['nullable', 'string'],
            'eventReminderHours' => ['nullable', 'integer'],
            'sendChangeNotification' => ['nullable', 'boolean'],

            'series' => ['nullable', Rule::toOne()],
            'team' => ['required', Rule::toOne()],

            'filter' => [
                'nullable',
                'array',
                Rule::filter()
            ],
            'filter.startDate' => [Rule::dateTime()],
            'filter.endDate' => [Rule::dateTime()],
        ];
    }

}
