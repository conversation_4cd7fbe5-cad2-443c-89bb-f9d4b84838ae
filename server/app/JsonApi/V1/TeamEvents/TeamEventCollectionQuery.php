<?php

namespace App\JsonApi\V1\TeamEvents;

use Illuminate\Contracts\Validation\Rule;
use Lara<PERSON><PERSON>sonA<PERSON>\Laravel\Http\Requests\ResourceQuery;
use LaravelJsonApi\Validation\Rule as JsonApiRule;

class TeamEventCollectionQuery extends ResourceQuery {

    /**
     * Get the validation rules that apply to the request query parameters.
     *
     * @return array<string, array<int, Rule|string>>
     */
    public function rules(): array {
        return [
            'fields' => [
                'nullable',
                'array',
                JsonApiRule::fieldSets(),
            ],
            'filter' => [
                'nullable',
                'array',
                JsonApiRule::filter(),
            ],
            'include' => [
                'nullable',
                'string',
                JsonApiRule::includePaths([
                    'team',
                    'team.members',
                    'team.members.statusRole',
                    'votes',
                    'votes.teamMember',
                    'votes.comment',
                    'votes.author',
                ]),
            ],
            'page' => [
                'nullable',
                'array',
                JsonApiRule::page(),
            ],
            'sort' => [
                'nullable',
                'string',
                JsonApiRule::sort(),
            ],
            'withCount' => [
                'nullable',
                'string',
                JsonApiRule::countable(),
            ],
        ];
    }
}
