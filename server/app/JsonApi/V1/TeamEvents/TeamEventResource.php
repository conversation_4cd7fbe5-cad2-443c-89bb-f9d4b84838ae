<?php

namespace App\<PERSON>son<PERSON><PERSON>\V1\TeamEvents;

use App\Http\Controllers\AuthController;
use App\JsonApi\CommonRessource;
use App\JsonApi\CommonSchema;
use App\Models\TeamEvent;
use App\Types\EventReminderType;
use App\Types\EventResponseType;
use App\Types\EventSeriesType;
use App\Types\EventVoteReminderType;
use App\Types\TeamEventMatchType;
use App\Types\TeamEventType;
use App\Types\TeamPermissionType;
use Carbon\Carbon;
use Illuminate\Http\Request;
use <PERSON>vel<PERSON>son<PERSON><PERSON>\Core\Resources\ConditionalFields;
use <PERSON><PERSON><PERSON>sonA<PERSON>\Core\Resources\Relation;

/**
 * @extends CommonRessource<TeamEvent>
 */
class TeamEventResource extends CommonRessource {
    private function getTeamEvent(): TeamEvent {
        return $this->resource;
    }

    /**
     * Get the resource's attributes.
     *
     * @param Request|null $request
     *
     * @return array{
     *     eventType: TeamEventType,
     *     title: string|null,
     *     subText: string|null,
     *     details: string|null,
     *     dateBegin: Carbon|string|null,
     *     timeBegin: Carbon|string|null,
     *     timeEnd: Carbon|string|null,
     *     timeMeet: Carbon|string|null,
     *     seriesType: EventSeriesType|null,
     *     responseType: EventResponseType|null,
     *     cancelledAt: Carbon|null,
     *     addressString: string|null,
     *     matchType: TeamEventMatchType|null,
     *     matchOpponentName: string|null,
     *     voteReminderType: EventVoteReminderType|null,
     *     voteReminderHours: int|null,
     *     eventReminderType: EventReminderType|null,
     *     eventReminderHours: int|null,
     *     sendChangeNotification: bool,
     *     createdAt: Carbon|null,
     *     updatedAt: Carbon|null,
     *     detailsManager?: string|null
     * }
     */
    public function attributes($request): array {
        $person = AuthController::getCurrentUser()?->person;
        $teamEvent = $this->getTeamEvent();

        $attributes = [
            'eventType'               => $teamEvent->event_type,
            'title'                   => $teamEvent->title,
            'subText'                 => $teamEvent->sub_text,
            'details'                 => $teamEvent->details,
            'dateBegin'               => CommonSchema::getIso8601DateFormat($teamEvent->date_begin),
            'timeBegin'               => CommonSchema::getIso8601TimeFormat($teamEvent->time_begin),
            'timeEnd'                 => CommonSchema::getIso8601TimeFormat($teamEvent->time_end),
            'timeMeet'                => CommonSchema::getIso8601TimeFormat($teamEvent->time_meet),
            'seriesType'              => $teamEvent->series_type,
            'responseType'            => $teamEvent->response_type,
            'cancelledAt'             => $teamEvent->cancelled_at,
            'addressString'           => $teamEvent->address_string,
            'matchType'               => $teamEvent->match_type,
            'matchOpponentName'       => $teamEvent->match_opponent_name,
            'voteReminderType'        => $teamEvent->vote_reminder_type,
            'voteReminderHours'       => $teamEvent->vote_reminder_hours,
            'eventReminderType'       => $teamEvent->event_reminder_type,
            'eventReminderHours'      => $teamEvent->event_reminder_hours,
            'sendChangeNotification'  => true,
            'createdAt'               => $teamEvent->created_at,
            'updatedAt'               => $teamEvent->updated_at,
        ];

        if ($person?->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_CREATE, $teamEvent->team)) {
            $attributes['detailsManager'] = $teamEvent->details_manager;
        }

        return $attributes;
    }

    /**
     * Get the resource's relationships.
     *
     * @param Request|null $request
     *
     * @return iterable<Relation>
     */
    public function relationships($request): iterable {
        return [
            $this->relation('team'),
            $this->relation('votes'),
            $this->relation('series'),
        ];
    }
}
