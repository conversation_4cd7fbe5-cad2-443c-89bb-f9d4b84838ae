<?php

namespace App\JsonA<PERSON>\V1\TeamEvents;

use App\JsonA<PERSON>\CommonSchema;
use App\JsonA<PERSON>\Filters\Date;
use App\JsonA<PERSON>\Filters\DateOrNull;
use App\JsonApi\V1\TeamEventSeries\TeamEventSeriesSchema;
use App\JsonApi\V1\TeamEventTasks\TeamEventTaskSchema;
use App\JsonApi\V1\TeamEventVotes\TeamEventVoteSchema;
use App\JsonApi\V1\Teams\TeamSchema;
use App\Models\TeamEvent;
use LaravelJsonApi\Contracts\Schema\Field;
use Lara<PERSON><PERSON>son<PERSON><PERSON>\Contracts\Schema\Filter;
use Laravel<PERSON>sonApi\Eloquent\Contracts\Paginator;
use <PERSON><PERSON><PERSON>sonA<PERSON>\Eloquent\Fields\Boolean;
use LaravelJsonApi\Eloquent\Fields\DateTime;
use Lara<PERSON>JsonApi\Eloquent\Fields\ID;
use LaravelJsonApi\Eloquent\Fields\Number;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Relations\BelongsTo;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Relations\HasMany;
use <PERSON><PERSON>JsonA<PERSON>\Eloquent\Fields\Str;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Filters\Scope;
use LaravelJsonApi\Eloquent\Filters\WhereIdIn;
use LaravelJsonApi\Eloquent\Filters\WhereIn;
use LaravelJsonApi\Eloquent\Pagination\PagePagination;

class TeamEventSchema extends CommonSchema {

    public static string $model = TeamEvent::class;

    protected $defaultSort = ['dateBegin', 'timeBegin'];

    /**
     * Get the resource fields.
     *
     * @return array<int, Field>
     */
    public function fields(): array {
        return self::withTimestamps([
            ID::make()->uuid(),
            Str::make('eventType'),
            Str::make('title'),
            Str::make('subText'),
            Str::make('details'),
            Str::make('detailsManager'),
            DateTime::make('dateBegin')->sortable(),
            DateTime::make('timeBegin')->sortable(),
            DateTime::make('timeEnd'),
            DateTime::make('timeMeet'),
            Str::make('seriesType'),
            Str::make('responseType'),
            DateTime::make('cancelledAt'),
            Str::make('addressString'),
            Str::make('matchType'),
            Str::make('matchOpponentName'),
            Str::make('voteReminderType'),
            Number::make('voteReminderHours'),
            Str::make('eventReminderType'),
            Number::make('eventReminderHours'),
            Boolean::make('sendChangeNotification'),

            // Relations
            BelongsTo::make('team')->type(TeamSchema::type()),
            HasMany::make('votes')->type(TeamEventVoteSchema::type()),
            BelongsTo::make('series')->type(TeamEventSeriesSchema::type()),
            HasMany::make('tasks')->type(TeamEventTaskSchema::type()),
        ]);
    }

    /**
     * Get the resource filters.
     *
     * @return array<int, Filter>
     */
    public function filters(): array {
        return [
            WhereIdIn::make($this),
            Scope::make('events'),
            Scope::make('notCancelled'),
            Scope::make('seriesParent'),
            WhereIn::make('team', 'team_id')->delimiter(','),
            WhereIn::make('eventType')->delimiter(','),
            /**
             * TODO make queries more complex:
             * startDate should include everything where end date is in range, if end date is not null
             * maybe DateOrNull need a second column to check as fallback?
             */
            DateOrNull::make('startDate', 'date_begin')->gte(),
            Date::make('endDate', 'date_begin')->lte(),
        ];
    }

    public function pagination(): ?Paginator {
        return PagePagination::make();
    }

}
