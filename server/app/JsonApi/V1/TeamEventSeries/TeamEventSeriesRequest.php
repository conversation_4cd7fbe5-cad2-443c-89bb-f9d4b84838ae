<?php

namespace App\JsonApi\V1\TeamEventSeries;

use App\Rules\RRule;
use Illuminate\Validation\Rule;
use LaravelJsonApi\Laravel\Http\Requests\ResourceRequest;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Validation\Rule as JsonApiRule;

class TeamEventSeriesRequest extends ResourceRequest {

    /**
     * Get the validation rules for the resource.
     *
     * @return array<string, array<int, Rule|string>>
     */
    public function rules(): array {
        return [
            'eventData'   => ['nullable', JsonApiRule::toOne()],
            'rruleString' => ['required', 'string', new RRule()],
        ];
    }
}
