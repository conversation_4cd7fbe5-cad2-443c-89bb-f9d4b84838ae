<?php

namespace App\<PERSON>son<PERSON><PERSON>\V1\TeamEventSeries;

use App\<PERSON>son<PERSON><PERSON>\CommonSchema;
use App\<PERSON>sonA<PERSON>\V1\TeamEvents\TeamEventSchema;
use App\Models\TeamEventSeries;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Contracts\Schema\Field;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Contracts\Schema\Filter;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Contracts\Paginator;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Fields\ID;
use Lara<PERSON><PERSON>sonA<PERSON>\Eloquent\Fields\Relations\HasMany;
use Lara<PERSON><PERSON>sonA<PERSON>\Eloquent\Fields\Relations\HasOne;
use <PERSON><PERSON><PERSON>sonA<PERSON>\Eloquent\Fields\Str;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Filters\WhereIdIn;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Pagination\PagePagination;

class TeamEventSeriesSchema extends CommonSchema {
    /**
     * The model the schema corresponds to.
     *
     * @var string
     */
    public static string $model = TeamEventSeries::class;

    /**
     * Get the resource fields.
     *
     * @return array<int, Field>
     */
    public function fields(): array {
        return self::withTimestamps([
            ID::make()->uuid(),
            Str::make('rruleString'),

            // Relations
            HasOne::make('eventData')->type(TeamEventSchema::type()),
            HasMany::make('events')->type(TeamEventSchema::type()),
        ]);
    }

    /**
     * Get the resource filters.
     *
     * @return array<int, Filter>
     */
    public function filters(): array {
        return [
            WhereIdIn::make($this),
        ];
    }

    /**
     * Get the resource paginator.
     *
     * @return Paginator|null
     */
    public function pagination(): ?Paginator {
        return PagePagination::make();
    }

}
