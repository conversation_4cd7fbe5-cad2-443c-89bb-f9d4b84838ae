<?php

namespace App\<PERSON>son<PERSON><PERSON>\V1\TeamStatsRanges;

use App\<PERSON>sonA<PERSON>\CommonSchema;
use App\<PERSON>son<PERSON><PERSON>\V1\Teams\TeamSchema;
use App\Models\TeamStatsRange;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Contracts\Schema\Field;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Contracts\Schema\Filter;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Contracts\Paginator;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Fields\DateTime;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\ID;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Relations\BelongsTo;
use <PERSON><PERSON><PERSON>sonA<PERSON>\Eloquent\Fields\Str;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Filters\Where;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Filters\WhereIdIn;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Pagination\PagePagination;

class TeamStatsRangeSchema extends CommonSchema {

    /**
     * The model the schema corresponds to.
     *
     * @var string
     */
    public static string $model = TeamStatsRange::class;

    protected $defaultSort = ['-endDate', '-startDate'];

    /**
     * Get the resource fields.
     *
     * @return array<int, Field>
     */
    public function fields(): array {
        return self::withTimestamps([
            ID::make()->uuid(),
            DateTime::make('startDate')->sortable(),
            DateTime::make('endDate')->sortable(),
            Str::make('name'),

            // Relations
            BelongsTo::make('team')->type(TeamSchema::type()),
        ]);
    }

    /**
     * Get the resource filters.
     *
     * @return array<int, Filter>
     */
    public function filters(): array {
        return [
            WhereIdIn::make($this),
            Where::make('teamId', 'team_id'),
        ];
    }

    /**
     * Get the resource paginator.
     *
     * @return Paginator|null
     */
    public function pagination(): ?Paginator {
        return PagePagination::make();
    }

}
