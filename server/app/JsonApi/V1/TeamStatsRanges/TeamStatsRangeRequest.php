<?php

namespace App\JsonApi\V1\TeamStatsRanges;

use Illuminate\Validation\Rule;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Laravel\Http\Requests\ResourceRequest;
use LaravelJsonApi\Validation\Rule as JsonApiRule;

class TeamStatsRangeRequest extends ResourceRequest {

    /**
     * Get the validation rules for the resource.
     *
     * @return array<string, array<int, Rule|string>>
     */
    public function rules(): array {
        return [
            'startDate' => ['required', 'date_format:Y-m-d'],
            'endDate'   => ['required', 'date_format:Y-m-d'],
            'name'      => ['nullable', 'string'],
            'team'      => ['required', JsonApiRule::toOne()],
        ];
    }

}
