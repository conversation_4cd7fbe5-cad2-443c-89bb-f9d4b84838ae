<?php

namespace App\JsonApi\V1\TeamLedgerDues;

use App\Rules\RRule;
use App\Rules\Money;
use LaravelJsonApi\Laravel\Http\Requests\ResourceRequest;
use <PERSON>vel<PERSON>son<PERSON><PERSON>\Validation\Rule as JsonApiRule;

class TeamLedgerDuesRequest extends ResourceRequest
{

    /**
     * Get the validation rules for the resource.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'title' => ['required', 'string'],
            'amount' => ['nullable', new Money()],
            'item' => ['nullable', 'string'],
            'dateBegin' => ['required', 'date_format:Y-m-d'],
            'ledger' => ['required', JsonApiRule::toOne()],
            'parent' => ['nullable', JsonApiRule::toOne()],
            'deletedAt' => ['nullable', JsonApiRule::dateTime()],
            'rruleString' => ['nullable', 'string', new RRule()],
        ];
    }

}
