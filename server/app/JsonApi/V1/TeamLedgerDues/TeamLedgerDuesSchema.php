<?php

namespace App\<PERSON>son<PERSON><PERSON>\V1\TeamLedgerDues;

use App\<PERSON>son<PERSON><PERSON>\CommonSchema;
use App\<PERSON>sonA<PERSON>\Fields\Money;
use App\JsonApi\V1\TeamLedgers\TeamLedgerSchema;
use App\Models\TeamLedgerDues;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Contracts\Paginator;
use Lara<PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\DateTime;
use Lara<PERSON><PERSON>sonA<PERSON>\Eloquent\Fields\ID;
use Lara<PERSON><PERSON>sonA<PERSON>\Eloquent\Fields\Relations\BelongsTo;
use Laravel<PERSON>sonApi\Eloquent\Fields\SoftDelete;
use Laravel<PERSON>sonApi\Eloquent\Fields\Str;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Filters\Where;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Filters\WhereIdIn;
use <PERSON><PERSON><PERSON>sonA<PERSON>\Eloquent\Pagination\PagePagination;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\SoftDeletes;

class TeamLedgerDuesSchema extends CommonSchema
{
    use SoftDeletes;

    /**
     * The model the schema corresponds to.
     *
     * @var string
     */
    public static string $model = TeamLedgerDues::class;

    /**
     * Get the resource fields.
     *
     * @return array
     */
    public function fields(): array
    {
        return self::withTimestamps([
            ID::make()->uuid(),
            SoftDelete::make('deletedAt'),
            Str::make('title')->sortable(),
            Money::make('amount'),
            Str::make('item'),
            DateTime::make('dateBegin')->sortable()->serializeUsing($this::iso8601DateFormat()),
            Str::make('rruleString'),

            // Relations
            BelongsTo::make('ledger')->type(TeamLedgerSchema::type()),
            BelongsTo::make('parent')->type(self::type()),
        ]);
    }

    /**
     * Get the resource filters.
     *
     * @return array
     */
    public function filters(): array
    {
        return [
            WhereIdIn::make($this),
            Where::make('ledgerId', 'team_ledger_id'),
        ];
    }

    /**
     * Get the resource paginator.
     *
     * @return Paginator|null
     */
    public function pagination(): ?Paginator
    {
        return PagePagination::make();
    }

}
