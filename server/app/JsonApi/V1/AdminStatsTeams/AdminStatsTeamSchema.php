<?php

namespace App\<PERSON>son<PERSON><PERSON>\V1\AdminStatsTeams;

use App\JsonApi\CommonSchema;
use App\Models\AdminStatsTeam;
use Carbon\Carbon;
use Laravel<PERSON>sonApi\Eloquent\Contracts\Paginator;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Boolean;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\DateTime;
use Lara<PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\ID;
use Lara<PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Number;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Fields\Str;
use Lara<PERSON><PERSON>sonA<PERSON>\Eloquent\Filters\WhereIdIn;
use Laravel<PERSON>sonApi\Eloquent\Pagination\PagePagination;

class AdminStatsTeamSchema extends CommonSchema
{
    
    public const int MONTH_SINCE_LAST_VOTE_FOR_ACTIVE = 2;

    /**
     * The model the schema corresponds to.
     *
     * @var string
     */
    public static string $model = AdminStatsTeam::class;

    /**
     * Get the resource fields.
     *
     * @return array
     */
    public function fields(): array
    {
        return self::withTimestamps([
            ID::make()->uuid(),
            Str::make('name')->sortable(),
            DateTime::make('trialEndsAt')->sortable()->readOnly(),
            Boolean::make('isSubscribed')->readOnly(),
            Boolean::make('subscriptionEndsAd')->extractUsing(static fn (AdminStatsTeam $model, $column, $value) => $model->isSubscribed() ? $model->getActiveSubscription()?->ends_at : null),
            Boolean::make('isActive')->extractUsing(static fn (AdminStatsTeam $model, $column, $value) => $model->votes()->whereDate('team_event_votes.updated_at', '>=',Carbon::now()->subMonths(self::MONTH_SINCE_LAST_VOTE_FOR_ACTIVE))->exists()),
            Number::make('membersCount')->extractUsing(static fn (AdminStatsTeam $model, $column, $value) => $model->members()->count()),
            Number::make('eventsCount')->extractUsing(static fn (AdminStatsTeam $model, $column, $value) => $model->events()->events()->count()),
            Number::make('votesCountManual')->extractUsing(static fn (AdminStatsTeam $model, $column, $value) => $model->votes()->whereHas('voterPerson')->count()),
            Number::make('votesCountAutomatic')->extractUsing(static fn (AdminStatsTeam $model, $column, $value) => $model->votes()->whereDoesntHave('voterPerson')->count()),
            Number::make('registeredMembersCount')->extractUsing(static fn (AdminStatsTeam $model, $column, $value) => $model->registeredMembers()->count()),
            Str::make('ledgerId')->extractUsing(static fn (AdminStatsTeam $model, $column, $value) => $model->ledger()->first('id')),
            Number::make('ledgerFineCount')->extractUsing(static fn (AdminStatsTeam $model, $column, $value) => $model->ledger()->first()?->fines()->count()),
            Number::make('ledgerDuesCount')->extractUsing(static fn (AdminStatsTeam $model, $column, $value) => $model->ledger()->first()?->dues()->count()),
            Number::make('ledgerTransactionCount')->extractUsing(static fn (AdminStatsTeam $model, $column, $value) => $model->ledger()->first()?->transactions()->count()),
        ]);
    }

    /**
     * Get the resource filters.
     *
     * @return array
     */
    public function filters(): array
    {
        return [
            WhereIdIn::make($this),
        ];
    }

    /**
     * Get the resource paginator.
     *
     * @return Paginator|null
     */
    public function pagination(): ?Paginator
    {
        return PagePagination::make();
    }

}
