<?php

namespace App\<PERSON>son<PERSON><PERSON>\V1\TeamPermissions;

use App\Json<PERSON><PERSON>\CommonSchema;
use App\Models\TeamPermission;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Contracts\Schema\Field;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Contracts\Schema\Filter;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Contracts\Paginator;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Fields\ID;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Str;
use Lara<PERSON><PERSON>sonA<PERSON>\Eloquent\Filters\WhereIdIn;
use <PERSON><PERSON><PERSON>sonA<PERSON>\Eloquent\Pagination\PagePagination;

class TeamPermissionSchema extends CommonSchema {

    public static string $model = TeamPermission::class;

    /**
     * Get the resource fields.
     *
     * @return array<int, Field>
     */
    public function fields(): array {
        return [
            ID::make(),
            Str::make('name'),
        ];
    }

    /**
     * Get the resource filters.
     *
     * @return array<int, Filter>
     */
    public function filters(): array {
        return [
            WhereIdIn::make($this),
        ];
    }

    public function pagination(): ?Paginator {
        return PagePagination::make();
    }

}
