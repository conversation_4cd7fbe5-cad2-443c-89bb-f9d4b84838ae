<?php

namespace App\Json<PERSON>pi\V1\Users;

use App\Http\Controllers\AuthController;
use App\<PERSON>sonA<PERSON>\CommonSchema;
use App\JsonApi\V1\Persons\PersonSchema;
use App\Models\User;
use App\Types\FeatureType;
use <PERSON><PERSON>\Pennant\Feature;
use Laravel<PERSON>son<PERSON><PERSON>\Contracts\Schema\Field;
use LaravelJson<PERSON>pi\Contracts\Schema\Filter;
use Lara<PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Contracts\Paginator;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Boolean;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\ID;
use LaravelJsonApi\Eloquent\Fields\Relations\BelongsTo;
use <PERSON><PERSON><PERSON>son<PERSON>pi\Eloquent\Fields\Str;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Filters\WhereIdIn;
use Lara<PERSON><PERSON>sonA<PERSON>\Eloquent\Pagination\PagePagination;

class UserSchema extends CommonSchema {

    public static string $model = User::class;

    /**
     * @return array<int, Field>
     */
    public function fields(): array {
        return self::withTimestamps([
            ID::make()->uuid(),
            Str::make('email')->sortable(),
            Boolean::make('hasVerifiedEmail')->extractUsing(
                static fn (User $model, $column, $value) => $model->hasVerifiedEmail()
            ),
            Boolean::make('isProblemProvider')->extractUsing(
                static fn (User $model, $column, $value) => AuthController::isProblemProviderForSendGrid($model->email)
            )->hidden(fn($request) => !Feature::active(FeatureType::ADMIN_STATS->value)),

            Str::make('origin')->on('registrationInformation')
                ->hidden(fn($request) => !Feature::active(FeatureType::ADMIN_STATS->value)),
            Str::make('competition')->on('registrationInformation')
                ->hidden(fn($request) => !Feature::active(FeatureType::ADMIN_STATS->value)),

            // Relations
            BelongsTo::make('person')->type(PersonSchema::type()),
        ]);
    }

    /**
     * @return array<int, Filter>
     */
    public function filters(): array {
        return [
            WhereIdIn::make($this),
        ];
    }

    public function pagination(): ?Paginator {
        return PagePagination::make();
    }

}
