<?php

namespace App\JsonApi\V1\Users;

use App\Http\Controllers\AuthController;
use App\Types\FeatureType;
use <PERSON><PERSON>\Pennant\Feature;
use LaravelJsonApi\Laravel\Http\Requests\ResourceQuery;
use Laravel<PERSON>son<PERSON><PERSON>\Validation\Rule as JsonApiRule;

class UserCollectionQuery extends ResourceQuery
{

    /**
     * Get the validation rules that apply to the request query parameters.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'fields' => [
                'nullable',
                'array',
                JsonApiRule::fieldSets(),
            ],
            'filter' => [
                'nullable',
                'array',
                JsonApiRule::filter(),
            ],
            'include' => [
                'nullable',
                'string',
                JsonApiRule::includePaths([
                    'person',
                    'person.teamMembers',
                    'person.teamMembers.team',
                ]),
            ],
            'page' => [
                'nullable',
                'array',
                JsonApiRule::page(),
            ],
            'sort' => [
                'nullable',
                'string',
                JsonApiRule::sort(),
            ],
            'withCount' => [
                'nullable',
                'string',
                JsonApiRule::countable(),
            ],
        ];
    }
}
