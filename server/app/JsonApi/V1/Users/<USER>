<?php

namespace App\<PERSON>sonApi\V1\Users;

use Laravel<PERSON>sonA<PERSON>\Validation\Rule;
use LaravelJsonA<PERSON>\Laravel\Http\Requests\ResourceRequest;

class UserRequest extends ResourceRequest {

    /**
     * Get the validation rules for the resource.
     *
     * @return array<string, array<int, \Illuminate\Contracts\Validation\Rule|string>>
     */
    public function rules(): array {
        return [
            'person' => [Rule::toOne()],
        ];
    }

}
