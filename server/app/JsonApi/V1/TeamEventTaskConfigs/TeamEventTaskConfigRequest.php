<?php

namespace App\JsonApi\V1\TeamEventTaskConfigs;

use Illuminate\Validation\Rule;
use <PERSON><PERSON><PERSON>sonA<PERSON>\Laravel\Http\Requests\ResourceRequest;
use LaravelJsonApi\Validation\Rule as JsonApiRule;

class TeamEventTaskConfigRequest extends ResourceRequest {

    /**
     * Get the validation rules for the resource.
     *
     * @return array<string, array<int, Rule|string>>
     */
    public function rules(): array {
        return [
            'team'  => ['required', JsonApiRule::toOne()],
            'title' => ['required', 'string'],
        ];
    }

}
