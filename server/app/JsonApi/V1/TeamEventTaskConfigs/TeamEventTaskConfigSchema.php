<?php

namespace App\<PERSON>son<PERSON><PERSON>\V1\TeamEventTaskConfigs;

use App\JsonA<PERSON>\CommonSchema;
use App\JsonApi\V1\TeamEventTasks\TeamEventTaskSchema;
use App\JsonA<PERSON>\V1\Teams\TeamSchema;
use App\Models\TeamEventTaskConfig;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Contracts\Schema\Field;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Contracts\Schema\Filter;
use Lara<PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Contracts\Paginator;
use <PERSON><PERSON><PERSON>sonA<PERSON>\Eloquent\Fields\ID;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Relations\BelongsTo;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Relations\HasMany;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Str;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Filters\Where;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Filters\WhereIdIn;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Pagination\PagePagination;

class TeamEventTaskConfigSchema extends CommonSchema {

    /**
     * The model the schema corresponds to.
     *
     * @var class-string
     */
    public static string $model = TeamEventTaskConfig::class;

    protected $defaultSort = 'title';

    /**
     * Get the resource fields.
     *
     * @return array<int, Field>
     */
    public function fields(): array {
        return self::withTimestamps([
            ID::make()->uuid(),
            Str::make('title')->sortable(),

            // Relations
            BelongsTo::make('team')->type(TeamSchema::type()),
            HasMany::make('tasks')->type(TeamEventTaskSchema::type()),
        ]);
    }

    /**
     * Get the resource filters.
     *
     * @return array<int, Filter>
     */
    public function filters(): array {
        return [
            WhereIdIn::make($this),
            Where::make('teamId', 'team_id'),
        ];
    }

    /**
     * Get the resource paginator.
     *
     * @return Paginator|null
     */
    public function pagination(): ?Paginator {
        return PagePagination::make();
    }

}
