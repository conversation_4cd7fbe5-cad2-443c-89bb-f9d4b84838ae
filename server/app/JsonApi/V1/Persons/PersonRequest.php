<?php

namespace App\<PERSON>sonApi\V1\Persons;

use Illuminate\Validation\Rule;
use LaravelJsonApi\Laravel\Http\Requests\ResourceRequest;
use Laravel<PERSON>sonApi\Validation\Rule as JsonApiRule;

class PersonRequest extends ResourceRequest {

    /**
     * Get the validation rules for the resource.
     *
     * @return array<string, array<int, Rule|string>>
     */
    public function rules(): array {
        return [
            'firstname' => ['string'],
            'lastname' => ['string'],
        ];
    }

}
