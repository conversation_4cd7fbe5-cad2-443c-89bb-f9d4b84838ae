<?php

namespace App\<PERSON>son<PERSON><PERSON>\V1\Persons;

use App\<PERSON>son<PERSON><PERSON>\CommonSchema;
use App\<PERSON>sonApi\V1\TeamMembers\TeamMemberSchema;
use App\JsonApi\V1\Users\UserSchema;
use App\Models\Person;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Contracts\Schema\Field;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Contracts\Schema\Filter;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Contracts\Paginator;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\ArrayHash;
use <PERSON><PERSON><PERSON>sonA<PERSON>\Eloquent\Fields\ID;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Relations\HasMany;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Relations\HasOne;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Str;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Filters\WhereIdIn;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Pagination\PagePagination;

class PersonSchema extends CommonSchema {

    public static string $model = Person::class;

    // For some reason this schema is getting loaded as 'people', thus we need
    // to actively override it here.
    public static function type(): string {
        return 'persons';
    }

    protected ?string $uriType = 'persons';

    /**
     * Get the resource fields.
     *
     * @return array<int, Field>
     */
    public function fields(): array {
        return self::withTimestamps([
            ID::make()->uuid(),
            Str::make('firstname')->sortable(),
            Str::make('lastname')->sortable(),

            // Custom fields
            ArrayHash::make('teamPermissions')->readOnly(), //->extractUsing($this->permissionNamesPerTeamId()),
            ArrayHash::make('activeFeatures')->readOnly(), //->extractUsing($this->activeFeatures()),

            // Relations
            HasMany::make('teamMembers')->type(TeamMemberSchema::type()),
            HasOne::make('user')->type(UserSchema::type()),
        ]);
    }

    /**
     * Get the resource filters.
     *
     * @return array<int, Filter>
     */
    public function filters(): array {
        return [
            WhereIdIn::make($this),
        ];
    }

    public function pagination(): ?Paginator {
        return PagePagination::make();
    }

}
