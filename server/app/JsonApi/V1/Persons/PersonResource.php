<?php

namespace App\JsonApi\V1\Persons;

use App\Http\Controllers\AuthController;
use App\JsonApi\CommonRessource;
use App\Models\Person;
use App\Types\FeatureType;
use Illuminate\Http\Request;
use Laravel\Pennant\Feature;
use LaravelJsonApi\Core\Resources\ConditionalFields;
use Carbon\Carbon;

/**
 * @extends CommonRessource<Person>
 */
class PersonResource extends CommonRessource
{
    
    private function getPerson(): Person {
        return $this->resource;
    }

    private function isSelf(): bool {
        $user = AuthController::getCurrentUser();
        return $user && $user->person_id === $this->getPerson()->id;
    }

    /**
     * Get the resource's attributes.
     *
     * @param Request|null $request
     *
     * @return array<int|string, Carbon|ConditionalFields|string|null>
     */
    public function attributes($request): array
    {
        if($this->hasExtraField('teamPermissions')) {
            $this->getPerson()->clearPermissionCache();
        }

        return [
            'firstname' => $this->getPerson()->firstname,
            'lastname'  => $this->getPerson()->lastname,
            $this->mergeWhen($this->isSelf() && $this->hasExtraField('teamPermissions'), [
                'teamPermissions' => $this->getPerson()->permissionNamesPerTeamId(),
            ]),
            $this->mergeWhen($this->isSelf() && $this->hasExtraField('activeFeatures'), [
                'activeFeatures' => $this->getPerson()->activeFeatures(),
            ]),
            'createdAt' => $this->getPerson()->created_at,
            'updatedAt' => $this->getPerson()->updated_at,
        ];
    }

    /**
     * Get the resource's relationships.
     *
     * @param Request|null $request
     *
     * @return array<int, ConditionalFields>
     */
    public function relationships($request): iterable {
        return [
            $this->mergeWhen($this->isSelf() || Feature::active(FeatureType::ADMIN_STATS->value), [
                $this->relation('teamMembers'),
                $this->relation('user'),
            ]),
        ];
    }

}
