<?php

namespace App\JsonApi\V1\Persons;

use Illuminate\Contracts\Validation\Rule;
use Lara<PERSON><PERSON>sonA<PERSON>\Laravel\Http\Requests\ResourceQuery;
use LaravelJsonApi\Validation\Rule as JsonApiRule;

class PersonQuery extends ResourceQuery {

    /**
     * Get the validation rules that apply to the request query parameters.
     *
     * @return array<string, array<int, Rule|string>>
     */
    public function rules(): array {
        return [
            'fields' => [
                'nullable',
                'array',
                JsonApiRule::fieldSets(),
            ],
            'filter' => [
                'nullable',
                'array',
                JsonApiRule::filter()->forget('id'),
            ],
            'include' => [
                'nullable',
                'string',
                JsonApiRule::includePaths([
                    'teamMembers',
                    'teamMembers.team',
                    'teamMembers.statusRole',
                ]),
            ],
            'page' => JsonApiRule::notSupported(),
            'sort' => JsonApiRule::notSupported(),
            'withCount' => [
                'nullable',
                'string',
                JsonApiRule::countable(),
            ],
        ];
    }
}
