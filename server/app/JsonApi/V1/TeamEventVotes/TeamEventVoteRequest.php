<?php

namespace App\<PERSON>sonApi\V1\TeamEventVotes;

use <PERSON><PERSON><PERSON>sonA<PERSON>\Laravel\Http\Requests\ResourceRequest;
use LaravelJsonApi\Validation\Rule;

class TeamEventVoteRequest extends ResourceRequest {

    /**
     * Get the validation rules for the resource.
     *
     * @return array<string, array<int, \Illuminate\Validation\Rule|string>>
     */
    public function rules(): array {
        return [
            'teamEvent' => ['required', Rule::toOne()],
            'teamMember' => ['required', Rule::toOne()],
            'vote' => ['required', 'string'],
        ];
    }

}
