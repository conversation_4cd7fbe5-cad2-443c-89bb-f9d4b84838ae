<?php

namespace App\<PERSON>son<PERSON><PERSON>\V1\TeamEventVotes;

use App\Json<PERSON><PERSON>\CommonSchema;
use App\JsonApi\V1\Comments\CommentSchema;
use App\JsonApi\V1\Persons\PersonSchema;
use App\JsonApi\V1\TeamEvents\TeamEventSchema;
use App\JsonApi\V1\TeamMembers\TeamMemberSchema;
use App\Models\TeamEventVote;
use Lara<PERSON><PERSON>sonA<PERSON>\Contracts\Schema\Field;
use Lara<PERSON><PERSON>sonA<PERSON>\Contracts\Schema\Filter;
use <PERSON><PERSON><PERSON>sonA<PERSON>\Eloquent\Contracts\Paginator;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Boolean;
use Lara<PERSON><PERSON>sonApi\Eloquent\Fields\ID;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Fields\Relations\BelongsTo;
use Lara<PERSON><PERSON>sonA<PERSON>\Eloquent\Fields\Relations\HasOne;
use <PERSON><PERSON><PERSON>sonApi\Eloquent\Fields\Str;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Filters\WhereIdIn;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Pagination\PagePagination;

class TeamEventVoteSchema extends CommonSchema {

    public static string $model = TeamEventVote::class;

    /**
     * @var string[]
     */
    protected array $with = [
        'teamEvent.team', // is accessed for relation "voterAuthor"
    ];
    
    /**
     * Get the resource fields.
     *
     * @return array<int, Field>
     */
    public function fields(): array {
        return self::withTimestamps([
            ID::make()->uuid(),
            Str::make('vote'),
            Boolean::make('hasSavedComment')->readOnly(),

            // Relations
            BelongsTo::make('teamEvent')->type(TeamEventSchema::type()),
            BelongsTo::make('teamMember')->type(TeamMemberSchema::type()),
            BelongsTo::make('author', 'voterPerson')->type(PersonSchema::type()),
            HasOne::make('comment')->type(CommentSchema::type()),
        ]);
    }

    /**
     * Get the resource filters.
     *
     * @return array<int, Filter>
     */
    public function filters(): array {
        return [
            WhereIdIn::make($this),
        ];
    }

    public function pagination(): ?Paginator {
        return PagePagination::make();
    }

}
