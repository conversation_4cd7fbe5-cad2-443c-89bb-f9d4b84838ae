<?php

namespace App\JsonApi\V1\TeamEventVotes;

use App\Http\Controllers\AuthController;
use App\JsonApi\CommonRessource;
use App\Models\TeamEventVote;
use App\Types\TeamEventVoteType;
use App\Types\TeamPermissionType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Core\Resources\ConditionalFields;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Core\Resources\Relation;

/**
 * @extends CommonRessource<TeamEventVote>
 */
class TeamEventVoteResource extends CommonRessource {

    private function getTeamEventVote(): TeamEventVote {
        return $this->resource;
    }

    /**
     * Get the resource's attributes.
     *
     * @param Request|null $request
     *
     * @return iterable<string, TeamEventVoteType|bool|Carbon|null>
     */
    public function attributes($request): iterable {
        return [
            'vote'            => $this->getTeamEventVote()->vote,
            'hasSavedComment' => $this->getTeamEventVote()->hasComment(),
            'createdAt'       => $this->getTeamEventVote()->created_at,
            'updatedAt'       => $this->getTeamEventVote()->updated_at,
        ];
    }

    /**
     * Get the resource's relationships.
     *
     * @param Request|null $request
     *
     * @return iterable<ConditionalFields|Relation>
     */
    public function relationships($request): iterable {
        $user = AuthController::getCurrentUser();

        return [
            $this->relation('teamEvent'),
            $this->relation('teamMember'),
            $this->mergeWhen($user && $user->can('view', $this->getTeamEventVote()->comment), [
                $this->relation('comment'),
            ]),
            $this->mergeWhen($user && $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_VOTE_OTHER, $this->getTeamEventVote()->teamEvent->team), [
                $this->relation('author', 'voterPerson'),
            ]),
        ];
    }

}
