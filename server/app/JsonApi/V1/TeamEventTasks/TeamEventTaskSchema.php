<?php

namespace App\<PERSON>son<PERSON><PERSON>\V1\TeamEventTasks;

use App\<PERSON>son<PERSON><PERSON>\CommonSchema;
use App\JsonApi\V1\TeamEvents\TeamEventSchema;
use App\JsonApi\V1\TeamEventTaskConfigs\TeamEventTaskConfigSchema;
use App\JsonApi\V1\TeamMembers\TeamMemberSchema;
use App\Models\TeamEventTask;
use Lara<PERSON><PERSON>son<PERSON><PERSON>\Contracts\Schema\Field;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Contracts\Schema\Filter;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Contracts\Paginator;
use LaravelJsonApi\Eloquent\Fields\ID;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\Relations\BelongsTo;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Filters\Has;
use Lara<PERSON><PERSON>sonA<PERSON>\Eloquent\Filters\Where;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Filters\WhereHas;
use Lara<PERSON><PERSON>sonA<PERSON>\Eloquent\Filters\WhereIdIn;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Pagination\PagePagination;

class TeamEventTaskSchema extends CommonSchema {

    /**
     * The model the schema corresponds to.
     *
     * @var class-string
     */
    public static string $model = TeamEventTask::class;

    /**
     * Get the resource fields.
     *
     * @return array<int, Field>
     */
    public function fields(): array {
        return self::withTimestamps([
            ID::make()->uuid(),

            // Relations
            BelongsTo::make('teamEvent')->type(TeamEventSchema::type()),
            BelongsTo::make('config')->type(TeamEventTaskConfigSchema::type()),
            BelongsTo::make('teamMember')->type(TeamMemberSchema::type()),
        ]);
    }

    /**
     * Get the resource filters.
     *
     * @return array<int, Filter>
     */
    public function filters(): array {
        return [
            WhereIdIn::make($this),
            Where::make('teamEventId', 'team_event_id'),
            Has::make($this, 'teamMember'),
            WhereHas::make($this, 'teamEvent'),
        ];
    }

    /**
     * Get the resource paginator.
     *
     * @return Paginator|null
     */
    public function pagination(): ?Paginator {
        return PagePagination::make();
    }

}
