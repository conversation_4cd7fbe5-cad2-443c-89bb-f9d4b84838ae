<?php

namespace App\<PERSON>sonApi\V1\TeamEventTasks;

use App\JsonApi\CommonRessourceRequest;
use Illuminate\Validation\Rule;
use Laravel<PERSON>sonA<PERSON>\Validation\Rule as JsonApiRule;

class TeamEventTaskRequest extends CommonRessourceRequest {

    /**
     * Get the validation rules for the resource.
     *
     * @return array<string, array<int, Rule|string>>
     */
    public function rules(): array {
        return [
            'teamEvent'  => ['required', JsonApiRule::toOne()],
            'config'     => ['required', JsonApiRule::toOne()],
            'teamMember' => ['nullable', JsonApiRule::toOne()],
        ];
    }

}
