<?php

namespace App\JsonApi;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Lara<PERSON><PERSON>sonA<PERSON>\Core\Resources\JsonApiResource;
use LaravelJsonApi\Core\Support\Str;

/**
 * @template TModel of Model
 * @property-read TModel $resource
 */
class CommonRessource extends JsonApiResource{
    
    /**
     * @return Request
     */
    private function getRequest(): Request {
        /** @var Request $request */
        $request = \App::make(Request::class);
        return $request;
    }

    /**
     * @return string[]
     */
    private function getExtraFields(): array {
        /** @var string[] $extraFieldsStringsPerType */
        $extraFieldsStringsPerType = $this->getRequest()->get('extra_fields', '');
        $extraFieldsString = $extraFieldsStringsPerType[$this->type()] ?? '';
        return explode(',', $extraFieldsString);
    }

    /**
     * @return string[]
     */
    private function getFields(): array {
        /** @var string[] $fieldsStringsPerType */
        $fieldsStringsPerType = $this->getRequest()->get('fields', '');
        $fieldsString = $fieldsStringsPerType[$this->type()] ?? '';
        return explode(',', $fieldsString);
    }

    protected function hasExtraField(string $field): bool {
        return in_array($field, $this->getExtraFields());
    }

    protected function hasSelectField(string $field): bool {
        return in_array($field, $this->getFields());
    }

    protected function hasFieldRequested(string $field): bool {
        return $this->hasSelectField($field) || $this->hasExtraField($field);
    }

    /**
     * Helper method to check if a relation is requested via the 'include' query parameter.
     *
     * @param string  $relationName
     *
     * @return bool
     */
    protected function isRelationRequested(string $relationName): bool {
        $includeParam = $this->getRequest()->query('include');

        if (empty($includeParam) || !is_string($includeParam)) {
            return false;
        }

        $requestedRelations = explode(',', $includeParam);

        foreach ($requestedRelations as $requestedRelation) {
            $requestedRelation = trim($requestedRelation);

            if (Str::is($relationName, $requestedRelation) || Str::is(($relationName.'.*'), $requestedRelation)) { // Use Str::is for wildcard support
                return true;
            }
        }
        return false;
    }
}
