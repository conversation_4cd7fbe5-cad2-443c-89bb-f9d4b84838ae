<?php

namespace App\JsonApi;

use Carbon\Carbon;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Contracts\Schema\Field;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Core\Support\Str;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\ArrayList;
use <PERSON><PERSON><PERSON>son<PERSON><PERSON>\Eloquent\Fields\DateTime;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\Eloquent\Schema;

/**
 * The CommonSchema class implements some common behavior for all schemas.
 *
 * 1. Normally Laravel Json Api uses dasherize types within URLs and form requests, since
 *    the library we use client side doesn't support this we use camel case types instead.
 *    - Both type() and uriType() convert to camel case
 */
abstract class CommonSchema extends Schema {

    protected bool $selfLink = false;
    protected int $maxDepth = 4;  // this is used for eager loading depth

    // type returns the camel case type as it is referred to within the schema
    public static function type(): string {
        return CommonSchema::dashToCamel(parent::type());
    }

    // uriType returns the camel case type as it is used in URLs
    public function uriType(): string {
        if ($this->uriType) {
            return $this->uriType;
        }

        return $this->uriType = CommonSchema::dashToCamel(Str::dasherize($this->type()));
    }

    public static function dashToCamel(string $str): string {
        $words = explode('-', strtolower($str));
        $result = '';
        foreach ($words as $word) {
            $result .= ucfirst(trim($word));
        }
        return lcfirst($result);
    }

    /**
     * Add timestamp fields to the given array of fields.
     *
     * @param array<int, Field> $fields
     * @return array<int, Field>
     */
    public static function withTimestamps(array $fields): array {
        return array_merge($fields, [
            DateTime::make('createdAt')->sortable()->readOnly(),
            DateTime::make('updatedAt')->sortable()->readOnly(),
            ArrayList::make('detachRelationsByName'), // @see CommonRessourceRequest::after()
        ]);
    }

    /**
     * Return a closure to format dates in ISO 8601 format.
     *
     * @return \Closure(Carbon|null): (string|null)
     */
    public static function iso8601DateFormat(): \Closure {
        return static fn(Carbon|null $value) => self::getIso8601DateFormat($value);
    }

    /**
     * Return a closure to format times in ISO 8601 format.
     *
     * @return \Closure(Carbon|null): (string|null)
     */
    public static function iso8601TimeFormat(): \Closure {
        return static fn(Carbon|null $value) => self::getIso8601TimeFormat($value);
    }

    /**
     * Format the given Carbon instance as an ISO 8601 date.
     *
     * @param Carbon|null $value
     *
     * @return Carbon|string|null
     */
    public static function getIso8601DateFormat(Carbon|null $value): Carbon|string|null {
        return isset($value) ? $value->format('Y-m-d') : $value;
    }

    /**
     * Format the given Carbon instance or string as an ISO 8601 time.
     *
     * @param Carbon|string|null $value
     *
     * @return Carbon|string|null
     */
    public static function getIso8601TimeFormat(Carbon|string|null $value): Carbon|string|null {
        return isset($value) ? $value->format('H:i') : $value;
    }
}
