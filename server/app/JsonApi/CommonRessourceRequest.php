<?php

namespace App\JsonApi;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Validation\Validator;
use LaravelJsonApi\Laravel\Http\Requests\ResourceRequest;

abstract class CommonRessourceRequest extends ResourceRequest {

    /**
     * https://laravel.com/docs/10.x/validation#performing-additional-validation-on-form-requests
     *
     * @return array<callable>
     */
    public function after(): array {
        return [
            /**
             * hack to allow detaching all relations since spraypaint does not send an empty array
             * @see SpraypaintBaseDetachRelationsWhenEmpty.ts
             * https://github.com/graphiti-api/spraypaint.js/issues/81
             */
            function (Validator $validator) {
                $detachRelationsByName = request()->input('data.attributes.detachRelationsByName');
                $model = $this->model();
                if (isset($detachRelationsByName, $model)) {
                    foreach ($detachRelationsByName as $relationName) {
                        if ($model->isRelation($relationName)) {
                            $relation = $model->{$relationName}();
                            $data = $validator->getData();
                            unset($data[$relationName]); // prevent existing values being merged
                            $validator->setData($data);
                            if ($relation instanceof BelongsToMany) {
                                $relation->detach();
                            } elseif ($relation instanceof BelongsTo) {
                                $relation->disassociate();
                            }
                        }
                    }
                }
            },

        ];
    }
}
