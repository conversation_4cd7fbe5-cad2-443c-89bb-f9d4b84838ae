<?php

namespace App\JsonApi\Authorizers;

use App\Types\FeatureType;
use Illuminate\Http\Request;
use <PERSON>vel<PERSON>sonA<PERSON>\Contracts\Auth\Authorizer;

class AdminStatsAuthorizer implements Authorizer
{
    /**
     * Authorize the index controller action.
     *
     * @param Request $request
     * @param string $modelClass
     * @return bool
     */
    public function index(Request $request, string $modelClass): bool
    {
        return \Feature::active(FeatureType::ADMIN_STATS->value);
    }

    /**
     * Authorize the store controller action.
     *
     * @param Request $request
     * @param string $modelClass
     * @return bool
     */
    public function store(Request $request, string $modelClass): bool
    {
        return false;
    }

    /**
     * Authorize the show controller action.
     *
     * @param Request $request
     * @param object $model
     * @return bool
     */
    public function show(Request $request, object $model): bool
    {
        return false;
    }

    /**
     * Authorize the update controller action.
     *
     * @param object $model
     * @param Request $request
     * @return bool
     */
    public function update(Request $request, object $model): bool
    {
        return false;
    }

    /**
     * Authorize the destroy controller action.
     *
     * @param Request $request
     * @param object $model
     * @return bool
     */
    public function destroy(Request $request, object $model): bool
    {
        return false;
    }

    /**
     * Authorize the show-related controller action
     *
     * @param Request $request
     * @param object $model
     * @param string $fieldName
     * @return bool
     */
    public function showRelated(Request $request, object $model, string $fieldName): bool
    {
        return false;
    }

    /**
     * Authorize the show-relationship controller action.
     *
     * @param Request $request
     * @param object $model
     * @param string $fieldName
     * @return bool
     */
    public function showRelationship(Request $request, object $model, string $fieldName): bool
    {
        return false;
    }

    /**
     * Authorize the update-relationship controller action.
     *
     * @param Request $request
     * @param object $model
     * @param string $fieldName
     * @return bool
     */
    public function updateRelationship(Request $request, object $model, string $fieldName): bool
    {
        return false;
    }

    /**
     * Authorize the attach-relationship controller action.
     *
     * @param Request $request
     * @param object $model
     * @param string $fieldName
     * @return bool
     */
    public function attachRelationship(Request $request, object $model, string $fieldName): bool
    {
        return false;
    }

    /**
     * Authorize the detach-relationship controller action.
     *
     * @param Request $request
     * @param object $model
     * @param string $fieldName
     * @return bool
     */
    public function detachRelationship(Request $request, object $model, string $fieldName): bool
    {
        return false;
    }

}
