<?php

namespace App\<PERSON>sonApi\Fields;

use LaravelJsonApi\Core\Support\Arr;
use LaravelJsonApi\Eloquent\Fields\Attribute;

class Money extends Attribute {

    /**
     * Create a number attribute.
     *
     * @param string      $fieldName
     * @param string|null $column
     *
     * @return Money
     */
    public static function make(string $fieldName, string $column = null): self
    {
        return new self($fieldName, $column);
    }
    
    protected function deserialize($value) {
        /**
         * @var null|array{
         *      amount: int,
         *      currency: string
         *  } $value
         */
        $value = parent::deserialize($value);

        if (is_null($value)) {
            return null;
        }
        return money($value['amount'], $value['currency']);
    }

    /**
     * @param null|string|array{}|array{
     *       amount: int,
     *       currency: string
     *   } $value
     *
     * @return void
     */
    protected function assertValue($value): void {
        if(!is_null($value)){
            if (!is_array($value) || (!empty($value) && !Arr::isAssoc($value))) {
                throw new \UnexpectedValueException(sprintf(
                    'Expecting the value of attribute %s to be an associative array.',
                    $this->name()
                ));
            }

            if(!isset($value['amount'], $value['currency'])) {
                throw new \UnexpectedValueException(sprintf(
                    'Expecting the value of attribute %s to be an array with amount and currency.',
                    $this->name()
                ));
            }
        }
    }
}
