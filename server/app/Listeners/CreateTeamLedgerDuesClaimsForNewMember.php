<?php

namespace App\Listeners;

use App\Events\TeamMemberCreated;
use App\Events\TeamMemberUpdated;

class CreateTeamLedgerDuesClaimsForNewMember
{
    /**
     * Handle the event.
     */
    public function handle(TeamMemberCreated|TeamMemberUpdated $event): void
    {
        if($event instanceof TeamMemberUpdated && !$event->teamMember->wasChanged('join_date')) {
            return;
        }
        if(!isset($event->teamMember->team->ledger)) {
            return;
        }
        foreach ($event->teamMember->team->ledger->dues()->withTrashed()->get() as $due) {
            $due->updateClaimsForMember($event->teamMember);
        }
    }
}
