<?php

namespace App\Listeners;

use App\Events\TeamCreated;
use App\Mail\TeamCreatedWelcomeEmail;
use App\Types\TeamRoleType;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendTeamCreatedWelcomeEmail implements ShouldQueue
{
    /**
     * Handle the event.
     */
    public function handle(TeamCreated $event): void
    {
        $team = $event->team;
        $user = $event->user;
        
        if(!$user) {
            $user = $team->membersWithRole(TeamRoleType::MANAGER)->first()?->person?->user;
        }

        try {
            if(!$user) {
                throw new \InvalidArgumentException('No user found for team');
            }

            Mail::to($user->email)
                ->bcc(config('mail.admin_bcc'))
                ->send(new TeamCreatedWelcomeEmail($user, $team));

            Log::info('Team created welcome email sent', [
                'team_id' => $team->id,
                'user_id' => $user->id,
                'user_email' => $user->email
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send team created welcome email', [
                'team_id' => $team->id,
                'user_id' => $user->id ?? 'Unknown',
                'error' => $e->getMessage()
            ]);
        }
    }
}
