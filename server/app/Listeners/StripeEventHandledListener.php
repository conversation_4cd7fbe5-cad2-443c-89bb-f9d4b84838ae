<?php

namespace App\Listeners;

use App\Mail\InvoicePaidEmail;
use App\Models\Subscription;
use App\Models\Team;
use App\Models\User;
use App\Types\SubscriptionPriceType;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use <PERSON><PERSON>\Cashier\Cashier;
use <PERSON><PERSON>\Cashier\Events\WebhookHandled;
use Lara<PERSON>\Cashier\Invoice;

class StripeEventHandledListener {
    /**
     * Handle the event.
     */
    public function handle(WebhookHandled $event): void {
        match ($event->payload['type']) {
            'customer.subscription.created' => $this->handleSubscriptionCreated($event),
            'invoice.payment_succeeded'     => $this->handleInvoicePaymentSucceeded($event),
            'checkout.session.completed'    => $this->handleCheckoutSessionCompleted($event),
            default                         => null,
        };
    }

    private function handleSubscriptionCreated(WebhookHandled $event): void {
        $payload = $event->payload['data']['object'];
        
        // Get team_id from metadata
        $teamId = $payload['metadata']['team_id'] ?? null;
        $priceTypeValue = $payload['metadata']['price_type'] ?? null;
        $priceType = SubscriptionPriceType::tryFrom($priceTypeValue);

        if (!$priceType) {
            Log::error('No price_type found in subscription metadata', ['payload' => $payload]);
            return;
        }
        
        if (!$teamId) {
            Log::error('No team_id found in subscription metadata', ['payload' => $payload]);
            return;
        }
        
        // Find the team
        $team = Team::find($teamId);
        if (!$team instanceof Team) {
            Log::error('Team not found for subscription', ['team_id' => $teamId]);
            return;
        }

        $subscriptionId = $payload['id'];
        
        // Check if team already has an active subscription
        if ($team->isSubscribed()) {
            Log::warning('Team already has an active subscription. Cancelling new subscription.', [
                'team_id'                  => $team->id,
                'existing_subscription_id' => $team->getActiveSubscription()?->id,
                'new_subscription_id'      => $subscriptionId
            ]);
            
            // Cancel the new subscription and issue refund
            try {
                // Get Stripe instance
                $stripe = Cashier::stripe();
                
                // Cancel the subscription immediately
                $stripe->subscriptions->cancel($subscriptionId, [
                    'invoice_now' => false,
                    'prorate'     => false
                ]);
                
                // Find any related invoices and refund if paid
                $invoices = $stripe->invoices->all([
                    'subscription' => $subscriptionId,
                    'status'       => 'paid'
                ]);
                
                foreach ($invoices->data as $invoice) {
                    $personName = 'Unbekannt';
                    $user = $team->getActiveSubscription()?->user; 
                    if ($user instanceof User) {
                        $personName = $user->person->getFullName();
                    }
                    
                    // Create refund for the invoice
                    $stripe->refunds->create([
                        'invoice'  => $invoice->id,
                        'reason'   => 'duplicate',
                        'metadata' => [
                            'description' => 'Abo wurde erstattet, weil bereits ein Abo für das Team "'.$team->name.'" durch "'.$personName.'" abgeschlossen wurde.',
                        ]
                    ]);
                    
                    Log::info('Refund issued for duplicate subscription', [
                        'invoice_id'      => $invoice->id,
                        'subscription_id' => $subscriptionId
                    ]);
                }
                
                return; // Exit early, don't associate this subscription with the team
            } catch (\Exception $e) {
                Log::error('Failed to cancel duplicate subscription', [
                    'subscription_id' => $subscriptionId,
                    'error'           => $e->getMessage()
                ]);
            }
        }
        
        // Find the user by Stripe customer ID
        $customerId = $payload['customer'];
        $user = User::where('stripe_id', $customerId)->first();
        if (!$user) {
            Log::error('User not found for Stripe customer', ['customer_id' => $customerId]);
            return;
        }
        
        // Get the subscription from the user
        $subscription = $user->subscriptions()->where('stripe_id', $subscriptionId)->first();
        if (!$subscription instanceof Subscription) {
            Log::error('Subscription not found in database', ['subscription_id' => $subscriptionId]);
            return;
        }
        
        // Associate the subscription with the team
        $subscription->team()->associate($team);
        $subscription->price_type = $priceType;
        $subscription->save();
        
        Log::info('Subscription associated with team', [
            'team_id'         => $team->id,
            'subscription_id' => $subscription->stripe_id
        ]);
    }

    /**
     * Handle paid invoice events from Stripe.
     */
    private function handleInvoicePaymentSucceeded(WebhookHandled $event): void {
        // https://docs.stripe.com/api/invoices/object
        $stripeInvoiceArray = $event->payload['data']['object'];

        $invoiceId = $stripeInvoiceArray['id'];
        $customerId = $stripeInvoiceArray['customer'];
        $subscriptionId = $stripeInvoiceArray['subscription'];
        $teamId = $stripeInvoiceArray['subscription_details']['metadata']['team_id'] ?? null;
        $amount = $stripeInvoiceArray['amount_paid'] ?? 0;
        
        $context = [
            'invoice_id'      => $invoiceId,
            'customer_id'     => $customerId,
            'subscription_id' => $subscriptionId,
            'team_id'         => $teamId,
            'amount_paid'     => $amount,
        ];
        
        Log::info('Processing paid invoice', $context);

        // Skip processing for zero-amount invoices
        if ($amount <= 0) {
            Log::info('Skipping zero-amount invoice', $context);
            return;
        }
 
        // Find the user by Stripe customer ID
        $user = User::where('stripe_id', $customerId)->first();
        if (!$user) {
            Log::error('User not found for Stripe customer',$context);
            return;
        }
        
        $team = Team::find($teamId);
        if (!$team instanceof Team) {
            Log::error('Team not found for subscription', $context);
            return;
        }

        try {
            $stripeInvoiceObject = \Stripe\Invoice::constructFrom($stripeInvoiceArray);
            
            // sets the team_id in the invoice metadata to use in search api
            Cashier::stripe()->invoices->update($invoiceId, [
                'metadata' => [
                    'team_id' => $team->id,
                ]
            ]);
            
            $cashierInvoice = new Invoice($user, $stripeInvoiceObject);

            // Send invoice email
            Mail::to($user->email)
                ->bcc(config('mail.admin_bcc'))
                ->send(new InvoicePaidEmail($cashierInvoice, $team));

            Log::info('Invoice email sent', [
                'invoice_id' => $invoiceId,
                'user_email' => $user->email
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to process paid invoice', [
                'invoice_id' => $invoiceId,
                'error'      => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle paid invoice events from Stripe.
     */
    private function handleCheckoutSessionCompleted(WebhookHandled $event): void {
        // https://docs.stripe.com/api/checkout/sessions/object
        $stripeCheckoutSessionsArray = $event->payload['data']['object'];

        // TODO fertigstellen, s. https://github.com/eltini/clubmanager/issues/946
        
        //  nur mode setup bearbeiten, um die subscription vom setup intent auf einen anderen User zu übertragen 
        if ($stripeCheckoutSessionsArray['mode'] !== 'setup') {
            return;
        }

        $customerId = $stripeCheckoutSessionsArray['customer'];
        $teamId = $stripeCheckoutSessionsArray['metadata']['team_id'] ?? null;
        $setupIntentId = $stripeCheckoutSessionsArray['setup_intent'];

        $context = [
            'customer_id'     => $customerId,
            'team_id'         => $teamId,
            'setup_intent_id' => $setupIntentId,
        ];

        // Find the user by Stripe customer ID
        $user = User::where('stripe_id', $customerId)->first();
        if (!$user) {
            Log::error('User not found for Stripe customer',$context);
            return;
        }

        $team = Team::find($teamId);
        if (!$team instanceof Team) {
            Log::error('Team not found for subscription', $context);
            return;
        }
        
        $setupIntent = Cashier::stripe()->setupIntents->retrieve($setupIntentId);
        $paymentMethodId = (string) $setupIntent->payment_method;
        
        $user->updateDefaultPaymentMethod($paymentMethodId);
        
        $oldSub = $team->getActiveSubscription();
        if (!$oldSub instanceof Subscription) {
            Log::error('Team has no old subscription', $context);
            return;
        }
        if (!$oldSub->onGracePeriod()) {
            Log::error('old subscription is not on grace period', $context);
            return;
        }
        $endsAt = $oldSub->ends_at ?? now(); // fallback damit es nicht null ist

        $type = config('cashier.lookup_keys.product.team');
        
        $subscription = Cashier::stripe()->subscriptionSchedules->create([
            'customer'     => $user->stripe_id,
            'start_date'   => $endsAt->addDay()->getTimestamp(),
            'end_behavior' => 'release',
            'phases'       => [[
                'automatic_tax' => [
                    'enabled' => Cashier::$calculatesTaxes,
                ],
                'items' => [[
                    'price' => $oldSub->stripe_price,
                ]],
                'default_payment_method' => $paymentMethodId,
                'metadata'               => [
                    'team_id' => $team->id,
                    'type'    => $type,
                ],
            ]],
        ]);
        
        $context['old_subscription_id'] = $oldSub->stripe_id;
        $context['scheduled_subscription'] = $subscription;
        
        Log::info('Scheduled subscription for transfer created', $context);
    }
}
