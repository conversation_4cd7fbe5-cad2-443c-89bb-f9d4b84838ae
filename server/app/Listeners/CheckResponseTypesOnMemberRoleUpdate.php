<?php

namespace App\Listeners;

use App\Events\TeamMemberStatusRoleUpdated;
use App\Types\TeamRoleType;

class CheckResponseTypesOnMemberRoleUpdate {

    public function handle(TeamMemberStatusRoleUpdated $event): void {
        if ($event->teamMember->statusRole->name === TeamRoleType::INACTIVE) {
            $event->teamMember->deleteFutureVotesForResponseTypeAutoYes();
        } elseif ($event->previousRole->name === TeamRoleType::INACTIVE) {
            $event->teamMember->checkFutureVotesForResponseType();
        }
    }
}
