<?php

namespace App\Traits;

use App\JsonApi\V1\Comments\CommentSchema;
use App\Models\Comment;
use App\Models\Person;
use Illuminate\Database\Eloquent\Relations\MorphOne;

/**
 * Classes using this trait have to be added to @see CommentSchema::fields() MorphTo to correctly convert parent types
 */
trait HasComment {

    protected static function bootHasComment(): void {
        static::deleting(function($parent) {
            $parent->comment()->delete();
        });
    }

    /**
     * @return MorphOne<Comment, $this>
     */
    public function comment(): MorphOne {
        return $this->morphOne(Comment::class, 'parent')->latestOfMany();
    }

    public function setComment(string $comment, Person $author = null): Comment {
        $commentModel = $this->comment()->create([
            'text' => $comment,
            'author_id' => $author?->id,
        ]);
        
        $commentModel->setRelation('author', $author);
        
        return $commentModel;
    }

    public function hasComment(): bool {
        if($this->relationLoaded('comment')) {
            return $this->comment instanceof Comment;
        }

        return $this->comment()->exists();
    }
}
