<?php

namespace App\Traits;

use App\Models\Comment;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * TODO not in use yet, test carefully when first used and remove TODO afterwards
 * Classes using this trait have to be added to @see CommentSchema::fields() MorphTo to correctly convert parent types
 */
trait HasComments {

    protected static function bootHasComments(): void {
        static::deleting(function($parent) {
            $parent->comments()->delete();
        });
    }

    /**
     * @return MorphMany<Comment>
     */
    public function comments(): MorphMany {
        return $this->morphMany(Comment::class, 'parent');
    }
}
