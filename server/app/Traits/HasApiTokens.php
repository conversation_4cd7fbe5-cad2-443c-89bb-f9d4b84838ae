<?php

namespace App\Traits;

use App\Models\PersonalAccessToken;
use DateTimeInterface;
use Illuminate\Support\Str;
use <PERSON><PERSON>\Sanctum\HasApiTokens as SanctumHasApiTokens;
use <PERSON><PERSON>\Sanctum\NewAccessToken;

trait HasApiTokens {
    use SanctumHasApiTokens {
        SanctumHasApiTokens::createToken as parentCreateToken;
    }

    /**
     * @param array<string> $abilities
     */
    public function createToken(
        string            $name = null,
        array             $abilities = ['*'],
        DateTimeInterface $expiresAt = null,
        string            $device_name = null,
        string            $device_type = null,
        string            $device_platform = null,
        string            $device_model = null,
        string            $device_os = null,
        string            $device_manufacturer = null,
        string            $fcm_token = null,
        string            $installation_id = null,
    ): NewAccessToken {
        /** @var PersonalAccessToken $token * */
        $token = $this->tokens()->create([
            'name'                => $name,
            'token'               => hash('sha256', $plainTextToken = Str::random(40)),
            'abilities'           => $abilities,
            'expires_at'          => $expiresAt,
            'device_name'         => $device_name,
            'device_type'         => $device_type,
            'device_platform'     => $device_platform,
            'device_model'        => $device_model,
            'device_os'           => $device_os,
            'device_manufacturer' => $device_manufacturer,
            'fcm_token'           => $fcm_token,
            'installation_id'     => $installation_id,
        ]);

        return new NewAccessToken($token, $token->getKey() . '|' . $plainTextToken);
    }
}
