<?php

namespace App\Traits;

use App\Models\Person;
use App\Models\Team;
use App\Models\TeamMember;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

trait HasTargetMembersAndPersons {

    /**
     * Tries to get the name based on the current class.
     * Overwrite if it does not match
     * @return string
     */
    protected function getMorphName(): string {
        $singularClassName = Str::lower(Str::singular($this->getTable()));
        return $singularClassName.'_target';
    }

    /**
     * Tries to use the plural form of teh morph name as table name.
     * Overwrite if it does not match
     * @return string
     */
    protected function getIntermediateTableName(): string {
        return Str::plural($this->getMorphName());
    }

    /**
     * @return MorphToMany<TeamMember, $this>
     */
    public function teamMembers(): MorphToMany {
        return $this->morphedByMany(TeamMember::class, $this->getMorphName());
    }
    
    /**
     * @return MorphToMany<Person, $this>
     */
    public function persons(): MorphToMany {
        return $this->morphedByMany(Person::class, $this->getMorphName());
    }

    /**
     * Returns a collection of all TeamMembers affected by this absence. These can
     * be directly referenced TeamMembers or all TeamMembers of a referenced Person.
     *
     * If a team is provided all collected members will be filtered by this team.
     *
     * @param Team|null $team The team to filter the members by
     *
     * @return Collection<int, TeamMember>
     */
    public function getUniqueTargetTeamMembers(Team $team = null): Collection {
        // get direct members
        $targetMembers = $this->teamMembers->collect();

        // get members from target persons
        $persons = $this->persons;
        $persons->loadMissing('teamMembers');
        foreach ($persons as $person) {
            foreach ($person->teamMembers as $teamMember) {
                $targetMembers->add($teamMember);
            }
        }

        $targetMembers = $targetMembers->unique('id');
        if (isset($team)) {
            $targetMembers = $targetMembers->where('team_id', '=', $team->id);
        }
        return $targetMembers->sortBy('name');
    }


}
