<?php

namespace App\Traits;

use Symfony\Component\HttpFoundation\Response;

trait HasNonInfiniteRRule {
    use HasRRule;

    protected static function bootHasNonInfiniteRRule(): void {
        static::creating(static function (self $self) {
            // check if R<PERSON><PERSON> has UNTIL or COUNT set to prevent infinite loops
            if($self->isRecurring() && $self->getRRule()->isInfinite()) {
                // TODO better change to internal exception and change to HttpException only when called from frontend
                abort(Response::HTTP_BAD_REQUEST,'recurring without UNTIL or COUNT are infinite and cannot be created');
            }
        });
    }

}
