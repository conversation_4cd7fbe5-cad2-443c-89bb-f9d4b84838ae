<?php

namespace App\Traits;

use App\JsonApi\V1\Invites\InviteSchema;
use App\Models\Invite;
use Illuminate\Database\Eloquent\Relations\MorphOne;

/**
 * Classes using this trait have to be added to @see InviteSchema::fields() MorphTo to correctly convert invitable types
 */
trait HasInvite {

    protected static function bootHasInvite(): void {
        static::deleting(function($invitable) {
            $invitable->invite()->delete();
        });
    }

    /**
     * Get the associated invite for the model.
     *
     * @return MorphOne<Invite, $this>
     */
    public function invite(): MorphOne {
        return $this->morphOne(Invite::class, 'invitable')->latestOfMany();
    }
}
