<?php

namespace App\Traits;


use App\Http\Controllers\AuthController;
use App\Models\Person;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property Person $author // cannot be null, added for php<PERSON> to not expect null values
 */
trait HasAuthor<PERSON>erson {

    protected static function bootHasAuthorPerson(): void {
        static::creating(static function($parent) {
            // add current users person as author
            if($parent->author_id === null) {
                $parent->author_id = AuthController::getCurrentUser()?->person->id;
            }
        });
    }
    
    /**
     * @return BelongsTo<Person, $this>
     */
    public function author(): BelongsTo {
        return $this->belongsTo(Person::class);
    }
}
