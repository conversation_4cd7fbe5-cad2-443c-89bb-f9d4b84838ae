<?php

namespace App\Traits;

use DateTimeInterface;
use RRule\RRule;

/**
 * @property string|RRule $rrule_string can be set as RRule and will be converted to string when saved to DB
 */
trait HasRRule {

    /**
     * @return RRule<DateTimeInterface>
     */
    public function getRRule(): RRule {
        if($this->rrule_string instanceof RRule) {
            return $this->rrule_string;
        }
        return new RRule($this->rrule_string);
    }

    public function setRRule(RRule $rrule): void {
        $this->rrule_string = $rrule;
    }

    public function isRecurring(): bool {
        try {
            // This is a workaround to check if the rrule string is valid. The getRRule()
            // method will throw an exception if the string is invalid by creating a new
            // RRule instance with it.
            // TODO(fabzo): Ideally this shouldn't throw an exception, but return null instead, since we should expect this string to be invalid.
            $this->getRRule();

            return isset($this->rrule_string);
        } catch (\InvalidArgumentException $e) {
            return false;
        }
    }

    public function setRRuleUntil(DateTimeInterface $date, bool $saveDirectly = true): void {
        $rule = $this->getRRule()->getRule();
        $rule['UNTIL'] = $date;
        $this->setRRule(new RRule($rule));
        if($saveDirectly) {
            $this->save();
        }
    }
}
