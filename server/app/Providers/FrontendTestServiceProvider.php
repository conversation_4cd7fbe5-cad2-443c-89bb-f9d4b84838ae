<?php

namespace App\Providers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;

class FrontendTestServiceProvider extends ServiceProvider
{

    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(Request $request): void
    {
        if(self::isRequestFromCypressFrontend()) {
            // default config for cypress frontend tests (see client/Readme.md#tests
            DB::setDefaultConnection('mysql_test');
            Mail::setDefaultDriver('array');
            Cache::setDefaultDriver('array');
            Queue::setDefaultDriver('sync');
        }
    }

    public static function isRequestFromCypressFrontend(): bool {
        $request = Request::capture();
        $isApiFrontendCalled = $request->getHost() === parse_url(config('app.api_test_url'))['host'];
        $isLaravelCypressCalled = Str::startsWith($request->getPathInfo(), '/__cypress');
        return $isApiFrontendCalled || $isLaravelCypressCalled;
    }
}
