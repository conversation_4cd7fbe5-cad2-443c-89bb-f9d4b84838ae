<?php

namespace App\Providers;

use App\Http\Controllers\AuthController;
use App\Models\Person;
use App\Models\User;
use App\Services\NumoLiveDemoService;
use App\Types\FeatureType;
use Illuminate\Support\ServiceProvider;
use Laravel\Pennant\Feature;

class FeatureServiceProvider extends ServiceProvider {

    public function register(): void {}

    public function boot(): void {
        Feature::resolveScopeUsing(fn($driver) => AuthController::getCurrentUser()?->person);

        $this->defineFeatures();
    }

    private function defineFeatures(): void {
        Feature::store('array')->define(FeatureType::TEAM_CREATE->value, fn(Person $person) => match (true) {
            config('app.allow_new_teams') => true,
            default => $person->user !== null && $this->isSuperAdmin($person->user),
        });
        Feature::define(FeatureType::ADMIN_STATS->value, fn(Person $person) => match (true) {
            $person->user !== null && $this->isSuperAdmin($person->user) => true,
            default => false,
        });
        Feature::define(FeatureType::DEVICE_LIST->value, fn(Person $person) => true);
        Feature::define(FeatureType::NOTIFICATIONS_ENABLED->value, fn(Person $person) => true);


        // used to hide feature in old App versions
        Feature::store('array')->define(FeatureType::SUBSCRIPTION->value, fn(Person $person) => match (true) {
            $person->user !== null && $this->isGoogleOrAppleTestUser($person->user) => false,
            default => true,
        });

        // used for extra features in live demo
        Feature::store('array')->define(FeatureType::LIVE_DEMO->value, fn(Person $person) => match (true) {
            $person->user !== null && $this->isLiveDemoUser($person->user) => true,
            default => false,
        });
    }

    function isThomasOrFabio(User $user): bool {
        return in_array($user->email, [
            '<EMAIL>',
            config('mail.thomas_numo'),
            '<EMAIL>',
            '<EMAIL>'
        ]);
    }
    
    private function isSuperAdmin(User $user): bool {
        $isPredefinedEmail = in_array($user->email, [
            config('mail.thomas_numo'),
            '<EMAIL>',
            '<EMAIL>',
            //'<EMAIL>',
        ]);

        // Fabio's test emails
        if (!$isPredefinedEmail && str_ends_with($user->email, '.<EMAIL>')) {
            $isPredefinedEmail = true;
        }

        return $isPredefinedEmail;
    }


    function isGoogleOrAppleTestUser(User $user): bool {
        return in_array($user->email, [
            '<EMAIL>',
            '<EMAIL>'
        ]);
    }

    function isLiveDemoUser(User $user): bool {
        return in_array($user->email, [
            NumoLiveDemoService::$managerUserEmail,
            NumoLiveDemoService::$memberUserEmail
        ]);
    }
    
}
