<?php

namespace App\Providers;

use App\Models\Subscription;
use App\Models\SubscriptionItem;
use Illuminate\Support\ServiceProvider;
use Lara<PERSON>\Cashier\Cashier;
use Laravel\Sanctum\Sanctum;

class AppServiceProvider extends ServiceProvider {
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register(): void {
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot(): void {
        // This instructs Sanctum to use our own implementation of the PersonalAccessToken model that includes device data
        Sanctum::usePersonalAccessTokenModel(\App\Models\PersonalAccessToken::class);

        Cashier::calculateTaxes(); // Configure Cashier to automatically calculate taxes using Stripe Tax.
        Cashier::useSubscriptionModel(Subscription::class);
        Cashier::useSubscriptionItemModel(SubscriptionItem::class);
    }
}
