<?php

namespace App\Providers;

// use Illuminate\Support\Facades\Gate;
use App\JsonApi\Authorizers\AdminStatsAuthorizer;
use App\JsonApi\V1\AdminStatsTeams\AdminStatsTeamSchema;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use LaravelJsonApi\Laravel\LaravelJsonApi;

class AuthServiceProvider extends ServiceProvider {
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot() {
        $this->registerPolicies();

        //
    }

    public function register() {
        parent::register();
        LaravelJsonApi::registerAuthorizer(AdminStatsAuthorizer::class, [
            AdminStatsTeamSchema::class
        ]);
    }

}
