<?php

namespace App\Providers;

use App\Actions\Fortify\CreateNewUser;
use App\Actions\Fortify\ResetUserPassword;
use App\Actions\Fortify\UpdateUserPassword;
use App\Actions\Fortify\UpdateUserProfileInformation;
use App\Models\User;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\ServiceProvider;
use Laravel\Fortify\Contracts\LoginResponse;
use Laravel\Fortify\Contracts\LogoutResponse;
use Laravel\Fortify\Fortify;
use Laravel\Sanctum\PersonalAccessToken;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class FortifyServiceProvider extends ServiceProvider {
    /**
     * Register any application services.
     */
    public function register(): void {
        Fortify::ignoreRoutes();

        //        $this->app->instance(LoginResponse::class, new class implements LoginResponse {
        //            public function toResponse($request)
        //            {
        //                $user = $request->user();
        //                return response()->json([
        //                    'user' => $user,
        //                    'token' => $user->createToken($request->device_name)->plainTextToken
        //                ]);
        //            }
        //        });

        $this->app->instance(LogoutResponse::class, new class implements LogoutResponse {
            public function toResponse($request): Response {
                $user = auth('sanctum')->user();
                if ($user instanceof User) {
                    // revoke token
                    $token = $user->currentAccessToken();
                    if ($token instanceof PersonalAccessToken) {
                        $token->delete();
                    }
                }

                // Return an empty JSON response to indicate success
                return new JsonResponse(null, 204);  // 204 No Content
            }
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void {
        Fortify::createUsersUsing(CreateNewUser::class);
        Fortify::updateUserProfileInformationUsing(UpdateUserProfileInformation::class);
        Fortify::updateUserPasswordsUsing(UpdateUserPassword::class);
        Fortify::resetUserPasswordsUsing(ResetUserPassword::class);

        RateLimiter::for('login', function (Request $request) {
            $email = (string)$request->email;

            return Limit::perMinute(5)->by($email . $request->ip());
        });

        RateLimiter::for('two-factor', function (Request $request) {
            return Limit::perMinute(5)->by($request->session()->get('login.id'));
        });

        //        Fortify::authenticateUsing(function (Request $request) {
        //            $request->validate([
        //                                   'email' => 'required|email',
        //                                   'password' => 'required',
        //                                   'device_name' => 'required',
        //                               ]);
        //
        //            $user = User::where('email', $request->email)->first();
        //
        //            if (! $user || ! \Hash::check($request->password, $user->password)) {
        //                return null;
        //            }
        //            return $user;
        //        });
    }
}
