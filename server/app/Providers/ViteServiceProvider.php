<?php

namespace App\Providers;

use Illuminate\Support\Facades\Vite;
use Illuminate\Support\ServiceProvider;

class ViteServiceProvider extends ServiceProvider {
    /**
     * Register services.
     */
    public function register(): void {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void {
        // @phpstan-ignore-next-line
        Vite::macro('image', fn(string $asset) => $this->asset("resources/images/{$asset}"));
        // @phpstan-ignore-next-line
        Vite::macro('video', fn(string $asset) => $this->asset("resources/videos/{$asset}"));
    }
}
