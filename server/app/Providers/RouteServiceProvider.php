<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider {
    /**
     * The path to the "home" route for your application.
     *
     * Typically, users are redirected here after authentication.
     *
     * @var string
     */
    public const HOME = '/home';

    /**
     * Define your route model bindings, pattern filters, and other route configuration.
     *
     * @return void
     */
    public function boot() {
        $this->configureRateLimiting();

        $this->routes(function () {

            if(App::environment('valet')) {
                Route::prefix('api')
                     ->name('prefix_api.')
                     ->middleware('api')
                     ->group(base_path('routes/api.php'));
            }

            Route::domain(parse_url(config('app.api_url'))['host'])
                ->name('api.')
                ->middleware('api')
                ->group(base_path('routes/api.php'));

            if(FrontendTestServiceProvider::isRequestFromCypressFrontend()) {
                // overwrite named routes for url generation
                Route::domain(parse_url(config('app.api_test_url'))['host'])
                    ->name('api.')
                    ->middleware('api')
                    ->group(base_path('routes/api.php'));
            }

            Route::middleware('web')
                ->group(base_path('routes/web.php'));

            Route::domain(config('app.frontend.url'))
                ->name('frontend.')
                ->group(base_path('routes/frontend.php'));
        });
    }

    /**
     * Configure the rate limiters for the application.
     *
     * @return void
     */
    protected function configureRateLimiting() {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });

        RateLimiter::for('invite', function (Request $request) {
            // Allow only one attempt to accept a specific invite per minute globally
            return Limit::perMinute(1)->by($request->str('token'));
        });
    }
}
