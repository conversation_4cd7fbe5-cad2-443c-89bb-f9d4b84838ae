<?php

namespace App\Jobs;

use App\Mail\TrialEndingReminderEmail;
use App\Models\Team;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class SendTrialEndingReminderEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Team $team,
        public User $user
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Überprüfen, ob der Testzeitraum noch aktiv ist
        if (!$this->team->trial_ends_at || $this->team->trial_ends_at->isPast()) {
            return;
        }

        // Überprüfen, ob der Testzeitraum in etwa 14 Tagen endet
        $daysUntilTrialEnds = Carbon::now()->diffInDays($this->team->trial_ends_at, false);
        if ($daysUntilTrialEnds < 13 || $daysUntilTrialEnds > 15) {
            return;
        }

        // Überprüfen, ob das Team bereits ein aktives Abonnement hat
        if ($this->team->isSubscribed()) {
            return;
        }

        // E-Mail senden
        Mail::to($this->user)
            ->bcc(config('mail.admin_bcc'))
            ->send(new TrialEndingReminderEmail($this->user, $this->team));
    }
}
