<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;

class RRule implements ValidationRule {
    public function validate(string $attribute, mixed $value, Closure $fail): void {
        try {
            \RRule\RRule::createFromRfcString($value);
            new \RRule\RRule($value);
        } catch (\InvalidArgumentException $e) {
            throw new BadRequestException('recurring rule is not valid', previous: $e);
        }
    }
}
