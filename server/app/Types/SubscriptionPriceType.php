<?php

namespace App\Types;

use <PERSON><PERSON>\TypeScriptTransformer\Attributes\TypeScript;
use Stripe\Subscription as StripeSubscription;

#[TypeScript]
enum SubscriptionPriceType: string {

    // CREATE TYPESCRIPT TYPES:
    // for any change, create types for client by running: artisan typescript:transform

    case TEAM_YEARLY = 'team_yearly';
    case TEAM_HALF_YEARLY = 'team_half_yearly';
    case TEAM_MONTHLY = 'team_monthly';
}
