<?php

namespace App\Types;

use App\Models\Team;
use App\Models\TeamPermission;
use JetBrains\PhpStorm\ArrayShape;
use <PERSON><PERSON>\TypeScriptTransformer\Attributes\TypeScript;

#[TypeScript]
enum TeamPermissionType: string {

    // DELETING VALUES:
    // delete values together with migration that deletes all related entries since these are $casts for TeamPermission->name
    // it will result in an error, when a database entry does not find a corresponding enum!

    // ADDING VALUES:
    // create migration and call TeamPermissionType::addOrCreateDefaultPermissions();

    // CREATE TYPESCRIPT TYPES:
    // for any change, create types for client by running: artisan typescript:transform

    case TEAM_MEMBER_CREATE = 'team.member.create';
    case TEAM_MEMBER_DELETE = 'team.member.delete';
    case TEAM_MEMBER_UPDATE = 'team.member.update';

    case TEAM_EVENT_CREATE = 'team.event.create';
    case TEAM_EVENT_DELETE = 'team.event.delete';

    // Allows voting for yourself, usually all members have this permission
    case TEAM_EVENT_VOTE_SELF = 'team.event.vote.self';

    // Allows voting for other members of a team, usually only managers have this permission
    case TEAM_EVENT_VOTE_OTHER = 'team.event.vote.other';

    case TEAM_EVENT_TASK_CREATE = 'team.event.task.create';
    case TEAM_EVENT_TASK_DELETE = 'team.event.task.delete';
    case TEAM_EVENT_TASK_ASSIGN_SELF = 'team.event.task.assign.self';
    case TEAM_EVENT_TASK_ASSIGN_OTHER = 'team.event.task.assign.other';


    case TEAM_MEMBER_INVITE_CREATE_SELF = 'team.member.invite.create.self';
    case TEAM_MEMBER_INVITE_CREATE_OTHER = 'team.member.invite.create.other';
    case TEAM_INVITE_CREATE = 'team.invite.create';
    case TEAM_UPDATE = 'team.update';

    case TEAM_STATS_RANGE_CREATE = 'team.stats.range.create';
    case TEAM_STATS_RANGE_DELETE = 'team.stats.range.delete';

    case TEAM_SUBSCRIPTION_MANAGE = 'team.subscription.manage';
    
    case TEAM_LEDGER_MANAGE = 'team.ledger.manage';
    case TEAM_LEDGER_VIEW = 'team.ledger.view';


    public static function addOrCreateDefaultPermissions(bool $addToExistingTeams = true): void {
        TeamPermission::upsert(
            collect(self::cases())->map(function (TeamPermissionType $teamPermissionType) {
                return ['name' => $teamPermissionType];
            })->toArray()
            , ['name']);

        if($addToExistingTeams) {
            Team::get()->each(function (Team $team) {
                TeamRoleType::setDefaultRolesAndPermissions($team);
            });
        }
    }

    /**
     * @param array<string> $permissionNames
     */
    public static function removePermissionsTypes(array $permissionNames): void {
        TeamPermission::whereIn('name', $permissionNames)->delete();
    }
}
