<?php

namespace App\Types;

use <PERSON><PERSON>\TypeScriptTransformer\Attributes\TypeScript;

#[TypeScript]
enum TeamEventType: string {

    // CREATE TYPESCRIPT TYPES:
    // for any change, create types for client by running: artisan typescript:transform

    case NONE = 'none';
    case TRAINING = 'training';
    case MATCH = 'match';
    case TOURNAMENT = 'tournament';
    case EVENT = 'event';
}
