<?php

namespace App\Types;

use <PERSON>tie\TypeScriptTransformer\Attributes\TypeScript;
use Stripe\Subscription as StripeSubscription;

#[TypeScript]
enum SubscriptionStatusType: string {

    // CREATE TYPESCRIPT TYPES:
    // for any change, create types for client by running: artisan typescript:transform

    case STATUS_ACTIVE = StripeSubscription::STATUS_ACTIVE;
    case STATUS_CANCELED = StripeSubscription::STATUS_CANCELED;
    case STATUS_INCOMPLETE = StripeSubscription::STATUS_INCOMPLETE;
    case STATUS_INCOMPLETE_EXPIRED = StripeSubscription::STATUS_INCOMPLETE_EXPIRED;
    case STATUS_PAST_DUE = StripeSubscription::STATUS_PAST_DUE;
    case STATUS_PAUSED = StripeSubscription::STATUS_PAUSED;
    case STATUS_TRIALING = StripeSubscription::STATUS_TRIALING;
    case STATUS_UNPAID = StripeSubscription::STATUS_UNPAID;
}
