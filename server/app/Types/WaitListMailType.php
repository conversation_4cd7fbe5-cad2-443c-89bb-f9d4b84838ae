<?php

namespace App\Types;

use <PERSON><PERSON>\TypeScriptTransformer\Attributes\TypeScript;

#[TypeScript]
enum WaitListMailType: string {

    // CREATE TYPESCRIPT TYPES:
    // for any change, create types for client by running: artisan typescript:transform

    case UNKNOWN = 'unknown';
    case ALREADY_REGISTERED = 'alreadyRegistered';
    case ALREADY_ON_INVITE_LIST = 'alreadyOnInviteList';
    case ALREADY_ON_WAIT_LIST = 'alreadyOnWaitList';
    case ADDED_TO_WAIT_LIST = 'addedToWaitList';
}
