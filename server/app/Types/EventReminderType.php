<?php

namespace App\Types;

use <PERSON><PERSON>\TypeScriptTransformer\Attributes\TypeScript;

#[TypeScript]
enum EventReminderType: string {

    // CREATE TYPESCRIPT TYPES:
    // for any change, create types for client by running: artisan typescript:transform

    // Note: This type is mapped to an enum in the TeamEvent table. If you change this,
    //       you must also change the enum in the TeamEvent table.
    
    case NONE = 'none';
    case HOURS_BEFORE_EVENT = 'hours_before_event';
} 
