<?php

namespace App\Types;

use App\Models\Team;
use App\Models\TeamMember;
use App\Models\TeamPermission;
use App\Models\TeamRole;
use Illuminate\Database\Eloquent\Collection;
use Spatie\TypeScriptTransformer\Attributes\TypeScript;

#[TypeScript]
enum TeamRoleType: string {

    // CREATE TYPESCRIPT TYPES:
    // for any change, create types for client by running: artisan typescript:transform

    // Default status roles that cannot be deleted, only assigned permissions can be modified by user
    case MANAGER = 'manager';
    case MEMBER = 'member';
    case INACTIVE = 'inactive';
    
    // Default custom roles
    case TREASURER  = 'treasurer';

    /**
     * @var array<string, array<TeamPermissionType>>
     */
    private const array DEFAULT_ROLE_PERMISSIONS = [
        TeamRoleType::MANAGER->value => [
            TeamPermissionType::TEAM_MEMBER_CREATE,
            TeamPermissionType::TEAM_MEMBER_DELETE,
            TeamPermissionType::TEAM_MEMBER_UPDATE,
            TeamPermissionType::TEAM_EVENT_CREATE,
            TeamPermissionType::TEAM_EVENT_DELETE,
            TeamPermissionType::TEAM_EVENT_VOTE_OTHER,
            TeamPermissionType::TEAM_EVENT_TASK_CREATE,
            TeamPermissionType::TEAM_EVENT_TASK_DELETE,
            TeamPermissionType::TEAM_EVENT_TASK_ASSIGN_OTHER,
            TeamPermissionType::TEAM_INVITE_CREATE,
            TeamPermissionType::TEAM_MEMBER_INVITE_CREATE_OTHER,
            TeamPermissionType::TEAM_UPDATE,
            TeamPermissionType::TEAM_STATS_RANGE_CREATE,
            TeamPermissionType::TEAM_STATS_RANGE_DELETE,
            TeamPermissionType::TEAM_SUBSCRIPTION_MANAGE,
            TeamPermissionType::TEAM_LEDGER_VIEW,
        ],
        TeamRoleType::MEMBER->value => [
            TeamPermissionType::TEAM_EVENT_VOTE_SELF,
            TeamPermissionType::TEAM_EVENT_TASK_ASSIGN_SELF,
            TeamPermissionType::TEAM_MEMBER_INVITE_CREATE_SELF, // TODO, use later, when parents or others can be invited
            TeamPermissionType::TEAM_LEDGER_VIEW,
        ],
        TeamRoleType::INACTIVE->value => [
        ],
        TeamRoleType::TREASURER->value => [
            TeamPermissionType::TEAM_LEDGER_MANAGE,
        ],
    ];

    /**
     * @return TeamPermissionType[][]
     */
    public static function getDefaultRolesAndPermissions(): array {
        return self::DEFAULT_ROLE_PERMISSIONS;
    }

    public static function setDefaultRolesAndPermissions(Team $team): void {
        $allPermissions = TeamPermission::all();
        foreach (self::DEFAULT_ROLE_PERMISSIONS as $roleName => $permissionNames) {
            $rolePermissions = self::getPermissionsForNames($permissionNames, $allPermissions);

            $role = TeamRole::createRoleIfNotExists($team, TeamRoleType::from($roleName));
            $missingPermissions = $rolePermissions->filter(function (TeamPermission $teamPermission) use ($role) {
                return $role->permissions->doesntContain($teamPermission);
            });
            $role->permissions()->saveMany($missingPermissions);
        }
    }

    public static function setDefaultRolesForTeamMember(TeamMember $teamMember): void {
        $query = $teamMember->team->roles()->where('name', TeamRoleType::MEMBER);
        $teamMember->roles()->save(
            TeamRole::createRoleIfNotExists($teamMember->team, TeamRoleType::MEMBER)
        );
    }

    /**
     * @param TeamPermissionType[] $permissionNames
     * @param Collection<int, TeamPermission>|null $allPermissions
     *
     * @return Collection<int, TeamPermission>
     */
    public static function getPermissionsForNames(array $permissionNames, Collection $allPermissions = null): Collection {
        $allPermissions = $allPermissions ?? TeamPermission::all();
        $permissionNames = collect($permissionNames);
        return $allPermissions->filter(function (TeamPermission $permission) use ($permissionNames) {
            return $permissionNames->contains($permission->name);
        });
    }
}
