<?php

namespace App\Types;

use <PERSON><PERSON>\TypeScriptTransformer\Attributes\TypeScript;

#[TypeScript]
enum FeatureType: string {

    // CREATE TYPESCRIPT TYPES:
    // for any change, create types for client by running: artisan typescript:transform

    case ADMIN_STATS = 'admin.stats';
    case TEAM_CREATE = 'team.create';
    case DEVICE_LIST = 'device.list';
    case NOTIFICATIONS_ENABLED = 'notifications.enabled';

    // used to hide feature in old App versions only for google and apple test user
    case SUBSCRIPTION = 'subscription';

    // used to change frontend for live demo 
    case LIVE_DEMO = 'live_demo';
}
