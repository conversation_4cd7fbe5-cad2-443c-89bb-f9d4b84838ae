<?php

namespace App\Types;

use <PERSON><PERSON>\TypeScriptTransformer\Attributes\TypeScript;

#[TypeScript]
enum TeamLedgerClaimStatusType: string {

    // CREATE TYPESCRIPT TYPES:
    // for any change, create types for client by running: artisan typescript:transform

    case FULFILLED = 'fulfilled';
    case EXEMPT = 'exempt';
    case UNFULFILLED = 'unfulfilled';
    case UNFULFILLED_OVERDUE = 'unfulfilled_overdue';
}
