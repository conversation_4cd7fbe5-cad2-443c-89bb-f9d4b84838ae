<?php

namespace App\Events;

use App\Models\TeamMember;
use App\Models\TeamRole;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TeamMemberStatusRoleUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public TeamMember $teamMember,
        public TeamRole $previousRole
    )
    {}
}
