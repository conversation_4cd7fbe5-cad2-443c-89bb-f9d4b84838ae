<?php

namespace App\Events;

use App\Http\Controllers\AuthController;
use App\Models\Team;
use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TeamCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public User|null $user;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public Team $team
    ) {
        $this->user = AuthController::getCurrentUser();
    }
}
