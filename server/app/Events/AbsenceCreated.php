<?php

namespace App\Events;

use App\Models\Absence;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * cannot be dispatched directly from absence since members and persons are added after creation
 */
class AbsenceCreated {
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public Absence $absence
    ) {}
}
