<?php

namespace App\Policies;

use App\Models\Absence;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class AbsencePolicy {
    use HandlesAuthorization;

    public function viewAny(User $user): bool {
        return true; // is regulated in server/app/JsonApi/V1/Server.php#serving with global scope
    }

    public function view(User $user, Absence $absence): bool {
        return true; // is regulated in server/app/JsonApi/V1/Server.php#serving with global scope
    }

    public function create(User $user): bool {
        return true;
    }

    public function update(User $user, Absence $absence): bool {
        return true; // is regulated in server/app/JsonApi/V1/Server.php#serving with global scope
    }

    public function delete(User $user, Absence $absence): bool {
        return true; // is regulated in server/app/JsonApi/V1/Server.php#serving with global scope
    }

    public function restore(User $user, Absence $absence): bool {
        return false;
    }

    public function forceDelete(User $user, Absence $absence): bool {
        return false;
    }
}
