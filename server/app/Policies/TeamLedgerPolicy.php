<?php

namespace App\Policies;

use App\Models\Team;
use App\Models\TeamLedger;
use App\Models\User;
use App\Types\FeatureType;
use App\Types\TeamPermissionType;
use <PERSON><PERSON>\Pennant\Feature;

class TeamLedgerPolicy
{
    private function getTeamFromRequest(): Team {
        $id = request()->input('data.relationships.team.data.id');
        return Team::findOrFail($id);
    }
    
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return false;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, TeamLedger $teamLedger): bool
    {
        return Feature::active(FeatureType::ADMIN_STATS->value) 
            || $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_LEDGER_VIEW, $teamLedger->team);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        $team = $this->getTeamFromRequest();
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_LEDGER_MANAGE, $team);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, TeamLedger $teamLedger): bool
    {
        $team = $this->getTeamFromRequest();
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_LEDGER_MANAGE, $team);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, TeamLedger $teamLedger): bool
    {
        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, TeamLedger $teamLedger): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, TeamLedger $teamLedger): bool
    {
        return false;
    }
}
