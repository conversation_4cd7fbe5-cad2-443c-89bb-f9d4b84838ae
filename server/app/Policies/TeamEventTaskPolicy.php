<?php

namespace App\Policies;

use App\Models\Team;
use App\Models\TeamEvent;
use App\Models\TeamEventTask;
use App\Models\TeamMember;
use App\Models\User;
use App\Types\TeamPermissionType;

class TeamEventTaskPolicy
{
    private function getTeamFromRequest(): Team {
        $id = request()->input('data.relationships.teamEvent.data.id');
        return TeamEvent::findOrFail($id)->team;
    }

    private function getTeamMemberFromRequest(): ?TeamMember {
        $id = request()->input('data.relationships.teamMember.data.id');
        if(isset($id)) {
            return TeamMember::findOrFail($id);
        }
        return null;
    }

    /**
     * returns true if a member is set in request and current user has permission to assign it
     * false if no member is set in request or user has no permission to assign it
     *
     * @param User $user
     *
     * @return bool
     */
    private function canAssignMemberIfSet(User $user): bool {
        $teamMember = $this->getTeamMemberFromRequest();
        if($teamMember instanceof TeamMember) {
            $team = $teamMember->team;
            $userPerson = $user->person;

            $canAssignOther = $userPerson->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_TASK_ASSIGN_OTHER, $team);
            $isSamePerson = $teamMember->person !== null && $teamMember->person->id === $userPerson->id;
            $canAssignSelf = $userPerson->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_TASK_ASSIGN_SELF, $team);

            return $canAssignOther || ($isSamePerson && $canAssignSelf);
        }
        return false;
    }

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // is secured by global scope where each query contains only configs from teams of the current user
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, TeamEventTask $teamEventTask): bool
    {
        // is secured by global scope where each query contains only configs from teams of the current user
        return true;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        $team = $this->getTeamFromRequest();
        return $team->isPremium() && $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_TASK_CREATE, $team);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, TeamEventTask $teamEventTask): bool
    {
        $team = $teamEventTask->teamEvent->team;
        return $this->canAssignMemberIfSet($user) ||
            ($team->isPremium() && $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_TASK_CREATE, $team));
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, TeamEventTask $teamEventTask): bool
    {
        $team = $teamEventTask->teamEvent->team;
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_TASK_DELETE, $team);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, TeamEventTask $teamEventTask): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, TeamEventTask $teamEventTask): bool
    {
        return false;
    }
}
