<?php

namespace App\Policies;

use App\Models\TeamEventSeries;
use App\Models\User;
use App\Types\TeamPermissionType;

class TeamEventSeriesPolicy {
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, TeamEventSeries $teamEventSeries): bool {
        return true;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool {
        return true;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, TeamEventSeries $teamEventSeries): bool {
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_CREATE, $teamEventSeries->eventData->team);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, TeamEventSeries $teamEventSeries): bool {
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_DELETE, $teamEventSeries->eventData->team);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, TeamEventSeries $teamEventSeries): bool {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, TeamEventSeries $teamEventSeries): bool {
        return false;
    }
}
