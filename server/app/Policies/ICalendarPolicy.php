<?php

namespace App\Policies;

use App\Models\ICalendar;
use App\Models\User;
use App\Types\FeatureType;
use Illuminate\Auth\Access\HandlesAuthorization;
use Laravel\Pennant\Feature;

class ICalendarPolicy {
    use HandlesAuthorization;

    public function viewAny(User $user): bool {
        return true; // is regulated in server/app/JsonApi/V1/Server.php#serving with global scope
    }

    public function view(User $user, ICalendar $iCalendar): bool {
        return true; // is regulated in server/app/JsonApi/V1/Server.php#serving with global scope
    }

    public function create(User $user): bool {
        return true;
    }

    public function update(User $user, ICalendar $iCalendar): bool {
        return true; // is regulated in server/app/JsonApi/V1/Server.php#serving with global scope
    }

    public function delete(User $user, ICalendar $iCalendar): bool {
        return true; // is regulated in server/app/JsonApi/V1/Server.php#serving with global scope
    }

    public function restore(User $user, ICalendar $iCalendar): bool {
        return false;
    }

    public function forceDelete(User $user, ICalendar $iCalendar): bool {
        return false;
    }
}
