<?php

namespace App\Policies;

use App\Models\Absence;
use App\Models\DeviceInfo;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class DeviceInfoPolicy {
    use HandlesAuthorization;

    public function viewAny(User $user): bool {
        return true;
    }

    public function view(User $user, DeviceInfo $deviceInfo): bool {
        return true;
    }

    public function create(User $user): bool {
        return false;
    }

    public function update(User $user, DeviceInfo $deviceInfo): bool {
        return true;
    }

    public function delete(User $user, DeviceInfo $deviceInfo): bool {
        return false;
    }

    public function restore(User $user, DeviceInfo $deviceInfo): bool {
        return false;
    }

    public function forceDelete(User $user, DeviceInfo $deviceInfo): bool {
        return false;
    }
}
