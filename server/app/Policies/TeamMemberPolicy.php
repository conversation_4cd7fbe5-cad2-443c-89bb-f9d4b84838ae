<?php

namespace App\Policies;

use App\Models\Team;
use App\Models\TeamMember;
use App\Models\User;
use App\Types\TeamPermissionType;

class TeamMemberPolicy {

    private function getTeamFromRequest(): Team {
        $id = request()->input('data.relationships.team.data.id');
        return Team::findOrFail($id);
    }

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, TeamMember $teamMember): bool {
        return true;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool {
        $team = $this->getTeamFromRequest();
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_MEMBER_CREATE, $team);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, TeamMember $teamMember): bool {
        return  $user->person_id === $teamMember->person_id
            || $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_MEMBER_UPDATE, $teamMember->team);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, TeamMember $teamMember): bool {
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_MEMBER_DELETE, $teamMember->team);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, TeamMember $teamMember): bool {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, TeamMember $teamMember): bool {
        return false;
    }
}
