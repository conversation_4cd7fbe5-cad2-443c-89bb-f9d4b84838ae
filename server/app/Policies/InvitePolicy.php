<?php

namespace App\Policies;

use App\JsonApi\V1\TeamMembers\TeamMemberSchema;
use App\<PERSON>sonApi\V1\Teams\TeamSchema;
use App\Models\Invite;
use App\Models\Team;
use App\Models\TeamMember;
use App\Models\User;
use App\Types\FeatureType;
use App\Types\TeamPermissionType;

class InvitePolicy {

    private function getInvitableClassFromRequest(): string|null {
        $type = request()->input('data.relationships.invitable.data.type');
        if($type === TeamMemberSchema::type()) {
            return TeamMember::class;
        }
        if($type === TeamSchema::type()) {
            return Team::class;
        }
        return null;
    }

    private function getTargetTeamFromRequest(): Team|null {
        $id = request()->input('data.relationships.invitable.data.id');
        $invitableClass = $this->getInvitableClassFromRequest();
        if($invitableClass === TeamMember::class) {
            return TeamMember::findOrFail($id)->team;
        }
         if ($invitableClass === Team::class) {
             return Team::findOrFail($id);
         }
         return null;
    }

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Invite $invite): bool {
        return $this->canCreateUpdateOrDelete($invite->invitable::class, $user, $invite->getRelatedTeam());
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool {
        return $this->canCreateUpdateOrDelete($this->getInvitableClassFromRequest(), $user, $this->getTargetTeamFromRequest());
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Invite $invite): bool {
        return $this->canCreateUpdateOrDelete($invite->invitable::class, $user, $invite->getRelatedTeam());
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Invite $invite): bool {
        return $this->canCreateUpdateOrDelete($invite->invitable::class, $user, $invite->getRelatedTeam());
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Invite $invite): bool {
        return true;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Invite $invite): bool {
        return true;
    }

    /**
     * @param string $invitableClass
     * @param User   $user
     * @param Team   $team
     *
     * @return bool|null
     */
    private function canCreateUpdateOrDelete(string $invitableClass, User $user, Team $team) {
        if ($invitableClass === TeamMember::class) {
            return $user->person->hasPermissionForTeam(
                TeamPermissionType::TEAM_MEMBER_INVITE_CREATE_OTHER, $team);
        }

        if ($invitableClass === Team::class) {
            return $user->person->hasPermissionForTeam(
                TeamPermissionType::TEAM_INVITE_CREATE, $team);
        }
    }
}
