<?php

namespace App\Policies;

use App\Models\Team;
use App\Models\User;
use App\Types\FeatureType;
use App\Types\TeamPermissionType;
use <PERSON><PERSON>\Pennant\Feature;

class TeamPolicy {
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool {
        return true; // is regulated in server/app/JsonApi/V1/Server.php#serving with global scope
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Team $team): bool {
        return true; // is regulated in server/app/JsonApi/V1/Server.php#serving with global scope
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool {
        return Feature::store('array')->active(FeatureType::TEAM_CREATE->value);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Team $team): bool {
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_UPDATE, $team);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Team $team): bool {
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_UPDATE, $team)
            || Feature::active(FeatureType::ADMIN_STATS->value);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Team $team): bool {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Team $team): bool {
        return false;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function startSubscription(User $user, Team $team): bool {
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_SUBSCRIPTION_MANAGE, $team);
    }
    
    /**
     * Determine whether the user can update the model.
     */
    public function cancelSubscription(User $user, Team $team): bool {
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_SUBSCRIPTION_MANAGE, $team);
    }


    /**
     * Determine whether the user can update the model.
     */
    public function viewInvoices(User $user, Team $team): bool {
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_SUBSCRIPTION_MANAGE, $team);
    }
}
