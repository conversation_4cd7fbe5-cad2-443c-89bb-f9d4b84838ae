<?php

namespace App\Policies;

use App\Models\Team;
use App\Models\TeamLedger;
use App\Models\TeamLedgerFine;
use App\Models\User;
use App\Types\TeamPermissionType;

class TeamLedgerFinePolicy
{
    private function getTeamFromRequest(): Team {
        $id = request()->input('data.relationships.ledger.data.id');
        return TeamLedger::findOrFail($id)->team;
    }
    
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // is secured by global scope where each query contains only teams from the current user
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, TeamLedgerFine $teamLedgerFine): bool
    {
        $team = $teamLedgerFine->ledger->team;
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_LEDGER_VIEW, $team);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        $team = $this->getTeamFromRequest();
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_LEDGER_MANAGE, $team);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, TeamLedgerFine $teamLedgerFine): bool
    {
        $team = $teamLedgerFine->ledger->team;
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_LEDGER_MANAGE, $team);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, TeamLedgerFine $teamLedgerFine): bool
    {
        $team = $teamLedgerFine->ledger->team;
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_LEDGER_MANAGE, $team);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, TeamLedgerFine $teamLedgerFine): bool
    {
        $team = $teamLedgerFine->ledger->team;
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_LEDGER_MANAGE, $team);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, TeamLedgerFine $teamLedgerFine): bool
    {
        $team = $teamLedgerFine->ledger->team;
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_LEDGER_MANAGE, $team);
    }
}
