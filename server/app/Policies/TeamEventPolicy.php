<?php

namespace App\Policies;

use App\Models\Team;
use App\Models\TeamEvent;
use App\Models\User;
use App\Types\TeamPermissionType;

class TeamEventPolicy {

    private function getTeamFromRequest(): Team {
        $id = request()->input('data.relationships.team.data.id');
        return Team::findOrFail($id);
    }

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool {
        // is secured by global scope where each query contains only teams from the current user
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, TeamEvent $teamEvent): bool {
        // is secured by global scope where each query contains only teams from the current user
        return true;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool {
        $team = $this->getTeamFromRequest();
        return $team->isPremium() && $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_CREATE, $team);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, TeamEvent $teamEvent): bool {
        return $teamEvent->team->isPremium() && $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_CREATE, $teamEvent->team);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, TeamEvent $teamEvent): bool {
        return $teamEvent->team->isPremium() && $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_DELETE, $teamEvent->team);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, TeamEvent $teamEvent): bool {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, TeamEvent $teamEvent): bool {
        return false;
    }
}
