<?php

namespace App\Policies;

use App\Models\Comment;
use App\Models\TeamEventVote;
use App\Models\User;
use App\Types\TeamPermissionType;

class CommentPolicy {

    private function isAuthor(User $user, Comment $comment): bool {
        return $comment->author->id === $user->person->id;
    }

    private function hasPermissionForTeam(User $user, TeamEventVote $parent, TeamPermissionType $permission = TeamPermissionType::TEAM_EVENT_VOTE_OTHER): bool {
        $team = $parent->teamMember->team;
        return $user->person->hasPermissionForTeam($permission, $team);
    }

    private function defaultPolicy(User $user, Comment $comment): bool {
        if ($this->isAuthor($user, $comment)) {
            return true;
        }

        if ($comment->parent instanceof TeamEventVote) {
            return $this->hasPermissionForTeam($user, $comment->parent);
        }

        return false;
    }

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool {
        return false;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Comment $comment): bool {
        return $this->defaultPolicy($user, $comment);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool {
        return true;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Comment $comment): bool {
        return $this->defaultPolicy($user, $comment);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Comment $comment): bool {
        return $this->defaultPolicy($user, $comment);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Comment $comment): bool {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Comment $comment): bool {
        return false;
    }
}
