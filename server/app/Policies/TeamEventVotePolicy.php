<?php

namespace App\Policies;

use App\Models\TeamEventVote;
use App\Models\TeamMember;
use App\Models\User;
use App\Types\TeamPermissionType;

class TeamEventVotePolicy {

    private function getTeamMemberFromRequest(): TeamMember {
        $id = request()->input('data.relationships.teamMember.data.id');
        return TeamMember::findOrFail($id);
    }

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, TeamEventVote $teamEventVote): bool {
        $votedTeam = $teamEventVote->teamMember->team;
        $userTeamIds = $user->person->teamMembers->pluck('team.id')->toArray();

        $isTeamMember = in_array($votedTeam->id, $userTeamIds);

        return $isTeamMember;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool {
        $teamMember = $this->getTeamMemberFromRequest();
        $team = $teamMember->team;
        $userPerson = $user->person;

        $canVoteOther = $userPerson->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_VOTE_OTHER, $team);
        $isSamePerson = $teamMember->person !== null && $teamMember->person->id === $userPerson->id;
        $canVoteSelf = $userPerson->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_VOTE_SELF, $team);

        return $canVoteOther || ($isSamePerson && $canVoteSelf);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, TeamEventVote $teamEventVote): bool {
        $team = $teamEventVote->teamMember->team;
        $userPerson = $user->person;

        $canVoteOther = $userPerson->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_VOTE_OTHER, $team);
        $isSamePerson = $teamEventVote->teamMember->person !== null && $teamEventVote->teamMember->person->id === $userPerson->id;
        $canVoteSelf = $userPerson->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_VOTE_SELF, $team);

        return $canVoteOther || ($isSamePerson && $canVoteSelf);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, TeamEventVote $teamEventVote): bool {
        $team = $teamEventVote->teamMember->team;
        $userPerson = $user->person;

        $isSamePerson = $teamEventVote->teamMember->person !== null && $teamEventVote->teamMember->person->id === $userPerson->id;
        $canVoteOther = $userPerson->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_VOTE_OTHER, $team);
        $canVoteSelf = $userPerson->hasPermissionForTeam(TeamPermissionType::TEAM_EVENT_VOTE_SELF, $team);

        return $canVoteOther || ($isSamePerson && $canVoteSelf);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, TeamEventVote $teamEventVote): bool {
        // We don't use soft delete, so there is nothing to restore
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, TeamEventVote $teamEventVote): bool {
        // We don't use soft delete, so there is nothing to delete
        return false;
    }
}
