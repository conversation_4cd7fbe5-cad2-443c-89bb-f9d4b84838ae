<?php

namespace App\Policies;

use App\Models\Team;
use App\Models\TeamStatsRange;
use App\Models\User;
use App\Types\TeamPermissionType;

class TeamStatsRangePolicy {
    private function getTeamFromRequest(): Team {
        $id = request()->input('data.relationships.team.data.id');
        return Team::findOrFail($id);
    }

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool {
        // is secured by global scope where each query contains only teams from the current user
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, TeamStatsRange $teamStatsRange): bool {
        // is secured by global scope where each query contains only teams from the current user
        return true;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool {
        $team = $this->getTeamFromRequest();
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_STATS_RANGE_CREATE, $team);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, TeamStatsRange $teamStatsRange): bool {
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_STATS_RANGE_CREATE, $teamStatsRange->team);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, TeamStatsRange $teamStatsRange): bool {
        return $user->person->hasPermissionForTeam(TeamPermissionType::TEAM_STATS_RANGE_DELETE, $teamStatsRange->team);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, TeamStatsRange $teamStatsRange): bool {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, TeamStatsRange $teamStatsRange): bool {
        return false;
    }
}
