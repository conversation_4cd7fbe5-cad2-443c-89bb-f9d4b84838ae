<?php

namespace App\Models\Scopes;

use App\Http\Controllers\AuthController;
use App\Models\Absence;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class LimitToCurrentUserAbsencesScope implements Scope {
    /**
     * @param Builder<Absence>|Absence $builder
     */
    public function apply(Builder|Absence $builder, Model $model): void {
        $user = AuthController::getCurrentUser();
        $builder->whereAuthorId($user?->person_id);
    }
}
