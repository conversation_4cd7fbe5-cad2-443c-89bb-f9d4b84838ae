<?php

namespace App\Models\Scopes;

use App\Http\Controllers\AuthController;
use App\Models\ICalendar;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class LimitToCurrentUserICalendarsScope implements Scope {
    /**
     * @param Builder<ICalendar>|ICalendar $builder
     */
    public function apply(Builder|ICalendar $builder, Model $model): void {
        $user = AuthController::getCurrentUser();
        $builder->whereAuthorId($user?->person_id);
    }
}
