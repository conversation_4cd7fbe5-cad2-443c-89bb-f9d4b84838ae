<?php

namespace App\Models\Scopes;

use App\Models\TeamEventTask;
use App\Types\FeatureType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Laravel\Pennant\Feature;

class LimitToCurrentUserTeamEventTaskConfigScope implements Scope {
    /**
     * @param Builder<TeamEventTask>|TeamEventTask $builder
     */
    public function apply(Builder|TeamEventTask $builder, Model $model): void {
        if (!Feature::active(FeatureType::ADMIN_STATS->value)) {
            $builder->whereHas('config');
        }
    }
}
