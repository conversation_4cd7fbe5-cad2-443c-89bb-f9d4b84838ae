<?php

namespace App\Models\Scopes;

use App\Models\TeamEvent;
use App\Models\TeamEventTaskConfig;
use App\Types\FeatureType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Laravel\Pennant\Feature;

class LimitToCurrentUserTeamRelationScope implements Scope {
    /**
     * @param Builder<TeamEvent>|Builder<TeamEventTaskConfig>|TeamEvent|TeamEventTaskConfig $builder
     */
    public function apply(Builder|TeamEvent|TeamEventTaskConfig $builder, Model $model): void {
        if (!Feature::active(FeatureType::ADMIN_STATS->value)) {
            $builder->whereHas('team');
        }
    }
}
