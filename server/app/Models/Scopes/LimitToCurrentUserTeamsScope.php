<?php

namespace App\Models\Scopes;

use App\Http\Controllers\AuthController;
use App\Models\Team;
use App\Models\TeamMember;
use App\Types\FeatureType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Laravel\Pennant\Feature;

class LimitToCurrentUserTeamsScope implements Scope {
    /**
     * @param Builder<Team>|Team $builder
     */
    public function apply(Builder|Team $builder, Model $model): void {
        if (!Feature::active(FeatureType::ADMIN_STATS->value)) {
            $user = AuthController::getCurrentUser();
            $builder->whereHas('members', function (Builder|TeamMember $query) use ($user) {
                $query->wherePersonId($user->person_id);
            });
        }
    }
}
