<?php

namespace App\Models;

use App\Types\FeatureType;
use App\Types\TeamPermissionType;
use Database\Factories\PersonFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Laravel\Pennant\Concerns\HasFeatures;
use Laravel\Pennant\Feature;

class Person extends Model {
    use HasUuids, HasFeatures;
    
    /** @use HasFactory<PersonFactory> */
    use HasFactory;

    protected $table = 'persons';

    protected $fillable = [
        'firstname',
        'lastname',
    ];

    protected static function booted(): void {
        static::deleting(static function (Person $person) {
            $person->clearPermissionCache();
        });
    }

    public function clearPermissionCache(): void {
        Cache::forget($this->getPermissionCacheKey());
    }

    private function getPermissionCacheKey(): string {
        return $this->id . '_' . 'permissionNamesPerTeamId';
    }

    /**
     * @return HasMany<TeamMember, $this>
     */
    public function teamMembers(): HasMany {
        return $this->hasMany(TeamMember::class);
    }

    /**
     * @return Builder<Team>|Team
     */
    public function teams(): Builder|Team {
        return Team::whereRelation('members', 'person_id', '=', $this->id);
    }

    /**
     * @return HasOne<User, $this>
     */
    public function user(): HasOne {
        return $this->hasOne(User::class);
    }

    public function getFullName(): string {
        return $this->firstname . ' ' . $this->lastname;
    }

    public function hasPermissionForTeam(TeamPermissionType $permissionType, Team $team): bool {
        $permissionNamesPerTeam = $this->permissionNamesPerTeamId();
        return isset($permissionNamesPerTeam[$team->id])
            && in_array($permissionType->value, $permissionNamesPerTeam[$team->id]);
    }

    /**
     * permissionNamesPerTeamId returns an array of permission names per team id.
     *
     * @return array<string, array<string>>
     *     teamid => ['createAB', 'doXY']
     */
    public function permissionNamesPerTeamId(): array {
        /** @var array<string, array<string>> $permissionsPerTeam */
        $permissionsPerTeam = Cache::rememberForever($this->getPermissionCacheKey(), function (): array {
            $permissionsPerTeam = [];
            foreach ($this->teamMembers as $teamMember) {
                if (!isset($permissionsPerTeam[$teamMember->team_id])) {
                    $permissionsPerTeam[$teamMember->team_id] = [];
                }
                foreach ($teamMember->permissions() as $permission) {
                    if (!in_array($permission->name->value, $permissionsPerTeam[$teamMember->team_id])) {
                        $permissionsPerTeam[$teamMember->team_id][] = $permission->name->value;
                    }
                }
            }

            return $permissionsPerTeam;
        });
        return $permissionsPerTeam;
    }

    /**
     * @return Collection<int, string>
     */
    public function activeFeatures(): Collection {
        return collect([...Feature::all(), ...Feature::store('array')->all()])
            ->filter(fn(bool $active, string $feature) => $active)
            ->keys();
    }

    /**
     * @return MorphToMany<Absence, $this>
     */
    public function absences(): MorphToMany {
        return $this->morphToMany(Absence::class, 'absence_target');
    }
    
    /**
     * @return MorphToMany<ICalendar, $this>
     */
    public function iCalendarSubscriptions(): MorphToMany {
        return $this->morphToMany(ICalendar::class, 'i_calendar_target');
    }
}
