<?php

namespace App\Models;

use App\JsonApi\V1\TeamLedgers\TeamLedgerSchema;
use Cknow\Money\Money;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Support\Collection;

/**
 * @property Team $team // cannot be null, added for phpstan to not expect null values
 */
class TeamLedger extends Model {
    use HasUuids;

    /**
     * @return BelongsTo<Team, $this>
     */
    public function team(): BelongsTo {
        return $this->belongsTo(Team::class);
    }

    /**
     * @return HasMany<TeamLedgerTransaction, $this>
     */
    public function transactions(): HasMany {
        return $this->hasMany(TeamLedgerTransaction::class);
    }

    /**
     * @return HasMany<TeamLedgerFine, $this>
     */
    public function fines(): HasMany {
        return $this->hasMany(TeamLedgerFine::class);
    }

    /**
     * @return HasMany<TeamLedgerDues, $this>
     */
    public function dues(): HasMany {
        return $this->hasMany(TeamLedgerDues::class);
    }

    /**
     * @return HasMany<TeamLedgerClaim, $this>
     */
    public function claims(): HasMany {
        return $this->hasMany(TeamLedgerClaim::class);
    }

    /**
     * @param TeamMember|null $member
     *
     * @return HasMany<TeamLedgerClaim, $this>
     */
    public function getClaims(TeamMember $member = null): HasMany  {
        return $this->claims()
            ->withMemberOrAll($member);
    }
    
    /**
     * @param TeamMember|null $member
     *
     * @return HasMany<TeamLedgerClaim, $this>
     */
    public function getMoneyClaims(TeamMember $member = null): HasMany {
        return $this->getClaims($member)
            ->withMoney();
    }

    /**
     * @param TeamMember|null $member
     *
     * @return HasMany<TeamLedgerClaim, $this>
     */
    private function getItemClaims(TeamMember $member = null): HasMany {
        return $this->getClaims($member)
            ->withItem();
    }

    /**
     * Amount that is in the ledger, INCLUDING credit
     *
     * @param TeamMember|null $member
     *
     * @return Money
     */
    public function getBalance(TeamMember $member = null): Money {
        $transactionSum = $this->transactions()
            ->withMemberOrAll($member)
            ->sum('amount');
        return money($transactionSum);
    }

    /**
     * Amount that is in the ledger, INCLUDING credit
     *
     * @return Money
     */
    public function getBalanceFromNonMembers(): Money {
        $transactionSum = $this->transactions()
            ->withoutMember()
            ->sum('amount');
        return money($transactionSum);
    }

    public function getSumInFromNonMembers(): Money {
        $transactionSum = $this->transactions()
            ->withoutMember()
            ->where('amount', '>', 0)
            ->sum('amount');
        return money($transactionSum);
    }

    public function getSumOutFromNonMembers(): Money {
        $transactionSum = $this->transactions()
            ->withoutMember()
            ->where('amount', '<', 0)
            ->sum('amount');
        return money($transactionSum);
    }

    /**
     * Amount that is in the ledger, WITHOUT credit
     *
     * @param TeamMember|null $member
     *
     * @return Money
     */
    public function getAvailableBalance(TeamMember $member = null): Money {
        return $this->getBalance($member)
            ->subtract($this->getCreditBalance($member))
        ;
    }
    /**
     * Amount that is in the ledger
     * WITHOUT credit
     * WITHOUT incoming and outgoing transactions from non-members
     *
     * @return Money
     */
    public function getAvailableBalanceFromMembers(): Money {
        return $this->getAvailableBalance()
            ->subtract($this->getBalanceFromNonMembers());
    }

    /**
     * Amount that is in the ledger, but reserved as credit for a member and may be withdrawn
     *
     * @param TeamMember|null $member
     *
     * @return Money
     */
    public function getCreditBalance(TeamMember $member = null): Money {
        $balance = $this->getBalance($member)
            ->subtract($this->getFulfilledClaimBalance($member));

        if(!isset($member)) {
            $balance = $balance->subtract($this->getBalanceFromNonMembers());
        }
        return $balance;
    }

    /**
     * Amount to reduce claim balance with credit that is available for unfulfilled money claims
     *
     * @return Money
     */
    private function getCreditBalanceFromMembersWithUnfulfilledMoneyClaims(): Money {
        $balance = money(0);
        /** @var Collection<string, TeamMember> $membersWithUnfulfilledMoneyClaims */
        $membersWithUnfulfilledMoneyClaims = $this->members()->withTrashed()
            ->whereHas('teamLedgerClaims', function (Builder $builder): Builder {
                /** @var Builder<TeamLedgerClaim> $builder */
                return $builder
                    ->withMoney()
                    ->notExempt()
                    ->notFulfilled()
                    ->notFuture();
            })->get();
        
        
        foreach ($membersWithUnfulfilledMoneyClaims as $member) {
            $balance = $balance->add($this->getCreditBalance($member));
        }
        
        return $balance;
    }

    public function getTotalClaimBalance(TeamMember $member = null): Money {
        return $this->getClaimBalance($member)
            ->add($this->getFutureClaimBalance($member));
    }

    public function getClaimBalance(TeamMember $member = null): Money {
        $claimSum = $this->getMoneyClaims($member)
            ->notExempt()
            ->notFulfilled()
            ->notFuture()
            ->sum('amount');
        $claimBalance = money($claimSum);
        
        if($claimBalance->isPositive()){
            if(isset($member)){
                $reducedClaimBalance = $claimBalance->subtract($this->getCreditBalance($member));
            } else {
                $reducedClaimBalance = $claimBalance->subtract($this->getCreditBalanceFromMembersWithUnfulfilledMoneyClaims()); 
            }
            // do not reduce below zero
            $claimBalance = Money::max(money(0), $reducedClaimBalance);
        }
        return $claimBalance;
    }

    public function getFutureClaimBalance(TeamMember $member = null): Money {
        if(isset($member)){
            $claimSum = $this->getMoneyClaims($member)
                ->notExempt()
                ->notFulfilled()
                ->future()
                ->sum('amount');
            $futureClaimBalance = money($claimSum);
            
            // if claim balance is already reduced by credit balance, so if claim balance is zero, there could be credit balance left for future claims
            if($this->getClaimBalance($member)->isZero()) {
                $reducedFutureClaimBalance = $futureClaimBalance->subtract($this->getCreditBalance($member));
                // do not reduce below zero
                $futureClaimBalance = Money::max(money(0), $reducedFutureClaimBalance);
            }
        } else {
            // the total sum cannot be calculated from claims and credit balance since it is reduced by credit sum from members that may also be used to reduce claim balance 
            $futureClaimBalance = money(0);
            /** @var Collection<string, TeamMember> $membersWithUnfulfilledFutureMoneyClaims */
            $membersWithUnfulfilledFutureMoneyClaims = $this->members()->withTrashed()
                ->whereHas('teamLedgerClaims', function (Builder $builder): Builder {
                    /** @var Builder<TeamLedgerClaim> $builder */
                    return $builder
                        ->withMoney()
                        ->notExempt()
                        ->notFulfilled()
                        ->future();
                })->get();
            
            foreach ($membersWithUnfulfilledFutureMoneyClaims as $memberWithClaims) {
                $futureClaimBalance = $futureClaimBalance->add($this->getFutureClaimBalance($memberWithClaims));
            }
        }
        return $futureClaimBalance;
    }

    public function getFulfilledClaimBalance(TeamMember $member = null): Money {
        $claimSum = $this->getMoneyClaims($member)
            ->notExempt()
            ->fulfilled()
            ->sum('amount');
        return money($claimSum);
    }

    /**
     * used in @see TeamLedgerSchema as relation
     * @return HasMany<TeamLedgerClaim, $this>
     */
    public function dueItemClaims(): HasMany {
        return $this->getDueItemClaims();
    }
    
    /**
     * used in @see TeamLedgerSchema as relation
     * @return HasMany<TeamLedgerClaim, $this>
     */
    public function fineClaims(): HasMany {
        return $this->claims()
            ->whereClaimableType(TeamLedgerFine::class);
    }

    /**
     * @param TeamMember|null $member
     *
     * @return HasMany<TeamLedgerClaim, $this>
     */
    public function getDueItemClaims(TeamMember $member = null): HasMany {
        return $this->getItemClaims($member)
            ->notExempt()
            ->notFulfilled()
            ->notFuture();
    }

    /**
     * @param TeamMember|null $member
     *
     * @return HasMany<TeamLedgerClaim, $this>
     */
    public function getFutureItemClaims(TeamMember $member = null): HasMany {
        return $this->getItemClaims($member)
            ->future();
    }
    
    public function fulfillClaimsFromCredit(TeamMember $member): void {
        $creditLeft = $this->getCreditBalance($member);
        if($creditLeft->lessThanOrEqual(money(0))) {
            return;
        }
            
        // get  claims for member by amount, biggest first
        $claims = $this
            ->getMoneyClaims($member)
            ->notExempt()
            ->notFulfilled()
            ->orderBy('due_date')
            ->orderByDesc('amount')
            ->get();

        foreach ($claims as $claim) {
            if($creditLeft->greaterThanOrEqual($claim->amount)) {
                $claim->setFulfilled();
                $creditLeft = $creditLeft->subtract($claim->amount);
            }
        }
    }

    /**
     * @return HasManyThrough<TeamMember, $this>
     */
    public function members(): HasManyThrough {
        return $this->hasManyThrough(
            TeamMember::class,
            Team::class,
            'id',       // Foreign key on TeamLedger (team_id)
            'team_id',  // Foreign key on TeamMember
            'team_id',  // Local key on TeamLedger
            'id'        // Local key on Team
        );
    }
    
    /**
     * @return HasManyThrough<TeamMember, $this>
     */
    public function formerMembers(): HasManyThrough {
        return $this->members()
            ->onlyTrashed() // Get soft-deleted (former) members
            ->withTransactionsOrClaims(); // get only relevant members
    }
    
    /**
     * @return Collection<string, Collection<string, array{
     *     totalClaimBalance: Money,
     *     claimBalance: Money,
     *     futureClaimBalance: Money,
     *     availableBalance: Money,
     *     creditBalance: Money,
     *     dueItemClaimIds: Collection<int, string>
     * }>>
 */
    public function getMemberStatus(): Collection {
        return Collection::make([
            'active' => $this->members->mapWithKeys(
                fn($member) => [$member->id => $this->getStatusForMember($member)]
            ),
            'former' => $this->formerMembers->mapWithKeys(
                fn($member) => [$member->id => $this->getStatusForMember($member)]
            ),
        ]);
    }

    /**
     * @param TeamMember $member
     *
     * @return array{
     *          totalClaimBalance: Money,
     *          claimBalance: Money,
     *          futureClaimBalance: Money,
     *          availableBalance: Money,
     *          creditBalance: Money,
     *          dueItemClaimIds: Collection<int, string>
     *      }
     */
    private function getStatusForMember(TeamMember $member): array {
        return [
            'totalClaimBalance' => $this->getTotalClaimBalance($member),
            'claimBalance' => $this->getClaimBalance($member),
            'futureClaimBalance' => $this->getFutureClaimBalance($member),
            'availableBalance' => $this->getAvailableBalance($member),
            'creditBalance' => $this->getCreditBalance($member),
            'dueItemClaimIds' => $this->getDueItemClaims($member)->get(['id'])
                ->map(fn($item) => $item->id),
        ];
    }

}
