<?php

namespace App\Models;

use App\Models\Interfaces\Recurring;
use App\Traits\HasNonInfiniteRRule;
use App\Types\EventSeriesType;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * @property TeamEvent $eventData // cannot be null, added for phpstan to not expect null values
 */
class TeamEventSeries extends Model implements Recurring {
    use HasUuids, HasNonInfiniteRRule;

    protected $fillable = [
        'rrule_string',
    ];

    protected static function booted() {
        static::updated(static function (TeamEventSeries $teamEventSeries) {
            $teamEventSeries->deleteEventsNotInRange();
        });
    }

    private function deleteEventsNotInRange(): void {
        $rrule = $this->getRRule();
        foreach ($this->events as $event) {
            if (!$rrule->occursAt($event->date_begin->toDateTime())) {
                $event->delete();
            }
        }
    }

    /**
     * @return HasOne<TeamEvent, $this>
     */
    public function eventData(): HasOne {
        return $this->hasOne(TeamEvent::class, 'series_id')
            ->where(
                'series_type',
                '=',
                EventSeriesType::RECURRING_PARENT
            );
    }

    /**
     * @return HasMany<TeamEvent, $this>
     */
    public function events(): HasMany {
        return $this->hasMany(TeamEvent::class, 'series_id')
            ->where(
                'series_type',
                '=',
                EventSeriesType::RECURRING_CHILD
            );
    }

    public function createRecurringEvents(): void {
        $this->eventData->loadMissing('team');
        foreach ($this->getRRule()->getOccurrences() as $occurrence) {
            $child = $this->eventData->replicate();
            $child->series_type = EventSeriesType::RECURRING_CHILD;
            $child->date_begin = $occurrence;

            // Manually copy loaded relations to prevent lazy loading
            foreach ($this->eventData->getRelations() as $relationName => $relationValue) {
                $child->setRelation($relationName, $relationValue);
            }
            $child->save();
        }
    }
}
