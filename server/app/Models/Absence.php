<?php

namespace App\Models;

use App\Models\Interfaces\Recurring;
use App\Traits\HasAuthorPerson;
use App\Traits\HasRRule;
use App\Traits\HasTargetMembersAndPersons;
use App\Types\AbsenceType;
use App\Types\TeamEventVoteType;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Absence extends Model implements Recurring {
    use HasUuids, HasRRule;
    use HasAuthorPerson;
    use HasTargetMembersAndPersons;

    protected $fillable = [
        'absence_type',
        'name',
        'date_begin',
        'date_end',
        'rrule_string',
        'author_id',
    ];

    protected $casts = [
        'absence_type' => AbsenceType::class,
        'date_begin'   => 'date',
        'date_end'     => 'date',
    ];

    protected static function booted() {

        static::deleted(function (Absence $absence) {
            /** @var TeamEventVote $vote */
            foreach ($absence->votes()->future()->get() as $vote) {
                $teamEvent = $vote->teamEvent;
                $vote->delete();
                $teamEvent->setVoteNoForAbsencesInRange();
                $teamEvent->updateVotesAccordingToResponseType();
            }
        });
    }

    /**
     * @return HasMany<TeamEventVote, $this>
     */
    public function votes(): HasMany {
        return $this->hasMany(TeamEventVote::class);
    }

    /**
     * Update votes for all events in the date range of this absence.
     *
     * This will:
     * - Delete all votes for events that are no longer in the date range of the absence
     * - Add a NO vote for all events in the date range of the absence (TODO: Does it though? What does the reset do?)
     *
     * @param bool $touchOnlyFutureVotes Note: this is set if it's an update of an existing absence
     */
    public function updateVotesForEventDateRange(bool $touchOnlyFutureVotes): void {
        // This removes all votes that are out of the date range and resets votes on
        // the events to their default state, taking other possible absences into account.
        $this->removeVotesForEventsOutOfRange($touchOnlyFutureVotes);

        // This one deletes all votes and recreates them based on the events
        // of the team members this absence references.
        $this->resetAndApplyNoVotesForMembersEvents($touchOnlyFutureVotes);
    }

    private function removeVotesForEventsOutOfRange(bool $touchOnlyFutureVotes): void {
        $votesQuery = $this->votes();
        if ($touchOnlyFutureVotes) {
            $votesQuery->future();
        }

        foreach ($votesQuery->get() as $vote) {
            $teamEvent = $vote->teamEvent;
            if (!$this->isEventWithinAbsenceRange($teamEvent)) {
                // This vote has been cast on behalf of this absence but is no longer valid, thus
                // - We delete the vote
                // - Have the event reset the votes according to the response type selected by the user
                // - Have the event check if another absence overrides the vote
                //
                // -- Explanation (delete / update / vote chain) --
                // What could happen is that there is another absence set for events every Friday, which got
                // superseded by a new absence due to an illness. If the latter one is removed the previous
                // one for each Friday should still remain valid.

                // Delete the current vote
                $vote->delete();

                // TODO(fabzo): We need to check if this deleted vote gets recreated by any of the following functions

                // Recreate all missing votes on the event (basically recreate what we just deleted with the default)
                $teamEvent->updateVotesAccordingToResponseType();

                // Check if another absence overrides the vote and set it accordingly
                $teamEvent->setVoteNoForAbsencesInRange();
            }
        }
    }
    
    /**
     * Resets and applies NO votes for all events affected by this absence.
     *
     * This function performs the following steps:
     * - Deletes all existing votes (or future votes if specified) related to this absence.
     * - Retrieves a list of all team members affected by this absence.
     * - For each affected team member, retrieves the events within the absence date range.
     * - Sets a NO vote for each event within the absence date range for each affected team member.
     *
     * @param bool $touchOnlyFutureVotes If true, only future votes are affected.
     *
     * @return void
     */
    private function resetAndApplyNoVotesForMembersEvents(bool $touchOnlyFutureVotes): void {
        $members = $this->getUniqueTargetTeamMembers();

        // delete all existing votes and add again since target members may have changed
        $votesQuery = $this->votes();
        if ($touchOnlyFutureVotes) {
            $votesQuery->future();
        }
        $votesQuery->delete();

        $this->createNoVotesVorMembers($members, $touchOnlyFutureVotes);
    }
    

    /**
     * @return Collection<int, TeamEvent>
     */
    private function getAffectedEventsForMember(TeamMember $teamMember, bool $onlyFutureEvents = false): Collection {
        // get all events in range
        $queryStartDate = $this->date_begin;
        if ($onlyFutureEvents && Carbon::createMidnightDate() > $this->date_begin) {
            $queryStartDate = Carbon::createMidnightDate();
        }
        $query = $teamMember->team->events()->whereDate('date_begin', '>=', $queryStartDate);
        if ($this->date_end !== null) {
            $query->whereDate('date_begin', '<=', $this->date_end);
        }
        $events = $query->get();

        // filter events based on recurring rule
        if ($this->isRecurring()) {
            $events = $events->filter(function (TeamEvent $event) {
                return $this->isEventWithinAbsenceRange($event);
            });
        }

        return $events;
    }

    public function isEventWithinAbsenceRange(TeamEvent $teamEvent): bool {
        if ($this->isRecurring()) {
            // cannot use Carbon date with occursAt since there is a bug in Carbon: https://github.com/eltini/clubmanager/issues/997
            return $this->getRRule()->occursAt($teamEvent->date_begin->toDateTime());
        }

        return $this->date_begin <= $teamEvent->date_begin
            && (
                $this->date_end === null || $this->date_end >= $teamEvent->date_begin
            );
    }

    /**
     * @param Collection<int, TeamMember> $members
     * @param bool $touchOnlyFutureVotes
     *
     * @return void
     */
    public function createNoVotesVorMembers(Collection $members, bool $touchOnlyFutureVotes): void {
        foreach ($members as $member) {
            $teamEvents = $this->getAffectedEventsForMember($member, $touchOnlyFutureVotes);
            foreach ($teamEvents as $teamEvent) {
                $this->createNoVote($teamEvent, $member);
            }
        }
    }
    
    /**
     * @param TeamEvent $teamEvent
     * @param Collection<int, TeamMember> $members
     *
     * @return void
     */
    public function createNoVotesForEventAndMembers(TeamEvent $teamEvent, Collection $members): void {
        foreach ($members as $member) {
            $this->createNoVote($teamEvent, $member);
        }
    }
    
    private function createNoVote(TeamEvent $teamEvent, TeamMember $member): TeamEventVote {
        return $teamEvent->createVote(
            teamMember: $member,
            vote: TeamEventVoteType::NO,
            voterPerson: $this->author,
            absence: $this,
            comment: $this->getDefaultComment(),
        );
    }

    private function getDefaultComment(): string {
        return once(function () {
            $prefix = match ($this->absence_type) {
                AbsenceType::SICK     => 'Krank',
                AbsenceType::VACATION => 'Urlaub',
                AbsenceType::INACTIVE => 'Inaktiv',
                default               => 'Abwesend',
            } . ' ';
    
            if ($this->date_end === null) {
                $rangeString = 'seit ' . $this->date_begin->format('d.m.Y');
            } else {
                $rangeString = 'vom ' . $this->date_begin->format('d.m.Y') . ' - ' . $this->date_end->format('d.m.Y');
            }
    
            if ($this->isRecurring()) {
                $rrule = $this->getRRule();
                $rangeString = $rrule->humanReadable([
                    'locale'         => 'de',
                    'date_formatter' => fn(\DateTime $date) => $date->format('d.m.Y'),
                    'include_until'  => $rrule->isFinite(),
                ]);
            }
            return $prefix . '(' . $rangeString . ')';
        });
    }
}
