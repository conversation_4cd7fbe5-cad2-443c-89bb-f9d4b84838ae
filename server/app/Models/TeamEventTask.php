<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property TeamEvent $teamEvent // cannot be null, added for php<PERSON> to not expect null values
 */
class TeamEventTask extends Model {
    use HasUuids;

    protected $fillable = [
        'config_id',
        'team_member_id',
    ];

    /**
     * @return BelongsTo<TeamEventTaskConfig, $this>
     */
    public function config(): BelongsTo {
        return $this->belongsTo(TeamEventTaskConfig::class, 'config_id');
    }

    /**
     * @return BelongsTo<TeamEvent, $this>
     */
    public function teamEvent(): BelongsTo {
        return $this->belongsTo(TeamEvent::class);
    }

    /**
     * @return BelongsTo<TeamMember, $this>
     */
    public function teamMember(): BelongsTo {
        return $this->belongsTo(TeamMember::class);
    }
}
