<?php

namespace App\Models\Interfaces;

use DateTimeInterface;
use RRule\RRule;

interface Recurring {

    /**
     * @return RRule<DateTimeInterface>
     */
    public function getRRule(): RRule;

    /**
     * @param RRule<DateTimeInterface> $rrule
     *
     * @return void
     */
    public function setRRule(RRule $rrule): void;

    public function isRecurring(): bool;

    public function setRRuleUntil(DateTimeInterface $date, bool $saveDirectly = true): void;
}
