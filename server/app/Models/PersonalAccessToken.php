<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use <PERSON><PERSON>\Sanctum\PersonalAccessToken as SanctumPersonalAccessToken;

class PersonalAccessToken extends SanctumPersonalAccessToken {
    /**
     * @var string[]
     */
    protected $fillable = [
        'name',
        'token',
        'abilities',
        'expires_at',
        'installation_id',
    ];

    /**
     * Note: Don't delete. Used in User::routeNotificationForFcm.
     *
     * @return BelongsTo<DeviceInfo, $this>
     */
    public function deviceInfo(): BelongsTo {
        return $this->belongsTo(DeviceInfo::class, 'installation_id', 'id');
    }
}
