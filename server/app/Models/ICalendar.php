<?php

namespace App\Models;

use App\Traits\HasAuthorPerson;
use App\Traits\HasTargetMembersAndPersons;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\URL;

class ICalendar extends Model {
    use HasUuids;
    use HasAuthorPerson;
    use HasTargetMembersAndPersons;
    
    protected $fillable = [
        'author_id',
        'internal_name',
    ];

    protected function casts(): array {
        return [
            'id'        => 'string',
            'author_id' => 'string',
        ];
    }

    public function getPublicLink(): string {
        return URL::route(
            'api.icalendar.publicLink',
            ['iCalendar' => $this]
        );
    }

    /**
     * There may be a customizable parameter in future, but this should always create a default name
     * 
     * @return string
     */
    public function getDefaultName(): string {
        $teamNamesCommaSeparated = $this->getUniqueTargetTeamMembers()->pluck('team.name')->unique()->implode(', ');
        return 'Numo (' . $teamNamesCommaSeparated . ')';
    }

    /**
     * @return Collection<string, TeamEvent> A collection of TeamEvent
     */
    public function getTeamEvents(): Collection {
        $teamIds = $this->getUniqueTargetTeamMembers()->pluck('team_id')->unique()->toArray();
        /** @var Collection<string, TeamEvent> */
        return TeamEvent::events()
            ->whereIn('team_id', $teamIds)
            ->with('team')
            ->get();
            
            
    }
}
