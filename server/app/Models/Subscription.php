<?php

namespace App\Models;

use App\Types\SubscriptionPriceType;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Lara<PERSON>\Cashier\Subscription as CashierSubscription;

class Subscription extends CashierSubscription {
    use HasUuids;

    /**
     * @var array<string, string|class-string>
     */
    protected $casts = [
        'price_type' => SubscriptionPriceType::class,

        /* from parent class */
        'ends_at' => 'datetime',
        'quantity' => 'integer',
        'trial_ends_at' => 'datetime',
        /* from parent class */
    ];


    /**
     * @return BelongsTo<Team, $this>
     */
    public function team(): BelongsTo {
        return $this->belongsTo(Team::class);
    }

    /**
     * Get the user that owns the subscription.
     *
     * @return BelongsTo<User, $this>
     */
    public function user(): BelongsTo {
        return parent::user();
    }
}
