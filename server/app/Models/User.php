<?php

namespace App\Models;

use App\Notifications\VerifyEmail;
use App\Traits\HasApiTokens;
use Database\Factories\UserFactory;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Kyranb\Footprints\TrackableInterface;
use Kyranb\Footprints\TrackRegistrationAttribution;
use Laravel\Cashier\Billable;
use Stripe\Customer;
use function Illuminate\Events\queueable;

/**
 * @property Person $person // cannot be null, added for phpstan to not expect null values
 */
class User extends Authenticatable implements MustVerifyEmail, TrackableInterface {
    use HasApiTokens;
    /** @use HasFactory<UserFactory> */
    use HasFactory;
    use Notifiable;
    use HasUuids;
    use Billable {
        subscription as billableSubscription;
    }
    use TrackRegistrationAttribution;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'email',
        'password',
        'person_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'trial_ends_at'     => 'datetime',
    ];

    public static function createWithPerson(string $email, string $password, string $firstname = '', string $lastname = ''): User {
        $person = Person::create([
            'firstname' => $firstname,
            'lastname'  => $lastname,
        ]);

        /** @var User $user */
        $user = $person->user()
            ->create([
                'email'    => $email,
                'password' => Hash::make($password),
            ]);

        return $user;
    }

    protected static function booted(): void {
        static::updated(queueable(function (User $user) {
            if ($user->hasStripeId()) {
                $user->syncStripeCustomerDetails();
            }
        }));
    }

    /**
     * Syncs the user data to stripe customer details.
     *
     * @return Customer
     */
    public function syncStripeCustomerDetails(): Customer {
        return $this->updateStripeCustomer([
//            'name'  => $this->person->getFullName(),
            'email' => $this->email,
            //            'phone' => $this->stripePhone(),
//                        'address' => $this->stripeAddress(),
//                        'address' => [
            //                 'city' => 'Nettetal',
//                             'country' => 'DE',
            //                 'line1' => 'Hinsbecker Str. 32',
            //                 'postal_code' => '41334',
//                         ],
            //            'preferred_locales' => $this->stripePreferredLocales(),
            //            'metadata' => $this->stripeMetadata(),
        ]);
    }

    public function subscription(string $type = 'default'): Subscription|\Laravel\Cashier\Subscription {
        return $this->billableSubscription($type);
    }

    /**
     * @return BelongsTo<Person, $this>
     */
    public function person(): BelongsTo {
        return $this->belongsTo(Person::class);
    }

    /**
     * Retrieve FCM tokens for routing FCM notifications.
     *
     * Fetches all non-null FCM tokens associated with the user and returns them as an array.
     *
     * NOTE: This function looks unused, but that is just due to Laravel's ungodly habit of calling things based on naming patterns.
     *       It is actually used by Laravel's notification system to route FCM notifications to the user. So don't delete it.
     *
     * @return array<string> An array of FCM tokens or an empty string if no tokens are found.
     */
    public function routeNotificationForFcm(): array {
        return $this->tokens()
            ->whereNotNull('installation_id')
            ->whereHas('deviceInfo', function ($query) {
                $query->where('push_notifications_enabled', true);
            })
            ->with('deviceInfo')
            ->get()
            ->pluck('deviceInfo.fcm_token')
            ->toArray();
    }

    /**
     * Overrides the default email verification notification method from the MustVerifyEmail interface.
     *
     * This customized function allows for an optional Invite model to be passed, enabling the email
     * verification process to be tailored based on the invitation details.
     *
     * @return void
     */
    public function sendEmailVerificationNotification(): void {
        $this->notify(new VerifyEmail($this->invite));
    }

    public function generateEmailVerificationToken(): void {
        $token = Str::random(64);
        $this->email_verification_token = $token;
        $this->save();
    }

    /**
     * create relation for column invite_token with Invite model
     *
     * @return BelongsTo<Invite, $this>
     */
    public function invite(): BelongsTo {
        return $this->belongsTo(Invite::class, 'invite_token', 'token');
    }

    /**
     * @return HasOne<RegistrationInformation>
     */
    public function registrationInformation(): HasOne {
        return $this->hasOne(RegistrationInformation::class);
    }

    /**
     * Check if the user is the owner of the given team member ID.
     *
     * @param string $teamMemberId
     *
     * @return bool
     */
    public function isOwnerOfTeamMember(string $teamMemberId): bool {
        foreach ($this->person->teamMembers as $member) {
            if ($member->id === $teamMemberId) {
                return true;
            }
        }
        return false;
    }
}
