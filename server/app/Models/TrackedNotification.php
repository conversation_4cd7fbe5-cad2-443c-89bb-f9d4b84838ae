<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class TrackedNotification extends Model {
    use HasUuids;

    // Specify the table if it's not the pluralized version of the model name
    protected $table = 'tracked_notifications';

    // Define which attributes can be mass assignable
    protected $fillable = [
        'notifiable_id',
        'notifiable_type',
        'message_title',
        'message_type',
        'message_uuid',
        'sent_at',
        'received_at',
    ];

    // Define the dates property to let Eloquent know which fields are date types
    /**
     * @var string[]
     */
    protected array $dates = [
        'sent_at',
        'received_at',
        'created_at',
        'updated_at',
    ];

    /**
     * Get the notifiable model that this notification belongs to.
     *
     * @return MorphTo<Model, $this>
     */
    public function notifiable(): MorphTo {
        return $this->morphTo();
    }
}
