<?php

namespace App\Models;

use App\Models\Abstracts\TeamLedgerClaimable;
use App\Traits\HasAuthorPerson;
use App\Types\TeamLedgerClaimStatusType;
use Carbon\Carbon;
use Cknow\Money\Casts\MoneyIntegerCast;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property TeamLedger $ledger // cannot be null, added for phpstan to not expect null values 
 * @property TeamMember $teamMember // cannot be null, added for phpstan to not expect null values
 */
class TeamLedgerClaim extends Model {
    use HasUuids;
    use HasAuthorPerson;

    protected $fillable = [
        'team_ledger_id',
        'team_member_id',
//        'author_id',
        'exempt',
        'title',
        'due_date',
        'amount',
        'item',
        'fulfilled_at',
    ];

    /**
     * @return string[]
     */
    protected function casts(): array {
        return [
            'exempt' => 'boolean',
            'due_date' => 'date',
            'amount' => MoneyIntegerCast::class,
            'fulfilled_at' => 'datetime',
        ];
    }

    protected static function booted(): void {
        static::creating(static function(TeamLedgerClaim $claim) {
            self::findRelatedClaimableAndInheritValues($claim);
        });

        static::created(static function(TeamLedgerClaim $claim) {
            if($claim->isMoneyClaim()) {
                $claim->ledger->fulfillClaimsFromCredit($claim->teamMember);
            }
        });
        
        static::deleted(static function(TeamLedgerClaim $claim) {
            if($claim->isMoneyClaim()) {
                $claim->ledger->fulfillClaimsFromCredit($claim->teamMember);
            }
        });

        static::updated(static function(TeamLedgerClaim $claim) {
            if($claim->wasChanged('due_date')) {

                $oldClaimableId = $claim->claimable_id;
                self::findRelatedClaimableAndInheritValues($claim);
                $claim->saveQuietly();
                if($oldClaimableId !== $claim->claimable_id
                    && $claim->isMoneyClaim()
                ) {
                    if($claim->isFulfilled()) {
                        $claim->setFulfilled(false);
                    }
                    $claim->ledger->fulfillClaimsFromCredit($claim->teamMember);
                }
            }
        });
    }

    private static function findRelatedClaimableAndInheritValues(TeamLedgerClaim $claim): void {
        if($claim->claimable instanceof TeamLedgerClaimable) {
            $claimable = self::getClaimableForDueDate($claim->claimable, $claim->due_date);
            $claim->associateClaimableAndInheritValues($claimable);
        }
    }

    /**
     * this only changes the values, save() has to be called manually afterwards
     * @param TeamLedgerClaimable $claimable
     *
     * @return void
     */
    public function associateClaimableAndInheritValues(TeamLedgerClaimable $claimable): void {
        $this->claimable()->associate($claimable);

        $this->team_ledger_id = $this->claimable->ledger->id;
        $this->title = $this->claimable->title;
        $this->amount = $this->claimable->amount;
        $this->item = $this->claimable->item;
    }
    
    /**
     * get correct claimable for due date,
     * creation from frontend will always be done with the newest child but due date may relate to any parent
     * updating due_date will have to link to the active claimable for that date 
     * 
     * @param TeamLedgerClaimable $claimable
     * @param Carbon $dueDate
     *
     * @return TeamLedgerClaimable
     */
    private static function getClaimableForDueDate(TeamLedgerClaimable $claimable, Carbon $dueDate): TeamLedgerClaimable {
        if ($claimable->date_begin->isAfter($dueDate)) {
            if($claimable->hasParentClaimable()) {
                $parentClaimable = $claimable->getParentClaimable();
                if($parentClaimable) { // for phpstan
                    $claimable = self::getClaimableForDueDate($parentClaimable, $dueDate);
                }
            }
        } else {
            if($claimable->hasChildClaimable()) {
                $childClaimable = $claimable->getChildClaimable();
                if($childClaimable && $childClaimable->date_begin->isBefore($dueDate)) {
                    $claimable = self::getClaimableForDueDate($childClaimable, $dueDate);
                }
            }
        }
        return $claimable;
    }
    
    public function isFulfilled(): bool {
        return $this->fulfilled_at !== null;
    }

    public function getStatus(): TeamLedgerClaimStatusType {
        if($this->exempt) {
            return TeamLedgerClaimStatusType::EXEMPT;
        } else if($this->isFulfilled()) {
            return TeamLedgerClaimStatusType::FULFILLED;
        } else if($this->due_date->isBefore(Carbon::today()->addDay())) {
            return TeamLedgerClaimStatusType::UNFULFILLED_OVERDUE;
        } else {
            return TeamLedgerClaimStatusType::UNFULFILLED;
        }
    }

    public function setFulfilled(bool $fulfilled = true, bool $saveDirectly = true): static {
        $this->fulfilled_at = $fulfilled ? Carbon::now() : null;
        if($saveDirectly) {
            $this->save();
        }
        return $this;
    }

    public function setExempt(bool $exempt = true, bool $saveDirectly = true): static {
        $this->exempt = $exempt;
        if($saveDirectly) {
            $this->save();
        }
        return $this;
    }

    /**
     * @param Builder<self> $builder
     *
     * @return Builder<self>
     */
    public function scopeNotExempt(Builder $builder): Builder {
        return $builder->whereExempt(false);
    }

    /**
     * @param Builder<self> $builder
     *
     * @return Builder<self>
     */
    public function scopeNotFuture(Builder $builder): Builder {
        return $builder->whereDate('due_date', '<=', Carbon::today());
    }

    /**
     * @param Builder<self> $builder
     *
     * @return Builder<self>
     */
    public function scopeFuture(Builder $builder): Builder {
        return $builder->whereDate('due_date', '>', Carbon::today());
    }

    /**
     * @param Builder<self> $builder
     *
     * @return Builder<self>
     */
    public function scopeWithMoney(Builder $builder): Builder {
        return $builder->whereNotNull('amount');
    }

    /**
     * @param Builder<self> $builder
     *
     * @return Builder<self>
     */
    public function scopeWithItem(Builder $builder): Builder {
        return $builder->whereNull('amount');
    }

    public function isMoneyClaim(): bool {
        return $this->amount !== null;
    }

    public function isItemClaim(): bool {
        return $this->amount === null;
    }

    /**
     * @param Builder<self> $builder
     *
     * @return Builder<self>
     */
    public function scopeFulfilled(Builder $builder): Builder {
        return $builder->whereNotNull('fulfilled_at');
    }

    /**
     * @param Builder<TeamLedgerClaimable> $builder
     *
     * @return Builder<TeamLedgerClaimable>
     */
    public function scopeNotFulfilled(Builder $builder): Builder {
        return $builder->whereNull('fulfilled_at');
    }

    /**
     * @param Builder<self> $builder
     * @param TeamMember|null $member
     *
     * @return Builder<self>
     */
    public function scopeWithMemberOrAll(Builder $builder, TeamMember $member = null): Builder {
        if(isset($member)) {
           return $builder->whereTeamMemberId($member->id);
        }
        return $builder;
    }

    /**
     * @return BelongsTo<TeamLedger, $this>
     */
    public function ledger(): BelongsTo {
        return $this->belongsTo(TeamLedger::class, 'team_ledger_id');
    }

    /**
     * @return BelongsTo<TeamMember, $this>
     */
    public function teamMember(): BelongsTo {
        return $this->belongsTo(TeamMember::class)->withTrashed();
    }

    /**
     * @return MorphTo<TeamLedgerClaimable, $this>
     */
    public function claimable(): MorphTo {
        /** @var MorphTo<TeamLedgerClaimable, $this> $morph */
        $morph = $this->morphTo()->withTrashed();
        return $morph;
    }
}
