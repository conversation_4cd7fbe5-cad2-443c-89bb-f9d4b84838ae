<?php

namespace App\Models;

use App\Types\TeamRoleType;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class TeamRole extends Model {
    use HasUuids;

    public $timestamps = false;
    protected $fillable = [
        'name',
    ];
    protected $casts = [
        'name' => TeamRoleType::class,
    ];

    public static function createRoleIfNotExists(Team $team, TeamRoleType $roleName): TeamRole {
        $query = $team->roles()->where('name', $roleName);
        if (!$query->exists()) {
            return $team->roles()->create(['name' => $roleName]);
        }
        return $query->first();
    }

    /**
     * @return BelongsTo<Team, $this>
     */
    public function team(): BelongsTo {
        return $this->belongsTo(Team::class);
    }

    /**
     * @return BelongsToMany<TeamPermission, $this>
     */
    public function permissions(): BelongsToMany {
        return $this->belongsToMany(TeamPermission::class);
    }

    /**
     * @return BelongsToMany<TeamMember, $this>
     */
    public function teamMembers(): BelongsToMany {
        return $this->belongsToMany(TeamMember::class);
    }
}
