<?php

namespace App\Models;

use App\Notifications\EmailForNewWaitListEntryNotification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class RegistrationAllowlist extends Model {
    use Notifiable;

    protected $fillable = [
        'email',
        'user_id',
        'allow_registration',
    ];

    /**
     * @var string
     */
    protected $table = "registration_allowlist";

    /**
     * @param Builder<RegistrationAllowlist>|RegistrationAllowlist $query
     *
     * @return Builder<RegistrationAllowlist>|RegistrationAllowlist
     */
    public function scopeRegistrationAllowed(Builder|RegistrationAllowlist $query): Builder|RegistrationAllowlist {
        // alreadyOnInviteList: user_id === null and allow_registration = true
        return $query
            ->whereNull('user_id')
            ->where('allow_registration', true);
    }

    /**
     * @param Builder<RegistrationAllowlist>|RegistrationAllowlist $query
     */
    public function scopeOnWaitList(Builder|RegistrationAllowlist $query): void {
        // alreadyOnInviteList: user_id === null and allow_registration = false
        $query
            ->whereNull('user_id')
            ->where('allow_registration', false);
    }

    public function sendEmailForNewWaitListEntry(): void {
        $this->notify(new EmailForNewWaitListEntryNotification());
    }
}
