<?php

namespace App\Models;

use App\Models\Abstracts\TeamLedgerClaimable;
use App\Models\Interfaces\Recurring;
use App\Traits\HasNonInfiniteRRule;
use Carbon\Carbon;
use Cknow\Money\Casts\MoneyIntegerCast;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

class TeamLedgerDues extends TeamLedgerClaimable implements Recurring {
    use SoftDeletes, HasUuids, HasNonInfiniteRRule;

    protected $fillable = [
        'team_ledger_id',
        'title',
        'amount',
        'item',
        'parent_id',
        'date_begin',
        'rrule_string',
    ];

    /**
     * @return string[]
     */
    protected function casts(): array {
        return [
            'date_begin' => 'date',
            'amount' => MoneyIntegerCast::class,
        ];
    }

    protected static function booted(): void {
        parent::booted();
        static::creating(static function(TeamLedgerDues $dues) {
            $dues->checkRRuleMatchingParent();
        });
        static::created(static function(TeamLedgerDues $dues) {
            $dues->createOrRemoveClaimsForMembersBasedOnJoinDate();
        });

        static::updating(static function(TeamLedgerDues $dues) {
            $dues->checkRRuleMatchingParent();
        });

        static::updated(static function(TeamLedgerDues $dues) {
            if($dues->wasChanged('date_begin') 
                || $dues->wasChanged('rrule_string')
            ) {
                if($dues->isRecurring()){
                    $dues->removeOutOfRangeClaimsForMembers();
                } else {
                    $dues->claims()->update(['due_date' => $dues->date_begin,]);
                }
                
                $dues->createOrRemoveClaimsForMembersBasedOnJoinDate();
            }
        });
        static::softDeleted(static function(TeamLedgerDues $dues) {
            if($dues->deleted_at && $dues->isRecurring()) {
                $dues->setRRuleUntil($dues->deleted_at, false);
                $dues->saveQuietly();
                $dues->removeOutOfRangeClaimsForMembers();
            }
            
        });
    }

    /**
     * wenn `date_begin` geändert wird oder ein child angelegt wird, könnte die RRule theoretisch anders sein.
     * money claims vom parent könnten gelöscht, vom child neu angelegt werden und dann mit dem Guthaben neu verrechnet werden.
     * Allerdings wird dann `exempt` nicht übernommen.
     * item claims haben nur fulfilled at, es lässt sich also kaum / gar nicht vergleichen welche schon erfüllt wurden
     * das könnte also je nach Änderung der rrule sehr komplex werden zu prüfen.
     *
     * Hier wird daher sichergestellt:
     * recurring muss beim parent und child gleich sein, sonst ist die Änderung / Erstellung nicht möglich.
     * Es wird geprüft, ob in dem überlappenden Teil die `occurrences` übereinstimmen, das Ergebnis muss also nur stimmen.
     * Es kann zum Beispiel von monatlich auf wöchentlich gewechselt werden, wenn nur das letzte Ereignis oder gar keins in der Überlappung enthalten sind.
     *
     * Trifft das zu, dann können die bestehenden claims innerhalb der Überlappung mit dem child neu verlinkt werden.
     *
     * @return void
     */
    private function checkRRuleMatchingParent(): void {

        $oldDues = $this->getParentClaimable();
        if($oldDues instanceof self) {
            if(!$this->isRecurring() && !$oldDues->isRecurring()) {
                return;
            }
            if($this->isRecurring() !== $oldDues->isRecurring()) {
                throw new \InvalidArgumentException('old and new dues have to be recurring');
            }

            $overlappingOccurrencesFromOld = collect($oldDues->getRRule()->getOccurrencesAfter($this->date_begin->copy()->startOfDay(), true))
                ->map(fn($occurrence) => Carbon::instance($occurrence)->startOfDay());
            $overlappingOccurrencesFromNew = collect($this->getRRule()->getOccurrencesBefore(Carbon::create($oldDues->getRRule()->getRule()['UNTIL']), true))
                ->map(fn($occurrence) => Carbon::instance($occurrence)->startOfDay());
            $occurrencesInOldButNotInNew = $overlappingOccurrencesFromOld->diff($overlappingOccurrencesFromNew);
            if($occurrencesInOldButNotInNew->isNotEmpty()) {
                throw new \InvalidArgumentException('old and new dues recurring rule does not match');
            }
        }
    }



    private function getClaim(TeamMember $member, \DateTime $dueDate): TeamLedgerClaim|null {
        return $this->claims->first(
            fn(TeamLedgerClaim $claim) =>
                $claim->team_member_id === $member->id
                && $claim->due_date->isSameDay($dueDate)
        );
    }

    private function removeOutOfRangeClaimsForMembers(): void {
        $this->refresh();
        if($this->isRecurring()) {
            $rrule = $this->getRRule();
            foreach ($this->claims as $claim) {
                if(!$rrule->occursAt($claim->due_date->toDateTime())) {
                    $claim->delete();
                }
            }
        }
    }

    private function createOrRemoveClaimsForMembersBasedOnJoinDate(TeamMember $onlyForMember = null): void {
        $this->refresh();
        $claimsData = new Collection();

        if(isset($onlyForMember)) {
            $members = collect([$onlyForMember]);
        } else {
            // get deleted members as well and create claims before deleted date
            /** @var Collection<string, TeamMember> $members */
            $members = $this->ledger->members()->withTrashed()->get();
        }

        if($this->isRecurring()) {
            $occurrences = $this->getRRule()->getOccurrences();
        } else {
            $occurrences = [$this->date_begin];
        }

        foreach ($occurrences as $occurrence) {
            foreach ($members as $member) {

                // check if claim already exists and delete when member join or deleted date does not match
                $existingClaim = $this->getClaim($member, $occurrence);
                if($existingClaim instanceof TeamLedgerClaim) {
                    if(!$member->isActiveOnDate($occurrence)) {
                        $existingClaim->delete();
                    }
                    continue;
                }

                // check if claim should be created based on member join or deleted date
                if($member->isActiveOnDate($occurrence)) {
                    // create claim data for mass creation
                    $claimsData->add([
                        'team_member_id' => $member->id,
                        'due_date' => $occurrence,
                    ]);
                }
            }
        }

        $this->claims()->createMany($claimsData);
    }


    public function updateClaimsForMember(TeamMember $member): void {
        $this->createOrRemoveClaimsForMembersBasedOnJoinDate($member);
    }

}
