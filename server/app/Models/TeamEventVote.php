<?php

namespace App\Models;

use App\Traits\HasComment;
use App\Types\TeamEventVoteType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property TeamEvent $teamEvent // cannot be null, added for phpstan to not expect null values
 * @property TeamMember $teamMember // cannot be null, added for phpstan to not expect null values
 */
class TeamEventVote extends Model {
    use HasUuids;
    use HasComment;

    protected $fillable = [
        'team_event_id',
        'team_member_id',
        'vote',
        'voter_person_id',
        'absence_id',
    ];

    protected $casts = [
        'vote' => TeamEventVoteType::class,
    ];

    protected static function booted(): void {
        static::creating(static function (TeamEventVote $teamEventVote) {
            $teamMemberAllowedForEvent = $teamEventVote->teamMember->team_id === $teamEventVote->teamEvent->team_id;
            abort_if(!$teamMemberAllowedForEvent, 403, 'member does not belong to team');

            // delete existing vote so there are no duplicates
            TeamEventVote::where('team_event_id', $teamEventVote->team_event_id)
                ->where('team_member_id', $teamEventVote->team_member_id)
                ->delete();
        });

        static::updating(static function (TeamEventVote $teamEventVote) {
            if ($teamEventVote->vote !== TeamEventVoteType::NO && $teamEventVote->absence instanceof Absence) {
                $teamEventVote->absence()->disassociate();
                $teamEventVote->comment()->delete();
            }
        });
    }

    /**
     * @return BelongsTo<Absence, $this>
     */
    public function absence(): BelongsTo {
        return $this->belongsTo(Absence::class, 'absence_id');
    }

    /**
     * @return BelongsTo<TeamEvent, $this>
     */
    public function teamEvent(): BelongsTo {
        return $this->belongsTo(TeamEvent::class);
    }

    /**
     * @return BelongsTo<TeamMember, $this>
     */
    public function teamMember(): BelongsTo {
        return $this->belongsTo(TeamMember::class);
    }

    /**
     * @return BelongsTo<Person, $this>
     */
    public function voterPerson(): BelongsTo {
        return $this->belongsTo(Person::class, 'voter_person_id');
    }

    /**
     * @param Builder<TeamEventVote>|TeamEventVote $query
     */
    public function scopeWithoutAuthor(Builder|TeamEventVote $query): void {
        $query->whereNull('voter_person_id');
    }

    /**
     * @param Builder<TeamEventVote> $query
     */
    public function scopeFuture(Builder $query): void {
        $query->whereRelation('teamEvent', 'date_begin', '>=', Carbon::createMidnightDate());
    }
}
