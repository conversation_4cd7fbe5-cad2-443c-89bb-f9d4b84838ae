<?php

namespace App\Models\Abstracts;

use App\Models\Interfaces\Recurring;
use App\Models\TeamLedger;
use App\Models\TeamLedgerClaim;
use Cknow\Money\Money;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use RRule\RRule;
use Staudenmeir\LaravelAdjacencyList\Eloquent\HasRecursiveRelationships;

/**
 * @property string $id
 * @property string $team_ledger_id
 * @property TeamLedger $ledger
 * @property string $title
 * @property Money|null $amount
 * @property string|null $item
 * @property Carbon $date_begin
 */
abstract class TeamLedgerClaimable extends Model {
    use SoftDeletes;
    use HasRecursiveRelationships {
        parent as parentWithoutTrashed;
        children as childrenWithoutTrashed;
    }

    protected static function booted(): void {
        static::creating(static function(TeamLedgerClaimable $claimable) {
            if($claimable->hasParentClaimable() && $claimable->getParentClaimable() && $claimable->date_begin->isBefore($claimable->getParentClaimable()->date_begin)) {
                throw new \InvalidArgumentException('date begin cannot be before original date begin');
            }
        });
        static::created(static function(TeamLedgerClaimable $claimable) {
            $claimable->getParentClaimable()?->delete();
            $claimable->adjustDateBeginAndFulfillClaimsForChildClaimable();
        });
        static::updated(static function(TeamLedgerClaimable $claimable) {
            if($claimable->wasChanged('amount') 
                || $claimable->wasChanged('title') 
                || $claimable->wasChanged('item') 
            ) {
                $claimable->adjustNonDateRelatedValuesAndFulfillClaims();
            }
            if($claimable->wasChanged('date_begin')) {
                $claimable->adjustDateBeginAndFulfillClaimsForChildClaimable();
            }
        });

        static::forceDeleted(static function(TeamLedgerClaimable $claimable) {

            $teamMembersWithChangedClaims = $claimable->claims->map(fn($claim) => $claim->teamMember)->unique();
            
            $parentClaimable = $claimable->getParentClaimable();
            if($parentClaimable) { // restore parent and move claims to parent
                $parentClaimable->restore();
                /** @var TeamLedgerClaim $claim */
                foreach ($claimable->claims as $claim) {
                    $claim->associateClaimableAndInheritValues($parentClaimable);
                    if($claim->isMoneyClaim()) {
                        $claim->setFulfilled(false, false);
                    }
                    $claim->saveQuietly();
                }
            } else {                
                $claimable->claims()->delete();
            }
            
            foreach ($teamMembersWithChangedClaims as $member) {
                $claimable->ledger->fulfillClaimsFromCredit($member);
            }
        });
    }

    public function hasParentClaimable(): bool {
        return $this->{$this->getParentKeyName()} !== null;
    }

    /**
     * @return BelongsTo<TeamLedgerClaimable, TeamLedgerClaimable>
     */
    public function parent(): BelongsTo {
        return $this->parentWithoutTrashed()->withTrashed();
    }

    /**
     * @return HasMany<TeamLedgerClaimable, $this>
     */
    public function children(): HasMany {
        return $this->childrenWithoutTrashed()->withTrashed();
    }

    /**
     * @return TeamLedgerClaimable|null
     */
    public function getParentClaimable(): TeamLedgerClaimable|null {
        return $this->parent()->first();
    }
    
    public function hasChildClaimable(): bool {
        return $this->children()->exists();
    }

    /**
     * @return TeamLedgerClaimable|null
     */
    public function getChildClaimable(): TeamLedgerClaimable|null {
        return $this->children()->first();
    }

    /**
     * @return BelongsTo<TeamLedger, $this>
     */
    public function ledger(): BelongsTo {
        return $this->belongsTo(TeamLedger::class, 'team_ledger_id');
    }

    /**
     * @return MorphMany<TeamLedgerClaim, $this>
     */
    public function claims(): MorphMany {
        return $this->morphMany(TeamLedgerClaim::class, 'claimable');
    }

    private function adjustNonDateRelatedValuesAndFulfillClaims(): void {
        { // handle money claims
            $moneyClaims = $this->claims()
                ->withMoney()
                ->get();

            foreach ($moneyClaims as $claim) {
                $claim->amount = $this->amount;
                $claim->title = $this->title;
                $claim->setFulfilled(false, false);
                $claim->saveQuietly();
            }

            $teamMembersWithChangedClaims = $moneyClaims->map(fn($claim) => $claim->teamMember)->unique();
            foreach ($teamMembersWithChangedClaims as $member) {
                $this->ledger->fulfillClaimsFromCredit($member);
            }
        }

        { // handle item claims
            $itemClaims = $this->claims()
                ->withItem()
                ->get();

            foreach ($itemClaims as $claim) {
                $claim->item = $this->item;
                $claim->title = $this->title;
                $claim->saveQuietly();
            }
        }
    }
    
    private function adjustDateBeginAndFulfillClaimsForChildClaimable(): void {
        // TODO split into multiple smaller methods and reuse parts
        $parentClaimable = $this->getParentClaimable();

        if($parentClaimable instanceof self) {
            { // adjust parent date_begin and set date_end in rrule (if recurring)
//                sets date_begin of the parent due if same day
                if($parentClaimable->date_begin->isSameDay($this->date_begin)) {
                    $parentClaimable->date_begin = $this->date_begin->copy()->subDay();
                }
//                sets date end within rrule for recurring dues
                if($parentClaimable instanceof Recurring && $parentClaimable->isRecurring()) {
                    $parentClaimable->setRRuleUntil($this->date_begin->copy()->subDay(), false);
                }
                $parentClaimable->saveQuietly();
            }


            { // handle money claims move from parent to child
                $moneyClaimsInRange = $parentClaimable->claims()
                    ->withMoney()
                    ->whereDate('due_date', '>=', $this->date_begin)
                    ->get();

                foreach ($moneyClaimsInRange as $claim) {
                    $claim->claimable()->associate($this);
                    $claim->amount = $this->amount;
                    $claim->title = $this->title;
                    $claim->setFulfilled(false, false);
                    $claim->saveQuietly();
                }

                $teamMembersWithChangedClaims = $moneyClaimsInRange->map(fn($claim) => $claim->teamMember)->unique();
                foreach ($teamMembersWithChangedClaims as $member) {
                    $this->ledger->fulfillClaimsFromCredit($member);
                }
            }

            { // handle money claims move from child to parent
                $moneyClaimsInRange = $this->claims()
                    ->withMoney()
                    ->whereDate('due_date', '<', $this->date_begin)
                    ->get();

                foreach ($moneyClaimsInRange as $claim) {
                    $claim->claimable()->associate($parentClaimable);
                    $claim->amount = $parentClaimable->amount;
                    $claim->title = $parentClaimable->title;
                    $claim->setFulfilled(false, false);
                    $claim->saveQuietly();
                }

                $teamMembersWithChangedClaims = $moneyClaimsInRange->map(fn($claim) => $claim->teamMember)->unique();
                foreach ($teamMembersWithChangedClaims as $member) {
                    $this->ledger->fulfillClaimsFromCredit($member);
                }
            }

            { // handle item claims move from parent to child
                $itemClaimsInRange = $parentClaimable->claims()
                    ->withItem()
                    ->notFulfilled()
                    ->whereDate('due_date', '>=', $this->date_begin)
                    ->get();

                foreach ($itemClaimsInRange as $claim) {
                    $claim->claimable()->associate($this);
                    $claim->item = $this->item;
                    $claim->title = $this->title;
                    $claim->saveQuietly();
                }
            }

            { // handle item claims move from child to parent
                $itemClaimsInRange = $this->claims()
                    ->withItem()
                    ->notFulfilled()
                    ->whereDate('due_date', '<=', $parentClaimable->date_begin)
                    ->get();

                foreach ($itemClaimsInRange as $claim) {
                    $claim->claimable()->associate($parentClaimable);
                    $claim->item = $parentClaimable->item;
                    $claim->title = $parentClaimable->title;
                    $claim->saveQuietly();
                }
            }

            $this->refresh();
            $parentClaimable->refresh();
        }
    }
}
