<?php

namespace App\Models;

use App\Traits\HasAuthorPerson;
use Cknow\Money\Casts\MoneyIntegerCast;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property TeamLedger $ledger // cannot be null, added for phpstan to not expect null values
 */
class TeamLedgerTransaction extends Model {
    use HasUuids;
    use HasAuthorPerson;

    protected $fillable = [
        'team_member_id',
//        'author_id',
        'team_ledger_id',
        'title',
        'amount',
    ];

    /**
     * @return string[]
     */
    protected function casts(): array {
        return [
            'amount' => MoneyIntegerCast::class,
        ];
    }

    protected static function booted(): void {
        static::creating(static function(TeamLedgerTransaction $transaction) {
            if(self::isMemberBalanceAfterTransactionNegative($transaction)) {
                throw new \InvalidArgumentException('member balance cannot be negative');
            }

            if($transaction->title === null && $transaction->teamMember && $transaction->amount) {
                if($transaction->amount->lessThan(money(0))) {
                    $transaction->title = 'Auszahlung';
                } else {
                    $transaction->title = 'Einzahlung';
                }
            }
        });

        static::created(static function(TeamLedgerTransaction $transaction) {
            if($transaction->teamMember !== null && $transaction->amount->greaterThan(money(0))) {
                $transaction->ledger->fulfillClaimsFromCredit($transaction->teamMember);
            }
        });
    }

    private static function isMemberBalanceAfterTransactionNegative(TeamLedgerTransaction $transaction): bool {
        if($transaction->teamMember === null || $transaction->amount->isPositive()) {
            return false;
        }
        $balanceAfterTransaction = $transaction->ledger->getCreditBalance($transaction->teamMember)->add($transaction->amount);
        return $balanceAfterTransaction->isNegative();
    }

    /**
     * @param Builder<TeamLedgerTransaction> $builder
     *
     * @return Builder<TeamLedgerTransaction>
     */
    public function scopeWithoutMember(Builder $builder): Builder {
        return $builder->whereNull('team_member_id');
    }

    /**
     * @param Builder<TeamLedgerTransaction> $builder
     * @param TeamMember|null $member
     *
     * @return Builder<TeamLedgerTransaction>
     */
    public function scopeWithMemberOrAll(Builder $builder, TeamMember $member = null): Builder {
        if(isset($member)) {
            return $builder->whereTeamMemberId($member->id);
        }
        return $builder;
    }

    /**
     * @return BelongsTo<TeamMember, $this>
     */
    public function teamMember(): BelongsTo {
        return $this->belongsTo(TeamMember::class)->withTrashed();
    }

    /**
     * @return BelongsTo<TeamLedger, $this>
     */
    public function ledger(): BelongsTo {
        return $this->belongsTo(TeamLedger::class, 'team_ledger_id');
    }
}
