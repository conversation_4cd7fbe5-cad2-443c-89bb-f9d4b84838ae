<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property Team $team // cannot be null, added for php<PERSON> to not expect null values
 */
class TeamEventTaskConfig extends Model {
    use HasUuids;

    protected $fillable = [
        'title',
    ];

    /**
     * @return BelongsTo<Team, $this>
     */
    public function team(): BelongsTo {
        return $this->belongsTo(Team::class);
    }

    /**
     * @return HasMany<TeamEventTask, $this>
     */
    public function tasks(): HasMany {
        return $this->hasMany(TeamEventTask::class, 'config_id');
    }
}
