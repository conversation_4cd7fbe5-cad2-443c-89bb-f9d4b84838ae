<?php

namespace App\Models;

use App\Events\TeamMemberCreated;
use App\Events\TeamMemberStatusRoleUpdated;
use App\Events\TeamMemberUpdated;
use App\Models\Scopes\LimitToCurrentUserTeamsScope;
use App\Traits\HasInvite;
use App\Types\TeamRoleType;
use Carbon\Carbon;
use Fico7489\Laravel\Pivot\Traits\PivotEventTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

/**
 * @property Team $team // cannot be null, added for phpstan to not expect null values
 * @property TeamRole $statusRole // cannot be null, added for phpstan to not expect null values
 */
class TeamMember extends Model {
    use HasUuids, HasInvite, SoftDeletes;
    use PivotEventTrait;

    protected $fillable = [
        'name',
        'team_id',
        'person_id',
        'team_role_id',
        'join_date',
    ];
    /**
     * @var string[] 
     */
    protected $dispatchesEvents= [
        'created' => TeamMemberCreated::class,
        'updated' => TeamMemberUpdated::class,
    ];

    /**
     * @return string[]
     */
    protected function casts(): array {
        return [
            'join_date' => 'date',
        ];
    }

    protected static function booted() {
        static::creating(function (TeamMember $teamMember) {
            if (!isset($teamMember->join_date)) {
                $teamMember->join_date = Carbon::createMidnightDate();
            }
            $teamMember->setDefaultRoleIfMissing(false);
        });

        static::created(function (TeamMember $teamMember) {
            $teamMember->checkFutureVotesForResponseType();
        });

        static::updated(static function (TeamMember $teamMember) {
            $changes = $teamMember->getChanges();
            if (isset($changes['team_role_id'])) {
                $teamMember->person?->clearPermissionCache();
                /** @var string $originalRoleId */
                $originalRoleId = $teamMember->getOriginal('team_role_id');
                $previousRole = TeamRole::findOrFail($originalRoleId);
                event(new TeamMemberStatusRoleUpdated($teamMember, $previousRole));
            }
        });
        
        static::pivotDetached(function (TeamMember $teamMember, $relationName, $pivotIds) {
            if($relationName === 'roles') {
                $teamMember->person?->clearPermissionCache();
            }
        });

        static::pivotAttached(function (TeamMember $teamMember, $relationName, $pivotIds, $pivotIdsAttributes) {
            if($relationName === 'roles') {
                $teamMember->person?->clearPermissionCache();
            }
        });
            
        
        static::deleting(function (TeamMember $teamMember) {
            $teamMember->statusRole()->associate($teamMember->team->getTeamRole(TeamRoleType::INACTIVE))->save();
            $teamMember->teamLedgerClaims()->future()->delete();
        });
    }

    public function isActiveOnDate(\DateTimeInterface $date): bool {
        return $this->join_date->lessThanOrEqualTo($date) &&
            ($this->deleted_at === null || $this->deleted_at->isAfter($date));
    }

    public function checkFutureVotesForResponseType(): void {
        $teamEventsInFuture = $this->team->events()->future()->get();
        foreach ($teamEventsInFuture as $teamEvent) {
            $teamEvent->updateVotesAccordingToResponseType();
        }
    }

    /**
     * @return BelongsTo<Team, $this>
     */
    public function team(): BelongsTo {
        return $this->belongsTo(Team::class)
            // has to be without global scope to allow access to the team after creation.
            // Since this is done only for the request to get the team based on a member, the access to the team is always ok
            ->withoutGlobalScope(LimitToCurrentUserTeamsScope::class);
    }

    /**
     * @return BelongsTo<Person, $this>
     */
    public function person(): BelongsTo {
        return $this->belongsTo(Person::class);
    }
    
    public function setDefaultRoleIfMissing(bool $withSave = true): void {
        if (isset($this->team_role_id)) {
            return;
        }

        $this->statusRole()->associate(TeamRole::createRoleIfNotExists($this->team, TeamRoleType::MEMBER));
        if ($withSave && $this->isDirty()) {
            $this->save();
        }
    }
    
    /**
     * 
     * @return BelongsTo<TeamRole, $this>
     */
    public function statusRole(): BelongsTo {
        return $this->belongsTo(TeamRole::class, 'team_role_id');
    }

    /**
     * 
     * @return BelongsToMany<TeamRole, $this>
     */
    public function roles(): BelongsToMany {
        return $this->belongsToMany(TeamRole::class);
    }

    /**
     * @param Builder<TeamMember>|TeamMember $query
     */
    public function scopeRegistered(Builder|TeamMember $query): void {
        $query->has('person.user');
    }

    /**
     * @param Builder<TeamMember>|TeamMember $query
     */
    public function scopeActive(Builder|TeamMember $query): void {
        $query->whereRelation('statusRole', 'name', '!=', TeamRoleType::INACTIVE);
    }

    public function isAvailableForTeamInvite(): bool {
        return !$this->isRegistered() && $this->statusRole->name !== TeamRoleType::MANAGER;
    }

    public function isRegistered(): bool {
        return $this->person !== null;
    }

    /**
     * @return Collection<int, TeamPermission>
     */
    public function permissions(): Collection {
        $roles = $this->roles->collect()->add($this->statusRole);
        return $roles
            ->flatMap(fn(TeamRole $role) => $role->permissions)
            ->unique('name');
    }

    /**
     * @return MorphToMany<Absence, $this>
     */
    public function absences(): MorphToMany {
        return $this->morphToMany(Absence::class, 'absence_target');
    }
    
    /**
     * @return MorphToMany<ICalendar, $this>
     */
    public function iCalendarSubscriptions(): MorphToMany {
        return $this->morphToMany(ICalendar::class, 'i_calendar_target');
    }

    public function deleteFutureVotesForResponseTypeAutoYes(): void {
        $teamEventsInFuture = $this->team->events()->future()->get();
        foreach ($teamEventsInFuture as $teamEvent) {
            $teamEvent->deleteVotesForResponseTypeAutoYes($this);
        }
    }

    /**
     * @return HasMany<TeamLedgerTransaction, $this>
     */
    public function teamLedgerTransactions(): HasMany {
        return $this->hasMany(TeamLedgerTransaction::class);
    }

    /**
     * @return HasMany<TeamLedgerClaim, $this>
     */
    public function teamLedgerClaims(): HasMany {
        return $this->hasMany(TeamLedgerClaim::class);
    }
    
    /**
     * @param Builder<self> $query
     *
     * @return Builder<self>
     */
    public function scopeWithTransactionsOrClaims(Builder $query): Builder {
        return $query->where(function(Builder $q) {
            $q->whereHas('teamLedgerTransactions')
                ->orWhereHas('teamLedgerClaims');
        });
    }
}
