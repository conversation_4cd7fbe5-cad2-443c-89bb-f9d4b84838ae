<?php

namespace App\Models;

use App\Traits\HasAuthorPerson;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Comment extends Model {
    use HasUuids;
    use HasAuthorPerson;

    protected $fillable = [
        'text',
        'parent',
        'author_id',
    ];

    /**
     * @return MorphTo<Model, $this>
     */
    public function parent(): MorphTo {
        return $this->morphTo();
    }
}
