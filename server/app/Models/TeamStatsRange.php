<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TeamStatsRange extends Model {
    use HasUuids;

    protected $fillable = [
        'start_date',
        'end_date',
        'name',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date'   => 'date',
    ];

    protected static function boot(): void {
        parent::boot();

        static::addGlobalScope('defaultOrder', static function (Builder|TeamStatsRange $builder) {
            $builder
                ->orderBy('end_date', 'desc')
                ->orderBy('start_date', 'desc');
        });
    }

    /**
     * @return BelongsTo<Team, $this>
     */
    public function team(): BelongsTo {
        return $this->belongsTo(Team::class);
    }

    /**
     * @param Builder<TeamStatsRange>|TeamStatsRange $builder
     *
     * @return Builder<TeamStatsRange>|TeamStatsRange
     */
    public function scopeActive(Builder|TeamStatsRange $builder, Carbon $date = null): Builder|TeamStatsRange {
        $referenceDate = $date ?? Carbon::createMidnightDate();
        return $builder
            ->where('end_date', '>=', $referenceDate)
            ->where('start_date', '<=', $referenceDate);
    }
}
