<?php

namespace App\Models;

use App\Models\Abstracts\TeamLedgerClaimable;
use Cknow\Money\Casts\MoneyIntegerCast;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class TeamLedgerFine extends TeamLedgerClaimable {
    use HasUuids;

    protected $fillable = [
        'team_ledger_id',
        'title',
        'amount',
        'item',
        'date_begin',
        'parent_id',
    ];

    /**
     * @return string[]
     */
    protected function casts(): array {
        return [
            'date_begin' => 'date',
            'amount' => MoneyIntegerCast::class,
        ];
    }
}
