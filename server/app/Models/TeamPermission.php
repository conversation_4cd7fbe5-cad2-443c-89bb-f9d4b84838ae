<?php

namespace App\Models;

use App\Types\TeamPermissionType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class TeamPermission extends Model {

    public $timestamps = false;

    protected $fillable = [
        'name',
    ];

    protected $casts = [
        'name' => TeamPermissionType::class,
    ];

    /**
     * @return BelongsToMany<TeamRole, $this>
     */
    public function roles(): BelongsToMany {
        return $this->belongsToMany(TeamRole::class);
    }
}
