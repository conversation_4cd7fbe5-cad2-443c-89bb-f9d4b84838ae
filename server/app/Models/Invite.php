<?php

namespace App\Models;

use App\Models\Scopes\LimitToCurrentUserTeamsScope;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Hidehalo\Nanoid\Client;

class Invite extends Model {
    use HasUuids;

    protected $fillable = [
        'invitable',
        'token',
    ];

    protected static function booted() {
        static::creating(function (Invite $invite) {
            if (!isset($invite->token)) {
                $invite->token = $invite->generateToken();
            }
        });
    }

    function generateToken(): string {
        return (new Client())->generateId(21);
    }

    /**
     * @return MorphTo<Model, $this>
     */
    public function invitable(): MorphTo {
        return $this->morphTo()->withoutGlobalScope(new LimitToCurrentUserTeamsScope());
    }

    public function getRelatedTeam(): Team {
        $team = null;
        if ($this->invitable instanceof Team) {
            $team = $this->invitable;
        } elseif ($this->invitable instanceof TeamMember) {
            $team = $this->invitable->team()
                ->withoutGlobalScope(new LimitToCurrentUserTeamsScope())
                ->first();
        }
        return $team;
    }
}
