<?php

namespace App\Services;

use App\Http\Controllers\AuthController;

class UserService {

    public function verifyEmail(string $token, string $email): bool {

        $user = AuthController::getCurrentUser();

        if (!$user) {
            return false;
        }
        if (!hash_equals(sha1($user->getEmailForVerification()), $email)) {
            return false;
        }

        if ($user->email_verification_token !== $token) {
            return false;
        }

        $user->email_verified_at = now();
        $user->email_verification_token = null;
        $user->invite()->disassociate();
        $user->save();

        return true;
    }
}
