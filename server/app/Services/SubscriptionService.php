<?php

namespace App\Services;

use App\Models\Team;
use App\Models\User;
use App\Types\SubscriptionPriceType;
use Carbon\Carbon;
use Cknow\Money\Money;
use Illuminate\Support\Collection;
use Lara<PERSON>\Cashier\Cashier;
use Stripe\Checkout\Session;
use Stripe\Exception\ApiErrorException;
use Stripe\Invoice;

class SubscriptionService {
    /**
     * key is @see SubscriptionPriceType
     * @return Collection<string, Money>
     */
    public function getTeamPricesNetto(): Collection {
        $price_cents_netto_team_yearly = config('cashier.price_eur_netto.team') * 100; // 50 EUR in cents
        $price_cents_netto_team_half_yearly = $price_cents_netto_team_yearly/10*6; // 30 EUR in cents, divide by 10 to make it 2 month more expensive
        $price_cents_netto_team_monthly = $price_cents_netto_team_half_yearly / 5; // 6 EUR in cents, divide by 5 to make it 1 month more expensive

        $prices = new Collection();
        $prices->put(SubscriptionPriceType::TEAM_YEARLY->value, money($price_cents_netto_team_yearly));
        $prices->put(SubscriptionPriceType::TEAM_HALF_YEARLY->value, money($price_cents_netto_team_half_yearly));
        $prices->put(SubscriptionPriceType::TEAM_MONTHLY->value, money($price_cents_netto_team_monthly));        
        
        return $prices;
    }

    /**
     * @return Collection<string, Money>
     * @throws ApiErrorException
     * @throws \Throwable
     */
    public function getTeamPricesNettoFromStripe(): Collection {
        // Lookup-Keys für alle Preise
        $lookupKeyYearly = config('cashier.lookup_keys.price.team_yearly');
        $lookupKeyMonthly = config('cashier.lookup_keys.price.team_monthly');
        $lookupKeyHalfYearly = config('cashier.lookup_keys.price.team_half_yearly');

        // Eine einzige Suchanfrage mit ODER-Verknüpfung für alle Lookup-Keys
        $searchQuery = "lookup_key:'".$lookupKeyYearly."' OR lookup_key:'".$lookupKeyMonthly."' OR lookup_key:'".$lookupKeyHalfYearly."'";
        $prices = Cashier::stripe()->prices->search(['query' => $searchQuery]);

        // Preise nach Lookup-Key zuordnen
        $priceYearly = null;
        $priceMonthly = null;
        $priceHalfYearly = null;

        foreach ($prices as $price) {
            if ($price->lookup_key === $lookupKeyYearly) {
                $priceYearly = $price;
            } elseif ($price->lookup_key === $lookupKeyMonthly) {
                $priceMonthly = $price;
            } elseif ($price->lookup_key === $lookupKeyHalfYearly) {
                $priceHalfYearly = $price;
            }
        }

        throw_if(!$priceYearly || !$priceMonthly || !$priceHalfYearly, new \Exception('Preis nicht gefunden'));

        $prices = new Collection();
        $prices->put(SubscriptionPriceType::TEAM_YEARLY->value,      money($priceYearly->unit_amount_decimal, $priceYearly->currency));
        $prices->put(SubscriptionPriceType::TEAM_HALF_YEARLY->value, money($priceMonthly->unit_amount_decimal, $priceMonthly->currency));
        $prices->put(SubscriptionPriceType::TEAM_MONTHLY->value,     money($priceHalfYearly->unit_amount_decimal, $priceHalfYearly->currency));        
        
        return $prices;
    }

    public function createStripeCheckoutSessionForTeam(User $user, Team $team, SubscriptionPriceType $priceType): Session {
        $lookupKey = match ($priceType) {
            SubscriptionPriceType::TEAM_MONTHLY     => config('cashier.lookup_keys.price.team_monthly'),
            SubscriptionPriceType::TEAM_HALF_YEARLY => config('cashier.lookup_keys.price.team_half_yearly'),
            SubscriptionPriceType::TEAM_YEARLY      => config('cashier.lookup_keys.price.team_yearly'),
        };

        $price = Cashier::stripe()->prices->search(['query' => "lookup_key: '".$lookupKey."'"])->first();
        abort_if(!$price, 400, 'Preis nicht gefunden');

        $teamSubscriptionUrl = config('app.frontend.url') . route(
            'frontend.team.subscription',
            $team->id,
            absolute: false
        );

        // Wenn ein aktives Abonnement mit Enddatum existiert und der aktuelle Benutzer
        // NICHT der ursprüngliche Sponsor ist, starten wir das neue Abonnement erst nach
        // Ablauf des aktuellen Abonnements. Resume ist keine Option, da der neue Sponsor eigenen Zahldaten hinterlegen muss.
        $existingSubscription = $team->getActiveSubscription();
        if ($existingSubscription
            && $existingSubscription->user_id !== $user->id
            && $existingSubscription->ends_at
            && $existingSubscription->onGracePeriod()
        ) {
            // Stelle sicher, dass Stripe-Kunde existiert
            if (!$user->hasStripeId()) {
                $user->createAsStripeCustomer();
            }

            $checkoutOptions['subscription_data'] = [
                'start_date' => $existingSubscription->ends_at->getTimestamp(),
            ];

            $formattedPrice = money($price->unit_amount_decimal, $price->currency)->format();
            $customText = 'Mit dem Speichern wird das Abo für das Team "' . $team->name . '" übernommen. Der Betrag von ' . $formattedPrice . ' zzgl. MwSt. wird nach Ablauf der Restlaufzeit erstmalig am ' . $existingSubscription->ends_at->addDay()->format('d.m.Y') . ' eingezogen.';

            $checkoutSession = Cashier::stripe()->checkout->sessions->create([
                'mode'                       => 'setup',
                'customer'                   => $user->stripe_id,
                'success_url'                => $teamSubscriptionUrl,
                'cancel_url'                 => $teamSubscriptionUrl,
                'billing_address_collection' => 'required',

                'payment_method_types' => ['card', 'paypal', 'sepa_debit'],

                'metadata' => [
                    'team_id'    => $team->id,
                    'price_type' => $priceType->value,
                ],
                'custom_text' => [
                    'submit' => [
                        'message' => $customText
                    ],
                ],
            ]);
        } else {
            $type = config('cashier.lookup_keys.product.team');
            $subscription = $user->newSubscription($type, $price->id)
                ->withMetadata([
                    'team_id'    => $team->id,
                    'price_type' => $priceType->value,
                ]);

            if ($team->trial_ends_at && $team->isOnTrial()) {
                $subscription->trialUntil($team->trial_ends_at);
            }
            $checkoutSession = $subscription->checkout([
                'customer'                   => $user->stripe_id,
                'success_url'                => $teamSubscriptionUrl,
                'cancel_url'                 => $teamSubscriptionUrl,
                'billing_address_collection' => 'required',

                'custom_text' => [
                    'submit' => [
                        'message' => 'Abo für das Team "' . $team->name . '" abschließen.'
                    ],
                ],
            ])->asStripeCheckoutSession();
        }
        return $checkoutSession;
    }

    /**
     * @param Team $team
     *
     * @return Collection<int, array{
     *     id: string,
     *     created: string,
     *     amountFormatted: string, 
     *     subscriptionType: string,
     *     pdfUrl: string
     * }>
     * @throws ApiErrorException
     */
    public function getInvoicesForTeam(Team $team): Collection {
        // metadata.team_id is set in StripeEventHandledListener::handleInvoicePaymentSucceeded
        // since a team subscription can be changed and paid by another person each time, we need to query by metadata 
        // Filter out zero-amount invoices by only including invoices with a positive amount
        /** @var Collection<int, Invoice> $invoices */
        $invoices = Cashier::stripe()->invoices->search([
            'query' => 'metadata["team_id"]:"'.$team->id.'" AND total>0',
            'limit' => 15,
        ]);

        $invoiceData = new Collection();
        foreach ($invoices as $invoice) {
            $invoiceData->add([
                'id'               => $invoice->id,
                'created'          => Carbon::createFromTimestamp($invoice->created)->format('d.m.Y'),
                'amountFormatted'  => money($invoice->total, $invoice->currency)->format(),
                'subscriptionType' => $invoice->lines->first()?->price?->lookup_key,
                'pdfUrl'           => $invoice->invoice_pdf,
            ]);
        }
        return $invoiceData;
    }
}
