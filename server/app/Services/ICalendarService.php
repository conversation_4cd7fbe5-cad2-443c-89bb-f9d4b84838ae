<?php

namespace App\Services;

use App\Models\ICalendar;
use App\Models\TeamMember;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Spatie\IcalendarGenerator\Components\Calendar;
use Spatie\IcalendarGenerator\Components\Event;
use Spatie\IcalendarGenerator\Properties\TextProperty;

class ICalendarService {

    public function getCalendarFeed(ICalendar $iCalendar): string {
        $cacheKey = 'icalendar-feed-' . $iCalendar->id;

//        return Cache::remember($cacheKey, now()->addDay(), function () use ($iCalendar) { // TODO activate caching
            return $this->generateCalendarFeed($iCalendar);
//        });
    }
    
    public function invalidateCalendarFeed(ICalendar $iCalendar): void {
        // TODO add triggers when relevant events are created / updated / deleted 
        $cacheKey = 'icalendar-feed-' . $iCalendar->id;
        Cache::forget($cacheKey);
    }

    private function generateCalendarFeed(ICalendar $iCalendar): string {
        $calendar = Calendar::create()
            ->productIdentifier('//numo-app.com//team-event-calendar')
            ->name($iCalendar->getDefaultName())
            ->refreshInterval(App::isProduction() ? 60 : 1)
        ;
        $calendar->appendProperty(new TextProperty('METHOD', 'REQUEST')); // has to be set to show cancelled events (https://github.com/spatie/icalendar-generator/issues/144) 
        
        // prepare member names per team
        $memberNamesCommaSeparatedPerTeam = $iCalendar->getUniqueTargetTeamMembers()->mapToGroups(function (TeamMember $member) {
            return [$member->team_id => $member->name];
        })->map(function(Collection $memberNames){
            return $memberNames->implode(', ');
        });
        
        $events = $iCalendar->getTeamEvents();
        foreach ($events as $event) {
            $iCalEvent = Event::create();
            if($event->updated_at){
                $iCalEvent->sequence((int)$event->updated_at->timestamp);
            }
            
            $title = ''; {
                if($event->cancelled_at) {
                    $title .= '[Abgesagt] ';
                }
                $title .= $event->getEventTitle();
                $title .= ' ('.$event->team->name.': '.$memberNamesCommaSeparatedPerTeam->get($event->team->id).')';
            }
            
            // check if time meet is set, then use time meet
            $timezone = $event->team->getTimezone();
            $startsAt = $event->dateTimeBegin()->copy();
            if($event->time_meet) {
                $startsAt->setTimeFrom($event->time_meet);
            }
            $startsAt->shiftTimezone($timezone);
            
            $description = ''; {
                if($event->time_meet) {
                    $description .= 'Treffen: ' . $event->dateTimeBegin()->copy()->setTimeFrom($event->time_meet)->shiftTimezone($timezone)->format('H:i')." Uhr\n";
                }
                $description .= 'Beginn: ' . $event->dateTimeBegin()->shiftTimezone($timezone)->format('H:i')." Uhr \n";
                if($event->time_end) {
                    $description .= 'Ende: ' . $event->dateTimeEnd()->copy()->shiftTimezone($timezone)->format('H:i')." Uhr \n";
                }
                
                if($event->sub_text) {
                    $description .= "\n" . $event->sub_text . "\n";
                }
                $description .= "\nTermin in Numo anzeigen:\n".$event->getPublicURL();
            }

            $iCalEvent
                ->name($title)
                ->description($description)
                ->uniqueIdentifier($event->id.'@numo') // add numo so it is always unique
                ->startsAt($startsAt)
                ->endsAt($event->dateTimeEnd(true)->shiftTimezone($timezone))
                ->url($event->getPublicURL())
            ;
            
            if($event->created_at) {
                $iCalEvent->createdAt($event->created_at);
            }
            
            if($event->address_string) {
                $iCalEvent->address($event->address_string);
            }

            $calendar->event($iCalEvent);
        }

        return $calendar->get();
    }
}
