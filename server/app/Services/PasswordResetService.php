<?php

namespace App\Services;

use App\Http\Controllers\AuthController;
use App\Mail\PasswordResetEmail;
use App\Models\User;
use App\Providers\FeatureServiceProvider;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class PasswordResetService {

    public function sendPasswordResetEmail(User $user): void {
        $token = Str::random(64);
        // TODO(fabzo): Add expiration date to token.
        $user->password_reset_token = $token;
        $user->save();

        $mail = Mail::to($user->email);
        
        if(AuthController::isProblemProviderForSendGrid($user->email)) {
            $mail->bcc(config('mail.admin_bcc'));    
        }
            
        $mail->send(new PasswordResetEmail($user->person->firstname, $user, $token));
    }

    public function resetPassword(string $token, string $email, string $newPassword): bool {
        $user = User::where('email', $email)
            ->where('password_reset_token', $token)
            ->first();

        if (!$user) {
            return false;
        }

        $user->password = bcrypt($newPassword);
        $user->password_reset_token = null;
        $user->save();

        return true;
    }
}
