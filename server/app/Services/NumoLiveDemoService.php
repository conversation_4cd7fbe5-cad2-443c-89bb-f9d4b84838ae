<?php

namespace App\Services;

use App\Models\Absence;
use App\Models\ICalendar;
use App\Models\Team;
use App\Models\TeamEvent;
use App\Models\TeamEventSeries;
use App\Models\User;
use App\Types\EventResponseType;
use App\Types\EventSeriesType;
use App\Types\TeamEventMatchType;
use App\Types\TeamEventType;
use App\Types\TeamEventVoteType;
use App\Types\TeamRoleType;
use Illuminate\Database\Eloquent\Builder;

class NumoLiveDemoService {
    public static string $managerUserEmail = '<EMAIL>';
    public static string $memberUserEmail = '<EMAIL>';
    private string $userPassword = 'Tpxs7xw29<n5ayCA';

    private string $trainerTeamName = 'Die wilden Kerle';
    private string $papaTeamName = 'Badminton';

    public function resetDemoData(): void {
        $trainerUser = $this->getOrCreateTrainerUser();
        $teamTrainer = $this->getOrCreateTrainerTeam($trainerUser);
        $this->resetTrainerUser($trainerUser, $teamTrainer);
        $this->resetTrainerTeam($teamTrainer, $trainerUser);

        $papaUser = $this->getOrCreatePapaUser();
        $teamPapa = $this->getOrCreatePapaTeam($papaUser);
        $this->resetPapaUser($papaUser, $teamPapa);
        $this->resetPapaTeam($teamPapa, $papaUser);
    }

    private function getOrCreateTrainerUser(): User {
        return User::where('email', self::$managerUserEmail)
            ->firstOr(function () {
                $user = User::createWithPerson(self::$managerUserEmail, $this->userPassword, 'Trainer', 'Demo');
                $user->markEmailAsVerified();
                return $user;
            });
    }

    private function getOrCreateTrainerTeam(User $trainerUser): Team {
        $teamManager = $trainerUser->person->teamMembers()
            ->whereRelation('statusRole', 'name', TeamRoleType::MANAGER)
            ->firstOr(function () use ($trainerUser) {
                // create team with manager member
                $team = Team::create(['name' => $this->trainerTeamName]);
                // remove default manager from current user
                $team->members()->forceDelete();
                // add user as manager
                $manager = $team->createMember(
                    'Thomas', 
                    TeamRoleType::MANAGER, $trainerUser,
                    joinDate: now()->subMonths(13)
                );
                $manager->roles()->attach($team->getTeamRole(TeamRoleType::TREASURER));
                return $manager;
            });

        // remove from all other teams
        $trainerUser->person->teamMembers()->wherenot('id', $teamManager->id)->forceDelete();

        $team = $teamManager->team;
        $team->trial_ends_at = null;
        $team->save();
        $team->refresh();
        return $team;
    }

    private function resetTrainerTeam(Team $team, User $trainerUser): void {
        // reset name
        $team->name = $this->trainerTeamName;

        // make sure invite link is deleted
        $team->invite()->forceDelete();

        // remove all other members than trainer
        $team->members()->where(function (Builder $builder) use ($trainerUser) {
            $builder
                ->wherePersonId(null)
                ->orWhere('person_id', '!=', $trainerUser->person->id);
        })->forceDelete();

        // add default members
        $joinDate = now()->subMonths(13);
        $team->createMember('Willi', TeamRoleType::MANAGER, joinDate: $joinDate);
        $team->createMember('Vanessa');
        $team->createMember('Fabi', joinDate: $joinDate);
        $team->createMember('Maxi', joinDate: $joinDate);
        $team->createMember('Juli', joinDate: $joinDate);
        $team->createMember('Joschka', joinDate: $joinDate);

        // remove and add task config
        $team->eventTaskConfigs()->forceDelete();
        $taskConfig = $team->eventTaskConfigs()->create(['title' => 'Trikotwäsche']);

        // remove and add ledger
        $team->ledger()->forceDelete();
        $team->ledger()->create()->fines()->create([
            'title'      => 'zu spät kommen',
            'amount'     => money(300),
            'date_begin' => now()->subDays(5),
        ]);

        // Zeitraum erstellen: vor 10 Wochen bis in 6 Wochen
        $team->statsRanges()->forceDelete();
        $team->statsRanges()->create([
            'name'       => 'Demo Zeitraum',
            'start_date' => now()->subWeeks(10)->startOfWeek(),
            'end_date'   => now()->addWeeks(6)->endOfWeek(),
        ]);

        $team->events()->forceDelete();

        // add match1:
        // letzten Samstag: Heim gegen Biestige Biester,
        // Zusage: alle außer Fabi
        // Trikotwäsche: Vanessa,
        $match2 = $team->events()->create([
            'match_opponent_name' => 'Biestige Biester',
            'match_type'          => TeamEventMatchType::HOME->value,
            'event_type'          => TeamEventType::MATCH->value,
            'response_type'       => EventResponseType::AUTO_YES->value,
            'date_begin'          => now()->previous('saturday'),
            'time_begin'          => '10:00:00',
            'address_string'      => 'Am Sportplatz 1, 41334 Nettetal', 
        ]);
        $match2
            ->votes()
            ->whereRelation('teamMember', 'name', '=', 'Fabi')
            ->update(['vote' => TeamEventVoteType::NO->value]);

        $match2->tasks()->create([
            'config_id'      => $taskConfig->id,
            'team_member_id' => $team->members->where('name', 'Vanessa')->firstOrFail()->id,
        ]);

        // add match2:
        // letzten Samstag: Auswärts gegen SV 1906,
        // Zusage: alle außer Vanessa
        // Trikotwäsche: Juli,
        $match2 = $team->events()->create([
            'match_opponent_name' => 'SV 1906',
            'match_type'          => TeamEventMatchType::AWAY->value,
            'event_type'          => TeamEventType::MATCH->value,
            'response_type'       => EventResponseType::AUTO_YES->value,
            'date_begin'          => now()->previous('saturday')->subWeek(),
            'time_begin'          => '10:00:00',
            'address_string'      => 'Am Sportplatz 1, 41334 Nettetal',
        ]);
        $match2
            ->votes()
            ->whereRelation('teamMember', 'name', '=', 'Vanessa')
            ->update(['vote' => TeamEventVoteType::NO->value]);

        $match2->tasks()->create([
            'config_id'      => $taskConfig->id,
            'team_member_id' => $team->members->where('name', 'Juli')->firstOrFail()->id,
        ]);

        // add training
        // Serientermin von vor 6 Wochen bis gestern: Di. + Fr. 17:00 - 18:30 Uhr, automatisch zugesagt
        $eventSeries = TeamEventSeries::create([
            'rrule_string' => (new \RRule\RRule([
                'FREQ'    => 'WEEKLY',
                'DTSTART' => now()->subWeeks(6)->startOfWeek(),
                'UNTIL'   => now()->subDay(),
                'BYDAY'   => ['TU', 'FR'],
            ]))->rfcString(),
        ]);
        $seriesParent = $team->events()->make([
            'series_type'   => EventSeriesType::RECURRING_PARENT->value,
            'title'         => 'Training',
            'event_type'    => TeamEventType::TRAINING->value,
            'response_type' => EventResponseType::AUTO_YES->value,
            'date_begin'    => now()->subWeeks(6)->startOfWeek(),
            'time_begin'    => '17:00:00',
            'time_end'      => '18:30:00',
        ]);
        $seriesParent->series()->associate($eventSeries);
        $seriesParent->save();
        
        // cancel latest training um es im Kalender Abo zu sehen
        $latestTraining = $team->events()->events()->latest()->first();
        if($latestTraining) {
            $latestTraining->cancelled_at = now()->subDays(5);
            $latestTraining->save();
        }

        // pro Training zufällig absagen für 2 zufälligen Member pro Training mit 50% Wahrscheinlichkeit
        $team->events()->events()->each(function (TeamEvent $teamEvent) use ($team) {
            $members = $team->members->filter(fn($member) => $member->person_id === null)->random(2);
            foreach ($members as $member) {
                if (random_int(0, 1) === 1) {
                    $teamEvent->createVote($member, TeamEventVoteType::NO);
                }
            }
        });

        $team->save();
    }

    private function resetTrainerUser(User $trainerUser, Team $teamTrainer): void {
        // nothing to do yet
    }

    private function getOrCreatePapaUser(): User {
        return User::where('email', self::$memberUserEmail)
            ->firstOr(function () {
                $user = User::createWithPerson(self::$memberUserEmail, $this->userPassword, 'Papa', 'Demo');
                $user->markEmailAsVerified();
                return $user;
            });
    }

    private function getOrCreatePapaTeam(User $papaUser): Team {
        $teamManager = $papaUser->person->teamMembers()
            ->whereRelation('statusRole', 'name', TeamRoleType::MANAGER)
            ->firstOr(function () use ($papaUser) {
                // create team with manager member 
                $team = Team::create(['name' => $this->papaTeamName]);
                // remove default manager from current user
                $team->members()->forceDelete();
                // add user as manager
                return $team->createMember('Paul', TeamRoleType::MANAGER, $papaUser);
            });

        // remove from all other teams
        $papaUser->person->teamMembers()->whereNot('id', $teamManager->id)->forceDelete();

        $team = $teamManager->team;
        $team->trial_ends_at = null;
        $team->save();
        $team->refresh();
        return $team;
    }

    private function resetPapaTeam(Team $team, User $papaUser): void {
        // reset name
        $team->name = $this->papaTeamName;

        // remove all other members
        $team->members()->where(function (Builder $builder) use ($papaUser) {
            $builder
                ->wherePersonId(null)
                ->orWhere('person_id', '!=', $papaUser->person->id);
        })->forceDelete();
        // add default members
        $team->createMember('Klaus');
        $team->createMember('Peter');
        $team->createMember('Franz');
        $team->createMember('Tim');
        $team->createMember('Steffi');
        $team->createMember('Ute');
        $team->createMember('Rosi');

        $team->events()->forceDelete();

        // Training morgen 19:30 – 21:00 Uhr
        // Rückmeldung von allen außer Papa und Klaus
        $training = $team->events()->create([
            'series_type'   => EventSeriesType::SINGLE->value,
            'title'         => 'Training',
            'event_type'    => TeamEventType::TRAINING->value,
            'response_type' => EventResponseType::AUTO_YES->value,
            'date_begin'    => now()->addDay(),
            'time_begin'    => '19:30:00',
            'time_end'      => '21:00:00',
        ]);

        // set vote to NO for Tim
        $training
            ->votes()
            ->whereRelation('teamMember', 'name', '=', 'Tim')
            ->update(['vote' => TeamEventVoteType::NO->value]);

        // delete votes for Papa and Klaus that have been created through AUTO_YES
        $training
            ->votes()
            ->where(function (Builder $builder) use ($papaUser) {
                return $builder
                    ->whereRelation('teamMember.person', 'id', '=', $papaUser->person->id)
                    ->orWhereRelation('teamMember', 'name', '=', 'Klaus');
            })
            ->forceDelete();
    }

    private function resetPapaUser(User $papaUser, Team $teamPapa): void {
        // remove absences
        Absence::where('author_id', $papaUser->person->id)->forceDelete();

        // remove iCal subscriptions
        ICalendar::where('author_id', $papaUser->person->id)->forceDelete();

        // create iCal subscriptions for Papa Team Member
        $teamMember = $papaUser->person->teamMembers->firstWhere('team_id', $teamPapa->id);
        $teamMember?->iCalendarSubscriptions()
            ->create(['author_id' => $papaUser->person->id,]);
    }

}
