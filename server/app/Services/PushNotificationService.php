<?php

namespace App\Services;

use App\JsonApi\V1\TeamEvents\TeamEventRequest;
use App\Models\Team;
use App\Models\TeamEvent;
use App\Models\TeamEventVote;
use App\Models\TeamMember;
use App\Models\User;
use App\Notifications\Push\ChangedTeamEventNotification;
use App\Notifications\Push\NewTeamEventNotification;
use App\Notifications\Push\UserJoinedTeamNotification;
use App\Notifications\Push\VoteForUpcomingEventChangedNotification;
use App\Types\FeatureType;
use App\Types\TeamRoleType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;

class PushNotificationService {

    /**
     * @param TeamEvent $teamEvent
     * @param array<string> $updatedKeys
     * @param User|null $currentUser
     *
     * @return void
     */
    function handleOnEventChange(TeamEvent $teamEvent, array $updatedKeys, User|null $currentUser): void {
        if (!ChangedTeamEventNotification::IsSupportedChange($updatedKeys)) {
            return;
        }
        
        if ($teamEvent->isValidRecurringParent()) {
            return;
        }
        
        $team = $teamEvent->team;
        $members = $team->members;
        $members->loadMissing('person.user');

        $members->each(function (TeamMember $member) use ($team, $teamEvent, $updatedKeys, $currentUser) {
            $user = $member->person?->user;
            if ($user && (!$currentUser || $user->id !== $currentUser->id)) {
                $user->notify(new ChangedTeamEventNotification($team, $teamEvent, $updatedKeys));
            }
        });
    }
    
    function handleOnEventCreate(TeamEvent $teamEvent, User|null $currentUser): void {
        $team = $teamEvent->team;
        $members = $team->members;
        $members->loadMissing('person.user');
        
        $members->each(function (TeamMember $member) use ($team, $teamEvent, $currentUser) {
            $user = $member->person?->user;
            if ($user && (!$currentUser || $user->id !== $currentUser->id)) {
                $user->notify(new NewTeamEventNotification($team, $teamEvent));
            }
        });
    }

    function handleOnTeamJoin(User $user, TeamMember $member): void {
        $this->getNotifiableManagers($member->team)->each(function (TeamMember $manager) use ($member, $user) {
            $managerUser = $manager->person?->user;
            $managerUser?->notify(new UserJoinedTeamNotification($user, $member, $member->team));
        });
    }

    /**
     * Handles the event when a vote changes for a team event.
     *
     * This function ensures that the user is the owner of the team member before sending a push notification.
     * It prevents notifications from being sent to trainers when they change votes for their team members.
     * If the event is within the allowed time range, it sends a push notification to all managers of the team
     * who have the notifications feature enabled.
     *
     * @param TeamEventVote $teamEventVote The team event vote instance.
     * @param User $user The user instance.
     *
     * @return void
     */
    function handleOnVoteChange(TeamEventVote $teamEventVote, User $user): void {
        // We ensure that the user is the owner of the team member before sending a push notification
        // this will prevent push notifications being sent to trainers when they change votes for members
        // of their teams (e.g. when syncing votes with WhatsApp or other side channels).
        if (!$user->isOwnerOfTeamMember($teamEventVote->team_member_id)) {
            return;
        }

        if (!$this->isEventWithinAllowedTimeRange($teamEventVote)) {
            return;
        }

        $this->getNotifiableManagers($teamEventVote->teamEvent->team)
            ->reject(fn(TeamMember $manager) => $manager->person?->user?->id === $user->id) // Do not send to the voting manager themselves
            ->each(function (TeamMember $manager) use ($teamEventVote) {
            $memberName = $teamEventVote->teamMember->name;
            $user = $manager->person?->user;

            $user?->notify(new VoteForUpcomingEventChangedNotification($memberName, $teamEventVote->teamEvent, $teamEventVote->vote));
        });
    }

    /**
     * Get a list of all managers for the given team who have associated user accounts and the
     * global push notification feature enabled.
     *
     * @param Team $team The team instance.
     *
     * @return Collection<int, TeamMember> The collection of managers with user accounts.
     */
    protected function getNotifiableManagers(Team $team): Collection {
        // Get a list of all managers for the given team
        /** @var Collection<int, TeamMember> $managers */
        $managers = $team->membersWithRole(TeamRoleType::MANAGER)->get()->load('person.user');

        // Filter out managers that aren't associated with a person and user account
        $managers = $managers->filter(function (TeamMember $manager) {
            return $manager->person !== null && $manager->person->user !== null;
        });

        // Filter out managers who don't have the global
        $managers = $managers->filter(function (TeamMember $manager) {
            return $manager->person && $manager->person->features()->active(FeatureType::NOTIFICATIONS_ENABLED->value);
        });

        return $managers;
    }

    /**
     * Checks if the event's start time is within the allowed time range.
     *
     * This function determines if the event's start time (`date_begin`) is within
     * the range of 2 hours past and 2 days in the future from the current time.
     */
    protected function isEventWithinAllowedTimeRange(TeamEventVote $teamEventVote): bool {
        $dateBegin = $this->mergeDateTime(
            $teamEventVote->teamEvent->date_begin,
            $teamEventVote->teamEvent->time_begin
        );
        $now = Carbon::now();
        $twoHoursPast = $now->copy()->subHours(2);
        $twoDaysFuture = $now->copy()->addDays(2);

        return $dateBegin->between($twoHoursPast, $twoDaysFuture);
    }

    protected function mergeDateTime(Carbon $date, Carbon|string $time): Carbon {
        return $date->copy()->setTimeFrom($time);
    }
}
