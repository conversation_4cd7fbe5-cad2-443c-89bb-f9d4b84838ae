<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

/**
 * Usage:
 *  ddev artisan feature:export features.json
 */
class FeatureExport extends Command {
    protected $signature = 'feature:export {output_file}';
    protected $description = '';

    public function handle(): void {
        $outputFile = $this->argument('output_file');
        $result = [];

        /** @var Collection<int, object{person_id: int, email: string}> $users */
        $users = DB::table('users')->select('person_id', 'email')->get();
        
        foreach ($users as $user) {
            $userData = [
                'person_id' => $user->person_id,
                'email' => $user->email,
                'features' => [],
            ];

            // Get features from the features table based on scope
            $features = DB::table('features')
                ->where('scope', 'App\Models\Person|' . $user->person_id)
                ->get();

            foreach ($features as $feature) {
                if (is_object($feature) && isset($feature->name, $feature->value)) {
                    $userData['features'][] = [
                        'name' => $feature->name,
                        'value' => $feature->value,
                    ];
                }
            }

            $result[] = $userData;
        }
        
        // Write result to the output file
        try {
            file_put_contents($outputFile, json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
            $this->info("Data successfully exported to {$outputFile}");
        } catch (\Exception $e) {
            $this->error("Failed to write to {$outputFile}: " . $e->getMessage());
        }
    }
}
