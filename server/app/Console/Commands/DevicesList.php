<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class DevicesList extends Command {
    protected $signature = 'devices:list {--email=}';
    protected $description = 'Checks all devices if they are still in use. If not, they are deleted.';

    public function handle(): void {
        Utils::failOnNonLocalEnvironment();

        // read --email option into variable
        $email = $this->option('email');

        // Find user_id based on email in users table
        $user_id = DB::table('users')
            ->where('email', $email)
            ->value('id');

        // Retrieve all tokens from personal_access_tokens (tokenable_id)
        $tokens = DB::table('personal_access_tokens')
            ->where('tokenable_id', $user_id)
            ->get();

        $this->info("");
        $this->info("Printing devices by token for user $email:");
        $this->info("");

        // Print all tokens line by line
        foreach ($tokens as $token) {
            $this->info("Token: $token->id");

            // For each token retrieve all devices from the device_info table using the installation_id of the token and the id of the device_info table
            $devices = DB::table('device_info')
                ->where('id', $token->installation_id)
                ->get();

            // Print all devices line by line, indented by 4 spaces
            foreach ($devices as $device) {
                $this->info("    ID: $device->id, Name: $device->name, Model: $device->model, Platform: $device->platform, Manufacturer: $device->manufacturer, OS: $device->os, OS Version: $device->os_version");
            }
        }

        $this->info("");
        $this->info("Printing devices by user_id $user_id:");
        $this->info("");

        // retrieve device_info entries by filtering on user_id directly
        $devices = DB::table('device_info')
            ->where('user_id', $user_id)
            ->get();

        // Print all devices line by line, indented by 4 spaces
        foreach ($devices as $device) {
            // Check if there is a personal_access_token for this device
            $token = DB::table('personal_access_tokens')
                ->where('installation_id', $device->id)
                ->first();

            $tokenId = $token ? $token->id : 'Missing';

            $this->info("ID: $device->id, Token ID: $tokenId, Name: $device->name, Model: $device->model, Platform: $device->platform, Manufacturer: $device->manufacturer, OS: $device->os, OS Version: $device->os_version");
        }

    }
}
