<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

/*
 * This command replaces all non-null fcm_token fields in the personal_access_tokens table with a random string. It
 * can be used when a database backup has been imported for debugging, and you want to avoid sending push notification
 * to real users.
 */
class ReplaceFcmToken extends Command {
    protected $signature = 'token:replace-fcm';
    protected $description = 'Replace non-null fcm_token in personal_access_tokens table with a random string';

    public function __construct() {
        parent::__construct();
    }

    public function handle(): void {
        Utils::failOnNonLocalEnvironment();

        $tokens = DB::table('device_info')
            ->whereNotNull('fcm_token')
            ->get();

        $count = 0;

        foreach ($tokens as $token) {
            DB::table('device_info')
                ->where('id', $token->id)
                ->update(['fcm_token' => Str::random(20)]);

            $count++;
        }

        if ($count > 0) {
            $this->info("Successfully replaced fcm_token for $count tokens.");
        } else {
            $this->info("No fcm_token fields need to be replaced.");
        }
    }
}
