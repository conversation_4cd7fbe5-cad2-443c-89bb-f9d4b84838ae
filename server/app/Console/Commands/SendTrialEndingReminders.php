<?php

namespace App\Console\Commands;

use App\Jobs\SendTrialEndingReminderEmail;
use App\Models\Team;
use App\Models\TeamMember;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SendTrialEndingReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:send-trial-ending-reminders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sendet Erinnerungen an Teams, deren Testzeitraum in 14 Tagen endet';

    /**
     * Execute the console command.
     */
    public function handle(): int {
        $twoWeeksFromNow = Carbon::now()->addDays(14)->startOfDay();
        $endOfDay = Carbon::now()->addDays(14)->endOfDay();

        // Teams finden, deren Testzeitraum in 14 Tagen endet
        $teams = Team::whereBetween('trial_ends_at', [$twoWeeksFromNow, $endOfDay])
            ->whereDoesntHave('activeSubscription')
            ->get();

        $countTeams = 0;
        $countMails = 0;
        foreach ($teams as $team) {
            $team->managersWithAssociatedUser()->with('person.user')->get()
                ->each(function ( $manager) use (&$countMails, $team) {
                    if($manager instanceof TeamMember && $manager->person && $manager->person->user) {
                        SendTrialEndingReminderEmail::dispatch($team, $manager->person->user);
                        $countMails++;
                    }
                });
            $countTeams++;
        }

        $this->info("Ablauf Testzeitraum E-Mails ({$countMails}) für {$countTeams} Teams in die Warteschlange gestellt.");
        
        return self::SUCCESS;
    }
}
