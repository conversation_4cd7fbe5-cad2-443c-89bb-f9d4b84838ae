<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

/**
 * Usage:
 *  ddev artisan feature:set --email=<EMAIL> device.list true
 */
class FeatureSet extends Command {
    protected $signature = 'feature:set {--email=} {feature} {enabled}';
    protected $description = '';

    public function handle(): void {
        // Utils::failOnNonLocalEnvironment();

        // read --email option into variable
        $email = $this->option('email');
        $feature = $this->argument('feature');
        $enabled = filter_var($this->argument('enabled'), FILTER_VALIDATE_BOOLEAN) ? 'true' : 'false';

        $person_id = DB::table('users')
            ->where('email', $email)
            ->value('person_id');

        $feature_id = DB::table('features')
            ->where('scope', 'App\Models\Person|' . $person_id)
            ->where('name', $feature)
            ->value('id');

        if ($feature_id) {
            DB::table('features')
                ->where('id', $feature_id)
                ->update(['value' => $enabled]);
        } else {
            DB::table('features')
                ->insert([
                    'scope' => 'App\Models\Person|' . $person_id,
                    'name' => $feature,
                    'value' => $enabled,
                ]);
        }
    }
}
