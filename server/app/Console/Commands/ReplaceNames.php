<?php

namespace App\Console\Commands;

use App\Models\TeamMember;
use App\Models\User;
use Faker\Factory as Faker;
use Illuminate\Console\Command;

/**
 * This command updates user and associated person names with fake data.
 */
class ReplaceNames extends Command {
    protected $signature = 'user:replace-names';
    protected $description = 'Update user and associated person names with fake data.';

    public function handle(): void {
        Utils::failOnNonLocalEnvironment();
        
        $faker = Faker::create();
        $users = User::with('person')->get();

        foreach ($users as $user) {
            $firstName = $faker->firstName;
            $lastName = $faker->lastName;
            $email = strtolower($firstName . '.' . $lastName . '@numo-app.com');

            $user->email = $email;
            $user->save();

            $user->person->firstname = $firstName;
            $user->person->lastname = $lastName;
            $user->person->save();

            $teamMember = TeamMember::where('person_id', $user->person->id)->first();
            if ($teamMember) {
                $teamMember->name = "{$firstName} {$lastName}";
                $teamMember->save();
            }

            $this->info("Updated user {$user->id} and associated person name to {$firstName} {$lastName}");
        }

        $this->info('All users and associated persons have been updated.');
    }
}
