<?php

namespace App\Console\Commands;

use App\Models\TeamEvent;
use App\Models\TeamMember;
use App\Notifications\Push\EventReminderNotification;
use App\Types\EventReminderType;
use App\Types\TeamEventVoteType;
use Illuminate\Console\Command;

/**
 * SendEventReminders sends event reminder notifications to team members.
 * 
 * Constraints:
 * - Notifications are only sent once per event.
 * - The event must have a reminder type set.
 * - The reminder date must have passed.
 */
class SendEventReminders extends Command
{
    protected $signature = 'notifications:send-event-reminders {--dry-run}';
    protected $description = 'Send event reminders to team members';

    public function handle(): void
    {
        $dryRun = $this->option('dry-run');
        
        $this->info("hello");

        TeamEvent::eventRemindersDue()
            ->notStarted()
            ->notCancelled()
            ->notSeriesParent()
            ->withoutEventReminderSent()
            ->with(['team', 'team.activeMembers'])
            ->whereResponseType(EventReminderType::NONE)
            ->get()
            ->each(function (TeamEvent $event) use ($dryRun) {
                $this->info("Processing event {$event->getEventTitle()}");

                $event->team->activeMembers->each(function ($member) use ($event, $dryRun) {
                    if ($this->hasVotedNo($event, $member)) {
                        $this->info("  Skipping reminder for {$member->name} because they voted no");
                        return;
                    }
                    
                    if ($member->person?->user) {
                        $this->info("  Sending reminder to {$member->person->user->email} for member {$member->name}");
                        
                        if (!$dryRun) {
                            $member->person->user->notify(new EventReminderNotification($event));
                        }
                    }
                });

                if (!$dryRun) {
                    $event->update(['event_reminder_sent' => true]);
                }
            });
    }
    
    function hasVotedNo(TeamEvent $event, TeamMember $member): bool {
        $vote = $event->votes()->where('team_member_id', $member->id)->first();
        return $vote !== null && $vote->vote === TeamEventVoteType::NO;
    }
} 
