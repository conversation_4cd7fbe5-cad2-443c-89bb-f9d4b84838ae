<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Laravel\Pennant\Feature;

/**
 * Usage:
 *  ddev artisan feature:list --email=f.z<PERSON><PERSON><PERSON>@gmail.com
 */
class FeatureList extends Command {
    protected $signature = 'feature:list {--email=}';
    protected $description = '';

    public function handle(): void {
        // Utils::failOnNonLocalEnvironment();

        // read --email option into variable
        $email = $this->option('email');

        // Find user_id based on email in users table
        $person_id = DB::table('users')
            ->where('email', $email)
            ->value('person_id');

        // Get features from features table based on scope (which is a composite of laravel entity name and entity id)
        $features = DB::table('features')
            ->where('scope', 'App\Models\Person|' . $person_id)
            ->get();

        // Print all features line by line
        $this->info("");
        $this->info("Printing features for user $email:");
        $this->info("");

        foreach ($features as $feature) {
            if (isset($feature->name) && isset($feature->value)) {
                $this->info(" - $feature->name: $feature->value");
            }
        }
        $this->info("");
    }
}
