<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class DataAnonymize extends Command {
    protected $signature = 'data:anonymize';
    protected $description = 'Anonymize data (to some degree)';

    public function handle(): void {
        Utils::failOnNonLocalEnvironment();
        
        $this->info('Starting data anonymization...');

        $this->call('token:replace-fcm');
        //$this->call('user:replace-names');
        $this->call('user:reset-passwords');

        $this->info('Data anonymization completed.');
    }
}
