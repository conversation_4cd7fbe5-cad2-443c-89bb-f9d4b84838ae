<?php

namespace App\Console\Commands;

use App\Models\TeamEvent;
use App\Models\TeamMember;
use App\Models\User;
use App\Notifications\Push\VoteReminderNotification;
use App\Types\EventResponseType;
use App\Types\EventSeriesType;
use App\Types\FeatureType;
use App\Types\TeamEventVoteType;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

/**
 * VoteReminderNotifications sends all outstanding vote reminder notifications to users.
 * 
 * Constraints:
 * - The associated person of a team member needs to have the notification feature enabled.
 * - Notifications are only sent to team members who have not yet voted.
 * - Notifications are only sent once per event.
 */
class SendVoteReminders extends Command {
    protected $signature = 'notifications:send-vote-reminders {--dry-run}';

    protected $description = 'Checks if any vote reminders need to be sent out';

    public function handle(): void {
        $dryRun = $this->option('dry-run');
        
        TeamEvent::voteRemindersDue()
            ->withoutVoteReminderSent()
            ->notSeriesParent()
            ->notCancelled()
            ->whereResponseType(EventResponseType::NONE)
            ->get()
            ->each(function (TeamEvent $event) use ($dryRun) {
                
                $notificationUsers = [];

                print "Processing event {$event->getEventTitle()}\n";
                
                // Print all event votes for debugging purposes
                print "  Votes: " . count($event->votes) . "\n";
                $event->votes->each(function ($vote) {
                    print "    Vote: {$vote}\n";
                });
                
                $event->team->members()->each(function (TeamMember $member) use ($event, &$notificationUsers) {
                    $vote = $event->votes()->where('team_member_id', $member->id)->first();
                    $alreadyVoted = $vote !== null && $vote->vote !== TeamEventVoteType::MAYBE;
                    
                    $notificationFeatureEnabled = $member->person?->features()->active(FeatureType::NOTIFICATIONS_ENABLED->value);
                    
                    if ($notificationFeatureEnabled && !$alreadyVoted) {
                        print "  Adding {$member->person?->user?->email} to vote reminder list due to member {$member->name} (voted: {$alreadyVoted}, feature: {$notificationFeatureEnabled})\n";
                        
                        // Append user to $notificationUsers
                        $notificationUsers[] = $member->person?->user;
                    }
                });
                
                // Deduplicate users in $notificationUsers by their id and send notifications
                collect($notificationUsers)->unique('id')->each(function (User|null $user) use ($event, $dryRun) {
                    if (!$dryRun) {
                        $user?->notify(new VoteReminderNotification($event->team, $event));
                    }
                });
                
                if (!$dryRun) {
                    $event->update(['vote_reminder_sent' => true]);
                }
            });
    }
}
