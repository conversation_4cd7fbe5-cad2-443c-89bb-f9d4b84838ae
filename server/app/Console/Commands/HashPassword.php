<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class HashPassword extends Command {
    protected $signature = 'hash:password {password}';
    protected $description = 'Hash a password';

    public function handle(): void {
        $password = $this->argument('password');
        $hashedPassword = Hash::make($password);

        $this->info("Plain Password: $password");
        $this->info("Hashed Password: $hashedPassword");
    }
}
