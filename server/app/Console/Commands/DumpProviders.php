<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class DumpProviders extends Command {
    protected $signature = 'dump:providers';
    protected $description = 'Dump all registered service providers';

    public function handle(): int {
        $app = $this->getLaravel();

        foreach ($app->getLoadedProviders() as $provider => $loaded) {
            $this->line($provider);
        }

        return 0;
    }
}
