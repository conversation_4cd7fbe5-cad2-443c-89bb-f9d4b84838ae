<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class DevicesCleanup extends Command {
    protected $signature = 'devices:cleanup';
    protected $description = 'Checks all devices if they are still in use. If not, they are deleted.';

    public function handle(): void {
        Utils::failOnNonLocalEnvironment();

        // join device_info with personal_access_tokens using the id -> installation_id relation
        $devices = DB::table('device_info')
            ->leftJoin('personal_access_tokens', 'device_info.id', '=', 'personal_access_tokens.installation_id')
            ->select('device_info.id', 'device_info.user_id', 'device_info.name', 'device_info.fcm_token', 'personal_access_tokens.id as token_id')
            ->get();

        // filter all those tokens that have an empty token_id
        $devices = $devices->filter(function ($device) {
            return $device->token_id === null;
        });

        // Print all devices line by line
        foreach ($devices as $device) {
            $this->info("Device: $device->id, User: $device->user_id, Name: $device->name, Token: $device->fcm_token, Token ID: $device->token_id");
        }

        // Delete all devices that are not in use
        foreach ($devices as $device) {
            DB::table('device_info')
                ->where('id', $device->id)
                ->delete();
        }

    }
}
