<?php

namespace App\Console\Commands;

use App\Services\SubscriptionService;
use App\Types\SubscriptionPriceType;
use Illuminate\Console\Command;
use Lara<PERSON>\Cashier\Cashier;
use Stripe\StripeClient;

class StripeSetupProducts extends Command {
    protected $signature = 'stripe:setup-products {--force : Force recreation of products}';
    protected $description = 'Set up initial products and prices in Stripe';

    public function __construct(
        private readonly SubscriptionService $subscriptionService
    ) {
        parent::__construct();
    }

    public function handle(): int {
        $this->info('Setting up Stripe products and prices...');
        
        $stripe = Cashier::stripe();
        $force = $this->option('force');
        
        // Set up tax calculation origin address
        $this->setupTaxCalculationAddress($stripe);
        
        // Set up payment method configuration
        $this->setupPaymentMethodConfiguration($stripe);
        
        $teamPrices = $this->subscriptionService->getTeamPricesNetto();
        $price_cents_netto_team_yearly = $teamPrices->get(SubscriptionPriceType::TEAM_YEARLY->value)?->getAmount();
        $price_cents_netto_team_half_yearly = $teamPrices->get(SubscriptionPriceType::TEAM_HALF_YEARLY->value)?->getAmount();
        $price_cents_netto_team_monthly = $teamPrices->get(SubscriptionPriceType::TEAM_MONTHLY->value)?->getAmount();
        
        // Define products with their prices
        $products = [
            [
                'name'                 => 'Numo Team Lizenz',
                'description'          => 'Lizenz für ein Team in Numo',
                'lookup_key'           => config('cashier.lookup_keys.product.team'),
                'tax_code'             => 'txcd_10103001', // SaaS tax code
                'statement_descriptor' => 'Numo Team Jahresabo', // Payment description on statement
                'prices'               => [
                    [
                        'currency'     => config('cashier.currency', 'eur'),
                        'unit_amount'  => $price_cents_netto_team_yearly,
                        'tax_behavior' => 'exclusive',
                        'recurring'    => [
                            'interval' => 'year',
                        ],
                        'lookup_key' => config('cashier.lookup_keys.price.team_yearly'),
                    ],
                    [
                        'currency'     => config('cashier.currency', 'eur'),
                        'unit_amount'  => $price_cents_netto_team_half_yearly,
                        'tax_behavior' => 'exclusive',
                        'recurring'    => [
                            'interval'       => 'month',
                            'interval_count' => 6,
                        ],
                        'lookup_key' => config('cashier.lookup_keys.price.team_half_yearly'),
                    ],
                    [
                        'currency'     => config('cashier.currency', 'eur'),
                        'unit_amount'  => $price_cents_netto_team_monthly, 
                        'tax_behavior' => 'exclusive',
                        'recurring'    => [
                            'interval'       => 'month',
                            'interval_count' => 1,
                        ],
                        'lookup_key' => config('cashier.lookup_keys.price.team_monthly'),
                    ],
                ],
            ],
        ];
        
        foreach ($products as $productData) {
            $this->createOrUpdateProduct($stripe, $productData, $force);
        }
        
        $this->info('Stripe products and prices setup completed successfully!');
        return 0;
    }
    
    /**
     * @param StripeClient $stripe
     * @param array<string, mixed> $productData
     * @param bool $force
     */
    private function createOrUpdateProduct(StripeClient $stripe, array $productData, bool $force): void {
        $lookupKey = $productData['lookup_key'];
        
        // Check if product already exists
        $existingProducts = $stripe->products->search([
            'query' => "active:'true' AND metadata['lookup_key']:'" . $lookupKey . "'",
        ]);
        
        if (count($existingProducts->data) > 0 && !$force) {
            $product = $existingProducts->data[0];
            $this->info("Product '" . $productData['name'] . "' already exists with ID: {$product->id}");
        } else {
            // If product exists and force is true, archive the old one
            if (count($existingProducts->data) > 0 && $force) {
                foreach ($existingProducts->data as $existingProduct) {
                    $stripe->products->update($existingProduct->id, [
                        'metadata' => ['lookup_key' => null],
                        'active'   => false
                    ]);
                    $this->info("Archived existing product: {$existingProduct->id}");
                }
            }
            
            // Create new product
            $product = $stripe->products->create([
                'name'                 => $productData['name'],
                'description'          => $productData['description'],
                'tax_code'             => $productData['tax_code'] ?? null,
                'statement_descriptor' => $productData['statement_descriptor'] ?? null,
                'metadata'             => [
                    'lookup_key' => $lookupKey,
                ],
            ]);
            
            $this->info("Created new product '" . $productData['name'] . "' with ID: {$product->id}");
        }
        
        // Create or update prices for the product
        /** @var array<int, array<string, mixed>> $prices */
        $prices = $productData['prices'] ?? [];
        foreach ($prices as $priceData) {
            $this->createOrUpdatePrice($stripe, $product->id, $priceData, $force);
        }
    }
    
    /**
     * @param StripeClient $stripe
     * @param string $productId
     * @param array<string, mixed> $priceData
     * @param bool $force
     */
    private function createOrUpdatePrice(StripeClient $stripe, string $productId, array $priceData, bool $force): void {
        $lookupKey = $priceData['lookup_key'];
        
        // Check if price already exists
        $existingPrices = $stripe->prices->search([
            'query' => 'lookup_key:"' . $lookupKey . '"',
        ]);
        
        if (count($existingPrices->data) > 0) {
            $this->info("Price for lookup key '" . $lookupKey . "' already exists with ID: {$existingPrices->data[0]->id}");
            if (!$force) {
                return;
            }
        }
        
        // If price exists and force is true, archive the old one
        if (count($existingPrices->data) > 0 && $force) {
            foreach ($existingPrices->data as $existingPrice) {
                // remove the lookup_key and deactivate the price
                $stripe->prices->update($existingPrice->id, [
                    'lookup_key' => null,
                    'active'     => false
                ]);
                $this->info("Archived existing price: {$existingPrice->id}");
            }
        }
        
        // Create new price
        $price = $stripe->prices->create([
            'product'      => $productId,
            'currency'     => $priceData['currency'],
            'unit_amount'  => $priceData['unit_amount'],
            'tax_behavior' => $priceData['tax_behavior'] ?? 'exclusive',
            'recurring'    => $priceData['recurring'],
            'lookup_key'   => $lookupKey,
        ]);
        
        $this->info("Created new price with lookup key '" . $lookupKey . "' and ID: {$price->id}");
    }
    /**
     * Set up the tax calculation origin address in Stripe
     * 
     * @param StripeClient $stripe
     * @return void
     */
    private function setupTaxCalculationAddress(StripeClient $stripe): void {
        try {
            $stripe->tax->settings->update([
                'defaults' => [
                    'tax_code' => 'txcd_10103001', // SaaS tax code
                ],
                'head_office' => [
                    'address' => [
                        'line1'       => 'Hinsbecker Str. 32',
                        'city'        => 'Nettetal',
                        'postal_code' => '41334',
                        'country'     => 'DE',
                    ],
                ],
            ]);
            $this->info('Tax calculation origin address set up successfully.');
        } catch (\Exception $e) {
            $this->error('Failed to set up tax calculation origin address: ' . $e->getMessage());
        }
    }
    /**
     * Set up payment method configuration in Stripe
     * 
     * @param StripeClient $stripe
     * @return void
     */
    private function setupPaymentMethodConfiguration(StripeClient $stripe): void {
        try {
            // Check if configuration already exists
            $existingConfigs = $stripe->paymentMethodConfigurations->all(['limit' => 10]);
            $configName = 'Default';
            $existingConfig = null;
            
            foreach ($existingConfigs->data as $config) {
                if ($config->name === $configName) {
                    $existingConfig = $config;
                    break;
                }
            }
            
            // Create or update payment method configuration
            $configParams = [
                'name' => $configName,
                'card' => [
                    'display_preference' => [
                        'preference' => 'on',
                    ],
                ],
                'paypal' => [
                    'display_preference' => [
                        'preference' => 'on',
                    ],
                ],
                'sepa_debit' => [ // Lastschrift (SEPA Direct Debit)
                    'display_preference' => [
                        'preference' => 'on',
                    ],
                ],
                'apple_pay' => [
                    'display_preference' => [
                        'preference' => 'on',
                    ],
                ],
                'google_pay' => [
                    'display_preference' => [
                        'preference' => 'on',
                    ],
                ],
            ];
            
            if ($existingConfig) {
                $config = $stripe->paymentMethodConfigurations->update($existingConfig->id, $configParams);
                $this->info("Updated payment method configuration '$configName' with ID: {$config->id}");
            } else {
                $config = $stripe->paymentMethodConfigurations->create($configParams);
                $this->info("Created payment method configuration '$configName' with ID: {$config->id}");
            }
        } catch (\Exception $e) {
            $this->error('Failed to set up payment method configuration: ' . $e->getMessage());
        }
    }
}
