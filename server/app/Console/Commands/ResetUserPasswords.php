<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

/**
 * This command resets all user passwords to a default value for local debugging.
 */
class ResetUserPasswords extends Command {
    protected $signature = 'user:reset-passwords';
    protected $description = 'Resets all user passwords to a default value for local debugging';

    public function handle(): void {
        Utils::failOnNonLocalEnvironment();

        User::query()->update(['password' => Hash::make('12345678')]);

        $this->info('All user passwords have been reset to the default.');
    }
}
