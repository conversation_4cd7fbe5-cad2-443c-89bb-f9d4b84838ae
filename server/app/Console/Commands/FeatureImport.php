<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

/**
 * Usage:
 *  ddev artisan feature:import features.json
 */
class FeatureImport extends Command {
    protected $signature = 'feature:import {input_file}';
    protected $description = 'Import features from a JSON file and apply them to the users';

    public function handle(): void {
        $inputFile = $this->argument('input_file');

        if (!file_exists($inputFile)) {
            $this->error("The file {$inputFile} does not exist.");
            return;
        }

        try {
            $fileContent = file_get_contents($inputFile);
            if ($fileContent === false) {
                $this->error("Failed to read the file: {$inputFile}");
                return;
            }
            
            $data = json_decode($fileContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->error("Invalid JSON in the file: " . json_last_error_msg());
                return;
            }

            if (!is_array($data)) {
                $this->error("Invalid JSON structure, expected an array of user data.");
                return;
            }

            foreach ($data as $userData) {
                $personId = $userData['person_id'] ?? null;
                $features = $userData['features'] ?? [];

                if (!$personId || !is_array($features)) {
                    $this->error("Invalid data format for user: " . json_encode($userData));
                    continue;
                }

                foreach ($features as $feature) {
                    $featureName = $feature['name'] ?? null;
                    $featureValue = $feature['value'] ?? null;

                    if (!$featureName || is_null($featureValue)) {
                        $this->warn("Skipping invalid feature for person_id {$personId}: " . json_encode($feature));
                        continue;
                    }

                    // Check if the feature already exists
                    $existingFeature = DB::table('features')
                        ->where('scope', 'App\Models\Person|' . $personId)
                        ->where('name', $featureName)
                        ->first();

                    if ($existingFeature !== null && property_exists($existingFeature, 'id')) {
                        // Update the existing feature
                        DB::table('features')
                            ->where('id', $existingFeature->id)
                            ->update(['value' => $featureValue]);
                    } else {
                        // Insert a new feature
                        DB::table('features')->insert([
                            'scope' => 'App\Models\Person|' . $personId,
                            'name' => $featureName,
                            'value' => $featureValue,
                        ]);
                    }
                }
            }

            $this->info("Features successfully imported from {$inputFile}");
        } catch (\Exception $e) {
            $this->error("Failed to import features: " . $e->getMessage());
        }
    }
}
