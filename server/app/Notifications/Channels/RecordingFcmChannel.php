<?php

namespace App\Notifications\Channels;

use App\Models\TrackedNotification;
use App\Notifications\Push\Base\PushNotification;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Kreait\Firebase\Exception\FirebaseException;
use Kreait\Firebase\Exception\Messaging\InvalidArgument;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;

/**
 * RecordingFcmChannel is a decorator for the FcmChannel that records sent push notifications in the database. It's designed to be used
 * as a drop-in replacement for the FcmChannel and implemented as a decorator to avoid race conditions when sending notifications through
 * different channels.
 */
class RecordingFcmChannel {
    protected FcmChannel $fcmChannel;

    // FcmChannel should be injected via <PERSON><PERSON>'s service container
    public function __construct(FcmChannel $fcmChannel) {
        $this->fcmChannel = $fcmChannel;
    }

    /**
     * @throws FirebaseException
     *
     * @return Collection<int, object>|null
     */
    public function send(object $notifiable, Notification $notification): ?Collection {
        try {
            if (!$this->isPushNotification($notification)) {
                Log::warning("Unsupported notification of type " . get_class($notification) . " for notifiable " . get_class($notifiable) . " with id " . $notifiable->id . " and uuid " . $notifiable->uuid . " in RecordingChannel::send()");
                return $this->fcmChannel->send($notifiable, $notification);
            }

            // note(fabzo): toFcm is again a magic Laravel notification function. Instead of providing a stub,
            // we are just disabling PHPStan for this line.
            /** @noinspection PhpPossiblePolymorphicInvocationInspection */
            // @phpstan-ignore-next-line
            $fcmMessage = $notification->toFcm($notifiable);

            $this->handleFirebaseMessage($notifiable, $fcmMessage);

            return $this->fcmChannel->send($notifiable, $notification);
        } catch (InvalidArgument $e) {
            if (str_contains($e->getMessage(), 'No registration tokens provided')) {
                // Not logging this right now, as it will be sent to us via Sentry on every occasion. I think this is the best
                // we can do here right now.

                // Log::error('Failed to send FCM notification: No registration tokens provided');
                return null;
            }

            Log::error('Failed to send FCM notification: ' . $e->getMessage());
            return null;
        }
    }

    private function isPushNotification(Notification $notification): bool {
        return $notification instanceof PushNotification;
    }

    private function handleFirebaseMessage(object $notifiable, FcmMessage $fcmMessage): void {
        $data = $fcmMessage->data;

        if ($data === null || !isset($data['uuid'])) {
            return;
        }

        $entry = new TrackedNotification([
            'message_title' => $fcmMessage->notification->title,
            'message_type'  => 'fcm',
            'message_uuid'  => $data['uuid'],
            'sent_at'       => now(),
        ]);

        $entry->notifiable()->associate($notifiable);
        $entry->save();
    }

}
