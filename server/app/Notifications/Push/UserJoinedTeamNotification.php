<?php

namespace App\Notifications\Push;

use App\Models\Team;
use App\Models\TeamMember;
use App\Models\User;
use App\Notifications\Push\Base\PushNotification;
use App\Notifications\Push\Base\PushNotificationData;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Lang;

class UserJoinedTeamNotification extends PushNotification implements ShouldQueue {
    use Queueable;

    public function __construct(User $user, TeamMember $member, Team $team) {
        // Link <frontend>/team/9c934e8c-4362-4847-84dc-ef79e83c29d0
        /** @var string $baseUrl */
        $baseUrl = config('app.frontend.url');
        $teamRoute = '/team';

        $fullPath = $this->join_url_parts($baseUrl, $teamRoute, $team->id);

        $message = $this->constructMessage($user->person?->getFullName() ?? '', $member->name, $team->name);

        parent::__construct(
            trans('notifications.user_joined.title'),
            $message,
            new PushNotificationData(deepLink: $fullPath),
        );
    }

    function constructMessage(string $personName, string $memberName, string $teamName): string {
        /** @var string */
        return Lang::get('notifications.user_joined.body', [
            'name' => $personName,
            'member' => $memberName,
            'team' => $teamName,
        ]);
    }

}
