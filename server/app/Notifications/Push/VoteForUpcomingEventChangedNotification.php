<?php

namespace App\Notifications\Push;

use App\Models\TeamEvent;
use App\Notifications\Push\Base\PushNotification;
use App\Notifications\Push\Base\PushNotificationData;
use App\Types\TeamEventVoteType;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;

class VoteForUpcomingEventChangedNotification extends PushNotification implements ShouldQueue {
    use Queueable;

    public function __construct(string $memberName, TeamEvent $event, TeamEventVoteType $vote) {
        // Link <frontend>/events/show/9c934e8c-4362-4847-84dc-ef79e83c29d0
        /** @var string $baseUrl */
        $baseUrl = config('app.frontend.url');
        $eventRoute = '/events/show';

        $fullPath = $this->join_url_parts($baseUrl, $eventRoute, $event->id);
        $dayOfWeek = $event->date_begin->locale('de_DE')->translatedFormat('l');
        $localizedVote = trans('votes.' . $vote->value);

        $message = $this->constructMessage($memberName, ucfirst($event->getEventTitle()), $dayOfWeek, $vote->value, $localizedVote);

        parent::__construct(
            trans('votes.vote_change_title'),
            $message,
            new PushNotificationData(deepLink: $fullPath),
        );
    }

    function constructMessage(string $memberName, string $eventTitle, string $dayOfWeek, string $voteValue, string $localizedVote): string {
        if ($voteValue === 'maybe') {
            return Lang::get('votes.messages.maybe', [
                'fullName' => $memberName,
                'eventTitle' => $eventTitle,
                'dayOfWeek' => $dayOfWeek
            ]);
        }
        return Lang::get('votes.messages.default', [
            'fullName' => $memberName,
            'eventTitle' => $eventTitle,
            'dayOfWeek' => $dayOfWeek,
            'localizedVote' => $localizedVote
        ]);
    }

}
