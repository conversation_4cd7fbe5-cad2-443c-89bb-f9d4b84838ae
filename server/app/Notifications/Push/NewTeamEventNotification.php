<?php

namespace App\Notifications\Push;

use App\Models\Team;
use App\Models\TeamEvent;
use App\Notifications\Push\Base\PushNotification;
use App\Notifications\Push\Base\PushNotificationData;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Lang;

class NewTeamEventNotification extends PushNotification implements ShouldQueue {
    use Queueable;

    public function __construct(Team $team, TeamEvent $teamEvent) {
        // Link <frontend>/events/show/9cfbc862-c74f-48eb-bfb5-82d6ec3b5594
        /** @var string $baseUrl */
        $baseUrl = config('app.frontend.url');
        $teamEventRoute = '/events/show/';

        // For series events, we want to link to the first child event, not the parent
        $targetEventId = $teamEvent->id;
        $targetEvent = $teamEvent;

        if ($teamEvent->isValidRecurringParent() && $teamEvent->series) {
            // Get the first child event of the series (if available)
            $firstChildEvent = $teamEvent->series->events()->orderBy('date_begin')->orderBy('time_begin')->first();
            
            if ($firstChildEvent) {
                $targetEventId = $firstChildEvent->id;
                $targetEvent = $firstChildEvent;
            }
        }

        $fullPath = $this->join_url_parts($baseUrl, $teamEventRoute, $targetEventId);

        // Construct the title with the event's date and time
        $formattedDate = $targetEvent->date_begin->format('d.m.Y');
        /** @var Carbon $timeBegin */
        $timeBegin = $targetEvent->time_begin;
        $formattedTime = $timeBegin->format('H:i');

        if ($teamEvent->isValidRecurringParent() && $teamEvent->series) {
            $timeSuffix = trans('notifications.team_event_created.time_suffix');
            $formattedTime = $targetEvent->dateTimeBegin()->format('H:i') . ' ' . $timeSuffix;
            $rrule = $teamEvent->series->getRRule();
            $humanReadableRrule = $rrule->humanReadable([
                'locale' => app()->getLocale(),
                'date_formatter' => fn(\DateTime $date) => $date->format('d.m.Y'),
                'include_until' => $rrule->isFinite(),
            ]);
            
            $title = trans('notifications.team_event_series_created.title', [
                'team' => $team->name
            ]);
            
            $message = $this->constructMessage(
                $teamEvent->getEventTitle(),
                $team->name,
                $formattedTime,
                $humanReadableRrule
            );
        } else {
            $baseTitle = trans('notifications.team_event_created.title');
            $timeSuffix = trans('notifications.team_event_created.time_suffix'); 
            $title = "{$baseTitle}: {$formattedDate} - {$formattedTime} {$timeSuffix}";
            $message = $this->constructMessage(
                $teamEvent->getEventTitle(),
                $team->name,
            );
        }

        parent::__construct(
            $title,
            $message,
            new PushNotificationData(deepLink: $fullPath),
        );
    }

    function constructMessage(string $eventName, string $teamName, ?string $time = null, ?string $rrule = null): string {
        if ($time && $rrule) {
            /** @var string */
            return Lang::get('notifications.team_event_series_created.body', [
                'eventName' => $eventName,
                'time' => $time,
                'rrule' => $rrule
            ]);
        }
        
        /** @var string */
        return Lang::get('notifications.team_event_created.body', [
            'eventName' => $eventName,
            'team' => $teamName,
        ]);
    }

}
