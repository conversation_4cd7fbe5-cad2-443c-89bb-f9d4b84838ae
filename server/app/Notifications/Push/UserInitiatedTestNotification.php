<?php

namespace App\Notifications\Push;

use App\Notifications\Push\Base\PushNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;

class UserInitiatedTestNotification extends PushNotification implements ShouldQueue {
    use Queueable;

    public function __construct() {
        parent::__construct(
            'Numo - Test Benachrichtigung',
            'Dies ist eine Test Benachrichtigung',
        );
    }
}
