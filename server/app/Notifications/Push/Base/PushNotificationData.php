<?php

declare(strict_types=1);

namespace App\Notifications\Push\Base;

use Illuminate\Support\Str;

/**
 * Represents the data for a push notification.
 */
class PushNotificationData {
    /**
     * Constructs a new instance of PushNotificationData.
     *
     * @param string $deepLink The URL that the app should open when the user taps the notification. Can be empty.
     */
    public function __construct(
        public string $deepLink = "",
        public string $uuid = "",
    ) {
        if ($this->uuid === "") {
            $this->uuid = Str::orderedUuid()->toString();
        }
    }
}
