<?php

namespace App\Notifications\Push\Base;

use App\Notifications\Channels\RecordingFcmChannel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\Exceptions\CouldNotSendNotification;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\AndroidConfig;
use NotificationChannels\Fcm\Resources\AndroidFcmOptions;
use NotificationChannels\Fcm\Resources\AndroidNotification;
use NotificationChannels\Fcm\Resources\ApnsConfig;
use NotificationChannels\Fcm\Resources\ApnsFcmOptions;

class PushNotification extends Notification implements ShouldQueue {
    use Queueable;

    public function __construct(
        public string                $title,
        public string                $body,
        public ?PushNotificationData $data = null,
    ) {
        if ($this->data === null) {
            // If no data is provided, create a new instance containing a fresh uuid
            $this->data = new PushNotificationData();
        }
    }

    /**
     * @return class-string[]
     */
    public function via(object $notifiable): array {
        return [RecordingFcmChannel::class];
    }

    public function getTitle(): string {
        return $this->title;
    }

    public function getBody(): string {
        return $this->body;
    }

    public function toFcm(object $notifiable): FcmMessage {
        $message = FcmMessage::create();

        if ($this->data !== null) {
            // Throws CouldNotSendNotification->invalidPropertyInArray if keys aren't strings
            $message = $message->data((array)$this->data);
        }

        return $message->notification(\NotificationChannels\Fcm\Resources\Notification::create()
            ->title($this->getTitle())
            ->body($this->getBody()))
            ->custom([
                'android' => [
                    'notification' => [
                        'color' => '#0A0A0A',
                    ],
                    'fcm_options'  => [
                        'analytics_label' => 'analytics_android',
                    ],
                ],
                'apns'    => [
                    'fcm_options' => [
                        'analytics_label' => 'analytics_ios',
                    ],
                ],
            ]);
    }

    // optional method when using kreait/laravel-firebase:^3.0, this method can be omitted, defaults to the default project
    public function fcmProject(object $notifiable, string $message): string {
        // $message is what is returned by `toFcm`
        return 'app'; // name of the firebase project to use
    }

    public function uuid(): string|null {
        if ($this->data == null || $this->data->uuid == null) {
            return null;
        }
        return $this->data->uuid;
    }

    /**
     * Convenience method to join URL parts when constructing
     * the deep link in concert implementations of the push notification.
     */
    function join_url_parts(string ...$parts): string {
        return implode('/', array_map(function ($part) {
            return trim($part, '/');
        }, $parts));
    }
}
