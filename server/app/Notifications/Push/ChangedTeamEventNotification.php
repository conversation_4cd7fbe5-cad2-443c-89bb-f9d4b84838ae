<?php

namespace App\Notifications\Push;

use App\Models\Team;
use App\Models\TeamEvent;
use App\Notifications\Push\Base\PushNotification;
use App\Notifications\Push\Base\PushNotificationData;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Lang;

class ChangedTeamEventNotification extends PushNotification implements ShouldQueue {
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @param Team $team
     * @param TeamEvent $teamEvent
     * @param array<string> $importantUpdates
     */
    public function __construct(Team $team, TeamEvent $teamEvent, array $importantUpdates) {
        /** @var string $baseUrl */
        $baseUrl = config('app.frontend.url');
        $teamEventRoute = '/events/show/';
        $fullPath = $this->join_url_parts($baseUrl, $teamEventRoute, $teamEvent->id);

        if ($teamEvent->wasRecentlyCreated) {
            // For new events, include the date/time in the title.
            $formattedDate = $teamEvent->date_begin->format('d.m.Y');
            
            // time_begin is a string, but the eloquent accessor logic converts it into a Carbon instance
            // that's why its type shows up as "mixed". We can safely assume it's a Carbon instance here.
            /** @var Carbon $timeBegin */
            $timeBegin = $teamEvent->time_begin;
            $formattedTime = $timeBegin->format('H:i');


            $title = "Neuer Termin: {$formattedDate} - {$formattedTime} Uhr";
            $message = "";
        } else {
            $bodyKey = self::determineBodyKey($importantUpdates);
            $message = $this->constructMessage(
                $bodyKey,
                $teamEvent->getEventTitle(),
                $team->name,
                $teamEvent->dateTimeBegin(),
                $teamEvent->time_meet,
                $teamEvent->address_string,
                $teamEvent->dateTimeEnd()
            );
            $title = trans('notifications.team_event_changed.title');
        }

        parent::__construct(
            $title,
            $message,
            new PushNotificationData(deepLink: $fullPath)
        );
    }

    function constructMessage(
        string $bodyKey,
        string $eventName,
        string $teamName,
        Carbon $datetime,
        ?string $meetingTime,
        ?string $meetingLocation,
        Carbon $timeEnd
    ): string {
        // Format the main datetime as d.m.Y H:i (without seconds)
        $formattedDateTime = $datetime->format('d.m.Y H:i');
        // Format meeting time if provided
        $formattedMeetingTime = $meetingTime ? Carbon::parse($meetingTime)->format('H:i') : null;

        // note: The calculation of meeting_length needs to be changed if we ever support events going over day boundaries
        $length = $datetime->diffForHumans($timeEnd);

        /** @var string $translation */
        $translation = Lang::get($bodyKey, [
            'eventName'        => $eventName,
            'team'             => $teamName,
            'datetime'         => $formattedDateTime . " Uhr",
            'meeting_time'     => $formattedMeetingTime. " Uhr",
            'meeting_location' => $meetingLocation,
            'meeting_length'   => $length,
        ]);
        
        return $translation;
    }

    /**
     * Check if the notification is supported for the given changes.
     *
     * @param array<string> $updatedKeys
     *
     * @return bool
     */
    public static function IsSupportedChange(array $updatedKeys): bool {
        return self::determineBodyKey($updatedKeys) !== '';
    }

    /**
     * This function maps TeamEventController.listOfImportantUpdates() results
     * to the correct notification body in lang/en/notifications.php.
     *
     * @param array<string> $updatedKeys
     *
     * @return string
     */
    private static function determineBodyKey(array $updatedKeys): string {
        if (in_array('dateBegin', $updatedKeys)) {
            return 'notifications.team_event_changed.meeting_rescheduled';
        }
        if (in_array('timeBegin', $updatedKeys) && in_array('addressString', $updatedKeys)) {
            return 'notifications.team_event_changed.time_begin_and_location_changed';
        }
        if (in_array('timeBegin', $updatedKeys)) {
            return 'notifications.team_event_changed.meeting_rescheduled';
        }
        if (in_array('addressString', $updatedKeys)) {
            return 'notifications.team_event_changed.meeting_location_changed';
        }
        if (in_array('timeMeet', $updatedKeys)) {
            return 'notifications.team_event_changed.meeting_time_changed';
        }

        return '';
    }
}
