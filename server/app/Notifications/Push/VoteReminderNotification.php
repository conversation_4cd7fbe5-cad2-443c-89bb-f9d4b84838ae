<?php

namespace App\Notifications\Push;

use App\Models\Team;
use App\Models\TeamEvent;
use App\Models\TeamMember;
use App\Models\User;
use App\Notifications\Push\Base\PushNotification;
use App\Notifications\Push\Base\PushNotificationData;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Lang;

class VoteReminderNotification extends PushNotification implements ShouldQueue {
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @param Team $team
     * @param TeamEvent $teamEvent
     */
    public function __construct(Team $team, TeamEvent $teamEvent) {
        // Link <frontend>/events/show/9cfbc862-c74f-48eb-bfb5-82d6ec3b5594
        /** @var string $baseUrl */
        $baseUrl = config('app.frontend.url');
        $teamEventRoute = '/events/show/';

        $fullPath = $this->join_url_parts($baseUrl, $teamEventRoute, $teamEvent->id);

        $message = $this->constructMessage(
            $teamEvent->getEventTitle(),
            $team->name,
        );

        parent::__construct(
            trans('notifications.vote_reminder.title'),
            $message,
            new PushNotificationData(deepLink: $fullPath),
        );
    }

    function constructMessage(string $eventName, string $teamName): string {
        /** @var string */
        return Lang::get('notifications.vote_reminder.body', [
            'eventName' => $eventName,
            'team'      => $teamName,
        ]);
    }
}
