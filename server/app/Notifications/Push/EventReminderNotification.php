<?php

namespace App\Notifications\Push;

use App\Models\TeamEvent;
use App\Models\TeamMember;
use App\Models\Team;
use App\Notifications\Push\Base\PushNotification;
use App\Notifications\Push\Base\PushNotificationData;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Lang;

class EventReminderNotification extends PushNotification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @param TeamEvent $event
     */
    public function __construct(TeamEvent $event)
    {
        /** @var string $baseUrl */
        $baseUrl = config('app.frontend.url');
        $eventRoute = '/events/show/';

        $fullPath = $this->join_url_parts($baseUrl, $eventRoute, $event->id);

        $dateTimeBegin = $event->dateTimeBegin();
        $time = $dateTimeBegin->format('H:i');
        $dateTimeText = $this->formatDateTime($dateTimeBegin, $time);

        $message = $this->constructMessage(
            $event->getEventTitle(),
            $event->team->name,
            $dateTimeText
        );

        parent::__construct(
            trans('notifications.event_reminder.title'),
            $message,
            new PushNotificationData(deepLink: $fullPath),
        );
    }

    private function formatDateTime(\DateTime $dateTime, string $time): string
    {
        $today = new \DateTime('today');
        $tomorrow = new \DateTime('tomorrow');

        if ($dateTime->format('Y-m-d') === $today->format('Y-m-d')) {
            // note(fabzo) Lang::get is a bit messy and can return a string or array depending on the input
            //             we are goign to trust that the language files are correct and always return a string
            /** @var string */
            return Lang::get('notifications.event_reminder.datetime.today', ['time' => $time]);
        }

        if ($dateTime->format('Y-m-d') === $tomorrow->format('Y-m-d')) {
            /** @var string */
            return Lang::get('notifications.event_reminder.datetime.tomorrow', ['time' => $time]);
        }

        /** @var string */
        return Lang::get('notifications.event_reminder.datetime.other', [
            'date' => $dateTime->format('d.m.Y'),
            'time' => $time
        ]);
    }

    function constructMessage(string $eventTitle, string $teamName, string $dateTimeText): string
    {
        /** @var string */
        return Lang::get('notifications.event_reminder.body', [
            'eventTitle' => $eventTitle,
            'teamName' => $teamName,
            'dateTimeText' => $dateTimeText,
        ]);
    }
} 
