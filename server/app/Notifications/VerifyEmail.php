<?php

namespace App\Notifications;

use App\Mail\VerificationEmail;
use App\Models\Invite;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;

class VerifyEmail extends Notification {
    use Queueable;

    public function __construct(
        public ?Invite $invite = null
    ) {}

    /**
     * @return string[]
     */
    public function via(User $user): array {
        return ['mail'];
    }

    public function toMail(User $user): Mailable {
        $verifyURL = $this->getVerificationUrl($user);

        return (new VerificationEmail($user->person->firstname, $user, $verifyURL))->to($user->email);
    }

    /**
     * @return string[]
     */
    public function toArray(User $user): array {
        return [];
    }

    protected function getVerificationUrl(User $user): string {
        $parameters = [
            'token'     => $user->email_verification_token,
            'emailHash' => sha1($user->getEmailForVerification()),
        ];
        if (isset($this->invite)) {
            $parameters['inviteToken'] = $this->invite->token;
        }
        return URL::route(
            'frontend.verification.verify',
            $parameters,
            false
        );
    }
}
