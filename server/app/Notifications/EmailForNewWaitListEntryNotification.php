<?php

namespace App\Notifications;

use App\Mail\NewWaitListEntryMail;
use App\Models\RegistrationAllowlist;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Notifications\Notification;

class EmailForNewWaitListEntryNotification extends Notification implements ShouldQueue {
    use Queueable;

    public function __construct() {}

    /**
     * @return string[]
     */
    public function via(RegistrationAllowlist $waitListEntry): array {
        return ['mail'];
    }

    public function toMail(RegistrationAllowlist $waitListEntry): Mailable {
        return (new NewWaitListEntryMail())
            ->to($waitListEntry->email);
    }

    /**
     * @return string[]
     */
    public function toArray(RegistrationAllowlist $waitListEntry): array {
        return [];
    }
}
