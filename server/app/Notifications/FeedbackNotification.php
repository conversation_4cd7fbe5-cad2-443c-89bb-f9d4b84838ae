<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class FeedbackNotification extends Notification implements ShouldQueue {
    use Queueable;

    public function __construct(
        public string $name = "",
        public string $email = "",
        public string $subject = "",
        public string $message = "",
    ) {}

    /**
     * @return string[]
     */
    public function via(object $notifiable): array {
        return ['mail'];
    }

    public function toMail(object $notifiable): MailMessage {
        return (new MailMessage)
            ->from('<EMAIL>', 'Numo Feedback')
            ->bcc('<EMAIL>')
            ->subject('Feedback: '.$this->subject)
            ->view('emails.contact', [
                'name'         => $this->name,
                'email'        => $this->email,
                'subject'      => $this->subject,
                // Rename message to user_message because message is a magic variable
                // containing a reference to the current message object within the template
                'user_message' => $this->message,
            ]);
    }

    /**
     * @return array<string, string>
     */
    public function toArray(object $notifiable): array {
        return [
            'name'    => $this->name,
            'email'   => $this->email,
            'subject' => $this->subject,
            'message' => $this->message,
        ];
    }
}
