<?php

namespace App\Mail;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class PasswordResetEmail extends Mailable {
    use Queueable, SerializesModels;

    public function __construct(
        public string $firstname,
        public User   $user,
        public string $token,
    ) {}

    /**
     * @return PasswordResetEmail
     */
    public function build() {
        return $this->subject('Passwort zurücksetzen')
            ->view('emails.password_reset')
            ->with([
                'firstname' => $this->firstname,
            ]);
    }
}
