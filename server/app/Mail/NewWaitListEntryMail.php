<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class NewWaitListEntryMail extends Mailable {
    use Queueable, SerializesModels;

    public function __construct()
    {}

    public function envelope(): Envelope {
        return new Envelope(
            subject: 'Willkommen auf der Warteliste für die numo App',
        );
    }

    public function content(): Content {
        return new Content(
            view: 'emails.new-wait-list-entry',
        );
    }

    /**
     * @return array<object>
     */
    public function attachments(): array {
        return [];
    }
}
