<?php

namespace App\Mail;

use App\Models\User;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class VerificationEmail extends Mailable {
    use SerializesModels;

    public function __construct(
        public string  $firstname,
        public User    $user,
        public string $verifyURL,
    ) {}

    public function envelope(): Envelope {
        return new Envelope(
            subject: 'E-Mail Adresse bestätigen',
        );
    }

    public function content(): Content {
        return new Content(
            view: 'emails.email_verification',
        );
    }

    /**
     * @return array<object>
     */
    public function attachments(): array {
        return [];
    }
}
