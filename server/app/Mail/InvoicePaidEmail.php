<?php

namespace App\Mail;

use App\Models\Team;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Mail\Mailables\Attachment;
use Laravel\Cashier\Invoice as CashierInvoice;
use Stripe\Invoice as StripeInvoice;
use Illuminate\Support\Facades\Http;

class InvoicePaidEmail extends Mailable
{
    use Queueable, SerializesModels;

    public CashierInvoice $cashierInvoice;
    public StripeInvoice $stripeInvoice;
    public User $user;
    public Team $team;

    /**
     * Create a new message instance.
     */
    public function __construct(
        CashierInvoice $cashierInvoice,
        Team $team
    ) {
        $this->cashierInvoice = $cashierInvoice;
        $this->stripeInvoice = $cashierInvoice->asStripeInvoice();
        $this->user = $cashierInvoice->owner() instanceof User ? $cashierInvoice->owner() : throw new \InvalidArgumentException('Invoice owner is not a User');
        $this->team = $team;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Rechnung für Dein Numo Abonnement ('.$this->team->name.')',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.invoice_paid',
            with: [
                'user' => $this->user,
                'invoice' => $this->cashierInvoice,
                'team' => $this->team,
                'amount' => $this->formatAmount($this->stripeInvoice->amount_paid),
                'date' => $this->formatDate($this->stripeInvoice->created),
                'invoiceNumber' => $this->stripeInvoice->number,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, Attachment>
     */
    public function attachments(): array
    {
        try {
            // Get the PDF URL from the Stripe invoice
            $pdfUrl = $this->stripeInvoice->invoice_pdf ?? '';
            
            // Download the PDF content with proper authentication
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . config('cashier.secret'),
            ])->get($pdfUrl);
            
            if ($response->successful()) {
                // Extrahiere den Dateinamen aus der URL oder verwende die Rechnungsnummer
                $fileName = $this->stripeInvoice->number ? 'Invoice-' . $this->stripeInvoice->number . '.pdf' : 'invoice.pdf';

                return [
                    Attachment::fromData(fn () => $response->body(), $fileName)
                        ->withMime('application/pdf'),
                ];
            }
            
            // Log error if download fails
            \Log::error('Failed to download invoice PDF', [
                'invoice_id' => $this->stripeInvoice->id,
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            
            return [];
        } catch (\Exception $e) {
            \Log::error('Exception downloading invoice PDF', [
                'invoice_id' => $this->stripeInvoice->id,
                'error' => $e->getMessage()
            ]);
            
            return [];
        }
    }

    /**
     * Format the amount in cents to a readable currency string.
     */
    private function formatAmount(int $amountInCents): string
    {
        return number_format($amountInCents / 100, 2, ',', '.') . ' €';
    }

    /**
     * Format the Unix timestamp to a readable date.
     */
    private function formatDate(int $timestamp): string
    {
        return date('d.m.Y', $timestamp);
    }
}
