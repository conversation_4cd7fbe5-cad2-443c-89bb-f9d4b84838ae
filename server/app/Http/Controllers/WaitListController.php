<?php

namespace App\Http\Controllers;

use App\Events\WaitListEntryCreatedEvent;
use App\Models\RegistrationAllowlist;
use App\Models\User;
use App\Types\WaitListMailType;
use Symfony\Component\HttpFoundation\JsonResponse;
use Illuminate\Http\Request;

class WaitListController extends Controller {
    public function addToWaitList(Request $request): JsonResponse {
        $attr = $request->validate(['email' => ['required', 'string', 'email', 'max:255']]);
        $email = $attr['email'];

        if(User::whereEmail($email)->exists()) {
            $mailState = WaitListMailType::ALREADY_REGISTERED;
        } else if(RegistrationAllowlist::registrationAllowed()->whereEmail($email)->exists()) {
            $mailState = WaitListMailType::ALREADY_ON_INVITE_LIST;
        } elseif (RegistrationAllowlist::onWaitList()->whereEmail($email)->exists()) {
            $mailState = WaitListMailType::ALREADY_ON_WAIT_LIST;
        } else {
            $waitListEntry = RegistrationAllowlist::create([
               'email' => $email
            ]);
            $mailState = WaitListMailType::ADDED_TO_WAIT_LIST;
            event(new WaitListEntryCreatedEvent($waitListEntry));
        }

        return response()->json([
            'mailState' => $mailState
        ]);
    }
}
