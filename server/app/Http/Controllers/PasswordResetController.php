<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Services\PasswordResetService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PasswordResetController extends Controller {
    public function __construct(private PasswordResetService $passwordResetService) {}

    public function forgotPassword(Request $request): JsonResponse {
        $request->validate([
            'email' => 'required|email|exists:users,email',
        ]);

        $email = $request->input('email');
        $user = User::where('email', $email)->first();

        $this->passwordResetService->sendPasswordResetEmail($user);

        return response()->json(['message' => 'Password reset email sent.']);
    }

    public function resetPassword(Request $request): JsonResponse {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email|exists:users,email',
            'password' => 'required|min:8|confirmed',
        ]);

        $token = $request->input('token');
        $email = $request->input('email');
        $newPassword = $request->input('password');

        $isReset = $this->passwordResetService->resetPassword($token, $email, $newPassword);

        if ($isReset) {
            return response()->json(['message' => 'Password reset successful.']);
        }

        return response()->json(['message' => 'Invalid password reset token.'], 400);
    }
}
