<?php

namespace App\Http\Controllers;

use App\Models\DeviceInfo;
use App\Models\PersonalAccessToken;
use App\Models\TrackedNotification;
use App\Models\User;
use App\Notifications\Push\Base\PushNotification;
use App\Notifications\Push\UserInitiatedTestNotification;
use App\Types\FeatureType;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Laravel\Pennant\Feature;

class PushNotificationController extends Controller {

    // Laravel Controller function to send a push notification to the logged-in user
    public function sendUserInitiatedTestNotification(): JsonResponse {
        /** @var User $user */
        $user = AuthController::getCurrentUser();

        // Send the push notification
        $user->notifyNow(new UserInitiatedTestNotification());

        // Return a success message
        return response()->json([
            'message' => 'Push notification gesendet.',
        ], 200);
    }

    public function getStatistics(): JsonResponse {
        if (!Feature::active(FeatureType::ADMIN_STATS->value)) {
            return response()->json([
                'message' => 'Not allowed to access this feature.',
            ], 403);
        }

        $uniqueInstallationCount = DeviceInfo::query()
            ->where(function ($query) {
                $query->where('platform', 'android')
                    ->orWhere('platform', 'ios');
            })
            ->distinct('installation_id')
            ->count('installation_id');

        $numberFcmTokens = DeviceInfo::query()
            ->distinct('fcm_token')
            ->count('fcm_token');

        return response()->json([
            'registeredUsers'                => User::count(),
            'pushNotificationCapableDevices' => $numberFcmTokens,
            'devices'                        => $uniqueInstallationCount,
        ], 200);
    }

    public function markPushNotificationAsReceived(Request $request): JsonResponse {
        $attr = $request->validate([
            'message_uuid' => 'required|string',
        ]);

        /** @var User $user */
        $user = AuthController::getCurrentUser();

        TrackedNotification::query()
            ->where('message_uuid', $attr['message_uuid'])
            ->where('notifiable_id', $user->id)
            ->update([
                'received_at' => now(),
            ]);

        return response()->json([
            'message' => '',
        ], 200);
    }
}
