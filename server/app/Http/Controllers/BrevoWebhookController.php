<?php

namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;

class BrevoWebhookController extends Controller
{
    private string $SLACK_WEBHOOK_URL = '*********************************************************************************';

    public function failed(Request $request): JsonResponse
    {
        $event = $request->input('event'); // "failed" oder "hard_bounce"
        $email = $request->input('email'); // Empfängeradresse
        $subject = $request->input('subject'); // Betreff
        $reason = $request->input('reason', 'none'); // Fehlergrund
        $messageId = $request->input('message-id'); // Id

        $logLink = $messageId ? "🔗 View in Brevo with message id `$messageId`: https://app-smtp.brevo.com/log" : '';

        // for now, the webhook is only called
        //        if ($event === "failed" || $event === "hard_bounce") {
        // E-Mail an Admin-Team senden
        //            Mail::raw("Fehlgeschlagene E-Mail an: $email\nGrund: $reason", function ($message) {
        //                $message->to('<EMAIL>')
        //                    ->subject('🚨 Fehlgeschlagene Transaktionsmail');
        //            });

        // 📌 Slack Nachricht senden
        Http::post($this->SLACK_WEBHOOK_URL, [
            'text' => "🚨 *Fehlgeschlagene E-Mail - `$event`!*\n📩 Empfänger: `$email`\nBetreff: `$subject`\n ❌ Grund: `$reason`\n $logLink",
        ]);
        //        }

        return response()->json(['message' => 'Webhook received'], 200);
    }
}
