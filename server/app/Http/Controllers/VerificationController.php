<?php

namespace App\Http\Controllers;

use App\Services\UserService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\UnauthorizedException;

class VerificationController extends Controller {
    public function __construct(private UserService $userService) {}

    public function triggerVerification(): JsonResponse {
        $user = AuthController::getCurrentUser();

        if(!$user) {
            throw new UnauthorizedException;
        }

        $user->sendEmailVerificationNotification();

        return response()->json(['email' => $user->email]);
    }

    public function confirmVerification(Request $request): JsonResponse {
        $request->validate([
            'token' => 'required',
            'email' => 'required',
        ]);

        $token = $request->input('token');
        $email = $request->input('email');

        $isVerified = $this->userService->verifyEmail($token, $email);

        if ($isVerified) {
            return response()->json(['message' => 'Email wurde verifiziert.']);
        }

        return response()->json(['message' => 'Unbekanntes Verifizierungstoken, möglicherweise ist der Link schon abgelaufen'], 400);
    }
}
