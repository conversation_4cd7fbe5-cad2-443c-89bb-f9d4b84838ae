<?php

namespace App\Http\Controllers;

use App\Models\DeviceInfo;
use App\Models\PersonalAccessToken;
use App\Models\RegistrationAllowlist;
use App\Models\TeamMember;
use App\Models\User;
use App\Types\TeamPermissionType;
use App\Types\TeamRoleType;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class UserController extends Controller {

    // Validate that every team has at least on remaining admin and delete empty teams
    function validateAndCleanUpUserDeletion(User $user): JsonResponse|null {
        foreach ($user->person->teams()->get() as $team) {
            $registeredMembers = $team->registeredMembers;
            $otherUsers = $registeredMembers->filter(fn($registeredMember) => $registeredMember->person->user->id !== $user->id);

            // Delete team if it no longer has any members remaining
            if($otherUsers->count() === 0) {
                $team->delete();
                continue;
            }

            // Check if the user is the only admin for the team
            $otherAdmins = $otherUsers->filter(fn($otherUser) => $otherUser->person->hasPermissionForTeam(TeamPermissionType::TEAM_MEMBER_UPDATE, $team));
            if($otherAdmins->count() === 0) {
                return response()->json([
                    'message' => "Du bist einziger Admin für das Team \"$team->name\". Bevor du deinen Account löschen kannst, musst du einem anderen registrierten User die Admin Rolle geben",
                ], Response::HTTP_FORBIDDEN);
            }
        }
        return null;
    }

    // delete will delete the currently authenticated user while leaving the person / members in the database
    public function delete(): JsonResponse {
        /** @var User $user */
        $user = AuthController::getCurrentUser();

        if ($user == null) {
            return response()->json([
                'message' => 'Nicht autorisiert',
            ], 401);
        }

        $resp = UserController::validateAndCleanUpUserDeletion($user);
        if ($resp != null) {
            return $resp;
        }

        try {
            DB::transaction(function () use (&$user) {
                $user->delete();

                $allowlist_entry = RegistrationAllowlist::whereUserId($user->id)
                    ->first();
                if ($allowlist_entry) {
                    $allowlist_entry->user_id = null;
                    $allowlist_entry->save();
                }
            });
        } catch (Throwable $e) {
            return response()->json([
                'message' => "Nutzer konnte nicht gelöscht werden.",
                'errors'  => [
                    'exception' => $e->getMessage(),
                ],
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json([
            'message' => 'Nutzer erfolgreich gelöscht.',
        ], 200);
    }

    public function leaveTeam(TeamMember $member): JsonResponse {
        $user = AuthController::getCurrentUser();
        if (!$user || $user->person_id !== $member->person_id) {
            return response()->json([
                'message' => 'Nicht autorisiert',
            ], 401);
        }

        $member->person()->disassociate();
        $member->statusRole()->associate($member->team->getTeamRole(TeamRoleType::INACTIVE));
        $member->save();

        return response()->json([
            'message' => 'Member: ' . $member->name,
        ], 200);
    }

    public function listPushNotificationCapableDevices(): JsonResponse {
        /** @var User $user */
        $user = AuthController::getCurrentUser();

        /* @var Collection<PersonalAccessToken> $tokens */
        $tokens = $user->tokens()
            ->whereNotNull('fcm_token')
            ->get(['device_name', 'device_platform', 'device_manufacturer', 'installation_id']);

        $data = $tokens->map(function ($token) {
            /** @var PersonalAccessToken $token */
            $deviceInfo = DeviceInfo::where('id', $token->installation_id)->first();
            return [
                'device_name'         => $deviceInfo->name,
                'device_platform'     => $deviceInfo->platform,
                'device_manufacturer' => $deviceInfo->manufacturer,
            ];
        });

        return response()->json([
            'message' => 'Push-Benachrichtigungen können an folgende Geräte gesendet werden.',
            'data'    => $data,
        ], 200);
    }

    public function updateDeviceInfo(Request $request): JsonResponse {
        $attr = $request->validate([
            'device_name'         => 'nullable',
            'device_model'        => 'nullable', // e.g. iPhone 12 Pro
            'device_platform'     => 'nullable', // e.g. iOS, Android, Electron, Web
            'device_os'           => 'nullable', // e.g. ios, android, windows, mac, linux, unknown
            'device_os_version'   => 'nullable', // e.g. 14.4.2, 11.2.3, 'unknown
            'device_language'     => 'nullable',
            'device_manufacturer' => 'nullable', // e.g. Apple, Samsung, Google, Microsoft, unknown
            'fcm_token'           => 'nullable', // Firebase messaging token
            'installation_id'     => 'nullable', // Unique installation ID
        ]);

        /** @var User $user */
        $user = AuthController::getCurrentUser();

        /** @var PersonalAccessToken $userAccessToken */
        $userAccessToken = $user->currentAccessToken();
        $plainToken = $userAccessToken->token;

        // We generate a new temporary installation_id in case none was sent with the request
        if (!isset($attr['installation_id'])) {
            $attr['installation_id'] = Str::uuid();
        }

        // Fetch the actual access token as an Eloquent model
        $accessToken = PersonalAccessToken::where('token', $plainToken)->first();
        $accessToken->installation_id = $attr['installation_id'];
        $accessToken->save();

        // Fetch DeviceInfo based on installation_id in accessToken and device info table
        $deviceInfo = DeviceInfo::where('id', $attr['installation_id'])->first();

        if (!$deviceInfo) {
            $deviceInfo = new DeviceInfo();
        }

        // Update the device info with the latest data
        $deviceInfo->user_id = $user->id;
        $deviceInfo->id = $attr['installation_id'];

        if (isset($attr['device_name'])) {
            $deviceInfo->name = $attr['device_name'];
        }
        if (isset($attr['device_model'])) {
            $deviceInfo->model = $attr['device_model'];
        }
        if (isset($attr['device_platform'])) {
            $deviceInfo->platform = $attr['device_platform'];
        }
        if (isset($attr['device_os'])) {
            $deviceInfo->os = $attr['device_os'];
        }
        if (isset($attr['device_os_version'])) {
            $deviceInfo->os_version = $attr['device_os_version'];
        }
        if (isset($attr['device_language'])) {
            $deviceInfo->language = $attr['device_language'];
        }
        if (isset($attr['device_manufacturer'])) {
            $deviceInfo->manufacturer = $attr['device_manufacturer'];
        }
        if (isset($attr['fcm_token'])) {
            $deviceInfo->fcm_token = $attr['fcm_token'];
        }
        $deviceInfo->last_seen = now();
        $deviceInfo->save();

        // This ensures that the device info is only associated with one specific token at a time
        if (isset($attr['installation_id'])) {
            PersonalAccessToken::where('installation_id', $accessToken->installation_id)
                ->whereNot('id', $accessToken->id)
                ->update(['installation_id' => null]);
        }

        // Note: In theory it would make sense to delete tokens that aren't associated with a token anymore.
        //       Otherwise we'll have a lot of no longer used live tokens in the database. But this would also revoke
        //       our staging and testing environment sessions.
        //
        //       TODO(fabzo): Wait, would it? Those are in different databases right? So they wouldn't be affected by this.
        //
        DB::table('device_info')
            ->where('user_id', $user->id)
            ->leftJoin('personal_access_tokens', 'device_info.id', '=', 'personal_access_tokens.installation_id')
            ->select('device_info.id', 'device_info.user_id', 'device_info.name', 'device_info.fcm_token', 'personal_access_tokens.id as token_id')
            ->whereNull('personal_access_tokens.id')
            ->delete();

        return response()->json([
            'message' => 'Geräteinformationen erfolgreich aktualisiert.',
        ], 200);
    }

}
