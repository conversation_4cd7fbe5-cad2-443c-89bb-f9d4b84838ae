<?php

namespace App\Http\Controllers;

use <PERSON><PERSON>\Cashier\Http\Controllers\WebhookController as CashierWebhookController;
use Symfony\Component\HttpFoundation\Response;

class StripeWebhookController extends CashierWebhookController {
    
    /**
     * Method is called from CashierWebhookController 
     * WebhookHandled Event is fired only when named method is found 
     *
     * @param  array<string, mixed> $payload
     * @return Response
     */
    public function handleInvoicePaymentSucceeded(array $payload): Response {
        return $this->successMethod();
    }
    
    /**
     * Method is called from CashierWebhookController 
     * WebhookHandled Event is fired only when named method is found 
     *
     * @param  array<string, mixed> $payload
     * @return Response
     */
    public function handleCheckoutSessionCompleted(array $payload): Response {
        return $this->successMethod();
    }
}
