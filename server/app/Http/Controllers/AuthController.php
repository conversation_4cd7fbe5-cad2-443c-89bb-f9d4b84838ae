<?php

namespace App\Http\Controllers;

use App\Actions\Fortify\PasswordValidationRules;
use App\Models\DeviceInfo;
use App\Models\Invite;
use App\Models\RegistrationAllowlist;
use App\Models\User;
use Exception;
use Illuminate\Auth\Events\Registered;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Laravel\Sanctum\NewAccessToken;
use Laravel\Sanctum\PersonalAccessToken;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class AuthController extends Controller {
    use PasswordValidationRules;

    public static JsonResponse $ERROR_NO_ALLOWLIST_ENTRY;
    public static JsonResponse $ERROR_USER_ALREADY_REGISTERED;
    public static JsonResponse $ERROR_USER_CREATION_FAILED;

    public function register(Request $request): \Illuminate\Http\Response|JsonResponse|Application|ResponseFactory {
        $attr = $request->validate([
            'firstname'           => ['required', 'string', 'max:255'],
            'lastname'            => ['required', 'string', 'max:255'],
            'email'               => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique(User::class),
            ],
            'password'            => $this->passwordRules(),

            // These are all marked as optional to introduce them gracefully
            'device_name'         => 'nullable',
            'device_model'        => 'nullable', // e.g. iPhone 12 Pro
            'device_platform'     => 'nullable', // e.g. iOS, Android, Electron, Web
            'device_os'           => 'nullable', // e.g. ios, android, windows, mac, linux, unknown
            'device_manufacturer' => 'nullable', // e.g. Apple, Samsung, Google, Microsoft, unknown
            'fcm_token'           => 'nullable', // Firebase messaging token
            'installation_id'     => 'nullable', // Unique installation ID
            'no_login'            => 'nullable',
            'info_origin'         => ['nullable', 'string'],
            'info_competition'    => ['nullable', 'string'],

            'invite_token' => [
                'nullable',
                'string',
                'max:255',
            ],
        ]);

        $allowlist_entry = RegistrationAllowlist::registrationAllowed()->whereEmail($attr['email'])->first();
        $invite_entry = null;
        if (isset($attr['invite_token'])) {
            $invite_entry = Invite::whereToken($attr['invite_token'])->first();
        }

        if (!config('app.allow_registration')) {
            // Invite entry takes precedence over allow-list entry. We will allow
            // the registration with an invite token even if the user is not on the allow-list.
            if ($allowlist_entry == null && $invite_entry == null) {
                return AuthController::$ERROR_NO_ALLOWLIST_ENTRY;
            }

            if ($allowlist_entry != null && $allowlist_entry->user_id != null) {
                return AuthController::$ERROR_USER_ALREADY_REGISTERED;
            }
        }

        $user = null;
        try {
            DB::transaction(function () use ($invite_entry, $attr, $allowlist_entry, &$user) {
                $user = User::createWithPerson(
                    $attr['email'],
                    $attr['password'],
                    $attr['firstname'],
                    $attr['lastname']
                );

                if ($invite_entry) {
                    $user->invite()->associate($invite_entry)->save();
                }

                // We skip the email verification for t-online.de users for the moment, since t-online.de doesn't
                // accept emails without a dedicated IP and rDNS setup. See https://github.com/eltini/clubmanager/issues/416.
                if ($this->isProblemProviderForSendGrid($attr['email'])) {
                    $user->markEmailAsVerified();
                }

                // We update the allow-list entry in case the user was already on it
                if ($allowlist_entry !== null) {
                    $allowlist_entry->user_id = $user->id;
                    $allowlist_entry->save();
                }
                if (!empty($attr['info_origin']) || !empty($attr['info_competition'])) {
                    $user->registrationInformation()->create([
                        'origin'      => $attr['info_origin'] ?? null,
                        'competition' => $attr['info_competition'] ?? null,
                    ]);
                }
            });
        } catch (Throwable $e) {
            Log::error($e);
            return AuthController::$ERROR_USER_CREATION_FAILED;
        }

        // This sends the email verification to the user via
        // EventServiceProvider -> SendEmailVerificationNotification
        if (!$user->hasVerifiedEmail()) {
            $user->generateEmailVerificationToken();
        }
        event(new Registered($user));

        $newToken = null;
        if (!($attr['no_login'] ?? false)) {
            $newToken = $this->createTokenFromRequest($user, $request, $attr);
        }

        return $this->createLoginResponse($user, $newToken);
    }

    public static function isProblemProviderForSendGrid(mixed $email): bool {
        return preg_match('/@problem-provider.local$/', $email) === 1 
//            || preg_match('/@t-online.de$/', $email) === 1
//            || preg_match('/@web.de$/', $email) === 1
//            || preg_match('/@yahoo.de$/', $email) === 1
//            || preg_match('/@outlook.de$/', $email) === 1
            ;
    }

    /**
     * @param array<string, string|null> $attr
     */
    private function createTokenFromRequest(User $user, Request $request, array $attr): NewAccessToken {
        return $user->createToken(
            name: '',
            device_name: $attr['device_name'] ?? $this->detectBrowser($request),
            device_type: $attr['device_type'] ?? "unknown",
            device_platform: $attr['device_platform'] ?? $this->detectPlatform($request),
            device_model: $attr['device_model'] ?? "unknown",
            device_os: $attr['device_os'] ?? "unknown",
            device_manufacturer: $attr['device_manufacturer'] ?? "unknown",
            fcm_token: $attr['fcm_token'] ?? null,
            installation_id: $attr['installation_id'] ?? null,
        );
    }

    /**
     * @throws ValidationException
     */
    public function login(Request $request): JsonResponse {
        $attr = $request->validate([
            'email'               => 'required|string|email|',
            'password'            => 'required|string',

            // These are all marked as optional to introduce them gracefully
            'device_name'         => 'nullable',
            'device_model'        => 'nullable', // e.g. iPhone 12 Pro
            'device_platform'     => 'nullable', // e.g. iOS, Android, Electron, Web
            'device_os'           => 'nullable', // e.g. ios, android, windows, mac, linux, unknown
            'device_manufacturer' => 'nullable', // e.g. Apple, Samsung, Google, Microsoft, unknown
            'fcm_token'           => 'nullable', // Firebase messaging token
            'installation_id'     => 'nullable', // Unique installation ID
        ]);

        $user = User::where('email', $attr['email'])->first();

        if (!$user || !Hash::check($attr['password'], $user->password)) {
            throw ValidationException::withMessages([
                'email' => ['E-Mail Adresse oder Passwort sind falsch'],
            ]);
        }
        $user->load('person');

        return $this->createLoginResponse($user, $this->createTokenFromRequest($user, $request, $attr));
    }

    private function createLoginResponse(User $user, ?NewAccessToken $token = null): JsonResponse {
        if ($token == null) {
            return response()->json([
                'userId' => $user->id,
            ]);
        } else {
            return response()->json([
                'token'  => $token->plainTextToken,
                'userId' => $user->id,
            ]);
        }
    }

    public function logout(Request $request): \Illuminate\Http\Response|JsonResponse|Application|ResponseFactory {
        $attr = $request->validate([
            'device_id' => 'nullable',
        ]);

        if (isset($attr['device_id'])) {
            // revoke token associated with given device_id

            // TODO(fabzo): Wrap in transaction

            DeviceInfo::where('id', $attr['device_id'])->delete();
            PersonalAccessToken::where('installation_id', $attr['device_id'])->delete();
        } else {
            // revoke token of current user
            $user = auth('sanctum')->user();

            if (!$user instanceof User) {
                throw new Exception('user not found');
            }

            $token = $user->currentAccessToken();
            if ($token instanceof PersonalAccessToken) {
                $token->delete();
            }
        }

        return response()->json([
            'message' => 'logout successful',
        ]);
    }

    public static function error(string $error_type, int $http_code, string $message): JsonResponse {
        return response()->json([
            'message' => $message,
            'errors'  => [
                $error_type => $message,
            ],
        ], $http_code);
    }

    public static function getCurrentUser(): User|null {
        /* @var User|null $user */
        $user = auth('sanctum')->user();
        return $user instanceof User ? $user : null;
    }

    /**
     * This is a naive implementation of a browser detection.
     *
     * A better way would be to use browscap. But the native PHP implementation requires a php.ini
     * configuration and the user land version requires too much setup for our use case.
     */
    private function detectBrowser(Request $request): string {
        $userAgent = $request->header('User-Agent');
        $browsers = [
            // Specialized Browsers & WebViews
            'Capacitor'      => function ($ua) {
                if (str_contains($ua, 'Android'))
                    return 'Capacitor Android';
                if (str_contains($ua, 'iPhone') || str_contains($ua, 'iPad'))
                    return 'Capacitor iOS';
                return 'Capacitor Unknown';
            },
            'Electron'       => 'Electron App',

            // General Browsers
            'MSIE'           => 'Internet Explorer',
            'Trident'        => 'Internet Explorer',
            'Edg/'           => 'Microsoft Edge',
            'EdgA'           => 'Microsoft Edge Android',
            'EdgiOS'         => 'Microsoft Edge iOS',
            'Opera Mini'     => 'Opera Mini',
            'OPR'            => 'Opera',
            'Opera'          => 'Opera',
            'Vivaldi'        => 'Vivaldi',
            'Brave'          => 'Brave',
            'UCBrowser'      => 'UC Browser',
            'SamsungBrowser' => 'Samsung Browser',
            'CriOS'          => 'Chrome iOS',
            'Chrome'         => function ($ua) {
                if (str_contains($ua, 'Android'))
                    return 'Chrome Android';
                return 'Google Chrome';
            },
            'Firefox'        => 'Mozilla Firefox',
            'FxiOS'          => 'Firefox iOS',
            'Safari'         => function ($ua) {
                if (str_contains($ua, 'iPhone') || str_contains($ua, 'iPad'))
                    return 'Mobile Safari';
                return 'Safari';
            },
            'Yowser'         => 'Yandex',
        ];

        foreach ($browsers as $keyword => $browser) {
            if (str_contains($userAgent, $keyword)) {
                return is_callable($browser) ? $browser($userAgent) : $browser;
            }
        }

        return 'Other/Unknown';
    }

    private function detectPlatform(Request $request): string {
        $userAgent = $request->header('User-Agent');

        $platforms = [
            // Mobile Platforms
            'Windows Phone'   => 'Windows Phone',
            'Android'         => 'Android',
            'iPad'            => 'iOS',
            'iPhone'          => 'iOS',
            'iPod'            => 'iOS',
            'BlackBerry'      => 'BlackBerry',
            'Symbian'         => 'Symbian',
            'Kindle'          => 'Kindle',
            'webOS'           => 'webOS',

            // Desktop Platforms
            'Macintosh'       => 'Mac OS X',
            'Linux'           => 'Linux',
            'Windows NT 5.0'  => 'Windows 2000',
            'Windows NT 5.1'  => 'Windows XP',
            'Windows NT 5.2'  => 'Windows 2003',
            'Windows NT 6.0'  => 'Windows Vista',
            'Windows NT 6.1'  => 'Windows 7',
            'Windows NT 6.2'  => 'Windows 8',
            'Windows NT 6.3'  => 'Windows 8.1',
            'Windows NT 10.0' => 'Windows 10',

            // Browsers
            'Edg/'            => 'Edge',
            'Trident/'        => 'Internet Explorer',
            'Firefox/'        => 'Firefox',
            'Chrome/'         => 'Chrome',
            'Safari/'         => 'Safari',
            'Opera/'          => 'Opera',
            'OPR/'            => 'Opera',
        ];

        foreach ($platforms as $keyword => $platform) {
            if (str_contains($userAgent, $keyword)) {
                return $platform;
            }
        }

        return 'Unknown';
    }

}

// TODO: PHP doesn't seem to be able to assign static variables without null checks in constructors otherwise, expectation would be
//       that this is defined above the class.
AuthController::$ERROR_NO_ALLOWLIST_ENTRY = AuthController::error('not_allowlisted', Response::HTTP_BAD_REQUEST, 'not allowlisted');
AuthController::$ERROR_USER_ALREADY_REGISTERED = AuthController::error('already_registered', Response::HTTP_BAD_REQUEST, 'already registered');
AuthController::$ERROR_USER_CREATION_FAILED = AuthController::error('user_creation_failed', Response::HTTP_INTERNAL_SERVER_ERROR, 'failed to create user');
