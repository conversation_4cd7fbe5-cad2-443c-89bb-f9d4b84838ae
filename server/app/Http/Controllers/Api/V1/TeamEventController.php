<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\AuthController;
use App\Http\Controllers\Controller;
use App\JsonApi\V1\TeamEvents\TeamEventQuery;
use App\JsonApi\V1\TeamEvents\TeamEventRequest;
use App\JsonApi\V1\TeamEventVotes\TeamEventVoteRequest;
use App\Models\TeamEvent;
use App\Models\TeamEventVote;
use App\Models\User;
use App\Services\PushNotificationService;
use Illuminate\Support\Facades\Log;
use LaravelJsonApi\Laravel\Http\Controllers\Actions;

class TeamEventController extends Controller {
    use Actions\FetchMany;
    use Actions\FetchOne;
    use Actions\Store;
    use Actions\Update;
    use Actions\Destroy;
    use Actions\FetchRelated;
    use Actions\FetchRelationship;
    use Actions\UpdateRelationship;
    use Actions\AttachRelationship;
    use Actions\DetachRelationship;

    public function __construct(
        public PushNotificationService $pushNotificationService,
    ) {
    }

    public function saved(TeamEvent $teamEvent, TeamEventRequest $request, TeamEventQuery $query): void {
        // We explicitly handle the change notification in saved instead of saving to ensure
        // that the event has successfully been created before sending the notification.
        $this->handleSendChangeNotification($teamEvent, $request);
    }

    private function handleSendChangeNotification(TeamEvent $teamEvent, TeamEventRequest $request): void {
        // When the user (or rather the UI) includes the sendChangeNotification flag in the request, it means
        // that the user wants to send a push notification to all team members when the event changes.
        $sendChangeNotification = $request->input('data.attributes.sendChangeNotification');

        if (!$sendChangeNotification) {
            return;
        }
        
        $isUpdate = $request->has('data.id');
        
        if ($isUpdate) {
            // Pass all updated attributes along with the notification
            $updatedKeys = array_map(
                fn($key) => str_replace('data.attributes.', '', (string)$key), 
                array_keys((array)$request->input('data.attributes', []))
            );
            
            if (count($updatedKeys) > 0) {
                $this->pushNotificationService->handleOnEventChange($teamEvent, $updatedKeys, AuthController::getCurrentUser());
            }
        } else {
            $this->pushNotificationService->handleOnEventCreate($teamEvent, AuthController::getCurrentUser());
        }
    }

}
