<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\JsonApi\V1\AdminStatsTeams\AdminStatsTeamSchema;
use App\Models\Person;
use App\Models\PersonalAccessToken;
use App\Models\RegistrationInformation;
use App\Models\Team;
use App\Models\TeamEvent;
use App\Models\TeamEventVote;
use App\Models\TeamLedger;
use App\Models\TeamLedgerDues;
use App\Models\TeamLedgerFine;
use App\Models\TeamLedgerTransaction;
use App\Models\TeamMember;
use App\Models\User;
use App\Types\TeamEventType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Kyranb\Footprints\Visit;
use LaravelJsonApi\Core\Responses\DataResponse;
use Laravel<PERSON>sonApi\Laravel\Http\Controllers\Actions;
use LaravelJsonApi\Laravel\Http\Requests\AnonymousCollectionQuery;

class AdminStatsTeamController extends Controller
{
    use Actions\FetchMany;
    use Actions\FetchOne;
    use Actions\Store;
    use Actions\Update;
    use Actions\Destroy;
    use Actions\FetchRelated;
    use Actions\FetchRelationship;
    use Actions\UpdateRelationship;
    use Actions\AttachRelationship;
    use Actions\DetachRelationship;

    public function searched($data, AnonymousCollectionQuery $query): DataResponse {

        $memberCounts = TeamMember::whereNotNull('person_id')
                                    ->selectRaw('COUNT(*) as member_count')
                                    ->groupBy('person_id')
                                    ->orderBy('member_count', 'desc')
                                    ->get();
        $personCountPerMemberCount = $memberCounts->groupBy('member_count')->map(function ($group) {
            return $group->count();
        });
                
//        foreach ($countsByMemberCount as $count => $persons) {
//            echo "$count members: $persons persons\n";
//        }
//        
        return DataResponse::make($data)
            ->withMeta([
               'totalActive' => Team::whereHas('votes', function (Builder $builder): Builder {
                    /** @var Builder<TeamEventVote> $builder */
                    return $builder->whereDate('team_event_votes.updated_at', '>=',Carbon::now()->subMonths(AdminStatsTeamSchema::MONTH_SINCE_LAST_VOTE_FOR_ACTIVE));
                })->count(), 
               
               'totalTeams2Month' => Team::whereDate('created_at', '>=', Carbon::now()->subMonths(2))->count(), 
               'totalTeams6Month' => Team::whereDate('created_at', '>=', Carbon::now()->subMonths(6))->count(), 
               'totalTeams12Month' => Team::whereDate('created_at', '>=', Carbon::now()->subMonths(12))->count(), 
               'totalTeamMembers2Month' => TeamMember::whereDate('created_at', '>=', Carbon::now()->subMonths(2))->count(), 
               'totalTeamMembers6Month' => TeamMember::whereDate('created_at', '>=', Carbon::now()->subMonths(6))->count(), 
               'totalTeamMembers12Month' => TeamMember::whereDate('created_at', '>=', Carbon::now()->subMonths(12))->count(), 
               'totalRegisteredTeamMembers2Month' => TeamMember::registered()->whereDate('created_at', '>=', Carbon::now()->subMonths(2))->count(),
               'totalRegisteredTeamMembers6Month' => TeamMember::registered()->whereDate('created_at', '>=', Carbon::now()->subMonths(6))->count(), 
               'totalRegisteredTeamMembers12Month' => TeamMember::registered()->whereDate('created_at', '>=', Carbon::now()->subMonths(12))->count(), 
               'totalUsers2Month' => User::whereDate('created_at', '>=', Carbon::now()->subMonths(2))->count(),
               'totalUsers6Month' => User::whereDate('created_at', '>=', Carbon::now()->subMonths(6))->count(),
               'totalUsers12Month' => User::whereDate('created_at', '>=', Carbon::now()->subMonths(12))->count(),
               
               'totalActiveTeams2Month' => Team::whereHas('votes', function (Builder $builder): Builder {
                   /** @var Builder<TeamEventVote> $builder */
                   return $builder->whereDate('team_event_votes.updated_at', '>=',Carbon::now()->subMonths(2));
               })->count(),
               'totalActiveTeams6Month' => Team::whereHas('votes', function (Builder $builder): Builder {
                   /** @var Builder<TeamEventVote> $builder */
                   return $builder->whereDate('team_event_votes.updated_at', '>=',Carbon::now()->subMonths(6));
               })->count(),
               'totalActiveTeams12Month' => Team::whereHas('votes', function (Builder $builder): Builder {
                   /** @var Builder<TeamEventVote> $builder */
                   return $builder->whereDate('team_event_votes.updated_at', '>=',Carbon::now()->subMonths(12));
               })->count(),
               'totalActiveUsers2Month' => User::whereHas('tokens', function (Builder $builder): Builder {
                   /** @var Builder<PersonalAccessToken> $builder */
                   return $builder->whereDate('last_used_at', '>=',Carbon::now()->subMonths(2));
               })->count(),
               'totalActiveUsers6Month' => User::whereHas('tokens', function (Builder $builder): Builder {
                   /** @var Builder<PersonalAccessToken> $builder */
                   return $builder->whereDate('last_used_at', '>=',Carbon::now()->subMonths(6));
               })->count(),
               'totalActiveUsers12Month' => User::whereHas('tokens', function (Builder $builder): Builder {
                   /** @var Builder<PersonalAccessToken> $builder */
                   return $builder->whereDate('last_used_at', '>=',Carbon::now()->subMonths(12));
               })->count(),
               
               'totalEvents2Month' => TeamEvent::events()->whereDate('created_at', '>=', Carbon::now()->subMonths(2))->count(),
               'totalEvents6Month' => TeamEvent::events()->whereDate('created_at', '>=', Carbon::now()->subMonths(6))->count(),
               'totalEvents12Month' => TeamEvent::events()->whereDate('created_at', '>=', Carbon::now()->subMonths(12))->count(),
               'totalEventSeries2Month' => TeamEvent::seriesParent()->whereDate('created_at', '>=', Carbon::now()->subMonths(2))->count(),
               'totalEventSeries6Month' => TeamEvent::seriesParent()->whereDate('created_at', '>=', Carbon::now()->subMonths(6))->count(),
               'totalEventSeries12Month' => TeamEvent::seriesParent()->whereDate('created_at', '>=', Carbon::now()->subMonths(12))->count(),
               'totalEventsTraining2Month' => TeamEvent::events()->whereEventType(TeamEventType::TRAINING->value)->whereDate('created_at', '>=', Carbon::now()->subMonths(2))->count(),
               'totalEventsTraining6Month' => TeamEvent::events()->whereEventType(TeamEventType::TRAINING->value)->whereDate('created_at', '>=', Carbon::now()->subMonths(6))->count(),
               'totalEventsTraining12Month' => TeamEvent::events()->whereEventType(TeamEventType::TRAINING->value)->whereDate('created_at', '>=', Carbon::now()->subMonths(12))->count(),
               'totalEventsMatch2Month' => TeamEvent::events()->whereEventType(TeamEventType::MATCH->value)->whereDate('created_at', '>=', Carbon::now()->subMonths(2))->count(),
               'totalEventsMatch6Month' => TeamEvent::events()->whereEventType(TeamEventType::MATCH->value)->whereDate('created_at', '>=', Carbon::now()->subMonths(6))->count(),
               'totalEventsMatch12Month' => TeamEvent::events()->whereEventType(TeamEventType::MATCH->value)->whereDate('created_at', '>=', Carbon::now()->subMonths(12))->count(),
               'totalEventsTournament2Month' => TeamEvent::events()->whereEventType(TeamEventType::TOURNAMENT->value)->whereDate('created_at', '>=', Carbon::now()->subMonths(2))->count(),
               'totalEventsTournament6Month' => TeamEvent::events()->whereEventType(TeamEventType::TOURNAMENT->value)->whereDate('created_at', '>=', Carbon::now()->subMonths(6))->count(),
               'totalEventsTournament12Month' => TeamEvent::events()->whereEventType(TeamEventType::TOURNAMENT->value)->whereDate('created_at', '>=', Carbon::now()->subMonths(12))->count(),
               'totalEventsEvent2Month' => TeamEvent::events()->whereEventType(TeamEventType::EVENT->value)->whereDate('created_at', '>=', Carbon::now()->subMonths(2))->count(),
               'totalEventsEvent6Month' => TeamEvent::events()->whereEventType(TeamEventType::EVENT->value)->whereDate('created_at', '>=', Carbon::now()->subMonths(6))->count(),
               'totalEventsEvent12Month' => TeamEvent::events()->whereEventType(TeamEventType::EVENT->value)->whereDate('created_at', '>=', Carbon::now()->subMonths(12))->count(),
               
               'totalEvents' => TeamEvent::events()->count(), 
               'totalEventSeries' => TeamEvent::seriesParent()->count(), 
               'totalEventsTraining' => TeamEvent::events()->whereEventType(TeamEventType::TRAINING->value)->count(), 
               'totalEventsMatch' => TeamEvent::events()->whereEventType(TeamEventType::MATCH->value)->count(), 
               'totalEventsTournament' => TeamEvent::events()->whereEventType(TeamEventType::TOURNAMENT->value)->count(), 
               'totalEventsEvent' => TeamEvent::events()->whereEventType(TeamEventType::EVENT->value)->count(), 
               
               'totalTeamMembers' => TeamMember::count(), 
               'totalUsers' => User::count(), 
               'registeredTeamMembers' => TeamMember::registered()->count(), 
               'totalVotesManual' => TeamEventVote::whereHas('voterPerson')->count(), 
               'totalVotesAutomatic' => TeamEventVote::whereDoesntHave('voterPerson')->count(), 
               'totalLedgers' => TeamLedger::count(), 
               'totalLedgerFines' => TeamLedgerFine::count(), 
               'totalLedgerDues' => TeamLedgerDues::count(), 
               'totalLedgerTransactions' => TeamLedgerTransaction::count(), 
                
               'personCountPerMemberCount' => $personCountPerMemberCount,
               'personCountWithoutMember' => User::whereDoesntHave('person.teamMembers')->count(),
                
                'originCounts' => RegistrationInformation::whereNotNull('origin')->groupBy('origin')->select('origin')->selectRaw('count(*) as count')->orderBy('count', 'desc')->get(),
                'competitionCounts' => RegistrationInformation::whereNotNull('competition')->groupBy('competition')->select('competition')->selectRaw('count(*) as count')->orderBy('count', 'desc')->get(),
                
               'landingPageCountTotal' => Visit::selectRaw('landing_page, count(DISTINCT footprint) as visit_count, count(DISTINCT user_id) as user_count')
                    ->groupBy('landing_page')
                    ->orderBy('visit_count', 'desc')
                    ->get(),
               'landingPageCount2Month' => Visit::selectRaw('landing_page, count(DISTINCT footprint) as visit_count, count(DISTINCT user_id) as user_count')
                    ->whereDate('created_at', '>=', Carbon::now()->subMonths(2))
                    ->groupBy('landing_page')
                    ->orderBy('visit_count', 'desc')
                    ->get(),
               'landingPageCount6Month' => Visit::selectRaw('landing_page, count(DISTINCT footprint) as visit_count, count(DISTINCT user_id) as user_count')
                    ->whereDate('created_at', '>=', Carbon::now()->subMonths(6)) 
                    ->groupBy('landing_page')
                    ->orderBy('visit_count', 'desc')
                    ->get(),
               'landingPageCount12Month' => Visit::selectRaw('landing_page, count(DISTINCT footprint) as visit_count, count(DISTINCT user_id) as user_count')
                    ->whereDate('created_at', '>=', Carbon::now()->subMonths(12))
                    ->groupBy('landing_page')
                    ->orderBy('visit_count', 'desc')
                    ->get(),
            ]);
    }
}
