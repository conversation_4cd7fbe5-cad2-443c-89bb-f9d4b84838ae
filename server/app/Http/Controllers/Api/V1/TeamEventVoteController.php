<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\JsonApi\V1\TeamEventVotes\TeamEventVoteRequest;
use App\Models\TeamEventVote;
use App\Services\PushNotificationService;
use <PERSON><PERSON><PERSON>sonApi\Laravel\Http\Controllers\Actions;

class TeamEventVoteController extends Controller {
    use Actions\FetchMany;
    use Actions\FetchOne;
    use Actions\Store;
    use Actions\Update;
    use Actions\Destroy;
    use Actions\FetchRelated;
    use Actions\FetchRelationship;
    use Actions\UpdateRelationship;
    use Actions\AttachRelationship;
    use Actions\DetachRelationship;

    public function __construct(
        public PushNotificationService $pushNotificationService,
    ) {
    }

    public function saved(TeamEventVote $teamEventVote, TeamEventVoteRequest $request): void {
        // We associate the event vote with the person of the requesting user here in the API
        // instead of in the Eloquent model to avoid it being modified when directly dealing with
        // the model classes.
        $user = $request->user();
        if ($user && $teamEventVote->voter_person_id !== $user->person_id) {
            $teamEventVote->voterPerson()->associate($user->person);
            $teamEventVote->save();
        }

        $this->pushNotificationService->handleOnVoteChange($teamEventVote, $user);
    }
}
