<?php

namespace App\Http\Controllers\Api\V1;

use App\Events\AbsenceCreated;
use App\Events\AbsenceUpdated;
use App\Http\Controllers\Controller;
use App\Models\Absence;
use LaravelJsonApi\Laravel\Http\Controllers\Actions;

class AbsenceController extends Controller {
    use Actions\FetchMany;
    use Actions\FetchOne;
    use Actions\Store;
    use Actions\Update;
    use Actions\Destroy;
    use Actions\FetchRelated;
    use Actions\FetchRelationship;
    use Actions\UpdateRelationship;
    use Actions\AttachRelationship;
    use Actions\DetachRelationship;

    public function created(Absence $absence): void {
        // created event has to be fired in controller since the relations are not available after model create event
        event(new AbsenceCreated($absence));
    }

    public function updated(Absence $absence): void {
        // updated event has to be fired in controller since the relations are not available after model create event
        event(new AbsenceUpdated($absence));
    }
}
