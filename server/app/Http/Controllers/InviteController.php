<?php

namespace App\Http\Controllers;

use App\Models\Invite;
use App\Models\Team;
use App\Models\TeamMember;
use App\Services\PushNotificationService;
use App\Types\TeamRoleType;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class InviteController extends Controller {
    public static JsonResponse $ERROR_INVALID_TOKEN;
    public static JsonResponse $ERROR_ACCEPTING_INVITE_FAILED;

    public function __construct(
        public PushNotificationService $pushNotificationService,
    ) {}

    public function accept(Request $request): \Illuminate\Http\Response|JsonResponse|Application|ResponseFactory {
        $attr = $request->validate([
            'token'         => ['required', 'string', 'max:255'],
            'memberId'      => ['uuid', 'string', 'max:255'],
            'newMemberName' => ['nullable', 'string', 'max:255'],
        ]);

        try {
            $error = DB::transaction(function () use ($attr) {
                $invite = Invite::whereToken($attr['token'])->first();
                if ($invite === null) {
                    return InviteController::$ERROR_INVALID_TOKEN;
                }

                $user = AuthController::getCurrentUser();
                if ($user === null || $user->person === null) {
                    return InviteController::$ERROR_ACCEPTING_INVITE_FAILED;
                }

                /** @var TeamMember|null $teamMember */
                $teamMember = null;

                if ($invite->invitable instanceof Team) {
                    if (isset($attr['memberId'])) {
                        $teamMember = $invite->invitable->members()->findOrFail($attr['memberId']);
                        if (!$teamMember->isAvailableForTeamInvite()) {
                            return InviteController::$ERROR_ACCEPTING_INVITE_FAILED;
                        }
                        $user->person->teamMembers()->save($teamMember);

                    } elseif (isset($attr['newMemberName'])) {
                        $teamMember = $invite->invitable->createMember(
                            name: $attr['newMemberName'],
                            user: $user
                        );
                    } else {
                        return InviteController::$ERROR_ACCEPTING_INVITE_FAILED;
                    }
                } elseif ($invite->invitable instanceof TeamMember) {
                    $teamMember = $invite->invitable;
                    $user->person->teamMembers()->save($teamMember);
                    $invite->delete();
                }

                if (isset($teamMember) && $teamMember->statusRole->name === TeamRoleType::INACTIVE) {
                    $teamMember->statusRole()->associate($teamMember->team->getTeamRole(TeamRoleType::MEMBER))->save();
                }

                if (isset($teamMember)) {
                    $this->pushNotificationService->handleOnTeamJoin($user, $teamMember);
                }

                return null;
            });

            if ($error != null) {
                return $error;
            }
        } catch (Throwable $e) {
            Log::error($e);
            return InviteController::$ERROR_ACCEPTING_INVITE_FAILED;
        }

        return response()->json([
            'message' => 'invite accepted',
        ]);
    }

    public static function error(string $error_type, int $http_code, string $message): JsonResponse {
        return response()->json([
            'message' => $message,
            'errors'  => [
                $error_type => $message,
            ],
        ], $http_code);
    }
}

InviteController::$ERROR_INVALID_TOKEN = InviteController::error('invalid_invite_token', Response::HTTP_BAD_REQUEST, 'invalid invite token');
InviteController::$ERROR_ACCEPTING_INVITE_FAILED = InviteController::error('accepting_invite_failed', Response::HTTP_INTERNAL_SERVER_ERROR, 'accepting invite failed');
