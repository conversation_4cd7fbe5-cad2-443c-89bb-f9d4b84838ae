<?php

namespace App\Http\Controllers;

use App\Models\ICalendar;
use App\Services\ICalendarService;
use Illuminate\Http\Response;

class ICalendarController extends Controller {

    public function __construct(
        private readonly ICalendarService $iCalendarService
    ) {}
    
    public function show(ICalendar $iCalendar): Response {
        $feed = $this->iCalendarService->getCalendarFeed($iCalendar);
        $filename = 'team-events-'.$iCalendar->id.'.ics';
        return response($feed)
            ->header('Content-Type', 'text/calendar; charset=utf-8')
            ->header('Content-Disposition', 'attachment; filename="'.$filename.'"');
    }
}
