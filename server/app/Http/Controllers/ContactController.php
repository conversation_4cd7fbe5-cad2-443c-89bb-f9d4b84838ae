<?php

namespace App\Http\Controllers;

use App\Notifications\FeedbackNotification;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;

class ContactController extends Controller {

    public function sendEmail(Request $request): JsonResponse {
        $request->validate([
            'full-name' => 'required|string|max:255',
            'email'     => 'required|email|max:255',
            'subject'   => 'required|string|max:255',
            'message'   => 'required|string',
        ]);

        Notification::route('mail', $request->input('email'))
            ->notify(new FeedbackNotification(
                $request->input('full-name'),
                $request->input('email'),
                $request->input('subject'),
                $request->input('message'),
            ));

        return response()->json(['message' => 'Vielen Dank für Ihr Feedback!'], 200);
    }

}
