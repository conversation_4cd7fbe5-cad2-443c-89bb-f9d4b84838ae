<?php

namespace App\Http\Controllers;

use App\Models\TeamLedger;
use App\Models\TeamLedgerClaim;
use App\Models\TeamMember;
use App\Types\TeamLedgerClaimStatusType;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;

class TeamLedgerController extends Controller {
    public function __construct() {

    }


    public function setClaimStatus(TeamLedgerClaim $claim, TeamLedgerClaimStatusType $status, Request $request): JsonResponse {
        Gate::authorize('update', $claim);
        if($claim->getStatus() === $status) {
            // prevent setting same status again
            return response()->json();
        }
        $attr = $request->validate([
            'createTransactionAmount' => ['nullable', 'numeric', 'not_in:0'],
        ]);

        $createTransactionAmount = $attr['createTransactionAmount'] ?? null;

        DB::transaction(function () use ($status, $claim, $createTransactionAmount) {

            switch ($status) {
                case TeamLedgerClaimStatusType::EXEMPT:
                    $claim->setFulfilled(false, false);
                    $claim->setExempt();
                    break;
                case TeamLedgerClaimStatusType::UNFULFILLED:
                    $claim->setExempt(false, false);
                    $claim->setFulfilled(false);
                    break;
                case TeamLedgerClaimStatusType::FULFILLED:
                    $claim->setExempt(false, false);
                    $claim->setFulfilled();
                    break;
            }

            if($claim->isMoneyClaim()) {
                if($createTransactionAmount) {
                    $amount = money($createTransactionAmount, $claim->amount->getCurrency());
                    if($amount->lessThan(money(0))) {
                        $title = 'Erstattung: ';
                    } else {
                        $title = 'Bezahlt: ';
                    }
                    $title .= $claim->title;
                    $title .= ' vom ' . $claim->due_date->format('d.m.Y');
                    $claim->ledger->transactions()->create([
                        'team_member_id' => $claim->team_member_id,
                        'amount' => $amount,
                        'title' => $title,
                    ]);
                } else if($status !== TeamLedgerClaimStatusType::FULFILLED) {
                    $claim->ledger->fulfillClaimsFromCredit($claim->teamMember);
                }
            }

        });

        return response()->json();
    }

    public function getCreditBalance(TeamLedger $ledger, ?TeamMember $member = null): JsonResponse {
        Gate::authorize('view', $ledger);
        return response()->json($ledger->getCreditBalance($member));
    }

}
