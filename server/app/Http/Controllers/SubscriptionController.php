<?php

namespace App\Http\Controllers;

use App\Models\Team;
use App\Services\SubscriptionService;
use App\Types\SubscriptionPriceType;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class SubscriptionController extends Controller {
    public function __construct(
        private readonly SubscriptionService $subscriptionService
    ) {
    }
    
    /**
     * Get subscription prices
     *
     * @param Team $team used only for authorization for now but could have individual prices for each team
     * @return JsonResponse
     */
    public function getPrices(Team $team): JsonResponse {
        $this->authorize('startSubscription', $team);
        return response()->json(
            $this->subscriptionService->getTeamPricesNetto()
        );
    }
    
    public function createCheckoutSession(Team $team, SubscriptionPriceType $priceType): JsonResponse {
        $this->authorize('startSubscription', $team);
        $user = AuthController::getCurrentUser();
        abort_if(!$user, 401, 'Nicht autorisiert');
                
        $checkoutSession = $this->subscriptionService->createStripeCheckoutSessionForTeam($user, $team, $priceType);

        return response()->json([
            'url' => $checkoutSession->url,
        ]);
    }

    public function cancelSubscription(Team $team): JsonResponse {
        $this->authorize('cancelSubscription', $team);

        if (!$team->isSubscribed()) {
            return response()->json(['message' => 'Team does not have an active subscription'], 400);
        }

        // Get the subscription
        $subscription = $team->getActiveSubscription();

        if (!$subscription) {
            return response()->json(['message' => 'Subscription not found'], 404);
        }
        
        if ($team->isOnTrial()) {
            // cancel immediately since we manage trial on our own
            $subscription->noProrate()->cancelNow();
        } else {
            // Standard cancellation at period end
            $subscription->cancel();
        }
        return response()->json([
            'message' => 'Subscription canceled successfully',
        ]);
    }

    /**
     * Create a checkout session to resume a canceled subscription
     */
    public function resumeSubscription(Team $team): JsonResponse {
        $this->authorize('startSubscription', $team);
        $user = AuthController::getCurrentUser();
        abort_if(!$user, 401, 'Nicht autorisiert');
        
        // Check if the team has a subscription that's ending
        abort_if(!$team->isSubscribed() || !$team->getActiveSubscription()?->onGracePeriod(), 400, 'Kein gekündigtes Abonnement gefunden');
        
        $team->getActiveSubscription()->resume();

        return response()->json([
            'message' => 'Subscription resumed successfully',
        ]);
    }

    /**
     * Get team invoices
     *
     * @param Team $team
     * @return JsonResponse
     */
    public function getInvoices(Team $team): JsonResponse {
        $this->authorize('viewInvoices', $team);

        try {
            $invoiceData = $this->subscriptionService->getInvoicesForTeam($team);

            return response()->json(['invoices' => $invoiceData]);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve team invoices', [
                'team_id' => $team->id,
                'error'   => $e->getMessage()
            ]);

            return response()->json(['error' => 'Failed to retrieve invoices'], 500);
        }
    }
}
