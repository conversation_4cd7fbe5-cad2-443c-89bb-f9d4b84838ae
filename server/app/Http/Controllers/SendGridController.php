<?php

namespace App\Http\Controllers;

use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class SendGridController extends Controller {
    private string $slackWebhookUrl = '*********************************************************************************';

    public function event(Request $request): \Illuminate\Http\Response|JsonResponse|Application|ResponseFactory {
        $events = $request->json()->all();

        foreach ($events as $event) {
            $email = $event['email'];
            $eventType = $event['event'];

            // Check for the "dropped" event
            if ($eventType === 'dropped' || $eventType === 'bounce' || $eventType === 'deferred') {
                $reason = $event['reason'] ?? 'No reason provided';

                $textSnippet = "";

                if ($eventType === 'dropped') {
                    $textSnippet = "has been dropped";
                } else if ($eventType === 'bounce') {
                    $textSnippet = "has bounced";
                } else if ($eventType === 'deferred') {
                    $textSnippet = "was deferred";
                }

                $slackMessage = [
                    'text' => "E-Mail to $email $textSnippet. Reason: $reason"
                ];

                Http::post($this->slackWebhookUrl, $slackMessage);
            }
        }

        return response()->json([
            'message' => 'Event processed.'
        ], 200);
    }
}
