<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware {
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [
        '/logout',
        '/sanctum/token',
        '/api/*',
        'stripe/*',

        // Stateless contact form without cookies, doesn't require CSRF protection
        '/contact/send',
        //        '/login'
    ];
}
