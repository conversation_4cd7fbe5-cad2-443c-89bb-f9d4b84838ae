<?php

namespace App\Http\Middleware;

use App\Http\Controllers\AuthController;
use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Sentry\State\Scope;

class SentryContext {
    public function handle(Request $request, Closure $next): mixed {
        if (app()->bound('sentry')) {
            \Sentry\configureScope(function (Scope $scope): void {
                $user = AuthController::getCurrentUser();
                $scope->setTag('loggedIn', ($user instanceof User) ? 'true' : 'false');
                if ($user instanceof User) {
                    $scope->setUser([
                        'id'    => $user->id,
                        'email' => $user->email,
                        'name'  => $user->person->getFullName(),
                    ]);
                }
            });
        }

        return $next($request);
    }
}
