# Welcome to Serverless!
#
# This file is the main config file for your service.
# It's very minimal at this point and uses default values.
# You can always add more config options for more control.
# We've included some commented out config examples here.
# Just uncomment any of them to get that config option.
#
# For full config options, check the docs:
#    docs.serverless.com
#
# Happy Coding!

service: clubmanager
# app and org for use with dashboard.serverless.com
#app: your-app-name
#org: your-org-name

# You can pin your service to only deploy with a specific Serverless version
# Check out our docs for more details
frameworkVersion: '2'

provider:
  name: aws
  runtime: nodejs14.x
  lambdaHashingVersion: ********
  stage: dev
  apiName: ${sls:stage}Clubmanager
  region: eu-central-1
  accountId: #{AWS::AccountId}
  profile: serverless
  iam:
    role:
      statements:
        - Effect: "Allow"
          Action:
            - "dynamodb:*"
          Resource:
            - "arn:aws:dynamodb:#{AWS::Region}:${AWS::AccountId}:table/*-${sls:stage}"
            - "arn:aws:dynamodb:#{AWS::Region}:${AWS::AccountId}:table/*-${sls:stage}/*"

# you can add statements to the Lambda function's IAM Role here
#  iamRoleStatements:
#    - Effect: "Allow"
#      Action:
#        - "s3:ListBucket"
#      Resource: { "Fn::Join" : ["", ["arn:aws:s3:::", { "Ref" : "ServerlessDeploymentBucket" } ] ]  }
#    - Effect: "Allow"
#      Action:
#        - "s3:PutObject"
#      Resource:
#        Fn::Join:
#          - ""
#          - - "arn:aws:s3:::"
#            - "Ref" : "ServerlessDeploymentBucket"
#            - "/*"

# you can define service wide environment variables here
#  environment:
#    variable1: value1

# you can add packaging information here
#package:
#  patterns:
#    - '!exclude-me.py'
#    - '!exclude-me-dir/**'
#    - include-me.py
#    - include-me-dir/**

functions:
  hello:
    handler: functions/handler.hello
    events:
      - httpApi:
          path: /hello
          method: get
  postConfirmationLambdaTrigger:
    handler: functions/cognito_post_confirmation.handler
    environment:
      USERTABLE: ${self:resources.Resources.LoginTable.Properties.TableName}
    events:
      - cognitoUserPool:
          pool: ${self:resources.Resources.CognitoUserPool.Properties.UserPoolName}
          trigger: PostConfirmation
          existing: true

#    The following are a few example events you can configure
#    NOTE: Please make sure to change your handler code to work with those events
#    Check the event documentation for details
#    events:
#      - httpApi:
#          path: /users/create
#          method: get
#      - websocket: $connect
#      - s3: ${env:BUCKET}
#      - schedule: rate(10 minutes)
#      - sns: greeter-topic
#      - stream: arn:aws:dynamodb:region:XXXXXX:table/foo/stream/1970-01-01T00:00:00.000
#      - alexaSkill: amzn1.ask.skill.xx-xx-xx-xx
#      - alexaSmartHome: amzn1.ask.skill.xx-xx-xx-xx
#      - iot:
#          sql: "SELECT * FROM 'some_topic'"
#      - cloudwatchEvent:
#          event:
#            source:
#              - "aws.ec2"
#            detail-type:
#              - "EC2 Instance State-change Notification"
#            detail:
#              state:
#                - pending
#      - cloudwatchLog: '/aws/lambda/hello'
#      - cognitoUserPool:
#          pool: MyUserPool
#          trigger: PreSignUp
#      - alb:
#          listenerArn: arn:aws:elasticloadbalancing:us-east-1:XXXXXX:listener/app/my-load-balancer/50dc6c495c0c9188/
#          priority: 1
#          conditions:
#            host: example.com
#            path: /hello

#    Define function environment variables here
#    environment:
#      variable2: value2

# you can add CloudFormation resource templates here
resources:
  Resources:
    CognitoUserPool:
      Type: "AWS::Cognito::UserPool"
      Properties:
        MfaConfiguration: OFF
        UserPoolName: clubmanager-pool-${sls:stage}
        UsernameAttributes:
          - email
        AutoVerifiedAttributes:
          - email
        EmailConfiguration:
          EmailSendingAccount: COGNITO_DEFAULT
        Policies:
          PasswordPolicy:
            MinimumLength: 6
            RequireLowercase: False
            RequireNumbers: True
            RequireSymbols: False
            RequireUppercase: True
    CognitoUserPoolClient:
      Type: "AWS::Cognito::UserPoolClient"
      Properties:
        ClientName: clubmanager-pool-client-${sls:stage}
        GenerateSecret: False
        UserPoolId:
          Ref: CognitoUserPool

    UserPoolLambdaInvokePermission:
      Type: AWS::Lambda::Permission
      Properties:
        Action: lambda:invokeFunction
        Principal: cognito-idp.amazonaws.com
        FunctionName: clubmanager-${sls:stage}-postConfirmationLambdaTrigger
        SourceArn: arn:aws:cognito-idp:#{AWS::Region}:#{AWS::AccountId}:userpool/*
      DependsOn: PostConfirmationLambdaTriggerLambdaFunction

    FrontendBucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: eltini-clubmanager-ui-${sls:stage}

    LoginTable:
      Type: "AWS::DynamoDB::Table"
      Properties:
        TableName: Login-${sls:stage}
        AttributeDefinitions:
          - AttributeName: "cognito_id"
            AttributeType: "S"
        KeySchema:
          - AttributeName: "cognito_id"
            KeyType: "HASH"
        BillingMode: PAY_PER_REQUEST

#    LoginTableAccessRole:
#      Type: "AWS::IAM::Role"
#      Properties:
#        RoleName: "LoginTableAccess-${sls:stage}"
#        AssumeRolePolicyDocument:
#          Version: "2012-10-17"
#          Statement:
#            - Effect: "Allow"
#              Principal:
#                Service:
#                  - "appsync.amazonaws.com"
#              Action:
#                - "sts:AssumeRole"
#        Policies:
#          - PolicyName: "Dynamo-${self:custom.appSync.serviceRole}-Policy"
#            PolicyDocument:
#              Version: "2012-10-17"
#              Statement:
#                - Effect: "Allow"
#                  Action:
#                    - "dynamodb:Query"
#                    - "dynamodb:BatchWriteItem"
#                    - "dynamodb:GetItem"
#                    - "dynamodb:DeleteItem"
#                    - "dynamodb:PutItem"
#                    - "dynamodb:Scan"
#                    - "dynamodb:UpdateItem"
#                  Resource:
#                    - "arn:aws:dynamodb:#{AWS::Region}:#{AWS::AccountId}:table/Login"
#                    - "arn:aws:dynamodb:#{AWS::Region}:#{AWS::AccountId}:table/Login/*"

#  Resources:
#    NewResource:
#      Type: AWS::S3::Bucket
#      Properties:
#        BucketName: my-new-bucket
#  Outputs:
#     NewOutput:
#       Description: "Description for the output"
#       Value: "Some output value"

package:
  patterns:
    - '!client/**'
    - '!node_modules/**'

plugins:
  - serverless-finch
  - aws-amplify-serverless-plugin
  - serverless-appsync-plugin
  - serverless-pseudo-parameters
  - serverless-offline

custom:
  client:
    bucketName: eltini-clubmanager-ui-${sls:stage}
    distributionFolder: client/dist/spa

  amplify:
    - filename: client/src/aws-exports.js
      type: javascript
      appClient: CognitoUserPoolClient
      s3bucket: disabled