declare global {
    // eslint-disable-next-line @typescript-eslint/no-namespace
    namespace Cypress {
        interface Chainable<Subject> {

            assertRedirect(path: string): Chainable<Subject>;
        }
    }
}

export function registerLaravelAssertions() {
    Cypress.Commands.add('assertRedirect', (path: string) => {
        cy.location('pathname').should('eq', `/${path}`.replace(/^\/\//, '/'));
    });
}
