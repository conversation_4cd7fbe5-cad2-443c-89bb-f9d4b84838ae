import { ROUTE_NAMES } from 'src/router/route-names';

declare global {
    namespace Cypress {
        interface Chainable<Subject> {
            login(email: string, expectedPath?: string | undefined): Chainable<Subject>

            logout(): Chainable<Subject>

            // seed(cleanupOnly?: boolean | undefined): Chainable<Subject>

            forceLogout(): Chainable<Subject>

            isMainPage(): Chainable<Subject>

            nav(menuItem: keyof typeof ROUTE_NAMES): Chainable<Subject>


        }
    }
}
