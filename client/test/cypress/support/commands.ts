// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add("login", (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add("drag", { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add("dismiss", { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite("visit", (originalFn, url, options) => { ... })

// DO NOT REMOVE
// Imports Quasar Cypress AE predefined commands
import { registerCommands as registerQuasarCommands } from '@quasar/quasar-app-extension-testing-e2e-cypress';
import 'cypress-wait-until';
import { ROUTE_NAMES } from 'src/router/route-names';
import { registerLaravelCommands } from './laravel/laravel-commands';

registerQuasarCommands();
registerLaravelCommands();



const defaultPassword = '12345678';

Cypress.Commands.overwrite('visit', (visit, url, options) => {
    // wait for the user data and permissions to be returned => does not happen on login page
    // cy.intercept('/v1/users/*').as('initializeUserSession')
    // cy.intercept('/v1/persons/*').as('initializeUserPermissions')

    // wait for each api requests that has been made
    cy.intercept('/v1/**').as('requests')

    visit(url, options);

    // this does not work, sometimes there is one wait more than requests, maybe there is a request beeing made before wait command is added?
    // cy.get('@requests.all', {timeout: 30000}).each((interception) => {
    //     cy.wait('@requests', ).then(request => {
    //         cy.log('wait for request', request.request.url);
    //     })
    // })

    // cy.wait('@initializeUserSession')
    // cy.wait('@initializeUserPermissions')

    // wait for loading mask to be shown and hidden again whenever api requests were made
    cy.get('@requests.all', ).then((all) => {
        if(all.length > 0){
            cy.get('#q-loading').should('exist')
            cy.get('#q-loading').should('not.exist')
        }
    })

})

Cypress.Commands.add('login', (email, expectedPath: string = '/') => {
    cy.session([email], () => {
        cy.visit('/');

        cy.url().should('include', '/auth/login');

        cy.get('input[name="email"]').type(email);
        cy.get('input[name="password"]').type(defaultPassword);
        cy.get('button[name="login"]').click();

        cy.location('pathname').should('eq', expectedPath);
    }, {
        validate() {
            cy.intercept('/v1/users/*').as('initializeUserSession')
            cy.visit('/');
            cy.wait('@initializeUserSession')
            cy.get('@initializeUserSession.all').should('have.length.at.least', 1)
        },
    })
});

Cypress.Commands.add('logout', () => {
    cy.visit('/');
    cy.dataCy('user-dropdown').click();
    cy.dataCy('logout-button').click();

    cy.location('pathname').should('eq', '/auth/login');
});

Cypress.Commands.add('forceLogout', () => {
    cy.clearLocalStorage('auth');
    cy.visit('/');

    cy.url().should('include', '/auth/login');
});

Cypress.Commands.add('isMainPage', () => {
    // We use equality here, since it's the root page and slashes are everywhere
    cy.visit('/');
    cy.location('pathname').should('eq','/');
});

Cypress.Commands.add('nav', (menuItem: keyof typeof ROUTE_NAMES) => {
    cy.dataCy('menu-' + menuItem).click();
});

// Cypress.Commands.add('seed', (cleanupOnly: boolean = false) => {
//     const isCI = Cypress.env('CI') ;
//     const command = (isCI ? 'cd ../server && php' : 'ddev') + ' artisan db:seed --class=CypressSeeder --env=testing';
//
//     cy.exec(command, {
//         env: { CYPRESS_CLEANUP_ONLY: cleanupOnly ? 'true' : 'false' },
//     });
// })
