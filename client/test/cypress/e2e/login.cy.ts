describe('Login', () => {
    before(() => {
        cy.forceLogout();
        cy.seed('CypressSeeder');
    });

    after(() => {
        // cy.seed('CypressSeeder');
    });

    afterEach(() => {
        // cy.forceLogout();
    });

    it('Registered user should be able to login', () => {
        cy.login('<EMAIL>');
        cy.isMainPage();
    });

    it('User should be able to logout', () => {
        cy.login('<EMAIL>');
        cy.visit('/');
        cy.logout();
    });

    it('User without assigned team see create form', () => {
        cy.login('<EMAIL>');
        cy.visit('/');
        cy.dataCy('team-create-form').should('exist')
        // cy.logout();
    });

    it('User with assigned team sees appointment overview', () => {
        cy.login('<EMAIL>');
        cy.visit('/');
        cy.dataCy('appointment-overview').should('exist');
        // cy.logout();
    });

    it('User with unverified email should see request to verify email', () => {
        cy.login('<EMAIL>', '/site/verify-mail');
        cy.visit('/');
        cy.dataCy('verify-email-request-message').should('exist')

        // Logout via custom logout button in LoginLayout
        cy.dataCy('logout-button').click();
    });

});
