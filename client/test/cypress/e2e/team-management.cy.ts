import { ROUTE_NAMES } from 'src/router/route-names';

describe('Team Management', () => {
    before(() => {
        cy.seed('CypressSeeder');
    });

    after(() => {
        // cy.seed(true);
    });

    it('Can add new team member', () => {
        cy.login('<EMAIL>');
        cy.visit('/');
        cy.nav(ROUTE_NAMES.TEAMS);

        cy.dataCy('"team-card-Cypress Team"').should('exist');
        cy.dataCy('show-team-members').click();
        cy.dataCy('add-team-member').click();

        cy.dataCy('new-member-name-input').type('New Team Member');
        cy.dataCy('save-new-member').last().click();

        // Reload and check if member still exists
        cy.reload()
        cy.dataCy('member-name-input').last().should('have.value', 'New Team Member');
    })

    // TODO(fabzo): Later
    // it('Can rename new team member', () => {
    //     cy.login('<EMAIL>');
    //     cy.nav('teams');
    //
    //    // TODO: Needs to create a team member first
    //
    //     const card = cy.get('div[name="team-card-Cypress Team"]')
    //     card.should('exist');
    //     card.get('a[name="show-team-members"]').click();
    //     cy.get('button[name="add-team-member"]').click();
    //
    //     cy.get('button[name="save-or-edit-member"]').last().click();
    //     cy.get('input[name="member-name-input"]').last().type('Edited New Team Member');
    //     cy.get('button[name="save-or-edit-member"]').last().click();
    //
    //     // Reload and check if member still exists
    //     cy.reload()
    //     cy.get('input[name="member-name-input"]').last().should('have.value', 'Edited New Team Member');
    // })

});
