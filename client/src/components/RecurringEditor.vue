<template>
  <q-card v-if="recurringOptions" class="q-mt-md" :flat="flat">
    <q-select
        v-if="ruleEditable"
      v-model="recurringOptions"
      :disable="!recurring.isDateBeginSet()"
      :options="recurring.getRecurringOptionTemplates()"
      filled
      label="Wiederholen?"
    />

    <template v-if="isRecurring()">
        <template v-if="!ruleEditable">
            {{recurring.getRecurrenceText()}}
        </template>
        <template v-else>
            <q-card-section v-if="recurringOptions.label === CustomRecurringOptionLabel"
                            class="row items-center q-py-none"
            >
                <q-input borderless label="Wiederholen alle" model-value="" readonly />
                <q-input
                    v-model.number="recurringOptions.options.interval"
                    :rules="[val => val > 0 || 'nur Zahlen über 0']"
                    class="q-mr-md"
                    dense
                    filled
                    hide-bottom-space
                    style="max-width: 90px"
                    type="number"
                />
                <q-select
                    :model-value="recurring.getSelectedRecurringFrequencyOption()"
                    :options="recurring.getRecurringFrequencyOptions()"
                    dense
                    filled
                    @update:model-value="newValue => setRecurringFrequencyOption(newValue)"
                />
            </q-card-section>
            <q-card-section v-if="recurringOptions.options.freq === Frequency.MONTHLY" class="row items-center q-py-none">
                <q-input borderless label="am" model-value="" readonly />
                <q-select
                    :model-value="recurring.getSelectedRecurringMonthOption()"
                    :options="recurring.getRecurringMonthOptions()"
                    :rules="[val => !!val || 'Wähle eine Option aus']"
                    dense
                    filled
                    hide-bottom-space
                    style="min-width: 150px"
                    @update:model-value="newValue => setRecurringMonthOption(newValue)"
                />
            </q-card-section>
            <q-card-section v-if="recurringOptions.options.freq === Frequency.WEEKLY">
                <q-input borderless label="Wiederholen am" model-value="" readonly />

                <q-field
                    :model-value="recurringOptions.options.byweekday"
                    :rules="[val => val.length !== 0 || 'Wähle mindestens eine Option aus']"
                    borderless
                >
                    <template v-slot:control>
                        <template
                            v-for="(weekday, index) in RecurringWeekDayOptions"
                            :key="index"
                        >
                            <q-checkbox
                                v-model="recurringOptions.options.byweekday"
                                :val="weekday"
                                class="q-pr-lg"
                                dense
                                left-label
                            >
                                {{ $q.lang.date.daysShort[weekday.getJsWeekday()] }}.
                                <q-tooltip :delay="1000">
                                    {{ $q.lang.date.days[weekday.getJsWeekday()] }}
                                </q-tooltip>
                            </q-checkbox>
                        </template>
                    </template>
                </q-field>
            </q-card-section>
        </template>

      <q-card-section v-if="dateEnd === undefined">
        <DateTimeInputField
          :mask="defaultDateMask"
          v-model="dateUntilFormatted"
          :rules="[val => !!val || 'Bitte ein Enddatum eingeben.']"
          label="Wiederholen bis"
          type="date"
        />
      </q-card-section>
    </template>
  </q-card>

</template>

<script lang="ts" setup>

  import {
    CustomRecurringOptionLabel,
    defaultNonRecurringOptionTemplate,
    RecurringFrequencyMergeOption,
    RecurringMonthMergeOption,
    RecurringOption,
    RecurringWeekDayOptions,
    useRecurring,
  } from 'src/composables/useRecurring';
  import { Frequency, Options as RRuleOptions } from 'rrule/dist/esm/types';
  import { computed, ref, watch, watchEffect } from 'vue';
  import { datetime, RRule } from 'rrule';
  import { defaultDateMask } from 'src/i18n';
  import DateTimeInputField from 'src/components/DateTimeInputField.vue';
  import { date, Quasar } from 'quasar';

  const props = withDefaults(defineProps<{
    dateBegin: Date | null
    dateEnd?: Date | null
    ruleEditable?: boolean
    flat?: boolean
  }>(), {
      ruleEditable: true,
      flat: false
  })

  const rruleString = defineModel<string | null>();
  const recurringOptions = ref<RecurringOption>(getInitialRecurringOptions());
  const rrule = computed(() => createRRule());

  const dateUntilLocal = ref<Date | null | undefined>(props.dateEnd ? props.dateEnd : recurringOptions.value.options.until);
  watch(() => props.dateEnd, value => dateUntilLocal.value = value);

  const recurring = useRecurring(rrule);
  watchEffect(() => {
    if (!isRecurring()) {
      rruleString.value = null;
    } else if (rrule.value) {
      rruleString.value = rrule.value.toString();
    }
  });

  watch(() => props.dateBegin, (value, oldValue) => {
      if (value?.toDateString() !== oldValue?.toDateString()) {
        resetWeekdayAndMonthOptions();
      }
    },
  );

  function resetWeekdayAndMonthOptions() {
    recurringOptions.value.options = {
      ...recurringOptions.value.options,
      bymonthday: null,
      byweekday: [],
    };
  }


  function getInitialRecurringOptions() {
    let _return: RecurringOption;
    if (rruleString.value === null) {
      _return = defaultNonRecurringOptionTemplate;
    } else {
      _return = {
        label: CustomRecurringOptionLabel,
        options: RRule.fromString(rruleString.value!).origOptions,
      };
      if (_return.options.interval === undefined) {
        _return.options.interval = 1;
      }
    }

    return _return;
  }

  function createRRule(): RRule {
    const mergeOptions: Partial<RRuleOptions> = {};

    const dateStart = props.dateBegin;
    if (dateStart) {
      mergeOptions.dtstart = datetime(dateStart.getFullYear(), dateStart.getMonth() + 1, dateStart.getDate());
    }

    const dateUntil = dateUntilLocal.value;
    if(dateUntil) {
      mergeOptions.until = datetime(dateUntil.getFullYear(), dateUntil.getMonth() + 1, dateUntil.getDate());
    } else {
      mergeOptions.until = null;
    }

    return new RRule({
      ...recurringOptions.value.options,
      ...mergeOptions,
    });
  }

  function isRecurring(): boolean {
    return recurringOptions.value.options.freq !== undefined;
  }

  function setRecurringFrequencyOption(option: RecurringFrequencyMergeOption) {
    recurringOptions.value.options = { ...recurringOptions.value.options, ...option.mergeOptions };
  }

  function setRecurringMonthOption(option: RecurringMonthMergeOption) {
    recurringOptions.value.options = { ...recurringOptions.value.options, ...option.mergeOptions };
  }

  const dateUntilFormatted = computed({
    get() {
      return dateUntilLocal.value ? date.formatDate(dateUntilLocal.value, defaultDateMask, Quasar.lang.date) : '';
    },
    set(value) {
      dateUntilLocal.value = date.extractDate(value, defaultDateMask);
    },
  });

</script>

<style lang="scss" scoped></style>
