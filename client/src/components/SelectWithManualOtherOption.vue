<template>
    <q-select
        v-if="!otherSelected"
        v-model="value"
        :options="optionsWithOther"
        :label="label"
        :outlined="outlined"
        @update:modelValue="handleValueUpdate"
    >
        <template v-slot:after>
            <slot name="after"></slot>
        </template>
    </q-select>
    <q-input
        v-else-if="otherSelected && valueType === 'string'"
        v-model="value"
        :label="showOtherInLabel ? label + ' ' + otherOptionName + ':' : label"
        autofocus
        :outlined="outlined"
        clearable
        type="text"
        @clear="onClear"
    >
        <template v-slot:append v-if="value?.length === 0">
            <q-icon name="cancel" class="cursor-pointer"
                @click="onClear"
            />
        </template>

        <template v-slot:after>
            <slot name="after"></slot>
        </template>
    </q-input>
    <q-input
        v-else-if="otherSelected && valueType === 'number'"
        v-model.number="value"
        :label="showOtherInLabel ? label + ' ' + otherOptionName + ':' : label"
        autofocus
        :outlined="outlined"
        clearable
        type="number"
        inputmode="numeric"
        pattern="[0-9]*"
        class="no-number-spinners"
        @clear="onClear"
        :rules="[numericRule]"
    >
        <template v-slot:append v-if="value?.length === 0">
            <q-icon name="cancel" class="cursor-pointer"
                @click="onClear"
            />
        </template>

        <template v-slot:after>
            <slot name="after"></slot>
        </template>
    </q-input>
</template>

<script lang="ts" setup>
    import { computed, ref, watch } from 'vue';

    type ValueType = 'string' | 'number';

    const props = withDefaults(defineProps<{
        label: string,
        options: string[]
        otherOptionName?: string,
        valueType?: ValueType,
        showOtherInLabel?: boolean,
        outlined?: boolean
    }>(), {
        otherOptionName: 'Sonstiges',
        valueType: 'string',
        showOtherInLabel: true,
        outlined: true
    });

    const numericRule = (val: unknown) => /^\d+$/.test(String(val)) || 'Bitte nur Zahlen eingeben';

    const optionsWithOther = computed(() => props.options.concat(props.otherOptionName))
    const value = defineModel<string | number>();

    const otherSelected = ref(false)
    const lastDropdownValue = ref<string | number | null>(null);


    function normalise(raw: unknown): string | number {
      return props.valueType === 'number' ? Number(raw) : String(raw);
    }

    const candidate = normalise(value.value);

    if (
      value.value !== undefined &&
      props.options.map(normalise).includes(candidate)
    ) {
      // candidate is now guaranteed to be the same primitive type
      lastDropdownValue.value = candidate as (typeof props.options)[number];
    }

    if (
      value.value !== undefined &&
      props.options.includes(String(value.value))
    ) {
        lastDropdownValue.value = value.value ?? null;
    }

    const handleValueUpdate = (val: string | number) => {
        if (val === props.otherOptionName) {
            otherSelected.value = true;
            value.value = props.valueType === 'number' ? 0 : '';
        } else {
            lastDropdownValue.value = val;
        }
    }

    function onClear() {
        // Revert to last selected dropdown value, or fallback to first option if not set
        if (lastDropdownValue.value !== null && lastDropdownValue.value !== undefined) {
            value.value = lastDropdownValue.value;
        } else {
            value.value = props.options[0] ?? null;
        }
        otherSelected.value = false;
    }

</script>

<style>
/* Remove number input spinners */
.no-number-spinners input[type="number"]::-webkit-inner-spin-button,
.no-number-spinners input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.no-number-spinners input[type="number"] {
    -moz-appearance: textfield;
}
</style>
