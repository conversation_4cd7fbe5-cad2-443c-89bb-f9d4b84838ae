<template>
    <q-input ref="inputRef"
             v-model="value"
             :clearable="clearable"
             :label="label"
             :rules="rules"
             @clear="clear"
    >

        <template v-slot:before>
            <q-icon v-if="type === 'date' || type === 'datetime'" class="cursor-pointer" name="event">
                <q-popup-proxy ref="qDateProxy" v-if="isReady" :target="inputRef?.$el" class="date-time-input-popup" cover
                               transition-hide="scale"
                               transition-show="scale"
                >
                    <q-date
                        v-model="value"
                        :mask="mask"
                        :options="options"
                        :title="value === undefined || value.length === 0 ? label + ' auswählen' : ''"
                        @update:model-value="$refs.qDateProxy.hide()"
                    >
                        <div class="row items-center justify-end">
                            <q-btn v-close-popup :label="textClosePopup" color="primary" flat />
                        </div>
                    </q-date>
                </q-popup-proxy>
            </q-icon>

            <q-icon v-if="type === 'time' || type === 'datetime'" class="cursor-pointer" name="access_time">
                <q-popup-proxy ref="qTimeProxy" v-if="isReady" :target="inputRef?.$el" cover transition-hide="scale"
                               transition-show="scale">
                    <q-time
                        v-model="value"
                        :mask="mask"
                        format24h

                    >
                        <div class="row items-center justify-end">
                            <q-btn v-close-popup :label="textClosePopup" color="primary" flat />
                        </div>
                    </q-time>
                </q-popup-proxy>
            </q-icon>
        </template>

        <template v-slot:after>
            <slot name="after"></slot>
        </template>
    </q-input>
</template>

<script lang="ts" setup>

    import { QDateProps, QInput, QInputProps } from 'quasar';
    import { nextTick, onMounted, ref } from 'vue';

    const props = withDefaults(defineProps<{
        type: 'date' | 'time' | 'datetime',
        mask: string,
        label?: string,
        textClosePopup?: string,
        clearable?: boolean,
        rules?: QInputProps['rules'],
        options?: QDateProps['options'],
    }>(), {
        textClosePopup: 'OK',
    });

    const value = defineModel<string>();

    function clear() {
        value.value = '';
    }

    const inputRef = ref<QInput | null>(null)
    const popupTarget = ref<HTMLElement | null>(null);
    const isReady = ref(false);

    onMounted(() => {
        nextTick(() => {
            if (inputRef.value && inputRef.value.$el) {
                // Assign the DOM element as the target for the popup
                popupTarget.value = inputRef.value.$el;
                isReady.value = true; // Mark as ready to render popups
            }
        });
    });


</script>

<style lang="scss" scoped>
    .date-time-input-popup {
        min-width: 1px !important;
    }

</style>
