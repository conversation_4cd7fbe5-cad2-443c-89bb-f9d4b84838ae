<template>
  <q-card class="q-py-sm page-header" flat square>
    <q-card-section class="q-pb-sm" horizontal>
      <q-card-section class="q-pa-none">
        <slot name="title">
          <div class="text-h5">{{ props.title }}</div>
          <div class="text-grey">
            {{ props.subTitle }}
          </div>
        </slot>
      </q-card-section>
      <q-space />
      <q-card-section class="q-pa-none">
        <slot></slot>
      </q-card-section>
    </q-card-section>
  </q-card>
</template>

<script lang="ts" setup>

  import { useMeta } from 'quasar';

  const props = defineProps<{
    title: string
    subTitle?: string
  }>();

  useMeta(() => ({
    title: props.title,
  }));

</script>

<style lang="scss" scoped>

</style>
