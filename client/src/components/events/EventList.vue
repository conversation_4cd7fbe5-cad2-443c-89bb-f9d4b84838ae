<template>
    <q-pull-to-refresh class="absolute-top full-height" @refresh="refresh" />

    <q-pull-to-refresh @refresh="refresh">
        <PageHeaderWithAction :sub-title="formattedDateFromTo" :title="props.title">
            <q-btn
                v-if="
          props.withCreateButton && hasPermissionForAnyTeam('team.event.create')
        "
                :to="{ name: $routeNames.EVENTS_CREATE }"
                color="primary"
                icon="add"
                label="Termin erstellen"
                size="12px"
            />
        </PageHeaderWithAction>

        <div data-cy="appointment-overview" /> <!-- marker for tests -->
        <template v-if="initialLoadingDone && !teamsQueryStatus.isFetching.value">
            <div v-if="allEvents.length > 0" class="q-pt-sm">
                <q-select
                    v-if="teams.length > 1"
                    v-model="selectedTeams"
                    :options="teams"
                    clearable
                    label="Teams filtern"
                    multiple
                    option-label="name"
                    use-chips
                >
                    <template v-if="$q.platform.is.mobile" v-slot:append>
                        <q-btn outline size="md" v-close-popup class="close-select-dialog q-mr-sm">OK</q-btn>
                    </template>
                </q-select>
            </div>

            <q-card-actions vertical v-if="props.showLoadMoreEventsButton">
                <q-btn
                    padding="sm"
                    :loading="isFetchingPreviousPage"
                    color="primary"
                    flat
                    icon="arrow_upward"
                    label="Ältere Termine anzeigen"
                    @click.prevent="loadEarlierEvents"
                />
            </q-card-actions>
            <template v-if="filteredEvents.length > 0">
                <template v-for="(event) in filteredEvents" :key="event.id">
                    <div class="q-mb-lg">
                        <EventCard
                            :is-fetching-team-event-data="isFetching"
                            :show-team-header="
                teams.length > 1 && selectedTeams?.length !== 1
              "
                            :teamEvent="event"
                        ></EventCard>
                    </div>
                </template>
            </template>
            <template v-else>
                <q-card class="bg-secondary q-mt-sm">
                    <q-card-section class="text-center " >
                        <div>Keine Termine gefunden</div>
                    </q-card-section>
                    <q-card-actions
                        v-if="hasPermissionForAnyTeam('team.event.create')"
                        vertical
                    >
                        <q-btn
                            :to="{ name: $routeNames.EVENTS_CREATE }"
                            icon="add"
                            color="primary"
                            label="Termin erstellen"
                        />
                    </q-card-actions>
                </q-card>
            </template>

            <q-card-actions vertical>
                <q-btn
                    v-if="props.showLoadMoreEventsButton"
                    class="q-mb-sm"
                    padding="sm"
                    :loading="isFetchingNextPage"
                    color="primary"
                    flat
                    icon="arrow_downward"
                    label="Mehr Termine anzeigen"
                    @click.prevent="loadLaterEvents"
                />

                <q-btn
                    v-if="props.showAllEventsButton"
                    :to="{ name: $routeNames.EVENTS }"
                    outline
                    label="Alle Termine anzeigen"
                />

                <q-btn
                    v-if="props.showSubscribeEventsButton"
                    :to="{ name: $routeNames.ICALENDARS }"
                    outline
                    icon="mdi-calendar-sync"
                    label="Termine abonnieren"
                />

            </q-card-actions>

        </template>
        <template v-else>
            <div>lade Termine...
                <q-spinner />
            </div>
        </template>
    </q-pull-to-refresh>
</template>

<script lang="ts" setup>
    import EventCard from 'src/components/events/EventCard.vue';
    import { computed, ComputedRef, ref, watch } from 'vue';
    import { useInfiniteQuery, useQuery } from '@tanstack/vue-query';
    import { queries } from 'src/queries';
    import TeamEvent from 'src/models/TeamEvent';
    import Team from 'src/models/Team';
    import PageHeaderWithAction from 'src/components/global/PageHeaderWithAction.vue';
    import { date, QBtn, Quasar } from 'quasar';
    import { watchOnce } from '@vueuse/core';
    import subtractFromDate = date.subtractFromDate;
    import addToDate = date.addToDate;

    const props = withDefaults(
        defineProps<{
            title?: string;
            withCreateButton?: boolean;
            showAllEventsButton?: boolean;
            showLoadMoreEventsButton?: boolean;
            showSubscribeEventsButton?: boolean;
            startDate: Date;
            endDate: Date;
        }>(),
        {
            title: 'Meine Termine',
            withCreateButton: false,
            showAllEventsButton: false,
            showLoadMoreEventsButton: false,
            showSubscribeEventsButton: false,
        },
    );

    const initialLoadingDone = ref(false);
    const startDate = ref(props.startDate);
    const endDate = ref(props.endDate);

    const formattedDateFromTo = computed(() => {
        return (
            date.formatDate(startDate.value, 'DD.MM.YYYY', Quasar.lang.date) +
            ' - ' +
            date.formatDate(endDate.value, 'DD.MM.YYYY', Quasar.lang.date)
        );
    });

    const allEvents = ref<TeamEvent[]>([]);

    function loadEarlierEvents() {
        startDate.value = subtractFromDate(startDate.value, { days: 14 });
    }

    function loadLaterEvents() {
        endDate.value = addToDate(endDate.value, { days: 14 });
        fetchNextPage();
    }


    watch(startDate, () => {
        // fetchPreviousPage({ pageParam: { startDate: newValue, endDate: oldValue } });
        fetchPreviousPage();
    });

    watch(endDate, () => {
        // fetchNextPage({ pageParam: { startDate: oldValue, endDate: newValue } });
        fetchNextPage();
    });

    function refresh(done: () => never) {
        refetch().then(() => done());
    }

    const {
        isFetchingPreviousPage,
        isFetchingNextPage,
        isFetching,
        fetchPreviousPage,
        fetchNextPage,
        refetch,
    } = useInfiniteQuery({
        ...queries.events.allWithTeamAndMembersAndVotes(
            startDate.value,
            endDate.value,
        ),
        initialPageParam: ({ startDate: startDate.value, endDate: endDate.value }),
        getNextPageParam: (lastPage, allPages, lastPageParam) => ({ startDate: lastPageParam.endDate, endDate: endDate.value }),
        getPreviousPageParam: (firstPage, allPages, firstPageParam) => ({ startDate: startDate.value, endDate: firstPageParam.startDate }),
        select: (data) => {
            // save data to ref since tanstack query makes returned value readonly for cache usage
            let allData: TeamEvent[] = [];
            data.pages.map((group) => {
                allData = allData.concat(group);
            });
            allEvents.value = allData;
            return data;
        },
        refetchOnWindowFocus: true,
        structuralSharing: false
    });

    const teams = ref<Team[]>([]);
    const selectedTeams = ref<Team[]>([]);
    const teamsQueryStatus = useQuery({
        ...queries.teams.all(false),
        select: (data) => {
            teams.value = data;
            if (data.length == 1) {
                selectedTeams.value = [data[0]];
            }
            return data;
        },
        refetchOnWindowFocus: false,
        refetchOnReconnect: false,
    });

    const filteredEvents: ComputedRef<TeamEvent[]> = computed(() => {
        return allEvents.value.filter((teamEvent) => {
            if (selectedTeams.value?.length > 0) {
                return selectedTeams.value.some(
                    (selectedTeam) => teamEvent.team.id === selectedTeam.id,
                );
            }
            return true;
        });
    });

    watchOnce(filteredEvents, () => {
        initialLoadingDone.value = true;
    });
</script>

<style lang="scss" scoped></style>
