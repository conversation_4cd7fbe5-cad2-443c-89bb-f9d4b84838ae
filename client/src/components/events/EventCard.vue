<template>
  <q-card :class="[teamEvent.eventType, {cancelled: teamEvent.isCancelled}]" class="event-card">

    <q-card-section v-if="showTeamHeader" class="group bg-filled text-white" horizontal>
      <q-card-section v-if="teamEvent.team.iconName" class="q-py-none q-pr-none">
        <q-icon :name=teamEvent.team.iconName />
      </q-card-section>
      <q-card-section class="q-py-none">
        <div class="text-subtitle2">{{ teamEvent.team.name }}</div>
      </q-card-section>
    </q-card-section>

    <q-separator />

    <router-link
      v-if="withDetailButton"
      :to="{name: $routeNames.EVENTS_SHOW, params: {eventId: teamEvent.id}}"
      style="text-decoration: none"
    >
      <EventCardHeader
        :team-event="teamEvent"
        :with-detail-button="withDetailButton" />
    </router-link>
    <template v-else>
      <EventCardHeader
        :team-event="teamEvent"
        :with-detail-button="withDetailButton" />
    </template>


    <q-card-section class="times text-center" horizontal>
      <template v-if="teamEvent.hasTimeMeet()">
        <q-card-section class="fit">
          Treffen: <strong>{{ teamEvent.getTimeMeetFormatted() }}</strong>
        </q-card-section>
        <q-separator inset vertical />
      </template>
      <q-card-section class="fit">
        Beginn: <strong>{{ teamEvent.getTimeBeginFormatted() }}</strong>
      </q-card-section>
      <template v-if="teamEvent.hasTimeEnd()">
        <q-separator inset vertical />
        <q-card-section class="fit">
          Ende: <strong>{{ teamEvent.getTimeEndFormatted() }}</strong>
        </q-card-section>
      </template>
    </q-card-section>

    <q-separator v-if="teamEvent.addressString" inset />
    <q-card-section v-if="teamEvent.addressString" horizontal>
      <q-card-section class="q-py-sm full-width">
        <strong>Adresse: </strong><span>{{ teamEvent.addressString }}</span>
      </q-card-section>
      <q-card-section class="q-pa-none row no-wrap items-center">
        <q-btn :href="teamEvent.getAddressUrlForGoogleMaps()"
               class="full-height"
               flat
               icon="location_on"
               target="_blank"
        />
      </q-card-section>
    </q-card-section>

    <q-separator v-if="teamEvent.details" inset />
    <q-card-section v-if="teamEvent.details" class="details" horizontal>
      <q-card-section class="q-py-sm">
        <span v-html="teamEvent.details" />
      </q-card-section>
    </q-card-section>

    <q-separator v-if="teamEvent.detailsManager" inset />
    <q-card-section v-if="teamEvent.detailsManager" class="detailsManager" horizontal>
      <q-card-section class="q-py-sm">
        <strong>Nur sichtbar für {{ TeamRole.getDisplayName('manager') }}:</strong><br>
        <span v-html="teamEvent.detailsManager" />
      </q-card-section>
    </q-card-section>

    <q-separator />

    <template v-if="teamEvent.team?.members?.length > 0">
      <q-card-section class="voting" horizontal>
        <div v-if="teamEvent.getVoteSum('yes') > 0"
             :style="{width: teamEvent.getVotePercent('yes') + '%'}"
             class="text-center vote-yes vote-active vote-line"
        >
            {{ teamEvent.getVoteSumForStatusRole('yes', 'member') }}
            (+{{ teamEvent.getVoteSumForStatusRole('yes', 'manager') }})
            <q-tooltip anchor="top right" class="text-body2" self="top right">
                {{ teamEvent.getVoteSumForStatusRole('yes', 'member') }} {{ TeamRole.getDisplayName('member', teamEvent.getVoteSumForStatusRole('yes', 'member')) }}
                (+ {{ teamEvent.getVoteSumForStatusRole('yes', 'manager') }} {{ TeamRole.getDisplayName('manager', teamEvent.getVoteSumForStatusRole('yes', 'manager'))  }})
            </q-tooltip>
        </div>

        <div v-if="teamEvent.getVoteSum('maybe') > 0"
             :style="{width: teamEvent.getVotePercent('maybe') + '%'}"
             class="text-center vote-maybe vote-active vote-line"
        >{{ teamEvent.getVoteSum('maybe') }}
        </div>

        <div v-if="teamEvent.getVoteSum('no') > 0"
             :style="{width: teamEvent.getVotePercent('no') + '%'}"
             class="text-center vote-no vote-active vote-line"
        >{{ teamEvent.getVoteSum('no') }}
        </div>

        <div v-if="teamEvent.getVoteSum('none') > 0"
             :style="{width: teamEvent.getVotePercent('none') + '%'}"
             class="text-center"
        >{{ teamEvent.getVoteSum('none') }}
        </div>


        <q-btn class="col-1" icon="groups" label="" outline padding="none" text-color="primary"
               @click="openVoteList"
        />
      </q-card-section>

      <q-separator />

      <span v-for="(member, index) in voters" :key="member.id">
        <EventCardVoter
          :member="member"
          :team-event="teamEvent"
          @deleteComment="deleteComment"
          @saveComment="saveComment"
          @voteClicked="voteClicked"
        />
        <q-separator v-if="index !== voters.length - 1" inset />
      </span>


      <q-dialog
        :model-value="voteListVisible"
        maximized
        @update:model-value="closeVoteList"
      >
        <EventVoteList
          :show-team-header="showTeamHeader"
          :team-event="teamEvent"
          @deleteComment="deleteComment"
          @saveComment="saveComment"
          @voteClicked="voteClicked"
        />
      </q-dialog>
    </template>
  </q-card>
</template>

<script lang="ts" setup>

  import { computed, watch } from 'vue';
  import EventVoteList from 'src/components/events/EventVoteList.vue';
  import TeamEvent from 'src/models/TeamEvent';
  import TeamEventVote from 'src/models/TeamEventVote';
  import TeamMember from 'src/models/TeamMember';
  import { useRouter } from 'vue-router';
  import TeamRole from '../../models/TeamRole';
  import EventCardVoter from 'src/components/events/EventCardVoter.vue';
  import Comment from 'src/models/Comment';
  import EventCardHeader from 'src/components/events/EventCardHeader.vue';
  import TeamEventVoteType = App.Types.TeamEventVoteType;

  const props = withDefaults(defineProps<{
    teamEvent: TeamEvent
    isFetchingTeamEventData: boolean
    showTeamHeader?: boolean
    withDetailButton?: boolean
  }>(), {
    showTeamHeader: false,
    withDetailButton: true,
  });

  const router = useRouter();
  const voteListVisible = computed(() => {
    return router.currentRoute.value.query['voteList'] === props.teamEvent.id;
  });

  async function openVoteList() {
    await router.push({ query: { voteList: props.teamEvent.id } });
  }

  async function closeVoteList() {
    // await router.push({name: router.currentRoute.value.name})
    await router.replace({ query: {} });
    // router.back()
  }

  const voters = computed(() => {
    return props.teamEvent.team.getAssignedMembers();
  });

  const teamEventDataFetching = () => {
    // used to fix https://github.com/eltini/clubmanager/issues/418
    return new Promise<void>((resolve) => {
      watch(() => props.isFetchingTeamEventData, (newValue, oldValue) => {
        if (!newValue && oldValue) {
          // event fetching done
          resolve();
        }
      });
    });
  };

  async function voteClicked(teamMember: TeamMember, vote: TeamEventVoteType) {
    props.teamEvent.setVoteUpdating(teamMember, vote);
    if (props.isFetchingTeamEventData) {
      const deleteAttempt = props.teamEvent.hasVoted(teamMember, vote);
      await teamEventDataFetching();
      const voteDifferentButAlreadySet = props.teamEvent.hasVoted(teamMember, vote) && !deleteAttempt;
      const voteAlreadyDeleted = deleteAttempt && !props.teamEvent.hasVoted(teamMember);
      if (voteDifferentButAlreadySet || voteAlreadyDeleted) {
        // abort vote since it was already set from somewhere else
        // see https://github.com/eltini/clubmanager/issues/622
        return;
      }
    }

    let eventVote = props.teamEvent.getVote(teamMember);
    const teamEvent = props.teamEvent;
    if (!eventVote) {
      eventVote = new TeamEventVote({
        teamEvent: teamEvent,
        teamMember: teamMember,
      } as TeamEventVote);
      teamEvent.votes.push(eventVote);
    }
    if (props.teamEvent.hasVoted(teamMember, vote)) {
      const index = teamEvent.votes.indexOf(eventVote);
      eventVote.destroy()
        .then(() => {
          props.teamEvent.setVoteUpdating(teamMember, vote, false);
        });
      if (index >= 0) {
        teamEvent.votes.splice(index, 1);
      }
    } else {
      eventVote = props.teamEvent.getVote(teamMember); // reload to get reactive vote
      if (eventVote) {
        eventVote.vote = vote;
        eventVote.save({
          with: ['teamEvent.id', 'teamMember.id'],
          returnScope: TeamEventVote.includes(['comment', 'author']),
        })
          .then(() => {
            props.teamEvent.setVoteUpdating(teamMember, vote, false);
            if (teamEvent.hasVoteComment(teamMember) && eventVote && !eventVote.hasSavedComment) {
              eventVote.comment = undefined;
            }
          });
      }
    }
  }

  function saveComment(comment: Comment) {
    if (comment.isTextEmpty()) {
      deleteComment(comment);
    } else {
      comment.save({ with: ['parent.id'] });
    }

  }

  function deleteComment(comment: Comment) {

    if (comment.isPersisted) {
      const commentable = comment.parent;
      comment.destroy();
      if (commentable) {
        // unset reference on parent
        commentable.comment = undefined;
      }
    }
  }

</script>

<style lang="scss" scoped>
  .event-card {

    &.cancelled {
      .bg-filled {
        background-color: darken($negative, 15%) !important;
      }
    }

    &.training {
      .bg-filled {
        // cannot use $primary, have to use var(--q-primary)
        // https://github.com/badsaarow/quasar2-storybook-boilerplate/issues/47#issuecomment-1057931751
        //background-color: var(--q-primary);
        background-color: $primary;
      }
    }

    &.match, &.tournament, &.event {
      .bg-filled {
        // cannot use $primary, have to use var(--q-primary)
        // https://github.com/badsaarow/quasar2-storybook-boilerplate/issues/47#issuecomment-1057931751
        background-color: darken($primary, 20%);
      }
    }

    .group {
    }

    .header {
    }

    .times {
    }

    .details {
    }

    .detailsManager {
      border-left: 0.3rem solid;
    }
  }
</style>
