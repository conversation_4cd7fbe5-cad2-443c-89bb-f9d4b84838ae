<template>
  <q-card-section class="voting" horizontal>
    <template v-if="voterNameVisible">
      <q-card-section class="col-4" horizontal>
        <q-avatar class="text-primary" color="secondary" icon="face" rounded size="lg" />
        <q-badge class="full-width q-pa-none ellipsis text-primary" color="secondary">{{ member.name }}</q-badge>
      </q-card-section>
      <q-separator vertical />
    </template>

    <q-card-section class="full-width" horizontal>
      <q-btn-group class="col-grow" spread>
        <q-btn
          :class="{'vote-yes vote-active': teamEvent.hasVoted(member, 'yes')}"
          :disable="!teamEvent.isVoteEnabled(member)"
          :loading="teamEvent.isVoteUpdating(member, 'yes')"
          icon="thumb_up"
          @click="voteClicked(member, 'yes')"
        />
        <q-separator inset vertical />
        <q-btn
          :class="{'vote-maybe vote-active': teamEvent.hasVoted(member, 'maybe')}"
          :disable="!teamEvent.isVoteEnabled(member)"
          :loading="teamEvent.isVoteUpdating(member, 'maybe')"
          icon="question_mark"
          @click="voteClicked(member, 'maybe')"
        />
        <q-separator inset vertical />
        <q-btn
          :class="{'vote-no vote-active': teamEvent.hasVoted(member, 'no')}"
          :disable="!teamEvent.isVoteEnabled(member)"
          :loading="teamEvent.isVoteUpdating(member, 'no')"
          icon="thumb_down"
          @click="voteClicked(member, 'no')"
        />
      </q-btn-group>

      <q-separator inset vertical />
      <q-btn :disable="!teamEvent.hasVoted(props.member)" :text-color="hasVoteComment && !isCommentVisible ? 'accent' : 'primary'" class="col-1" flat
             icon="comment"
             label=""
             @click="commentButtonClicked"
      />

    </q-card-section>
  </q-card-section>

  <q-slide-transition>
    <q-card-section v-if="isCommentVisible && comment" horizontal>
      <q-input ref="commentInput" v-model="comment.text" :dense="!isCommentEditable"
               :disable="!isCommentEditable"
               :label-slot="isCommentEditable"
               :loading="comment.isSaving"
               autogrow
               class="full-width"
               clearable
               filled
               @blur="saveComment"
               @clear="deleteComment"
               @keydown.enter.prevent="editButtonClicked">
        <template v-if="isCommentEditable" v-slot:label>Kommentar ({{ member.name }})</template>

        <template v-slot:after>
          <q-btn :icon="isCommentEditable ? 'check' : 'edit'" flat
                 stretch
                 @click="editButtonClicked"
          />
        </template>
      </q-input>
    </q-card-section>
  </q-slide-transition>
</template>

<script lang="ts" setup>
  import { useAuthStore } from 'src/stores/auth';
  import TeamMember from 'src/models/TeamMember';
  import TeamEvent from 'src/models/TeamEvent';
  import { computed, nextTick, ref } from 'vue';
  import Comment from 'src/models/Comment';
  import TeamEventVoteType = App.Types.TeamEventVoteType;

  const props = defineProps<{
    member: TeamMember
    teamEvent: TeamEvent
  }>();

  const voterNameVisible = computed(() => {
    const person = useAuthStore().person;
    if (person) {
      return person.teamMembers.length > 1;
    } else {
      return false;
    }
  });

  const commentInput = ref<HTMLInputElement>();
  const comment = computed(() => {
    return props.teamEvent.getVote(props.member)?.getOrCreateComment();
  });

  const hasVoteComment = computed(() => props.teamEvent.hasVoteComment(props.member));
  const isCommentVisible = ref(props.teamEvent.hasVoteComment(props.member));
  const isCommentEditable = ref(false);

  const emit = defineEmits<{
    voteClicked: [teamMember: TeamMember, vote: TeamEventVoteType],
    saveComment: [comment: Comment],
    deleteComment: [comment: Comment],
  }>();

  function voteClicked(teamMember: TeamMember, vote: TeamEventVoteType) {
    if (!isCommentVisible.value && vote !== props.teamEvent.getVote(props.member)?.vote) {
      isCommentEditable.value = true;
      isCommentVisible.value = true;
    }
    emit('voteClicked', teamMember, vote);
  }

  function commentButtonClicked() {
    isCommentVisible.value = !isCommentVisible.value;
    isCommentEditable.value = isCommentVisible.value && comment.value !== undefined && comment.value.isTextEmpty();
    if (isCommentEditable.value) {
      nextTick(() => {
        commentInput.value?.focus();
      });
    }
  }

  function editButtonClicked() {
    if (isCommentEditable.value) {
      if (!comment.value?.isSaving) {
        saveComment();
      }
      isCommentEditable.value = false;
    } else if (!comment.value?.isSaving && !comment.value?.isDeleting) {
      isCommentEditable.value = true;
      nextTick(() => {
        commentInput.value?.focus();
      });
    }
  }


  function saveComment() {
    if (comment.value && !comment.value?.isSaving) {
      emit('saveComment', comment.value);
    }
  }

  async function deleteComment() {
    if (comment.value) {
      emit('deleteComment', comment.value);
    }
    isCommentEditable.value = false;
    isCommentVisible.value = false;
  }

</script>

<style lang="scss" scoped>

</style>
