<template>
  <q-card>
    <q-list>
      <q-item class="bg-separator q-pr-xs q-py-none">
        <q-item-section>
          <q-item-label class="text-subtitle2">Aufgaben</q-item-label>
        </q-item-section>
        <q-item-section v-if="!isTaskListLoading && eventTaskData.length === 0">
          <q-item-label>Noch keine vorhanden</q-item-label>
        </q-item-section>
        <q-item-section side>
          <q-btn
            v-if="showTaskListEditableButton"
            :icon="isTaskListEditable ? 'check' : 'edit'"
            :loading="isTaskListLoading || isTaskConfigInitialLoading"
            class="full-height"
            flat
            @click="taskListEditableClicked"
          />
          <q-spinner v-else-if="isTaskListLoading" size="sm"></q-spinner>
        </q-item-section>
      </q-item>
      <q-separator />
      <q-item v-if="isTaskListError || isTaskConfigError">
        <q-item-section>
          <q-item-label class="text-negative"><PERSON><PERSON> beim <PERSON></q-item-label>
        </q-item-section>
      </q-item>

      <template v-else-if="!isTaskListLoading">

        <template
          v-for="(eventTask, index) in eventTaskDataSorted"
          :key="eventTask.id"
        >
          <EventTaskRow
            :event-task="eventTask as TeamEventTask"
            :is-task-list-editable="isTaskListEditable"
            :team-event="teamEvent"
            @deleteTask="deleteTask"
            @memberChanged="memberChanged"
          />

          <q-separator v-if="index !== eventTaskData.length - 1" inset />
        </template>


        <template v-if="isTaskListEditable && !isTaskConfigInitialLoading && hasPermissionForTeam(teamEvent.team, 'team.event.task.create')">
          <q-separator />

          <q-form @submit="saveNewTask">
            <q-item v-if="eventTaskConfigData.length > 0" dense>
              <q-item-section>
                <q-select
                  v-model="newTask.config"
                  :options="eventTaskConfigData"
                  :rules="[val => !!val || 'Bitte wähle eine Aufgabe aus']"
                  label="Aufgabe hinzufügen"
                  lazy-rules="ondemand"
                  option-label="title"
                />
              </q-item-section>
              <q-item-section v-if="hasPermissionForTeam(teamEvent.team, 'team.event.task.assign.other')" top>
                <q-select
                  v-model="newTask.teamMember"
                  :options="teamEvent.team.activeMembersSortedByName"
                  label="Person (Optional)"
                  option-label="name"
                />
              </q-item-section>
              <q-item-section side>
                <q-btn
                  :loading="newTask.isSaving"
                  class="full-height q-px-sm q-pb-lg"
                  dense
                  flat
                  icon="check"
                  type="submit"
                />
              </q-item-section>
            </q-item>
          </q-form>

          <q-form @submit="saveNewTaskConfig">
            <q-item class="q-pt-none" dense>
              <q-item-section>
                <q-input
                  v-model="newTaskConfig.title"
                  :rules="[
                      val => !!val || 'Bitte gib einen Titel ein',
                      val => eventTaskConfigData.find((config) => config.title === val) === undefined || 'Eine Aufgabe mit diesem Titel existiert schon'
                      ]"
                  label="Neue Aufgabe erstellen"
                  lazy-rules="ondemand"
                />
              </q-item-section>
              <q-item-section side>
                <q-btn
                  :loading="newTaskConfig.isSaving || isTaskConfigRefetching"
                  class="full-height q-px-sm q-pb-lg"
                  dense
                  flat
                  icon="check"
                  type="submit"
                />
              </q-item-section>
            </q-item>
          </q-form>
        </template>
      </template>

    </q-list>
  </q-card>
</template>

<script lang="ts" setup>

  import { computed, ref, unref } from 'vue';
  import { useMutation, useQuery } from '@tanstack/vue-query';
  import { saveTeamEventTaskConfigOnServer, saveTeamEventTaskOnServer } from 'src/queries/mutations/events';
  import { queries } from 'src/queries';
  import TeamEvent from 'src/models/TeamEvent';
  import TeamEventTask from 'src/models/TeamEventTask';
  import TeamEventTaskConfig from 'src/models/TeamEventTaskConfig';
  import TeamMember from 'src/models/TeamMember';
  import { useAuthStore } from 'src/stores/auth';
  import EventTaskRow from 'src/components/events/EventTaskRow.vue';
  import { sortBy } from 'lodash';

  const props = defineProps<{
    teamEvent: TeamEvent
  }>();

  const isTaskListEditable = ref(false);

  const eventTaskData = ref<TeamEventTask[]>([]);
  const eventTaskDataSorted = computed(() => {
    return sortBy(
      eventTaskData.value,
      task => task.config.title,
    );
  });
  const newTask = ref<TeamEventTask>(TeamEventTask.createForTeamEvent(props.teamEvent));
  const eventTaskConfigData = ref<TeamEventTaskConfig[]>([]);
  const newTaskConfig = ref<TeamEventTaskConfig>(TeamEventTaskConfig.createForTeam(props.teamEvent.team));

  const { hasPermissionForTeam } = useAuthStore();
  const showTaskListEditableButton = computed(() => {
    const team = props.teamEvent.team;
    return hasPermissionForTeam(team, 'team.event.task.create')
      || (eventTaskData.value.length > 0 && hasPermissionForTeam(team, 'team.event.task.delete'));
  });

  const {
    isPending: isTaskListLoading,
    isError: isTaskListError,
    refetch: refetchTaskList,
  } = useQuery({
    ...queries.eventTasks.allForEvent(props.teamEvent.id as string),
    select: (data) => {
      eventTaskData.value = data;
    },
  });


  const {
    isLoading: isTaskConfigInitialLoading,
    isError: isTaskConfigError,
    refetch: refetchTaskConfig,
    isFetched: isTaskConfigFetched,
    isRefetching: isTaskConfigRefetching,
  } = useQuery({
    ...queries.eventTasks.allConfigsForTeam(props.teamEvent.team.id as string),
    select: (data) => {
      eventTaskConfigData.value = data;
    },
    enabled: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });

  function taskListEditableClicked() {
    if (!isTaskConfigFetched.value) {
      refetchTaskConfig();
    }
    isTaskListEditable.value = !isTaskListEditable.value;
  }


  const { mutateAsync: saveTaskConfigOnServer } =
    useMutation({ mutationFn: saveTeamEventTaskConfigOnServer });

  async function saveNewTaskConfig() {
    await saveTaskConfigOnServer(newTaskConfig.value as TeamEventTaskConfig);
    await refetchTaskConfig();
    newTaskConfig.value = TeamEventTaskConfig.createForTeam(props.teamEvent.team);
  }


  const { mutateAsync: saveTaskOnServer } =
    useMutation({ mutationFn: saveTeamEventTaskOnServer });

  async function saveNewTask() {
    await saveTask(newTask.value as TeamEventTask);
    eventTaskData.value.push(unref(newTask));
    newTask.value = TeamEventTask.createForTeamEvent(props.teamEvent);
  }

  function saveTask(task: TeamEventTask) {
    return saveTaskOnServer(task);
  }

  function memberChanged(task: TeamEventTask, member: TeamMember) {
    task.teamMember = member;
    saveTask(task);
  }

  const { mutateAsync: deleteTaskOnServer } = useMutation({
    mutationFn: (task: TeamEventTask) => task.destroy(),
  });

  async function deleteTask(task: TeamEventTask) {
    await deleteTaskOnServer(task);
    await refetchTaskList();
  }

</script>

<style lang="scss" scoped></style>
