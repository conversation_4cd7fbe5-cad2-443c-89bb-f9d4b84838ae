<template>

  <q-expansion-item
    v-model="taskTableExpanded"
    expand-icon-class="q-pr-lg"
    expand-icon-toggle
    header-class="q-pa-none"
  >
    <template v-slot:header>
      <q-item class="full-width">
        <q-item-section class="text-subtitle1">
          <q-item-label>{{ eventTask.config.title }}</q-item-label>
        </q-item-section>
        <template v-if="isTaskListEditable && hasPermissionForTeam(teamEvent.team, 'team.event.task.assign.other')">
          <q-item-section>
            <q-select
              :loading="eventTask.isSaving"
              :model-value="eventTask.teamMember"
              :options="teamEvent.team.activeMembersSortedByName"
              clearable
              label="Person (Optional)"
              option-label="name"
              @update:modelValue="memberChanged"
            />
          </q-item-section>
        </template>

        <template v-else>
          <q-item-section side>
            <template v-if="eventTask.teamMember && !eventTask.isSaving">
              {{ eventTask.teamMember?.name }}
            </template>
            <template
              v-else-if="ownMembers.length !== 0 && (hasPermissionForTeam(teamEvent.team, 'team.event.task.assign.self') || hasPermissionForTeam(teamEvent.team, 'team.event.task.assign.other'))">
              <q-btn
                :loading="eventTask.isSaving"
                label="Übernehmen"
                no-caps
                @click="acceptTaskDialogVisible = true"
              />
            </template>
          </q-item-section>
        </template>

        <q-item-section v-if="isTaskListEditable && hasPermissionForTeam(teamEvent.team, 'team.event.task.delete')" side>
          <q-btn
            :loading="eventTask.isDeletingStarted"
            class="full-height q-px-sm"
            flat
            icon="delete"
            @click="confirmBeforeAction({
                      dialogOptions: {
                        title: 'Aufgabe aus diesem Termin entfernen?',
                      },
                      onOk: () => emit('deleteTask', eventTask),
                    })
              "
          />
        </q-item-section>
      </q-item>
    </template>

    <TasksTable
      v-if="taskTableExpanded && filterReady"
      ref="taskTable"
      :event-for-votes="teamEvent"
      :filter="filter"
      :selected-task-config-id="eventTask.config.id"
      :show-last-entry="true"
      :show-task-config-selection="false"
      :title="`im Zeitraum <strong>` + filter.getStartDateFormatted() + ` bis ` + filter.getEndDateOrTodayFormatted() + `</strong> übernommen:`"
    />

  </q-expansion-item>


  <CustomDialog v-if="ownMembers.length === 1"
                v-model="acceptTaskDialogVisible"
                title="Aufgabe übernehmen"
  >
    Möchtest du "{{ props.eventTask.config.title }}" übernehmen?
    <template v-slot:actions>
      <q-btn v-close-popup flat label="Abbrechen" />
      <q-btn v-close-popup flat label="Ja"
             @click="memberChanged(<TeamMember>ownMembers[0])"
      />
    </template>
  </CustomDialog>

  <CustomDialog v-if="ownMembers.length > 1"
                v-model="acceptTaskDialogVisible"
                :actions-vertical="true"
                title="Aufgabe übernehmen"
  >
    Wer soll "{{ props.eventTask.config.title }}" übernehmen?
    <template v-slot:actions>
      <template
        v-for="member in ownMembers"
        :key="member.id"
      >
        <q-btn v-close-popup :label="member.name" class="q-mb-sm" color="primary" no-caps
               stretch
               @click="memberChanged(<TeamMember>member)"
        />
      </template>
      <q-btn v-close-popup flat label="Abbrechen" stretch />
    </template>
  </CustomDialog>

</template>

<script lang="ts" setup>
  import TeamEventTask from 'src/models/TeamEventTask';
  import { confirmBeforeAction } from 'src/helpers/dialogs';
  import TeamEvent from 'src/models/TeamEvent';
  import TeamMember from 'src/models/TeamMember';
  import { ref, watch } from 'vue';
  import CustomDialog from 'src/components/CustomDialog.vue';
  import { useEventStatsFilter } from 'src/composables/useEventStatsFilter';
  import TasksTable from 'src/components/stats/TasksTable.vue';
  import { defaultDateMask } from 'src/i18n';
  import { date } from 'quasar';

  const props = defineProps<{
    eventTask: TeamEventTask,
    isTaskListEditable: boolean,
    teamEvent: TeamEvent,
  }>();

  const emit = defineEmits<{
    memberChanged: [eventTask: TeamEventTask, member: TeamMember],
    deleteTask: [eventTask: TeamEventTask],
  }>();

  const taskTableExpanded = ref<boolean>(false);
  const taskTable = ref<typeof TasksTable>();
  const ownMembers = ref<TeamMember[]>(props.teamEvent.team.getAssignedMembers());
  const acceptTaskDialogVisible = ref(false);

  const filter = useEventStatsFilter(props.teamEvent.team.id, false);
  const filterReady = ref(false);

  watch(() => props.teamEvent.activeStatsRange, () => {
      if (props.teamEvent.activeStatsRange) {
          filter.startDateString.value = props.teamEvent.activeStatsRange.startDateFormatted;
      } else {
          filter.startDateString.value = date.formatDate(date.buildDate({
              month: 1,
              day: 1,
              year: props.teamEvent.getDateTimeStart()?.getFullYear(),
          }), defaultDateMask);
      }
      filter.endDateString.value = props.teamEvent.getDateBeginFormatted(defaultDateMask);
      filterReady.value = true;
  }, { immediate: true });

  watch(() => props.eventTask.isSaving, (newValue) => {
    if (!newValue && <Date>props.teamEvent.getDateTimeStart() < filter.endDateOrToday.value) {
      taskTable.value?.refetchTaskList();
    }
  });

  function memberChanged(member: TeamMember) {
    emit('memberChanged', props.eventTask, member);
  }

</script>

<style lang="scss" scoped></style>
