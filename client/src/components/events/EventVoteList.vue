<template>
  <q-card class="vote-card">


    <q-card-section v-if="showTeamHeader" horizontal>
      <q-card-section v-if="teamEvent.team.iconName" class="q-py-none q-pr-none">
        <q-icon :name=teamEvent.team.iconName />
      </q-card-section>
      <q-card-section class="q-py-none">
        <div class="text-subtitle2">{{ teamEvent.team.name }}</div>
      </q-card-section>

    </q-card-section>

    <q-separator />

    <q-card-section>
      <q-btn v-if="showPopupCloseButton"
             v-close-popup class="absolute-top-right q-mr-sm q-mt-md" dense flat icon="close" />
      <div
        v-if="isFetching"
        class="absolute-top-left q-ml-sm q-mt-md"
      >
        <q-spinner
          :thickness="5"
          size="1.5em"
        />
      </div>
      <div class="text-center">
        <div class="text-h5">Te<PERSON><PERSON><PERSON><PERSON>liste</div>
        <div class="text-h6 q-pt-md">{{ teamEvent.getDisplayTitle() }}</div>
        <div v-if="teamEvent.hasDisplaySubtitle()" class="text-subtitle2">{{ teamEvent.getDisplaySubtitle() }}</div>
        <div class="q-pb-md">{{ teamEvent.getDateBeginFormatted('dd. DD.MM.YYYY') }} - {{ teamEvent.getTimeBeginFormatted() }} Uhr</div>
      </div>
      <q-separator />


      <q-list>
        <q-expansion-item v-for="(label, voteType) in voteBlocks" :key="voteType" :class="animationClass"
                          default-opened popup>

          <template v-slot:header>
            <q-item-section>
              <div class="text-subtitle2">{{ label }}</div>
              <div class="text-grey">
                <template v-for="(roleName, index) in teamEvent.getStatusRolesWithVotes(voteType)" :key="roleName">
                    {{ teamEvent.getVoteSumForStatusRole(voteType, roleName) }} {{ TeamRole.getDisplayName(roleName,
                    teamEvent.getVoteSumForStatusRole(voteType, roleName)) }}<span
                  v-if="index < teamEvent.getStatusRolesWithVotes(voteType).length - 1">, </span>
                </template>
              </div>
            </q-item-section>
            <q-item-section side>
              {{ teamEvent.getVoteSum(voteType) }}
            </q-item-section>
          </template>

          <q-separator />

          <q-list :class="animationClass">
              <span v-for="(member, index) in teamEvent.getMembersByVoteTypeSortedByName(voteType)" :key="member.id">
                <q-separator v-if="index !== 0" :class="animationClass"
                             :data-flip-id="'separator' + member.id" inset
                />
                <q-item :class="animationClass" :data-flip-id="member.id">
                  <q-item-section avatar>
                    <q-avatar icon="face" rounded size="lg" />
                  </q-item-section>

                  <q-item-section>
                    <q-tooltip v-if="teamEvent.getVote(member)?.isManualVote() && hasPermissionForTeam(teamEvent.team, 'team.event.vote.other')">
                      {{ teamEvent.getVote(member).getUpdatedAtDateFormatted() }}, {{ teamEvent.getVote(member).author?.name }}
                    </q-tooltip>
                    <q-item-label>
                      {{ member.name }}
                      <StatusRoleBadge :member="member" disabled />

                    </q-item-label>
                    <q-item-label v-if="teamEvent.hasVoteComment(member)" caption>
                      {{ teamEvent.getVote(member).getOrCreateComment().text }}
                    </q-item-label>
                  </q-item-section>


                  <q-item-section side>
                    <div class="q-gutter-xs voting">
                      <PopUpEditButton
                        v-if="teamEvent.isVoteEnabled(member) && teamEvent.hasVoted(member)"
                        v-model="teamEvent.getVote(member)!.commentText"
                        :hint="'Kommentar (' + member.name + ')'"
                        :is-saving="teamEvent.getVote(member)!.getOrCreateComment().isSaving"
                        class="q-pr-md"
                        icon="comment"
                        @save="emit('saveComment', teamEvent.getVote(member)!.getOrCreateComment())"
                      />

                      <q-btn :class="{'vote-yes vote-active': teamEvent.hasVoted(member, 'yes')}" :disable="!teamEvent.isVoteEnabled(member)"
                             :loading="teamEvent.isVoteUpdating(member, 'yes')"
                             :outline="!teamEvent.hasVoted(member, 'yes')"
                             :unelevated="teamEvent.hasVoted(member, 'yes')"
                             icon="thumb_up"
                             round
                             size="sm"
                             @click="voteClicked(member, 'yes')"
                      />
                      <q-btn :class="{'vote-maybe vote-active': teamEvent.hasVoted(member, 'maybe')}" :disable="!teamEvent.isVoteEnabled(member)"
                             :loading="teamEvent.isVoteUpdating(member, 'maybe')"
                             :outline="!teamEvent.hasVoted(member, 'maybe')"
                             :unelevated="teamEvent.hasVoted(member, 'maybe')"
                             icon="question_mark"
                             round
                             size="sm"
                             @click="voteClicked(member, 'maybe')"
                      />
                      <q-btn :class="{'vote-no vote-active': teamEvent.hasVoted(member, 'no')}" :disable="!teamEvent.isVoteEnabled(member)"
                             :loading="teamEvent.isVoteUpdating(member, 'no')"
                             :outline="!teamEvent.hasVoted(member, 'no')"
                             :unelevated="teamEvent.hasVoted(member, 'no')"
                             icon="thumb_down"
                             round
                             size="sm"
                             @click="voteClicked(member, 'no')"
                      />
                    </div>
                  </q-item-section>

                </q-item>
              </span>
          </q-list>
        </q-expansion-item>
      </q-list>

    </q-card-section>
  </q-card>
</template>

<script lang="ts" setup>

  import TeamEvent from 'src/models/TeamEvent';
  import TeamMember from 'src/models/TeamMember';
  import Comment from 'src/models/Comment';
  import PopUpEditButton from 'src/components/PopUpEditButton.vue';
  import { nextTick, onBeforeUpdate, onUpdated, ref } from 'vue';
  import Flip from 'gsap/Flip';
  import TeamRole from '../../models/TeamRole';
  import { useIsFetching } from '@tanstack/vue-query';
  import TeamEventVoteType = App.Types.TeamEventVoteType;
  import StatusRoleBadge from 'src/components/teams/StatusRoleBadge.vue';

  withDefaults(defineProps<{
    teamEvent: TeamEvent,
    showTeamHeader?: boolean,
    showPopupCloseButton?: boolean,
  }>(), {
    showTeamHeader: false,
    showPopupCloseButton: true,
  });

  const emit = defineEmits<{
    voteClicked: [teamMember: TeamMember, vote: TeamEventVoteType],
    saveComment: [comment: Comment],
    deleteComment: [comment: Comment],
  }>();

  const isFetching = useIsFetching();

  const state = ref<Flip.FlipState>();
  const animationClass = 'animated';

  function animate() {
    if (state.value) {
      Flip.from(state.value, {
        targets: '.' + animationClass,
        duration: 1,
        ease: 'expo.out',
        nested: true,
        simple: true,
        onComplete: () => state.value = undefined,
      });
    }
  }

  function saveStateBeforeAnimation() {
    state.value = Flip.getState('.' + animationClass);
  }

  onBeforeUpdate(() => {
    saveStateBeforeAnimation();
  });

  onUpdated(() => {
    animate();
  });

  function voteClicked(teamMember: TeamMember, vote: TeamEventVoteType) {
    saveStateBeforeAnimation();
    emit('voteClicked', teamMember, vote);
    nextTick(() => {
      animate();
    });
  }

  const voteBlocks: Record<TeamEventVoteType, string> = {
    'none': 'Warte auf Rückmeldung',
    'yes': 'Zusagen',
    'maybe': 'Unsicher',
    'no': 'Absagen / Abwesend',
  };

</script>

<style lang="scss" scoped>

  .vote-card {
    width: 100%;
    max-width: 500px;
  }
</style>
