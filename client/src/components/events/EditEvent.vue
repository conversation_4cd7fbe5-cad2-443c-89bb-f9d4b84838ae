<template>
    <PageHeaderWithAction :sub-title="teams?.length === 1 ? teams[0].name : undefined" :title="pageTitle">
        <q-btn v-if="withCopyButton" :to="{name: $routeNames.EVENTS_COPY, params: {eventId: eventId}}"
               class="q-mr-md q-mb-xs"
               color="primary"
               dense
               icon="copy_all"
               size="12px"
        >
            <q-tooltip>
                <PERSON><PERSON><PERSON>
            </q-tooltip>
        </q-btn>
        <q-btn class="q-mr-md q-mb-xs" color="primary"
               dense
               icon="close"
               size="12px"
               @click="$route.name === $routeNames.EVENTS_COPY ? $router.go(-2) : $router.back()"
        >
            <q-tooltip>
                Abbrechen
            </q-tooltip>
        </q-btn>
        <q-btn :loading="event?.isSaving" class="q-mb-xs"
               color="primary"
               dense
               icon="check"
               label="Speichern"
               size="12px"
               @click="eventForm?.submit"
        />
    </PageHeaderWithAction>

    <template v-if="event === undefined && !eventQuery.isSuccess.value">
        <div>lade Termin Daten...
            <q-spinner />
        </div>
    </template>
    <template v-else-if="!teamsQuerySuccessful">
        <div>lade Team Daten...
            <q-spinner />
        </div>
    </template>
    <template v-else>

        <q-form ref="eventForm" @submit="saveEvent">
            <template v-if="event && teams && teams.length > 1">
                <div class="q-py-md text-bold">Für welches Team ist der Termin?</div>
                <q-select
                    :model-value="event.team"
                    :options="teams"
                    label="Bitte Team auswählen"
                    option-label="name"
                    @update:model-value="handleTeamChange"
                />
            </template>

            <template v-if="event && event.team">
                <div class="q-py-md text-bold">Art des Termins</div>
                <div>
                    <q-field
                        v-model="event.eventType"
                        :rules="[val => !!val || 'Bitte wähle eine Option aus']"
                        borderless
                        tag="div"
                    >
                        <template v-slot:control>
                            <q-btn-toggle
                                v-model="event.eventType"
                                :options="TeamEventTypeOptions"
                                class="full-width"
                                no-caps
                                spread
                                stack
                                text-color="primary"
                                toggle-color="secondary"
                                toggle-text-color="primary"
                            />
                        </template>
                    </q-field>
                </div>


                <template v-if="event.eventType">

                    <div class="q-py-md text-bold">Informationen</div>

                    <template v-if="event.eventType === 'match'">
                        <q-input
                            v-model="event.matchOpponentName"
                            :rules="[val => !!val || 'Bitte gib einen Gegner ein']"
                            label="Gegner"
                        />

                        <q-select
                            v-model="event.matchType"
                            :options="EventMatchTypeOptions"
                            emit-value
                            label="Heim-/Auswärtsspiel"
                            :rules="[val => !!val || 'Bitte auswählen']"
                            map-options
                        >
                        </q-select>
                    </template>

                    <template v-else>
                        <q-input
                            v-model="event.title"
                            :rules="[val => !!val || 'Bitte gib einen Titel ein']"
                            label="Titel"
                        />
                        <q-input v-model="event.subText" label="Kurzinfo (optional)" />
                    </template>

                    <q-input borderless label="Details (optional)" readonly />
                    <text-editor v-model="event.details" />

                    <q-input borderless label-slot readonly>
                        <template v-slot:label>
                            Details <strong>{{ TeamRole.getDisplayName('manager') }}</strong> (optional)
                        </template>
                        <template v-slot:append>
                            <InfoIcon>
                                Notizen, die nur für die Rolle "<strong>{{ TeamRole.getDisplayName('manager') }}</strong>" sichtbar sind
                            </InfoIcon>
                        </template>
                    </q-input>
                    <text-editor v-model="event.detailsManager" />

                    <div class="q-py-md text-bold">Adresse</div>
                    <q-input v-model="event.addressString" label="Adresse" />

                    <div class="q-py-md text-bold">Datum und Uhrzeit</div>

                    <DateTimeInputField
                        v-model="event.dateBeginFormatted"
                        :mask="defaultDateMask"
                        :rules="[val => !!val || 'Bitte gib ein Datum an']"
                        label="Datum"
                        type="date"
                    />

                    <DateTimeInputField
                        v-model="event.timeBeginFormatted"
                        :mask="defaultTimeMask"
                        :rules="[val => !!val || 'Bitte gib eine Uhrzeit an']"
                        label="Startzeit"
                        type="time"
                    />

                    <DateTimeInputField
                        v-model="event.timeEndFormatted"
                        :mask="defaultTimeMask"
                        clearable
                        label="Endzeit (optional)"
                        type="time"
                    />
                    <DateTimeInputField
                        v-model="event.timeMeetFormatted"
                        :mask="defaultTimeMask"
                        clearable
                        label="Treffzeit (optional)"
                        type="time"
                    />

                    <RecurringEditor
                        v-if="!event.isPersisted && event.eventType === 'training'"
                        v-model="event.series.rruleString"
                        :date-begin="event.getDateTimeStart()"
                    />

                    <div class="q-py-md text-bold">Einstellungen für Teilnehmer</div>

                    <q-select
                        v-model="event.responseType"
                        :options="EventResponseTypeOptions"
                        emit-value
                        label="Anmeldeart"
                        map-options
                    >
                        <template v-slot:after>
                            <InfoIcon>
                                <div v-for="option in EventResponseTypeOptions" :key="option.value"
                                     class="q-py-sm"
                                >
                                    <strong>{{ option.label }}</strong><br />
                                    {{ option.info }}
                                </div>
                            </InfoIcon>
                        </template>
                    </q-select>

                    <template v-if="notificationFeatureEnabled">

                        <!-- Erinnerung zum Vote (nach x Tagen, x Tage vor Termin -->
                        <div class="row q-col-gutter-md items-center no-wrap">
                            <div class="col">
                                <q-select
                                    v-model="event.voteReminderType"
                                    :options="EventVoteReminderOptions"
                                    emit-value
                                    label="Abstimmungserinnerung"
                                    map-options
                                >
                                </q-select>
                            </div>
                            <div style="width: 150px">
                                <select-with-manual-other-option
                                    label="Stunden"
                                    v-model="event.voteReminderHours"
                                    :options="voteReminderHoursDefaults"
                                    valueType="number"
                                    :outlined="false"
                                    :show-other-in-label="false"
                                    :disable="event.voteReminderType === 'none'"
                                >
                                    <template v-slot:after>
                                        <InfoIcon>
                                            <div v-for="option in EventVoteReminderOptions" :key="option.value" class="q-py-sm">
                                                <strong>{{ option.label }}</strong><br />
                                                {{ option.info }}
                                            </div>
                                        </InfoIcon>
                                    </template>
                                </select-with-manual-other-option>
                            </div>
                        </div>

                        <div class="row q-col-gutter-md items-center no-wrap">
                            <div class="col">
                                <q-select
                                    v-model="event.eventReminderType"
                                    :options="EventReminderOptions"
                                    emit-value
                                    label="Termin Erinnerung"
                                    map-options
                                >
                                </q-select>
                            </div>
                            <div style="width: 150px">
                                <select-with-manual-other-option
                                    label="Stunden"
                                    v-model="event.eventReminderHours"
                                    :options="eventReminderHoursDefaults"
                                    valueType="number"
                                    :outlined="false"
                                    :show-other-in-label="false"
                                    :disable="event.eventReminderType === 'none'"
                                >
                                    <template v-slot:after>
                                        <InfoIcon>
                                            <div v-for="option in EventReminderOptions" :key="option.value" class="q-py-sm">
                                                <strong>{{ option.label }}</strong><br />
                                                {{ option.info }}
                                            </div>
                                        </InfoIcon>
                                    </template>
                                </select-with-manual-other-option>
                            </div>
                        </div>

                        <!-- Creating separate row for the checkbox with alignment tweaks -->
                        <div class="row items-center">
                            <div class="col">
                                <template v-if="editMode === 'edit'">
                                    <q-checkbox
                                        v-model="event.sendChangeNotification"
                                        label="Benachrichtigung Aktualisierung an Teilnehmer senden"
                                    />
                                </template>
                                <template v-else>
                                    <q-checkbox
                                        v-model="event.sendChangeNotification"
                                        label="Benachrichtigung über neuen Termin an Teilnehmer senden"
                                        class="q-mt-md"
                                    />
                                </template>
                            </div>
                        </div>
                    </template>

                    <q-card class="q-my-md" flat>
                        <q-card-section horizontal>
                            <q-card-section class="q-pa-none">
                                <q-btn
                                    :loading="event.isSaving && !cancelledChanged"
                                    color="primary"
                                    icon="check"
                                    label="Speichern"
                                    type="submit"
                                />
                            </q-card-section>
                            <q-space />
                            <q-card-section class="q-pa-none">
                                <template v-if="withDeleteButton">
                                    <template v-if="hasPermissionForTeam(event.team, 'team.event.create')">
                                        <q-btn
                                            v-if="(!event.isCancelled && !cancelledChanged) || (event.isCancelled && cancelledChanged)"
                                            :loading="event.isSaving && cancelledChanged"
                                            class="q-mr-md"
                                            color="negative"
                                            icon="event_busy"
                                            label="Absagen"
                                            outline
                                            @click="confirmBeforeAction({
                       dialogOptions: {
                            title: 'Soll der Termin wirklich abgesagt werden?',
                            message: 'Der Vorteil der Absage eines Termins gegenüber einer Löschung: Falls der Termin doch stattfindet, kannst du diesen wieder aktivieren und musst die Daten nicht neu einpflegen. Außerdem bleibt der Termin in der Übersicht in Rot stehen, so ist es für jeden gut sichtbar.'
                         },
                       onOk: cancelEvent
                     })"
                                        />
                                        <q-btn
                                            v-if="(event.isCancelled && !cancelledChanged) || (!event.isCancelled && cancelledChanged)"
                                            :loading="event.isSaving && cancelledChanged"
                                            class="q-mr-md"
                                            color="positive"
                                            icon="restore"
                                            label="Absage widerrufen"
                                            outline
                                            @click="confirmBeforeAction({
                       dialogOptions: {
                            title: 'Soll die Terminabsage widerrufen werden?'

                         },
                       onOk: restoreEvent
                     })"
                                        />
                                    </template>
                                    <q-btn
                                        v-if="hasPermissionForTeam(event.team, 'team.event.delete')"
                                        :loading="event.isDeletingStarted || event.series.isDeletingStarted"
                                        color="negative"
                                        icon="delete"
                                        label="Löschen"
                                        @click="handleDeleteEventClicked"
                                    />
                                </template>
                            </q-card-section>
                        </q-card-section>
                    </q-card>
                </template>
            </template>
        </q-form>

        <template v-if="event && event.team && event.eventType && teams">
            <q-separator class="q-my-sm" />
            Termin Vorschau
            <EventCard
                :is-fetching-team-event-data="eventQuery.isFetching.value"
                :show-team-header="teams.length > 1"
                :team-event="event"
                :with-detail-button="false"
            />
        </template>

        <CustomDialog v-model="deleteSeriesDialogVisible"
                      title="Serientermin löschen"
        >
            Dieser Termin gehört zu einer Serie<br>
            ({{ recurringInfoText }})
            <q-separator spaced />
            Möchtest du alle Termine <strong>ab {{ event?.dateBeginFormatted }}</strong> löschen,<br>
            oder nur den Termin am {{ event?.dateBeginFormatted }}?

            <template v-slot:actions>
                <q-btn v-close-popup color="primary" icon="event" label="Nur diesen Termin"
                       @click="showDeleteEventConfirmation"
                />
                <q-btn v-close-popup color="primary" icon="event_repeat" label="Alle Termine"
                       @click="showDeleteSeriesConfirmation"
                />
            </template>
        </CustomDialog>

    </template>
</template>

<script lang="ts" setup>

    import DateTimeInputField from 'src/components/DateTimeInputField.vue';
    import { useMutation, useQuery } from '@tanstack/vue-query';
    import TeamEvent, {
        EventMatchTypeOptions,
        EventResponseTypeOptions,
        EventVoteReminderOptions,
        TeamEventTypeOptions,
        EventReminderOptions,
    } from 'src/models/TeamEvent';
    import EventCard from 'src/components/events/EventCard.vue';
    import { computed, nextTick, onMounted, ref, watch, watchEffect } from 'vue';
    import { usePreventNavigation } from 'src/composables/usePreventNavigation';
    import { saveTeamEventOnServer, saveTeamEventSeriesOnServer } from 'src/queries/mutations/events';
    import { ROUTE_NAMES } from 'src/router/route-names';
    import { useRouter } from 'vue-router';
    import PageHeaderWithAction from 'src/components/global/PageHeaderWithAction.vue';
    import { confirmBeforeAction } from 'src/helpers/dialogs';
    import Team from 'src/models/Team';
    import { useAuthStore } from 'src/stores/auth';
    import TeamRole from '../../models/TeamRole';
    import InfoIcon from 'src/components/InfoIcon.vue';
    import TextEditor from 'src/components/TextEditor.vue';
    import { defaultDateMask, defaultTimeMask } from 'src/i18n';
    import { date, QForm } from 'quasar';
    import RecurringEditor from 'src/components/RecurringEditor.vue';
    import { queries } from 'src/queries';
    import { useRecurring } from 'src/composables/useRecurring';
    import { datetime } from 'rrule';
    import CustomDialog from 'src/components/CustomDialog.vue';
    import daysjs from 'dayjs';
    import SelectWithManualOtherOption from 'src/components/SelectWithManualOtherOption.vue';

    const props = withDefaults(defineProps<{
        pageTitle: string
        editMode?: 'edit' | 'create'
        eventId?: string
        createCopy?: boolean
        withCopyButton?: boolean
        withDeleteButton?: boolean
    }>(), {
        createCopy: false,
        withCopyButton: false,
        withDeleteButton: false,
    });

    const router = useRouter();
    const event = ref<TeamEvent>();
    const eventForm = ref<QForm>();
    const {
        getRangeString,
        getRecurrenceText,
    } = useRecurring(computed(() => event.value?.series.getRRule()));
    const recurringInfoText = computed(() => {
        if (event.value?.series.isRecurring()) {
            return getRecurrenceText() + ', ' + getRangeString();
        }
        return '';
    });
    const deleteSeriesDialogVisible = ref(false);

    const voteReminderHoursDefaults = [24, 48, 72];
    const eventReminderHoursDefaults = [2, 6, 12];

    onMounted(() => {
        if (props.eventId == null || props.eventId === '') {
            event.value = new TeamEvent();
            event.value.reset();
            event.value.voteReminderType = 'hours_before_event';
            event.value.voteReminderHours = 48;
            event.value.eventReminderType = 'hours_before_event';
            event.value.eventReminderHours = 2;
            refetchTeams();
        } else {
            eventQuery.refetch().then(() => {
                refetchTeams();
            });
        }
    });

    watch(() => event.value?.eventType, (value, oldValue) => {
        if (oldValue !== 'none' && oldValue !== undefined) {
            nextTick(() => {
                event.value?.resetValuesOnTypeChange();
            });
        }
    });

    watch(() => event.value?.team, (value, oldValue) => {
        if (event.value && !authStore.hasPermissionForTeam(event.value.team, 'team.event.create')) {
            router.replace({ name: ROUTE_NAMES.EVENTS_SHOW, params: { eventId: props.eventId } });
        }
    });

    const voteReminderHoursInputValue = ref('');

    const voteReminderHoursOnInputChange = (val: number | string) => {
        if (typeof val === 'string') {
            const numericVal = val.replace(/[^\d]/g, '');
            voteReminderHoursInputValue.value = +numericVal;
            event.value.voteReminderHours = +numericVal;
        } else {
            voteReminderHoursInputValue.value = val;
            event.value.voteReminderHours = val;
        }
    };

    const voteReminderHoursRules = [
        (val: number) => {
            // 'none' | 'hours_after_creation' | 'hours_before_event';
            if (event.value?.voteReminderType === 'none') {
                return true;
            }

            // hours_after_creation -> check if created + hours is before event start
            // hours_before_event -> check if event start - hours is after created

            let createdAtTime;
            if (event.value.createdAt === null) {
                createdAtTime = daysjs();
            } else {
                // createdAt should be ISO 8601 by default and thus supported by daysjs
                createdAtTime = daysjs(event.value.createdAt);
            }

            const beginDateTimeString = `${event.value.dateBeginFormatted} ${event.value.timeBeginFormatted}`;
            const beginDateTime = dayjs(beginDateTimeString, `${defaultDateMask} ${defaultTimeMask}`);

            // note(fabzo): change dateBeginFormatted here should we support events spanning a day boundary
            const endDateTimeString = `${event.value.dateBeginFormatted} ${event.value.timeEndFormatted}`;
            const endDateTime = daysjs(endDateTimeString, `${defaultDateMask} ${defaultTimeMask}`);

            // CreatedAt is in default ISO 8601 format
            const createDateTime = daysjs(event.value.createdAt);

            if (event.value.voteReminderType === 'hours_after_creation') {
                let newDateTime = beginDateTime.add(val, 'hour');

                if (newDateTime.isAfter(beginDateTime)) {
                    return `Reminder cannot be set after the event has already started (${endDateTime.format('DD.MM.YYYY HH:mm')})`;
                }

            } else {
                let newDateTime = beginDateTime.subtract(val, 'hour');

                if (newDateTime.isBefore(createdAtTime)) {
                    return `Reminder cannot be set before the event is created (${beginDateTime.format('DD.MM.YYYY HH:mm')})`;
                }
            }

            return true;
        },
    ];

    const eventQuery = useQuery({
        ...queries.events.forEdit(props.eventId as string),
        select: (data) => {
            // save data to ref since tanstack query makes returned value readonly for cache usage
            if (props.createCopy) {
                event.value = TeamEvent.createCopy(data);
            } else {
                event.value = data;
            }
            return data;
        },
        enabled: false,
    });

    const eventSaved = ref(false);
    const preventNavigation = computed<boolean>(() => {
        return !eventSaved.value
            && event.value !== undefined
            && !event.value.isDeletingStarted
            && event.value.isDirty('team');
    });

    usePreventNavigation(preventNavigation);

    const authStore = useAuthStore();

    const notificationFeatureEnabled = authStore.hasFeature('notifications.enabled');

    const {
        data: teams,
        isSuccess: teamsQuerySuccessful,
        refetch: refetchTeams,
    } = useQuery({
        ...queries.teams.all(false),
        refetchOnWindowFocus: false,
        refetchOnReconnect: false,
        enabled: false,
        select: (data: Team[]) => {
            return data.filter((team) => {
                return authStore.hasPermissionForTeam(team, 'team.event.create');
            });
        },
    });

    watchEffect(() => {
        if (event.value && !event.value.isPersisted && teams.value && teams.value.length == 1) {
            event.value.team = teams.value[0];
        }
    });

    const { mutateAsync: saveEventOnServer } =
        useMutation({ mutationFn: saveTeamEventOnServer });

    function saveEvent() {
        if (!notificationFeatureEnabled) {
            event.value.voteReminderType = 'none';
            event.value.eventReminderType = 'none';
        }
        saveEventOnServer(<TeamEvent>event.value).then(() => {
            eventSaved.value = true;
            if (event.value?.seriesType === 'single' || (!props.createCopy && props.eventId)) {
                router.push({ name: ROUTE_NAMES.EVENTS_SHOW, params: { eventId: event.value?.id } });
            } else {
                router.push({ name: ROUTE_NAMES.EVENTS });
            }
        });
    }

    const { mutateAsync: deleteTeamEvent } = useMutation({
        mutationFn: (teamEvent: TeamEvent) => teamEvent.destroy(),
    });

    function deleteEvent() {
        deleteTeamEvent(<TeamEvent>event.value).then(() => {
            router.push({ name: ROUTE_NAMES.EVENTS });
        });
    }

    const { mutateAsync: saveEventSeriesOnServer } =
        useMutation({ mutationFn: saveTeamEventSeriesOnServer });

    function deleteSeriesFutureEvents() {
        if (event.value && event.value.series) {
            // change series due date to event date -1 day
            const rrule = event.value.series.getRRule();
            const eventStartDate = event.value.getDateTimeStart();
            if (rrule && eventStartDate) {
                const dateUntil = date.subtractFromDate(
                    datetime(eventStartDate.getFullYear(), eventStartDate.getMonth() + 1, eventStartDate.getDate())
                    , { days: 1 },
                );
                rrule.origOptions = { ...rrule.origOptions, until: dateUntil };
                event.value.series.rruleString = rrule.toString();

                saveEventSeriesOnServer(event.value.series).then(() => {
                    eventSaved.value = true;
                    router.push({ name: ROUTE_NAMES.EVENTS });
                });
            }
        }

    }


    function showDeleteEventConfirmation() {
        confirmBeforeAction({
            dialogOptions: {
                title: 'Soll der Termin wirklich gelöscht werden?',
            },
            onOk: deleteEvent,
        });
    }

    function showDeleteSeriesConfirmation() {
        confirmBeforeAction({
            dialogOptions: {
                title: 'Sollen wirklich alle Termine der Serie ab ' + event.value?.dateBeginFormatted + ' gelöscht werden?',
                message: recurringInfoText.value,
            },
            onOk: deleteSeriesFutureEvents,
        });
    }

    function showDeleteSeriesDialog() {
        deleteSeriesDialogVisible.value = true;
    }

    function handleDeleteEventClicked() {
        switch (event.value?.seriesType) {
            case 'recurring_child':
                showDeleteSeriesDialog();
                break;
            case 'single':
            default:
                showDeleteEventConfirmation();
        }

    }

    const cancelledChanged = ref(false);

    function cancelEvent() {
        cancelledChanged.value = true;
        event.value?.setCancelled();
        saveEvent();
    }

    function restoreEvent() {
        cancelledChanged.value = true;
        event.value?.unsetCancelled();
        saveEvent();
    }

    function handleTeamChange(newValue: Team) {
        if (!event.value) {
            return;
        }
        if (!event.value.isPersisted) {
            event.value.team = newValue;
        } else {
            return confirmBeforeAction({
                dialogOptions: {
                    title: 'Soll das Team wirklich geändert werden?',
                    message: 'Alle vorhandenen Zu-/Absagen werden dabei gelöscht!',
                },
                onOk: () => (event.value as TeamEvent).team = newValue,
            });
        }
    }

    const eventReminderHoursInputValue = ref('');

    const eventReminderHoursOnInputChange = (val: number | string) => {
        if (typeof val === 'string') {
            const numericVal = val.replace(/[^\d]/g, '');
            eventReminderHoursInputValue.value = +numericVal;
            event.value.eventReminderHours = +numericVal;
        } else {
            eventReminderHoursInputValue.value = val;
            event.value.eventReminderHours = val;
        }
    };

</script>

<style lang="scss">
    .q-menu {
        min-width: 1rem !important;
    }

</style>
