<template>

  <q-card-section class="header bg-filled text-white" horizontal

  >
    <q-card-section class="q-py-xs">
      <div class="text-h6">{{ teamEvent.getDateBeginFormatted('dd.') }}</div>
      <div class="text-subtitle2">{{ teamEvent.getDateBeginFormatted('DD.MM.') }}</div>
    </q-card-section>
    <q-separator color="white" inset vertical />
    <q-card-section class="q-py-xs full-width">
      <div class="text-h6">{{ teamEvent.getDisplayTitle() }}</div>
      <div v-if="teamEvent.hasDisplaySubtitle()" class="text-subtitle2">{{ teamEvent.getDisplaySubtitle() }}</div>
    </q-card-section>
    <q-card-section class="q-pa-none row no-wrap items-center">
      <q-chip v-if="teamEvent.isCancelled" :ripple="false" color="white">Abgesagt</q-chip>
      <q-btn v-if="withDetailButton"
             class="full-height"
             flat
             icon="navigate_next"

      />
    </q-card-section>
  </q-card-section>
</template>

<script lang="ts" setup>

  import TeamEvent from 'src/models/TeamEvent';

  const props = withDefaults(defineProps<{
    teamEvent: TeamEvent
    withDetailButton?: boolean
  }>(), {
    withDetailButton: true,
  });
</script>

<style lang="scss" scoped></style>
