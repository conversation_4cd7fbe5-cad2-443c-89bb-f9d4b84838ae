<template>
  <q-btn :disable="isSaving" :icon="props.icon"
         :loading="isSaving"
         flat
         @click.stop="disable=false; nextTick(() => popup.show())"
  >
    <q-popup-edit
      ref="popup"
      v-slot="scope"
      v-model="value"
      :disable="disable"
      class="full-width"
      max-width="500px"
      @click="() => false"
      @hide="disable=true"
      @save="save"
    >
      <q-input
        v-model="scope.value"
        :hint="hint"
        autofocus
        dense
        @keyup.enter="scope.set"
      >
        <template v-slot:after>
          <q-btn
            dense flat icon="close"
            @click="scope.cancel"
          />

          <q-btn
            :disable="scope.initialValue === scope.value" dense flat
            icon="check"
            @click="scope.set"
          />
        </template>
      </q-input>
    </q-popup-edit>
  </q-btn>
</template>

<script lang="ts" setup>

  import { nextTick, ref } from 'vue';

  const props = withDefaults(defineProps<{
    hint?: string,
    isSaving?: boolean
    icon?: string
  }>(), {
    isSaving: false,
    icon: 'edit',
  });

  const value = defineModel<string>();

  const emit = defineEmits<{
    save: [value: string, initialValue: string]
  }>();

  const disable = ref(true);
  const popup = ref();

  function save(value: string, initialValue: string) {
    nextTick(() => {
      emit('save', value, initialValue);
    });
  }

</script>

<style lang="scss" scoped>

</style>
