<template>
  <q-icon :name="iconName" class="text-grey-6" size="sm">
    <q-tooltip anchor="top right" class="text-body2" self="top right">
      <slot></slot>
    </q-tooltip>
  </q-icon>
</template>

<script lang="ts" setup>


  withDefaults(defineProps<{
    iconName?: string
    title?: string
  }>(), {
    iconName: 'help',
    title: undefined,
  });

</script>

<style lang="scss" scoped>

</style>
