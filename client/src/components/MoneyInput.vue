<template>
    <q-input
        ref="inputRef"
        :model-value="formattedValue"
        outlined
        :clearable="clearable"
        :autofocus="autofocus"
        :label="label"
        :rules="combinedRules"
        lazy-rules="ondemand"
        @clear="clear"
    />
</template>

<script setup lang="ts">
    import { CurrencyInputOptions, useCurrencyInput, ValueScaling } from 'vue-currency-input';
    import { computed, nextTick, onMounted, watch } from 'vue';

    import { Money, MoneyClass } from 'src/helpers/money';
    import { QInput, QInputProps } from 'quasar';

    const props = withDefaults(defineProps<{
        label?: string,
        options?: Partial<CurrencyInputOptions>
        clearable?: boolean,
        rules?: QInputProps['rules'],
        noZeroAllowed?: boolean,
        autofocus?: boolean,
    }>(), {
      noZeroAllowed: false,
    });

    const money = defineModel<Money>({
        required: true,
        default: () => new MoneyClass(),
    });

    // Combine custom validation with provided rules
    const combinedRules = computed(() => {
      const zeroRule = () => !(props.noZeroAllowed && numberValue.value === 0) || 'Bitte gib einen positiven oder negativen Betrag ein.';

      // If no rules were provided, just use our zero rule
      if (!props.rules) {
        return [zeroRule];
      }

      // Otherwise, combine our rule with the provided rules
      return [zeroRule, ...(Array.isArray(props.rules) ? props.rules : [props.rules])];
    });

    onMounted(() => {
        nextTick(() => {
            setValue(+money.value.amount)
        })
    })

    function clear() {
        setValue(0);
    }

    const {
        inputRef,
        formattedValue,
        numberValue,
        setValue,
    } = useCurrencyInput({
        ...props.options,
        currency: money.value.currency,
        valueScaling: ValueScaling.precision,
        hideCurrencySymbolOnFocus: false
    }, false);

    watch(numberValue, (value) => {
        if(value === null) {
            value = 0
        }
        money.value = {...money.value, amount: value};
    })

</script>

<style lang="scss" scoped></style>
