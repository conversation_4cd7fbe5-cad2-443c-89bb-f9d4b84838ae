<template>
    <q-card>
        <q-card-section class="bg-primary text-white" horizontal>
            <q-card-section class="q-py-xs">
                <div class="text-h6">{{iCalendar.name}}</div>
            </q-card-section>
            <q-space/>
            <q-card-section class="q-pa-none no-wrap items-center">
                <q-btn class="q-pl-sm vertical-top q-pt-sm"
                       v-if="withEditButton"
                       :to="{name: $routeNames.ICALENDAR_EDIT, params: {iCalendarId: iCalendar.id}}"
                       flat
                       icon="edit"
                />
            </q-card-section>
        </q-card-section>

        <q-card-section v-if="iCalendar.internalName" class="q-py-none bg-secondary">
          {{iCalendar.internalName}}
        </q-card-section>

        <q-separator />

        <q-card-section>
            <q-list>
                enthält Termine für:
                <template v-for="(memberWithTeam) in sortBy(iCalendar.targetTeamMemberStrings)"
                          :key="memberWithTeam"
                >
                    <q-item-label caption>
                        {{ memberWithTeam }}
                    </q-item-label>
                </template>
            </q-list>
        </q-card-section>

        <q-card-actions vertical>
            <q-btn @click="openAddToCalendar(iCalendar)"
                   label="Kalender abonnieren"
                   icon="mdi-calendar-sync"
                   outline
                   ref="calendarButtonRef"
            />
            <q-btn
                icon="content_copy"
                label="iCal Link kopieren"
                class="q-mt-sm"
                no-caps
                outline
                @click="copyEventLink()"
            />
        </q-card-actions>

    </q-card>

</template>

<script lang="ts" setup>
  import ICalendar from 'src/models/ICalendar';
  import { atcb_action, ATCBActionEventConfig } from 'add-to-calendar-button';
  import { ref } from 'vue';
  import { Notify, QBtn } from 'quasar';
  import { Clipboard } from '@capacitor/clipboard';
  import { sortBy } from 'lodash';

  const props = withDefaults(defineProps<{
    iCalendar: ICalendar
    withEditButton?: boolean
  }>(), {
    withEditButton: true,
  });

  const calendarButtonRef = ref<QBtn>();

  function openAddToCalendar(iCalendar: ICalendar) {
      const config = {
          name: iCalendar.name,
          icsFile: iCalendar.publicLink,
          subscribe: true,
          options: ['Apple','Google','Outlook.com','Microsoft365','Yahoo'],
          hideBranding: true,
          language: 'de',
          bypassWebViewCheck: true,
          debug: true,
      } as ATCBActionEventConfig;
      atcb_action(config, calendarButtonRef.value?.$el);
  }

  function copyEventLink() {
      Clipboard.write({
          string: props.iCalendar.publicLink,
      });
      notifyMessage(
          'Link in die Zwischenablage kopiert.',
      );
  }

  function notifyMessage(message: string) {
      Notify.create({
          message: message,
          color: 'positive',
          position: 'bottom',
          timeout: 2000,
      });
  }

</script>

<style lang="scss" scoped></style>
