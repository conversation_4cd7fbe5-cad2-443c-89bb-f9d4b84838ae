<template>
  <PageHeaderWithAction
    :title="pageTitle">
    <q-btn class="q-mr-md q-mb-xs" color="primary"
           dense
           icon="close"
           size="12px"
           @click="$router.back()"
    >
      <q-tooltip>
        Abbrechen
      </q-tooltip>
    </q-btn>
    <q-btn :loading="iCalendar?.isSaving" class="q-mb-xs"
           color="primary"
           dense
           icon="check"
           label="Speichern"
           size="12px"
           @click="form?.submit"
    />
  </PageHeaderWithAction>

  <template v-if="iCalendar === undefined && !iCalendarQuery.isSuccess.value">
    <div>lade Daten...
      <q-spinner />
    </div>
  </template>
  <template v-else>
    <q-form v-if="iCalendar" ref="form" @submit="saveICalendar">

      <q-input
        v-model="iCalendar.internalName"
        label="Notiz (nur für mich selbst sichtbar)"
      />
<!--      <template v-if="hasMultipleTargetMembers">-->
      <template v-if="true">
        <div class="q-py-md text-bold">Welche Termine sollen enthalten sein?</div>
        <div>
          <q-field
            :model-value="(iCalendar.persons.length + iCalendar.teamMembers.length)"
            :rules="[val => val !== 0 || 'Bitte wähle mindestens eine Option aus']"
            borderless
          >
            <template v-slot:control>
              <!--
                wenn Beziehung möglich (#308) können hier mehrere Personen stehen
                und es muss noch ein "Alle" obendrüber
                -->
              <q-list dense>
                <template v-for="person in targetPersons" :key="person.id">

                  <q-item v-ripple tag="label">
                    <q-item-section avatar>
                      <q-checkbox
                        v-model="iCalendar.persons"
                        :val="person"
                      />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>Alle</q-item-label>
                    </q-item-section>
                  </q-item>

                  <template v-for="member in person.teamMembersSortedByName" :key="member.id">
                    <q-item v-ripple :inset-level="1" tag="label">
                      <q-item-section avatar>
                        <q-checkbox
                          v-model="iCalendar.teamMembers"
                          :disable="iCalendar.isPersonSelected(person)"
                          :val="member"
                        />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>{{ member.name }} ({{ member.team.name }})</q-item-label>
                      </q-item-section>
                    </q-item>
                  </template>

                </template>

              </q-list>

            </template>
          </q-field>
        </div>

        <!--      <div class="col-12 col-sm-6 q-gutter-sm bg-secondary">-->
        <!--        <div class="text-h6">Members</div>-->
        <!--        <div>-->
        <!--          <div v-for="member in absence.teamMembers" :key="member.id">-->
        <!--            {{ member.name }}-->
        <!--          </div>-->
        <!--        </div>-->

        <!--        <q-separator spaced />-->

        <!--        <div class="text-h6">Persons</div>-->
        <!--        <div>-->
        <!--          <div v-for="person in absence.persons" :key="person.id">-->
        <!--            {{ person.name }}-->
        <!--          </div>-->
        <!--        </div>-->
        <!--      </div>-->
      </template>


      <q-card v-if="iCalendar" class="q-my-md" flat>
        <q-card-section horizontal>
          <q-card-section class="q-pa-none">
            <q-btn
              :loading="iCalendar.isSaving"
              color="primary"
              icon="check"
              label="Speichern"
              type="submit"
            />
          </q-card-section>
          <q-space />
          <q-card-section class="q-pa-none">
            <template v-if="withDeleteButton">
              <q-btn
                :loading="iCalendar.isDeletingStarted"
                color="negative"
                icon="delete"
                label="Löschen"
                @click="confirmBeforeAction({
                       dialogOptions: {
                            title: 'Soll der Kalender Link wirklich gelöscht werden?',
                         },
                       onOk: deleteICalendar
                     })"
              />
            </template>
          </q-card-section>
        </q-card-section>
      </q-card>

    </q-form>
  </template>


</template>

<script lang="ts" setup>

  import { usePreventNavigation } from 'src/composables/usePreventNavigation';
  import PageHeaderWithAction from 'src/components/global/PageHeaderWithAction.vue';
  import { useAuthStore } from 'src/stores/auth';
  import { computed, onMounted, ref } from 'vue';
  import { QForm } from 'quasar';
  import { useMutation, useQuery } from '@tanstack/vue-query';
  import { useRouter } from 'vue-router';
  import { ROUTE_NAMES } from 'src/router/route-names';
  import Person from 'src/models/Person';
  import TeamMember from 'src/models/TeamMember';
  import { confirmBeforeAction } from 'src/helpers/dialogs';
  import { queries } from 'src/queries';
  import ICalendar from 'src/models/ICalendar';
  import { saveICalendarOnServer } from 'src/queries/mutations/iCalendars';

  const props = withDefaults(defineProps<{
    pageTitle: string
    iCalendarId?: string
    withDeleteButton?: boolean
  }>(), {
    withDeleteButton: false,
  });

  const router = useRouter();
  const iCalendar = ref<ICalendar>();
  const form = ref<QForm>();
  const authStore = useAuthStore();

  onMounted(() => {
      const persons = [useAuthStore().person as Person];
      const memberSum = persons.reduce((count, person) => person.teamMembers.length, 0);
      hasMultipleTargetMembers.value = memberSum > 1;
      if (memberSum === 1) {
          firstMember.value = persons.find((person) => person.teamMembers.length > 0)?.teamMembers[0] as TeamMember;
      }

    if (props.iCalendarId === undefined) {
        iCalendar.value = new ICalendar();
        iCalendar.value.reset();
        if(memberSum === 1 && firstMember.value) {
            iCalendar.value.teamMembers = [(firstMember.value)];
        }
    } else {
      iCalendarQuery.refetch();
    }
  });

  // duplicated code in AbsenceListPage.vue, cleanup whit #308
  const hasMultipleTargetMembers = ref(false);
  const firstMember = ref<TeamMember>();

  const targetPersons = ref([authStore.person as Person]);

  const iCalendarQuery = useQuery({
    ...queries.iCalendars.detail(props.iCalendarId as string),
    select: (data) => {
      // save data to ref since tanstack query makes returned value readonly for cache usage
      iCalendar.value = data;
      if (iCalendar.value) {
        // replace selected persons and teamMembers with objects that are used as checkbox options
        // https://forum.quasar-framework.org/topic/7806/using-checkbox-array-with-objects-and-mark-selected
        iCalendar.value.persons = iCalendar.value?.persons.map((person) => {
          return person.id === authStore.person?.id ? authStore.person : person;
        }) as Person[];

        iCalendar.value.teamMembers = iCalendar.value.teamMembers.map((selectedTeamMember) => {
          const teamMember = authStore.person?.teamMembers.find((teamMember) => teamMember.id === selectedTeamMember.id);
          return teamMember === undefined ? selectedTeamMember : teamMember;
        }) as TeamMember[];
      }
      return data;
    },
    enabled: false,
  });

  const iCalendarSaved = ref(false);
  const preventNavigation = computed<boolean>(() => {
    return !iCalendarSaved.value
      && iCalendar.value !== undefined
      && !iCalendar.value.isDeletingStarted
      && iCalendar.value.isDirty();
  });
  usePreventNavigation(preventNavigation);

  const { mutateAsync: saveOnServer } =
    useMutation({ mutationFn: saveICalendarOnServer });

  function saveICalendar() {
    saveOnServer(<ICalendar>iCalendar.value).then(async () => {
      iCalendarSaved.value = true;
      await router.push({
        name: ROUTE_NAMES.ICALENDARS,
      });
    });
  }

  const { mutateAsync: deleteICalendarOnServer } = useMutation({
    mutationFn: (iCalendar: ICalendar) => iCalendar.destroy(),
  });

  function deleteICalendar() {
    deleteICalendarOnServer(<ICalendar>iCalendar.value).then(() => {
      router.back();
    });
  }

</script>

<style lang="scss">

</style>
