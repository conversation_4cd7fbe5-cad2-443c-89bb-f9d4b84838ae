<template>
    <q-card flat bordered class="q-mb-md">
        <q-card-section class="bg-primary text-h6 text-white">
            Testbenachrichtigung
        </q-card-section>

        <q-separator />

        <NotificationSettings />
    </q-card>

    <q-card v-if="showDeviceList" flat bordered class="q-mb-md">
        <q-card-section class="bg-primary text-h6 text-white">
            Geräte und Benachrichtigungen
        </q-card-section>

        <q-separator />

        <DeviceTable v-model="deviceData" />
    </q-card>
</template>

<script setup lang="ts">

    import { useQuery } from '@tanstack/vue-query';
    import { queries, withRefCopy } from 'src/queries';
    import { ref } from 'vue';
    import DeviceTable from 'src/components/profile/DeviceTable.vue';
    import NotificationSettings from 'src/components/profile/NotificationSettings.vue';
    import { useAuthStore } from 'src/stores/auth';

    const authStore = useAuthStore();

    withDefaults(defineProps<{
        iconName?: string
        title?: string
    }>(), {
        iconName: 'help',
        title: undefined,
    });

    const showDeviceList = authStore.hasFeature('device.list');

    const deviceData = ref([]);
    const {
        data,
        isError,
        isLoading,
    } = useQuery(withRefCopy(queries.devices.all, deviceData));

</script>

<style lang="scss" scoped>

</style>
