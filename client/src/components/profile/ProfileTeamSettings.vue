<template>
  <q-card bordered class="q-mb-md" flat>
    <q-card-section class="bg-secondary" horizontal>
      <q-card-section class="full-width text-h6">
        Mitgliedschaften in Teams
      </q-card-section>
      <q-card-actions class="q-py-none">
        <q-btn
          :icon="isTeamMembershipEditable ? 'check' : 'edit'"
          :loading="isTeamMembershipSaving"
          class="full-height"
          flat
          @click="editTeamMembershipClicked"
        />
      </q-card-actions>
    </q-card-section>

    <q-card-section class="no-padding">
      <q-list class="team-membership">
        <q-form @submit="saveTeamMembershipData" v-if="person">
          <template v-for="team in person.teams" :key="team.id">
            <q-item-label header>{{ team.name }}</q-item-label>

            <q-item v-for="member in team.getAssignedMembers(true)" :key="member.id"
                    :inset-level=0.5
            >
              <q-item-section>
                <q-input
                  v-model="member.name"
                  :disable="!isTeamMembershipEditable"
                  :filled="isTeamMembershipEditable"
                  :rules="[val => !!val || 'Bitte gib einen Namen ein']"
                  borderless
                  class="full-width q-pb-none"
                  label-slot
                >
                  <template v-slot:label>
                    Name im Team
                    <StatusRoleBadge :member="member" class="q-ml-xs" />
                  </template>
                </q-input>
                <q-item-label v-if="leavingTeamMember?.id === member.id && errorMessageLeaveTeam"
                              class="text-red text-right"
                >
                  {{ errorMessageLeaveTeam }}
                </q-item-label>
              </q-item-section>
              <q-item-section v-if="isTeamMembershipEditable" side>
                <q-btn
                  :disable="isLeaveTeamPending"
                  :loading="leavingTeamMember === member"
                  class="full-height"
                  flat
                  icon="logout"
                  @click="confirmLeaveTeam(member)"
                >
                  <q-tooltip>Team verlassen</q-tooltip>
                </q-btn>
              </q-item-section>
            </q-item>

            <q-separator inset />
          </template>
          <q-item v-if="isTeamMembershipEditable">
            <q-btn
              :loading="isTeamMembershipSaving"
              color="primary"
              icon="check"
              label="Speichern"
              type="submit"
            />
          </q-item>
        </q-form>
      </q-list>

    </q-card-section>
  </q-card>
</template>


<script lang="ts" setup>

  import { computed, onBeforeUnmount, ref } from 'vue';
  import { useAuthStore } from 'src/stores/auth';
  import TeamMember from 'src/models/TeamMember';
  import { confirmBeforeAction } from 'src/helpers/dialogs';
  import { useMutation } from '@tanstack/vue-query';
  import { api } from 'src/boot/axios';
  import { AxiosError } from 'axios';
  import { APIError } from 'src/queries/api-errors';
  import StatusRoleBadge from 'src/components/teams/StatusRoleBadge.vue';

  withDefaults(defineProps<{
    iconName?: string
    title?: string
  }>(), {
    iconName: 'help',
    title: undefined,
  });

  const authStore = useAuthStore();
  const person = computed(() => authStore.person);

  const errorMessageLeaveTeam = ref('');
  const isTeamMembershipEditable = ref(false);
  const leavingTeamMember = ref<TeamMember>();

  const isTeamMembershipSaving = computed(() => {
    const membersSaving = person.value?.teamMembers.filter(member => member.isSaving).length;
    return membersSaving !== undefined && membersSaving > 0;
  });

  onBeforeUnmount(() => {
    if (isTeamMembershipEditable.value) {
      resetTeamMembershipData();
    }
  });

  function resetTeamMembershipData() {
    if (person.value) {
      for (const member of person.value.teamMembers) {
        if (member.isDirty()) {
          member.rollback();
        }
      }
    }
  }

  async function editTeamMembershipClicked() {
    if (isTeamMembershipEditable.value) {
      await saveTeamMembershipData();
    } else {
      isTeamMembershipEditable.value = true;
    }
  }

  async function saveTeamMembershipData() {
    errorMessageLeaveTeam.value = '';
    if (person.value) {
      for (const member of person.value.teamMembers) {
        if (member.isDirty()) {
          await member.save();
        }
      }
    }
    isTeamMembershipEditable.value = false;
  }

  const {
    mutate: leaveTeam,
    isPending: isLeaveTeamPending,
  } = useMutation({
    mutationFn: (member: TeamMember) => {
      leavingTeamMember.value = member;
      return api.post('/user/leaveTeam/' + member.id).then(data => data.data);
    },
    onError: (error) => {
      if (error instanceof AxiosError && error.response !== undefined) {
        const data = error.response.data as APIError;
        errorMessageLeaveTeam.value = data.message;
      }
      if (errorMessageLeaveTeam.value == '') {
        errorMessageLeaveTeam.value = 'Es ist ein unbekannter Fehler aufgetreten, bitte versuche es noch einmal.';
      }
    },
    onSuccess: async (data) => {
      errorMessageLeaveTeam.value = '';
      await useAuthStore().loadPermissions();
      leavingTeamMember.value = undefined;
    },
  });

  async function confirmLeaveTeam(member: TeamMember) {
    const message = 'Bist du sicher, dass du mit <strong>' + member.name + '</strong> aus dem Team <strong>' + member.team.name + '</strong> austreten möchtest?';
    await confirmBeforeAction({
      dialogOptions: {
        title: 'Team verlassen',
        html: true,
        message: message,
        ok: {
          label: 'Austreten',
          color: 'negative',
        },
      },
      onOk: () => leaveTeam(member),
    });
  }

</script>

<style lang="scss" scoped>

</style>
