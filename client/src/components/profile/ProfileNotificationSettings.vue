<template>
  <q-card bordered class="q-mb-md" flat>
    <q-card-section class="bg-primary text-h6 text-white">
      Push-Benachrichtigungen
    </q-card-section>

    <q-separator />

    <NotificationSettings />

  </q-card>
</template>

<script lang="ts" setup>

  import NotificationSettings from 'src/components/profile/NotificationSettings.vue';

  withDefaults(defineProps<{
    iconName?: string
    title?: string
  }>(), {
    iconName: 'help',
    title: undefined,
  });

</script>
