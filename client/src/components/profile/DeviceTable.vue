<template>
    <!-- Loop over all processedDevices and display a small q-card for each of them -->
    <q-list>
        <q-item v-for="device in processedDevices" :key="device" style="width: 100%">
            <q-card style="width: 100%">
                <q-card-section class="row items-center">
                    <div>
                        <div class="text-subtitle1">{{ device.displayName }}</div>
                    </div>
                    <q-space />
                    <!-- Benachrichtigen -->
                    <q-toggle
                        v-model="device.isPushNotificationEnabled"
                        :true-value="true"
                        :false-value="false"
                        :disable="device.isBrowser"
                        @update:model-value="togglePushNotifications($event, device)"
                    />
                </q-card-section>

                <q-card-actions class="row items-center">
                    <div class="q-pa-sm">
                        Zuletzt aktiv: {{ device.lastSeen ? device.deviceRef.getLastSeenFormatted('ddd, D. MMMM, HH:mm:ss') : 'Unbekannt' }}
                    </div>
                    <q-space />
                    <q-btn color="negative" @click="logoutDevice(device)">Ausloggen</q-btn>
                </q-card-actions>
            </q-card>
        </q-item>
    </q-list>
</template>

<script setup lang="ts">
    import { ref, watch } from 'vue';
    import { useAuthLogout } from 'src/composables/useAuthLogout';

    const auth = useAuthLogout();
    const model = defineModel();

    const isBrowser = ($row) => $row.name === 'browser' || $row.displayModel?.toLowerCase()?.includes('browser');
    const createDisplayName = (device) => {
        // iPhones tend to have a name that is just "iPhone" if the necessary entitlements are missing
        if (device.name === device.displayModel || device.displayModel?.includes('iPhone')) {
            return device.displayModel;
        }

        if (!device.name) {
            return device.displayModel;
        }

        if (!device.displayModel) {
            return device.name;
        }

        return `${device.name} - ${device.displayModel}`;
    };

    const preProcessDeviceData = (deviceData) => {
        if (deviceData === undefined) {
            return [];
        }

        return deviceData.map(device => ({
            ...device, // We need to copy all properties of the device object to the top level, for them to be accessible via the table columns
            deviceRef: device,
            isBrowser: isBrowser(device),
            isPushNotificationEnabled: ref(device.pushNotificationsEnabled && !isBrowser(device)),
            displayName: ref(createDisplayName(device)),
        }));
    };

    const processedDevices = ref(preProcessDeviceData(model.value));

    let previousDeviceData = '';
    watch(model, (newVal, oldVal) => {
        // Something is triggering this watch constantly, so we need to prevent it from running if the data hasn't changed
        const newDeviceData = JSON.stringify(newVal);
        if (previousDeviceData === newDeviceData) {
            return;
        }
        previousDeviceData = newDeviceData;

        processedDevices.value = preProcessDeviceData(newVal);
    });

    const columns = ref([
        { name: 'name', required: true, label: 'Name', align: 'left', field: 'displayName' },
        { name: 'pushNotificationsEnabled', required: true, label: 'Benachrichtigen', align: 'left', field: 'pushNotificationsEnabled' },
        { name: 'logout', required: true, label: 'Ausloggen', align: 'left', field: 'logout' },
    ]);

    const togglePushNotifications = (toggleInstance, device) => {
        device.deviceRef.pushNotificationsEnabled = toggleInstance;
        device.deviceRef.save();
    };

    function logoutDevice(device) {
        // Returns true if the device was successfully logged out -> remove it from the list
        if (auth.logoutDevice(device.id)) {
            processedDevices.value = processedDevices.value.filter((d) => d.id !== device.id);
        }
    }
</script>


<style scoped>
    @media (max-width: 600px) {
        .fixed-width {
            max-width: 120px;
            width: 120px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    .text-container {
        max-width: 120px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
</style>
