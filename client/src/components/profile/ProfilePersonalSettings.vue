<template>

    <q-form @submit.prevent="editPersonalInfoClicked">
      <q-card bordered class="q-mb-md" flat>
        <q-card-section class="bg-secondary" horizontal>
          <q-card-section class="full-width text-h6">
            Persönliche Informationen
          </q-card-section>
          <q-card-actions class="q-py-none">
            <q-btn
              :icon="isPersonalInfoEditable ? 'check' : 'edit'"
              :loading="isPersonalInfoSaving"
              class="full-height"
              flat
              type="submit"
            />
          </q-card-actions>
        </q-card-section>

            <q-list v-if="person && user">
              <q-item>
                <q-input
                  v-model="person.firstname"
                  :disable="!isPersonalInfoEditable"
                  :filled="isPersonalInfoEditable"
                  :rules="[(val) => !!val || 'Bitte gib deinen Vornamen an']"
                  borderless
                  class="full-width"
                  label="Vorname"
                />
              </q-item>

              <q-item>
                <q-input
                  v-model="person.lastname"
                  :disable="!isPersonalInfoEditable"
                  :filled="isPersonalInfoEditable"
                  :rules="[(val) => !!val || 'Bitte gib deinen Nachnamen an']"
                  borderless
                  class="full-width"
                  label="Nachname"
                />
              </q-item>

              <q-item>

                <!-- TODO Mail Adresse Bestätigungsprozess #230 -->
                <q-input
                  v-model="user.email"
                  :disable="true || !isPersonalInfoEditable"
                  :filled="isPersonalInfoEditable"
                  borderless
                  class="full-width"
                  label="E-Mail"
                />
              </q-item>

                <q-item>
                    <q-btn
                        :loading="sendPasswortResetMailPending"
                        color="primary"
                        icon="lock"
                        label="Passwort ändern"
                        @click="sendPasswortResetMail"
                    />
                </q-item>

              <q-item v-if="isPersonalInfoEditable">
                <q-btn
                  :loading="isPersonalInfoSaving"
                  color="primary"
                  icon="check"
                  label="Speichern"
                  type="submit"
                />
              </q-item>
            </q-list>
      </q-card>
    </q-form>

  <q-card bordered class="q-mb-md" flat>
    <q-card-section class="bg-secondary text-h6">
      Konto Einstellungen
    </q-card-section>

    <q-list>
      <q-item>

        <q-btn
          :loading="isDeletePending"
          class="flex flex-center q-my-xs"
          color="negative"
          icon="delete"
          label="Konto Löschen"
          @click="confirmDeleteAccount"
        />
      </q-item>
      <q-card v-if="deleteAccountErrorMessage" class="bg-red-1 error-message" flat>
          {{ deleteAccountErrorMessage }}
      </q-card>
    </q-list>
  </q-card>
</template>


<script lang="ts" setup>

  import { computed, onBeforeUnmount, ref } from 'vue';
  import { useAuthStore } from 'src/stores/auth';
  import { useMutation } from '@tanstack/vue-query';
  import { api } from 'src/boot/axios';
  import { AxiosError } from 'axios';
  import { APIError } from 'src/queries/api-errors';
  import { confirmBeforeAction } from 'src/helpers/dialogs';
  import { useRouter } from 'vue-router';
  import { sendPasswordResetToServer } from 'src/queries/mutations/auth';
  import { Notify } from 'quasar';

  withDefaults(defineProps<{
    iconName?: string
    title?: string
  }>(), {
    iconName: 'help',
    title: undefined,
  });

  const router = useRouter();
  const authStore = useAuthStore();

  const user = ref(authStore.user);
  const person = authStore.person;
  const isPersonalInfoEditable = ref(false);
  const deleteAccountErrorMessage = ref('');

  const isPersonalInfoSaving = computed(() => {
    return person?.isSaving || user.value?.isSaving;
  });

  onBeforeUnmount(() => {
    if (isPersonalInfoEditable.value) {
      resetPersonalInfo();
    }
  });

  function resetPersonalInfo() {
    if (person && person.isDirty() && !person.isSaving) {
      person?.rollback();
    }
    if (user.value && user.value.isDirty() && !user.value.isSaving) {
      user.value.rollback();
    }
  }

  async function editPersonalInfoClicked() {
    if (isPersonalInfoEditable.value) {
      if (person && person.isDirty() && !person.isSaving) {
        await person.save();
      }
      if (user.value && user.value.isDirty() && !user.value.isSaving) {
        await user.value.save();
      }
      isPersonalInfoEditable.value = false;
    } else {
      isPersonalInfoEditable.value = true;
    }
  }

  const {
      mutate: sendPasswortResetMail,
      isPending: sendPasswortResetMailPending
  } = useMutation({
      mutationFn: () => sendPasswordResetToServer(user.value!.email),
      onSuccess: (response) => {
          Notify.create({
              message: 'E-Mail wurde gesendet mit Anweisungen zum Ändern des Passworts',
              color: 'positive',
              position: 'top',
          });
      },
      onError: (error) => {
          Notify.create({
              message: 'Sendern der E-Mail mit Anweisungen zum Ändern des Passworts fehlgeschlagen. Bitte versuche es später noch eimal.',
              color: 'negative',
              position: 'top',
          });
      },
  });

  const {
    mutate: deleteUser,
    isPending: isDeletePending,
  } = useMutation({
    mutationFn: () => api.post('/user/delete').then(data => data.data),
    onError: (error) => {
      if (error instanceof AxiosError && error.response !== undefined) {
        const data = error.response.data as APIError;
        deleteAccountErrorMessage.value = data.message;
      }
      if (deleteAccountErrorMessage.value == '') {
        deleteAccountErrorMessage.value = 'Es ist ein unbekannter Fehler aufgetreten, bitte versuche es noch einmal.';
      }
    },
    onSuccess: async () => {
      router.go(0);
    },
  });

  async function confirmDeleteAccount() {
    await confirmBeforeAction({
      dialogOptions: {
        title: 'Bestätigen',
        message: 'Bist du sicher, dass du dein Konto löschen möchtest?',
        ok: {
          label: 'Löschen',
          color: 'negative',
        },
      },
      onOk: () => deleteUser(),
    });
  }

</script>

<style lang="scss" scoped>

</style>
