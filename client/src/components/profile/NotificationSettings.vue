<template>
    <div class="q-pa-md">
        <div v-if="pushNotificationsAvailable" class="q-mb-md">
            <div class="text-subtitle2">Systemeinstellungen für Benachrichtigungen:</div>
            <q-btn
                class="q-mt-sm"
                color="primary"
                icon="settings"
                label="Einstellungen öffnen"
                @click="openNotificationSettings"
            />
        </div>

        <div>
            <div class="text-subtitle2">Testbenachrichtigung:</div>
            <q-btn
                class="q-mt-sm"
                color="primary"
                icon="notifications_active"
                label="Test senden"
                @click="sendTestNotification"
            />
        </div>

        <q-dialog v-model="showPermissionDialog">
            <q-card>
                <q-card-section>
                    <div class="text-h6">Benachrichtigungen deaktiviert</div>
                </q-card-section>

                <q-card-section class="q-pt-none">
                    Um Testbenachrichtigungen zu senden, musst du zuerst die Benachrichtigungen in den Systemeinstellungen aktivieren.
                </q-card-section>

                <q-card-actions align="right">
                    <q-btn flat label="Abbrechen" color="primary" v-close-popup />
                    <q-btn flat label="Einstellungen öffnen" color="primary" @click="openNotificationSettings" v-close-popup />
                </q-card-actions>
            </q-card>
        </q-dialog>
    </div>
</template>

<script setup lang="ts">
    import { usePushNotificationSettings } from 'src/composables/usePushNotificationSettings';
    import { api } from 'src/boot/axios';
    import { useDeviceInfo } from 'src/composables/useDeviceInfo';
    import { ref, computed } from 'vue';
    import { PushNotifications } from '@capacitor/push-notifications';
    import { LocalNotifications } from '@capacitor/local-notifications';
    import { Capacitor } from '@capacitor/core';

    const { pushNotificationsAvailable, openNotificationSettings } = usePushNotificationSettings();
    const showPermissionDialog = ref(false);
    const isMobilePlatform = computed(() => Capacitor.getPlatform() === 'ios' || Capacitor.getPlatform() === 'android');

    const sendTestNotification = async () => {
        // On web browsers, just send the test notification
        if (!isMobilePlatform.value) {
            api.post('/push/test', {});
            return;
        }

        // Check if we have permission to send push notifications
        const pushPermission = await PushNotifications.checkPermissions();
        if (pushPermission.receive !== 'granted') {
            showPermissionDialog.value = true;
            return;
        }

        // Check if we have permission to send local notifications
        const localPermission = await LocalNotifications.checkPermissions();
        if (localPermission.display !== 'granted') {
            showPermissionDialog.value = true;
            return;
        }

        // If we have all required permissions, send the test notification
        api.post('/push/test', {});
    };

    // Temporarily update the device info here as well to ensure the test notification works
    useDeviceInfo().updateRemote();
</script>

