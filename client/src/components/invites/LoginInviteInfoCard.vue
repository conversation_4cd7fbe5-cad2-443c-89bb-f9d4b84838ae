<template>
  <q-card v-if="isRedirectToInvite" class="q-mb-md">

    <q-card-section v-if="inviteInfo.isLoading">
      <div class="text-h6">
        Prüfe Einladungslink
        <q-spinner />
      </div>
    </q-card-section>

    <q-card-section v-else>
      <div v-if="!inviteInfo?.invite" class="text-h6 text-red">
        {{ inviteInfo?.errorMessage }}
      </div>
      <div v-else class="text-h6">
        Bevor du die Einladung annehmen kannst, musst du dich einloggen oder
        registrieren.
      </div>
    </q-card-section>
  </q-card>
</template>

<script lang="ts" setup>
  import { useInviteRedirect } from 'src/composables/useInviteRedirect';
  import { computed } from 'vue';

  const props = defineProps<{
    redirectTo: string;
  }>();

  const {
    isRedirectToInvite,
    inviteInfo,
  } = useInviteRedirect(computed(() => props.redirectTo)); // props aren't reactive by default

</script>


<style lang="scss" scoped>

</style>
