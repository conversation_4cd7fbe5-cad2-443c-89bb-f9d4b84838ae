<template>
  <q-card v-if="isRedirectToVerifyMail" class="q-mb-md">
    <q-card-section>
      <div class="text-h6">
        Bevor du die Mail Adresse bestätigen kannst, musst du dich einloggen.
      </div>
    </q-card-section>
  </q-card>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { ROUTE_NAMES } from 'src/router/route-names';

  const props = defineProps<{
    redirectTo: string;
  }>();

  const router = useRouter();
  const isRedirectToVerifyMail = ref(false);

  watch(() => props.redirectTo, (newVal) => {
    const location = router.resolve({ path: props.redirectTo });
    if (location.name === ROUTE_NAMES.VERIFY_EMAIL) {
      isRedirectToVerifyMail.value = true;
    }
  });

</script>


<style lang="scss" scoped>

</style>
