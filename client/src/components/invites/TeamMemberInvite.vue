<template>
  <q-card-section>
    <div class="text-h6">Willst du dem Team "{{ invite.team.name }}" als "{{ teamMember.name }}" beitreten?</div>
  </q-card-section>
  <q-card-section v-if="errorMessage">
    <div class="text-h6 text-red">{{ errorMessage }}</div>
  </q-card-section>
  <q-card-actions align="center">
    <q-btn
      :loading="isLoadingAcceptingInvite"
      color="primary"
      label="Beitreten"
      @click="acceptInvite()"
    />
  </q-card-actions>
</template>

<script lang="ts" setup>
  import Invite from 'src/models/Invite';
  import TeamMember from 'src/models/TeamMember';
  import { useInvite } from 'src/composables/useInvite';

  const props = defineProps<{
    invite: Invite<TeamMember>;
  }>();

  const {
    acceptInvite,
    isLoadingAcceptingInvite,
    errorMessage,
    invitable: teamMember,
  } = useInvite(props.invite);

</script>

<style lang="scss" scoped>

</style>
