<template>
  <q-card-section>
    <div class="text-h6">Willst du dem Team "{{ invite.team.name }}" beitreten?</div>
    <div class="text-subtitle1 q-pb-sm">Wähle einfach deinen Namen aus den unten stehenden Teammitgliedern aus oder erstelle einen neuen Eintrag.</div>
    <template v-if="isLoadingTeamDetails">
      <div class="text-subtitle1">Teammitglieder werden geladen
        <q-spinner />
      </div>
    </template>
    <template v-else-if="isErrorTeamDetails">
      <div class="text-subtitle1 text-red">Es ist ein Fehler beim Laden der Teammitglieder aufgetreten</div>
    </template>
    <q-list v-else
            bordered
            separator
    >
      <q-item v-for="member in team.membersSortedByStatusRoleAndName" :key="member.id"
              :disable="!member.isAvailableForTeamInvite()"
      >
        <q-item-section>
          <q-item-label :class="{'text-weight-bold': member.isAvailableForTeamInvite()}">
            {{ member.name }}
          </q-item-label>
          <q-item-label v-if="member.isRegistered" caption>
            Bereits registriert
          </q-item-label>
        </q-item-section>
        <q-item-section top>
          <q-item-label>
            <StatusRoleBadge :member="member" />
          </q-item-label>
        </q-item-section>
        <q-item-section side top>
          <q-btn
            :class="{'invisible' : !member.isAvailableForTeamInvite()}"
            :disable="isLoadingAcceptingNewMember || isLoadingAcceptingMember()"
            :loading="isLoadingAcceptingMember(member)"
            color="primary"
            label="Beitreten"
            no-caps
            outline
            @click="acceptInviteWithMember(member)"
          />
        </q-item-section>
      </q-item>

      <q-item-label header>
        Du stehst noch nicht in der Liste? Dann trage hier deinen Namen ein
      </q-item-label>

      <form @submit.prevent="acceptInviteWithNewMember()">
        <q-item>
          <q-item-section>
            <q-input
              ref="newMemberNameInput"
              v-model="newMemberName"
              :rules="[val => val.length >= 3 || 'Bitte mindestens 3 Zeichen eingeben']"
              label="Name im Team"
              lazy-rules
            />
          </q-item-section>
          <q-item-section side>
            <q-btn
              :disable="isLoadingAcceptingMember()"
              :loading="isLoadingAcceptingNewMember"
              color="primary"
              label="Beitreten"
              no-caps
              outline
              type="submit"
            />
          </q-item-section>
        </q-item>
      </form>

    </q-list>
  </q-card-section>
  <q-card-section v-if="errorMessage">
    <div class="text-subtitle1 text-red">{{ errorMessage }}</div>
  </q-card-section>
</template>

<script lang="ts" setup>
  import Invite from 'src/models/Invite';
  import Team from 'src/models/Team';
  import { useInvite } from 'src/composables/useInvite';
  import StatusRoleBadge from 'src/components/teams/StatusRoleBadge.vue';

  const props = defineProps<{
    invite: Invite<Team>;
  }>();

  const {
    invitable: team,
    errorMessage,
    teamInvite: {
      acceptInviteWithMember,
      acceptInviteWithNewMember,
      newMemberNameInput,
      newMemberName,
      isLoadingTeamDetails,
      isErrorTeamDetails,
      isLoadingAcceptingMember,
      isLoadingAcceptingNewMember,
    },
  } = useInvite(props.invite);

</script>

<style lang="scss" scoped>

</style>
