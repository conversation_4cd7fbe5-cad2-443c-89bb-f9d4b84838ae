<template>
    <q-card>
        <q-card-section class="bg-primary q-pa-none">
            <q-item class="q-pr-none q-py-none text-white">
                <q-item-section top>
                    <div class="text-h6">{{ absence.rangeString }}</div>
                    <template v-if="absence.isRecurring()">
                        {{ recurring.getRecurrenceText() }}
                    </template>
                </q-item-section>

                <q-item-section side top v-if="withEditButton"
                                class="no-wrap ">

                    <q-btn class="q-pl-sm q-pt-sm no-wrap text-white"
                           flat
                           icon="edit"
                           no-caps
                           :to="{name: $routeNames.ABSENCE_EDIT, params: {absenceId: absence.id}}"
                    />
                </q-item-section>
            </q-item>
        </q-card-section>

        <q-card-section v-if="absence.name" class="q-py-none bg-secondary">
            {{absence.name}}
        </q-card-section>

        <q-separator />

        <q-card-section class="q-py-xs">
            <q-item class="q-pa-none">
                <q-item-section top>
                    <q-list v-if="showTargetMembers">
                        gilt für:
                        <template v-for="(memberWithTeam) in sortBy(absence.targetTeamMemberStrings)"
                                  :key="memberWithTeam"
                        >
                            <q-item-label caption>
                                {{ memberWithTeam }}
                            </q-item-label>
                        </template>
                    </q-list>
                </q-item-section>
                <q-item-section side top class="items-center">
                    <q-icon :name="absence.absenceTypeOption.icon" size="md" />
                    <div class="text-subtitle2">{{ absence.absenceTypeOption.label }}</div>
                </q-item-section>
            </q-item>

            <!--      <q-list>Personen:-->
            <!--      <template v-for="(person) in absence.persons"-->
            <!--                :key="person.id"-->
            <!--      >-->
            <!--        <q-item-label caption>-->
            <!--          {{person.name}}-->
            <!--        </q-item-label>-->
            <!--      </template>-->
            <!--      </q-list>-->
            <!--      <q-list>Member:-->
            <!--      <template v-for="(member) in absence.teamMembers"-->
            <!--                :key="member.id"-->
            <!--      >-->
            <!--        <q-item-label caption>-->
            <!--          {{member.name}}-->
            <!--        </q-item-label>-->
            <!--      </template>-->
            <!--      </q-list>-->
        </q-card-section>
    </q-card>
</template>

<script lang="ts" setup>
  import Absence from 'src/models/Absence';
  import { useRecurring } from 'src/composables/useRecurring';
  import { computed } from 'vue';
  import { sortBy } from 'lodash';
  import { QBtn } from 'quasar';

  const props = withDefaults(defineProps<{
    absence: Absence
    showTargetMembers: boolean
    withEditButton?: boolean
  }>(), {
    withEditButton: true,
    showTargetMembers: true,
  });

  const recurring = useRecurring(computed(() => props.absence.getRRule()));

</script>

<style lang="scss" scoped></style>
