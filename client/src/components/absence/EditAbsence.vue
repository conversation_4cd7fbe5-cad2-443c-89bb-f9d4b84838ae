<template>
  <PageHeaderWithAction
    :sub-title="singleMemberSubtitle"
    :title="pageTitle">
    <q-btn class="q-mr-md q-mb-xs" color="primary"
           dense
           icon="close"
           size="12px"
           @click="$router.back()"
    >
      <q-tooltip>
        Abbrechen
      </q-tooltip>
    </q-btn>
    <q-btn :loading="absence?.isSaving" class="q-mb-xs"
           color="primary"
           dense
           icon="check"
           label="Speichern"
           size="12px"
           @click="form?.submit"
    />
  </PageHeaderWithAction>

  <template v-if="absence === undefined && !absenceQuery.isSuccess.value">
    <div>lade Daten...
      <q-spinner />
    </div>
  </template>
  <template v-else>
    <q-form v-if="absence" ref="form" @submit="saveAbsence">

      <div class="q-py-md text-bold">Art der Abwesenheit</div>
      <div>
        <q-field
          v-model="absence.absenceType"
          :rules="[val => !!val || 'Bitte wähle eine Option aus']"
          borderless
          tag="div"
        >
          <template v-slot:control>
            <q-btn-toggle
              v-model="absence.absenceType"
              :options="AbsenceTypeOptions"
              class="full-width"
              no-caps
              spread
              stack
              text-color="primary"
              toggle-color="secondary"
              toggle-text-color="primary"
            />
          </template>
        </q-field>
      </div>

        <q-input
            v-model="absence.name"
            label="Notiz (nur für mich selbst sichtbar)"
        />

      <template v-if="hasMultipleTargetMembers">
        <div class="q-py-md text-bold">Für wen gilt die Abwesenheit?</div>
        <div>
          <q-field
            :model-value="(absence.persons.length + absence.teamMembers.length)"
            :rules="[val => val !== 0 || 'Bitte wähle mindestens eine Option aus']"
            borderless
          >
            <template v-slot:control>
              <!--
                wenn Beziehung möglich (#308) können hier mehrere Personen stehen
                und es muss noch ein "Alle" obendrüber
                -->
              <q-list dense>
                <template v-for="person in targetPersons" :key="person.id">

                  <q-item v-ripple tag="label">
                    <q-item-section avatar>
                      <q-checkbox
                        v-model="absence.persons"
                        :val="person"
                      />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>Alle</q-item-label>
                    </q-item-section>
                  </q-item>

                  <template v-for="member in person.teamMembersSortedByName" :key="member.id">
                    <q-item v-ripple :inset-level="1" tag="label">
                      <q-item-section avatar>
                        <q-checkbox
                          v-model="absence.teamMembers"
                          :disable="absence.isPersonSelected(person)"
                          :val="member"
                        />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>{{ member.name }} ({{ member.team.name }})</q-item-label>
                      </q-item-section>
                    </q-item>
                  </template>

                </template>

              </q-list>

            </template>
          </q-field>
        </div>

        <!--      <div class="col-12 col-sm-6 q-gutter-sm bg-secondary">-->
        <!--        <div class="text-h6">Members</div>-->
        <!--        <div>-->
        <!--          <div v-for="member in absence.teamMembers" :key="member.id">-->
        <!--            {{ member.name }}-->
        <!--          </div>-->
        <!--        </div>-->

        <!--        <q-separator spaced />-->

        <!--        <div class="text-h6">Persons</div>-->
        <!--        <div>-->
        <!--          <div v-for="person in absence.persons" :key="person.id">-->
        <!--            {{ person.name }}-->
        <!--          </div>-->
        <!--        </div>-->
        <!--      </div>-->
      </template>

      <div class="q-py-md text-bold">Datum und Regelmäßigkeit (z.b. jeden Freitag)</div>
      <div v-if="absence">
        <DateTimeInputField
          v-model="absence.dateBeginFormatted"
          :mask="defaultDateMask"
          :rules="[val => !!val || 'Bitte gib ein Datum an']"
          label="Datum"
          type="date"
        />

        <DateTimeInputField
          v-model="absence.dateEndFormatted"
          :mask="defaultDateMask"
          clearable
          label="Enddatum (optional)"
          type="date"
        />

        <RecurringEditor
          v-model="absence.rruleString"
          :date-begin="absence.getDateStart()"
          :date-end="absence.getDateEnd()"
        />
      </div>

      <q-card v-if="absence" class="q-my-md" flat>
        <q-card-section horizontal>
          <q-card-section class="q-pa-none">
            <q-btn
              :loading="absence.isSaving"
              color="primary"
              icon="check"
              label="Speichern"
              type="submit"
            />
          </q-card-section>
          <q-space />
          <q-card-section class="q-pa-none">
            <template v-if="withDeleteButton">
              <q-btn
                :loading="absence.isDeletingStarted"
                color="negative"
                icon="delete"
                label="Löschen"
                @click="confirmBeforeAction({
                       dialogOptions: {
                            title: 'Soll die Abwesenheit wirklich gelöscht werden?',
                         },
                       onOk: deleteAbsence
                     })"
              />
            </template>
          </q-card-section>
        </q-card-section>
      </q-card>

    </q-form>
  </template>


</template>

<script lang="ts" setup>

  import { usePreventNavigation } from 'src/composables/usePreventNavigation';
  import PageHeaderWithAction from 'src/components/global/PageHeaderWithAction.vue';
  import { useAuthStore } from 'src/stores/auth';
  import { computed, onMounted, ref } from 'vue';
  import { QForm } from 'quasar';
  import Absence, { AbsenceTypeOptions } from 'src/models/Absence';
  import { useMutation, useQuery } from '@tanstack/vue-query';
  import { saveAbsenceOnServer } from 'src/queries/mutations/absence';
  import { useRouter } from 'vue-router';
  import { defaultDateMask } from 'src/i18n';
  import DateTimeInputField from 'src/components/DateTimeInputField.vue';
  import { ROUTE_NAMES } from 'src/router/route-names';
  import Person from 'src/models/Person';
  import TeamMember from 'src/models/TeamMember';
  import { confirmBeforeAction } from 'src/helpers/dialogs';
  import RecurringEditor from 'src/components/RecurringEditor.vue';
  import { queries } from 'src/queries';

  const props = withDefaults(defineProps<{
    pageTitle: string
    absenceId?: string
    withDeleteButton?: boolean
  }>(), {
    withDeleteButton: false,
  });

  const router = useRouter();
  const absence = ref<Absence>();
  const form = ref<QForm>();
  const authStore = useAuthStore();

  onMounted(() => {
      const persons = [useAuthStore().person as Person];
      const memberSum = persons.reduce((count, person) => person.teamMembers.length, 0);
      hasMultipleTargetMembers.value = memberSum > 1;
      if (memberSum === 1) {
          firstMember.value = persons.find((person) => person.teamMembers.length > 0)?.teamMembers[0] as TeamMember;
      }

    if (props.absenceId === undefined) {
      absence.value = new Absence();
      absence.value.reset();
      if(memberSum === 1 && firstMember.value) {
          absence.value.teamMembers = [(firstMember.value)];
      }
    } else {
      absenceQuery.refetch();
    }
  });

  // duplicated code in AbsenceListPage.vue, cleanup whit #308
  const hasMultipleTargetMembers = ref(false);
  const firstMember = ref<TeamMember>();
  const singleMemberSubtitle = computed(() => {
    if (hasMultipleTargetMembers.value || !firstMember.value) {
      return undefined;
    } else {
      return firstMember.value.name + ' (' + firstMember.value?.team.name + ')';
    }
  });

  const targetPersons = ref([authStore.person as Person]);

  const absenceQuery = useQuery({
    ...queries.absences.detail(props.absenceId as string),
    select: (data) => {
      // save data to ref since tanstack query makes returned value readonly for cache usage
      absence.value = data;
      if (absence.value) {
        // replace selected persons and teamMembers with objects that are used as checkbox options
        // https://forum.quasar-framework.org/topic/7806/using-checkbox-array-with-objects-and-mark-selected
        absence.value.persons = absence.value?.persons.map((person) => {
          return person.id === authStore.person?.id ? authStore.person : person;
        }) as Person[];

        absence.value.teamMembers = absence.value.teamMembers.map((selectedTeamMember) => {
          const teamMember = authStore.person?.teamMembers.find((teamMember) => teamMember.id === selectedTeamMember.id);
          return teamMember === undefined ? selectedTeamMember : teamMember;
        }) as TeamMember[];
      }
      return data;
    },
    enabled: false,
  });

  const absenceSaved = ref(false);
  const preventNavigation = computed<boolean>(() => {
    return !absenceSaved.value
      && absence.value !== undefined
      && !absence.value.isDeletingStarted
      && absence.value.isDirty();
  });
  usePreventNavigation(preventNavigation);

  const { mutateAsync: saveOnServer } =
    useMutation({ mutationFn: saveAbsenceOnServer });

  function saveAbsence() {
    saveOnServer(<Absence>absence.value).then(async () => {
      absenceSaved.value = true;
      await router.push({
        name: ROUTE_NAMES.ABSENCES,
      });
    });
  }

  const { mutateAsync: deleteAbsenceOnServer } = useMutation({
    mutationFn: (absence: Absence) => absence.destroy(),
  });

  function deleteAbsence() {
    deleteAbsenceOnServer(<Absence>absence.value).then(() => {
      router.back();
    });
  }

</script>

<style lang="scss">

</style>
