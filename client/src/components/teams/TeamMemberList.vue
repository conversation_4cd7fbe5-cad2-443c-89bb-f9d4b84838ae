<template>
  <q-item class="text-grey">
    <q-item-section>Name</q-item-section>
    <q-item-section>Status</q-item-section>
    <q-item-section>Rollen</q-item-section>
    <q-item-section side v-if="(!$q.platform.is.mobile || !editAll) && (hasPermissionForTeam(team, 'team.member.invite.create.other') || hasFeature('admin.stats'))">
      Registriert?
    </q-item-section>
    <q-item-section no-wrap side top>
      <q-btn
        v-if="hasPermissionForTeam(team, 'team.member.update')"
        :icon="editAll ? 'check' : 'edit'"
        dense
        round
        tabindex="-1"
        @click="editAll = !editAll; setEditingMember(undefined);"
      />
    </q-item-section>
  </q-item>
  <q-separator />
  <TransitionGroup name="animated-list">
    <template v-for="(member, index) in memberList" :key="member.isPersisted ? member.id : index">
      <q-item :class="{'ownMember': authStore.isOwnMember(member)}">
        <q-item-section>
          <q-input
            ref="memberInputs"
            v-model="member.name"
            :borderless="!isEditable(member)"
            :label="!member.isPersisted ? 'Name' : ''"
            :loading="member.isSaving"
            :readonly="!isEditable(member)"
            :rules="[val => !!val || 'Bitte gib einen Namen ein']"
            hide-bottom-space
            data-cy="member-name-input"
            dense
            @blur="validateAndSave(member, index, true)"
            @keydown.enter.prevent="validateAndSave(member, index)"
          />
        </q-item-section>

        <q-item-section v-if="member.isPersisted">
          <q-item-label v-if="!isEditable(member) && member.statusRole.name !== 'member'" caption>
            <StatusRoleBadge :member="member" />
          </q-item-label>
          <q-select
            v-if="isEditable(member)"
            v-model="member.statusRole"
            :borderless="!isEditable(member)"
            :disable="!isDeletable(member)"
            :hide-dropdown-icon="!isEditable(member)"
            :option-label="(role: TeamRole) => role.getName()"
            :options="team.statusRoles"
            :readonly="!isEditable(member)"
            dense
            stack-label
            @update:model-value="triggerSaveMember(member, true)"
          />
        </q-item-section>

        <q-item-section v-if="member.isPersisted">
          <q-item-label v-if="!isEditable(member)" caption>
              <RoleBadges :member="member" />
          </q-item-label>
          <q-select
            v-if="isEditable(member)"
            v-model="member.roles"
            :borderless="!isEditable(member)"
            :hide-dropdown-icon="!isEditable(member)"
            :option-label="(role: TeamRole) => role.getName()"
            :options="team.customRoles"
            multiple
            :readonly="!isEditable(member)"
            dense
            @update:model-value="triggerSaveMember(member, true)"
          >
              <template v-if="$q.platform.is.mobile" v-slot:append>
                  <q-btn outline size="sm" v-close-popup class="close-select-dialog q-mr-sm">OK</q-btn>
              </template>
              <template v-slot:option="{ itemProps, opt, selected, toggleOption }">
                  <q-item v-bind="itemProps">
                      <q-item-section>
                          <q-item-label>{{(opt as TeamRole).getName()}}</q-item-label>
                      </q-item-section>
                      <q-item-section side>
                          <q-toggle :model-value="selected" @update:model-value="toggleOption(opt)" />
                      </q-item-section>
                  </q-item>
              </template>
          </q-select>
        </q-item-section>

        <q-item-section v-if="(!$q.platform.is.mobile || !isEditable(member)) && member.isPersisted && (hasPermissionForTeam(team, 'team.member.invite.create.other') || hasFeature('admin.stats'))"
                        class="text-right"
                        style="min-width: 75px; padding-left: 0"
                        side
        >
          <span v-if="member.isRegistered">
            <q-icon name="check" />
          </span>
          <q-btn-group v-else-if="hasPermissionForTeam(team, 'team.member.invite.create.other')"
                       outline
          >
            <q-btn
              v-if="member.hasPersistedInvite()"
              :loading="member.invite?.isDeletingStarted"
              dense
              icon="content_copy"
              outline
              @click="copyInvite(member)"
            />
            <q-btn
              v-if="!member.hasPersistedInvite()"
              :loading="member.invite?.isSaving"
              dense
              icon="add_link"
              outline
              @click="createInvite(member)"
            />

            <q-btn-dropdown :disable="member.invite?.isSaving || member.invite?.isDeletingStarted" dense outline>
              <q-list>
                <q-item v-if="member.hasPersistedInvite()" v-close-popup clickable @click="copyInvite(member)">
                  <q-item-section avatar>
                    <q-icon name="content_copy" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>Einladungslink kopieren</q-item-label>
                  </q-item-section>
                </q-item>

                <q-item v-if="member.hasPersistedInvite()" v-close-popup clickable @click="deleteInvite(member)">
                  <q-item-section avatar>
                    <q-icon name="clear" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>Einladungslink löschen</q-item-label>
                  </q-item-section>
                </q-item>

                <q-item v-if="!member.hasPersistedInvite()" v-close-popup clickable @click="createInvite(member)">
                  <q-item-section avatar>
                    <q-icon name="add_link" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>Einladungslink erstellen</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-btn-dropdown>
          </q-btn-group>
        </q-item-section>

        <q-item-section no-wrap side top>
          <div>
            <q-btn
              v-if="
                hasPermissionForTeam(team, 'team.member.delete') &&
                isEditable(member) &&
                isDeletable(member)
              "
              :loading="member.isDeletingStarted"
              :disable="member.isSaving"
              dense
              flat
              icon="delete"
              name="delete-member"
              round
              tabindex="-1"
              @click="
                member.isPersisted || member.name !== undefined
                  ? confirmBeforeAction({
                      dialogOptions: {
                        title: 'Soll das Mitglied wirklich gelöscht werden?',
                        message:
                          'Das Mitglied taucht dann in den Statistiken nicht mehr auf. ' +
                           'Sofern es noch offene Forderungen oder Guthaben in der Teamkasse gibt, wir das Mitglied dort weiterhin angezeigt.',
                      },
                      onOk: () => deleteClicked(member),
                    })
                  : deleteClicked(member)
              "
            />
            <q-btn
              v-if="
                hasPermissionForTeam(team, 'team.member.update') && !editAll
              "
              :disable="member.isDeletingStarted"
              :icon="isEditable(member) ? 'check' : 'edit'"
              dense
              data-cy="save-or-edit-member"
              flat
              round
              tabindex="-1"
              @click="
                isEditable(member)
                  ? setEditingMember(undefined)
                  : setEditingMember(member)
              "
            />
          </div>
        </q-item-section>
      </q-item>
      <q-separator inset />
    </template>
      <template v-if="newMemberInputVisible">
          <q-form @submit="addMemberAndShowNewMemberInput">
              <q-item >
                  <q-item-section>
                      <q-input
                          v-model="newMemberName"
                          ref="newMemberInput"
                          label="Name"
                          :rules="[val => !!val || 'Bitte gib einen Namen ein']"
                          lazy-rules="ondemand"
                          data-cy="new-member-name-input"
                          dense
                      />
                  </q-item-section>

                  <q-item-section no-wrap side top>
                      <div>
                          <q-btn
                              dense
                              flat
                              icon="delete"
                              name="hide-new-member-input"
                              round
                              tabindex="-1"
                              @click="newMemberInputVisible = false"
                          />
                          <q-btn
                              icon="check"
                              dense
                              data-cy="save-new-member"
                              flat
                              round
                              tabindex="-1"
                              type="submit"
                          />
                      </div>
                  </q-item-section>
              </q-item>
          </q-form>
          <q-separator inset />
      </template>
  </TransitionGroup>

  <q-item v-if="hasPermissionForTeam(team, 'team.member.create')">
    <q-item-section>
      <q-btn
        color="primary"
        icon="add"
        data-cy="add-team-member"
        label="Mitglied hinzufügen"
        outline
        @click="showNewMemberInputAndFocus()"
      />
    </q-item-section>
  </q-item>
</template>

<script lang="ts" setup>
  import { nextTick, Ref, ref, watch, watchEffect } from 'vue';
  import Team from 'src/models/Team';
  import TeamMember from 'src/models/TeamMember';
  import { Notify, QInput } from 'quasar';
  import { useAuthStore } from 'src/stores/auth';
  import { confirmBeforeAction } from 'src/helpers/dialogs';
  import { Clipboard } from '@capacitor/clipboard';
  import TeamRole from 'src/models/TeamRole';
  import StatusRoleBadge from 'src/components/teams/StatusRoleBadge.vue';
  import RoleBadges from 'src/components/teams/RoleBadges.vue';

  const props = defineProps<{
    team: Team
    isTeamReloading: boolean
  }>();

  const emit = defineEmits<{
    addMember: [name: string],
    deleteMember: [member: TeamMember],
    saveMember: [member: TeamMember, force: boolean],
    createInvite: [member: TeamMember],
    deleteInvite: [member: TeamMember],
  }>();

  const authStore = useAuthStore();
  const editingMember = ref<TeamMember | undefined>();
  const editAll = ref(false);

  const memberInputs = ref<QInput[]>([]);
  const newMemberInputVisible = ref(false)
  const newMemberInput = ref<QInput>();
  const newMemberName = ref<string>('');

  // eslint-disable-next-line vue/no-setup-props-destructure
  let memberList = ref<TeamMember[]>(
    props.team.membersSortedByStatusRoleAndName,
  ) as Ref<TeamMember[]>;

  watch(() => props.isTeamReloading, (newValue) => {
      // Reset editing state
      // reloading by pull down will overwrite member list and thus will remove all non persisted members.
    if (newValue) {
      setEditingMember(undefined);
      editAll.value = false;
    }
  });

  watchEffect(() => {
    const sortMembers =
      (!editAll.value && editingMember.value === undefined) ||
      props.team.members.length != memberList.value.length;

    // do not sort list while editing member names, wait till the last edit is done
    if (sortMembers) {
      memberList.value = props.team.membersSortedByStatusRoleAndName;
    }
  });

  function showNewMemberInputAndFocus() {
      newMemberName.value = '';
      newMemberInput.value?.resetValidation();
      newMemberInputVisible.value = true;
      nextTick(() => {
        newMemberInput.value?.focus();
      });
  }

  function addMemberAndShowNewMemberInput() {
      emit('addMember', newMemberName.value);
      showNewMemberInputAndFocus();
      const newMember = props.team.members.at(-1)!
      // trigger saving from here since doing it directly within addMember does not update UI properly
      emit('saveMember', newMember, false);
      // set editing for previously added member to edit roles
      setEditingMember(newMember);
  }

  function isEditable(member: TeamMember) {
    return editAll.value || editingMember.value === member || !member.isPersisted;
  }

  function isDeletable(member: TeamMember) {
    if (authStore.hasFeature('admin.stats')) {
      return true;
    }
    const isSelf = authStore.isOwnMember(member);
    return !isSelf || member.statusRole.name !== 'manager';
  }

  const validateAndSave = async (member: TeamMember, index: number, stayEditable: boolean = false) => {
      const memberInput = memberInputs.value.at(index)
      if (!memberInput) {
          return
      }
      memberInput.resetValidation();
      const isValid = await memberInput.validate();
      if (isValid) {
          triggerSaveMember(member);

          // have to wait 2 times since the "isSaving" value is not set after the first time
          // and data will be rolled back when setting editing member to undefined
          await nextTick();
          await nextTick();
          if(!stayEditable) {
              setEditingMember(undefined);
          }
      }
  };

  function triggerSaveMember(member: TeamMember, force: boolean = false) {
    emit('saveMember', member, force);
  }

  function setEditingMember(member: TeamMember | undefined) {
      // always reset all members that are not saving
      // this way, a single edit or editAll with validation errors will be reset when editing is finished
      memberList.value.forEach(member => {
          if(!member.isSaving) {
              member.rollback();
          }
      })
      editingMember.value = member;
  }

  function deleteClicked(member: TeamMember) {
    emit('deleteMember', member);
  }

  function createInvite(member: TeamMember) {
    emit('createInvite', member);
    const unwatch = watchEffect(async () => {
      if (member.invite?.isPersisted) {
        copyInvite(member);
        unwatch();
      }
    });
  }

  function deleteInvite(member: TeamMember) {
    emit('deleteInvite', member);
  }

  function copyInvite(member: TeamMember) {
    if (member.invite) {
      Clipboard.write({
        string: member.invite.getInviteLink(),
      });
      notifyMessage('Einladungslink für ' + member.name + ' in die Zwischenablage kopiert.');
    }
  }

  function notifyMessage(message: string) {
    Notify.create({
      message: message,
      color: 'positive',
      position: 'bottom',
      timeout: 2000,
    });
  }
</script>

<style lang="scss" scoped>
  .btn-placeholder {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: inline-flex;
    margin: 0 4px;
  }

  .q-btn-group {
    display: inline-flex;
    border-radius: 20px;
    padding: 2px;
  }

  .ownMember {
    border-left: 0.3rem solid;
    border-color: $accent;
  }
</style>
