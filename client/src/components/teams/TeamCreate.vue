<template>

  <template v-if="!hasFeature('team.create')">
    <div data-cy="team-create-feature-missing">
    <p>Aktuell können leider keine neuen Teams erstellt werden.<br>
    Du kannst aber weiterhin mit einer Einladung von deinem Trainer einem Team beizutreten.</p>
    <p>Wir werden bald wieder neue Teams zulassen.</p>
    </div>
  </template>
  <q-form v-else @submit="saveTeam" data-cy="team-create-form">
    <div class="q-py-md text-bold">Wie soll dein Team heißen?</div>
    <q-input
      v-model="team.name"
      :rules="[val => !!val || 'Bitte gib einen Namen ein']"
      label="Teamname"
    />
    <q-card class="q-my-md" flat>
      <q-card-section class="q-pa-none">
        <q-btn
          :loading="team.isSavingStarted"
          color="primary"
          icon="check"
          label="Speichern"
          type="submit"
        />
      </q-card-section>
    </q-card>
  </q-form>

</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import Team from 'src/models/Team';
  import { QForm } from 'quasar';
  import { useRouter } from 'vue-router';
  import { useMutation } from '@tanstack/vue-query';
  import { saveTeamOnServer } from 'src/queries/mutations/teams';
  import { useAuthStore } from 'src/stores/auth';
  import { ROUTE_NAMES } from 'src/router/route-names';

  const team = ref<Team>(new Team());
  const router = useRouter();

  const teamSaved = ref(false);

  const { mutateAsync: saveOnServer } =
    useMutation({ mutationFn: saveTeamOnServer });

  async function saveTeam() {
    await saveOnServer(<Team>team.value);
    teamSaved.value = true;
    await useAuthStore().loadPermissions();
    await router.push({
      name: ROUTE_NAMES.TEAM,
      params: { teamId: team.value?.id },
    });
  }
</script>

<style lang="scss" scoped></style>
