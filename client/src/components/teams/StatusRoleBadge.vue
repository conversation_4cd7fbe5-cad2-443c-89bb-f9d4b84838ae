<template>

  <q-badge
    v-if="member.deletedAt"
    :color="'accent'"
    :text-color="'primary'"
    label="gelöscht"
  />
  <q-badge
    v-else-if="member.statusRole.name !== 'member'"
    :color="member.statusRole.name === 'manager' ? 'primary' : 'grey'"
    :label="member.statusRole.getName()"
  />

</template>

<script lang="ts" setup>
  import TeamMember from 'src/models/TeamMember';

  defineProps<{
    member: TeamMember;
  }>();

</script>

<style lang="scss" scoped></style>
