<template>
  <q-card :data-cy="'team-card-' + team.name">
    <q-card-section class="bg-primary text-white" horizontal>
      <q-card-section class="q-py-xs">
        <div class="text-h6">{{ team.name }}</div>
      </q-card-section>
    </q-card-section>

    <q-separator />

      <TeamSubscriptionStatus :team="team"/>

    <q-card-section horizontal>
      <q-card-section>
        {{ team.membersCount }} Mitglieder
      </q-card-section>
      <q-separator inset vertical />
      <q-card-section>
        von mir verwaltet:
        <template v-for="(member) in team.getAssignedMembers(true)"
                  :key="member.id"
        >
          <q-item-label caption>
            {{ member.name }}
            <StatusRoleBadge :member="member" />
          </q-item-label>
        </template>
      </q-card-section>
    </q-card-section>

    <q-card-actions align="right">
      <q-btn
        :to="{name: $routeNames.TEAM, params: {teamId: team.id}}"
        color="primary"
        flat
        data-cy="show-team-members"
      >Mitglieder anzeigen
      </q-btn>
    </q-card-actions>
  </q-card>
</template>

<script lang="ts" setup>

  import Team from 'src/models/Team';
  import TeamSubscriptionStatus from 'src/components/teams/TeamSubscriptionStatus.vue';
  import StatusRoleBadge from 'src/components/teams/StatusRoleBadge.vue';

  const props = defineProps<{
    team: Team;
  }>();

</script>

<style lang="scss" scoped>

</style>
