import LayoutContainer from 'app/test/cypress/wrappers/LayoutContainer.vue';
import QuasarDrawer from '../../_tests_samples/QuasarDrawer.vue';

describe('QuasarDrawer', () => {
  it('should show a drawer', () => {
    cy.mount(LayoutContainer, {
      props: {
        component: QuasarDrawer,
      },
    });
    cy.dataCy('drawer')
      .should('exist')
      .dataCy('button')
      .should('not.be.visible');
    cy.get('.q-scrollarea .scroll').scrollTo('bottom', { duration: 500 });
    cy.get('.q-scrollarea .scroll').dataCy('button').should('be.visible');
  });
});
