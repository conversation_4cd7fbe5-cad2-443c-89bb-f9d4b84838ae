<template>
  <q-dialog v-model="value">
    <q-card>
      <q-card-section class="items-center q-pb-none ">
        <div class="text-h6 q-mr-lg">{{ title }}</div>
        <q-btn v-close-popup class="absolute-top-right" flat icon="close" />
      </q-card-section>
      <q-card-section>
        <slot></slot>
      </q-card-section>
      <q-card-actions :vertical="actionsVertical" align="right">
        <slot name="actions"></slot>
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>

  const props = withDefaults(defineProps<{
    title: string,
    actionsVertical?: boolean
  }>(), {
    actionsVertical: false,
  });

  const value = defineModel<boolean>();

</script>

<style lang="scss" scoped></style>
