<template>

  <template v-if="!isInitialLoadedAfterTeamChanged">
    <q-card-section>
      Te<PERSON>ine werden geladen
      <q-spinner />
    </q-card-section>
  </template>

  <template v-else-if="!hasTeamEvents">
    <q-card-section>
      <PERSON><PERSON><PERSON> dieses Team wurden noch keine Termine erstellt.
    </q-card-section>
  </template>

  <template v-else>
    <q-table
      v-model:selected="selected"
      :columns="columns"
      :loading="isFetching"
      :pagination="{rowsPerPage:0, sortBy: 'participation', descending: true}"
      :rows="rows"
      binary-state-sort
      hide-pagination
      row-key="id"
      table-class="sticky-header sticky-first-column"
    >
      <template v-slot:top-left>
        <q-toggle
          v-model="showSumYesPerStatusRole"
          :label="'Rollen in Summe anzeigen'"
        />
      </template>
      <template v-slot:top-right="props">
        <q-btn
          :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'" class="q-ml-md" dense
          flat
          round
          @click="props.toggleFullscreen"
        />
      </template>

      <template v-if="columns.length > 0" v-slot:top-row="props">
        <q-tr class="dense-header-row">
          <q-td class="vertical-top" no-hover>
            Summe
          </q-td>
          <q-td no-hover>

          </q-td>

          <q-td v-for="col in props.cols.slice(2) as ExtendedQTableColumn<StatisticRow>[]" :key="col.name" :class="{'vertical-top': showSumYesPerStatusRole, 'text-center': !showSumYesPerStatusRole}"
                no-hover>
            {{ col.voteSumYes }}
            <q-tooltip v-if="!showSumYesPerStatusRole">
              <template v-for="(sum, roleName) in col.voteSumPerStatusRoleName" :key="roleName">
                {{ sum }} {{ TeamRole.getDisplayName(roleName, sum) }}<br>
              </template>
            </q-tooltip>

            <template v-if="showSumYesPerStatusRole">
              Teilnehmer
              <q-separator class="q-mb-xs" />
            </template>
            <template v-if="showSumYesPerStatusRole">
              <template v-for="(sum, roleName) in col.voteSumPerStatusRoleName" :key="roleName">
                {{ sum }} {{ TeamRole.getDisplayName(roleName, sum) }}<br>
              </template>
            </template>
          </q-td>
        </q-tr>

      </template>

      <template v-slot:header-cell="props">
        <q-th :props="props" class="vertical-bottom">

          <template v-if="props.col.eventType">
            <q-btn
              :icon="TeamEventTypeOptions.find((option) => option.value === props.col.eventType)?.icon"
              :label="props.col.label"
              :to="{name: $routeNames.EVENTS_SHOW, params: {eventId: props.col.name}}"
              flat
              padding="none"
              size="12px"
              stack
            >
              <q-tooltip>
                {{ props.col.eventTooltip }}
              </q-tooltip>
            </q-btn>
          </template>

          <template v-else>
            {{ props.col.label }}
          </template>

        </q-th>
      </template>

      <template v-slot:body-cell-member="props">
        <q-td :props="props" auto-width>
          {{ props.value }}
          <StatusRoleBadge :member="props.row.member" />
        </q-td>
      </template>

      <template v-slot:body-cell-participation="props">
        <q-td :props="props" auto-width>
          {{ props.value }} ({{ getParticipationPercent(props.value) }}%)
        </q-td>
      </template>

      <template v-slot:body-cell="props">
        <q-td :props="props">
          <template v-if="props.col.eventType">
                      <span class="voting">
                        <q-icon
                          v-if="props.value === 'yes'"
                          class="vote-yes"
                          name="thumb_up"
                        />
                        <q-icon
                          v-else-if="props.value === 'no'"
                          class="vote-no"
                          name="thumb_down"
                        />
                        <q-icon
                          v-else-if="props.value === 'maybe'"
                          class="vote-maybe"
                          name="question_mark"
                        />
                        <q-icon
                          v-else-if="props.value === 'none'"
                          class="vote-none"
                          name="o_circle"
                        />
                      </span>
            <template v-if="props.row.voteComments[props.col.name]">
              <q-tooltip>
                {{ props.row.voteComments[props.col.name] }}
              </q-tooltip>
            </template>
          </template>
          <template v-else>
            {{ props.value }}
          </template>
        </q-td>
      </template>
    </q-table>
  </template>
</template>

<script lang="ts" setup>
  import { TeamEventTypeOptions } from 'src/models/TeamEvent';
  import { ExtendedQTableColumn, StatisticRow, useEventStatsParticipation } from 'src/composables/useEventStatsParticipation';
  import TeamRole from '../../models/TeamRole';
  import { EventStatsFilter } from 'src/composables/useEventStatsFilter';
  import StatusRoleBadge from 'src/components/teams/StatusRoleBadge.vue';

  const props = defineProps<{
    filter: EventStatsFilter
  }>();

  const {
    isInitialLoadedAfterTeamChanged,
    hasTeamEvents,
    isFetching,
    getParticipationPercent,
    columns,
    rows,
    selected,
    showSumYesPerStatusRole,
  } = useEventStatsParticipation(props.filter);

</script>

<style lang="scss" scoped></style>
