<template>

  <span v-if="isLoadingTeams">lade Team Daten... <q-spinner /></span>
  <span v-else-if="isErrorTeams"><PERSON><PERSON> beim <PERSON>den der Teams</span>
  <template v-else>
    <q-card>
      <q-card-section class="bg-primary text-white q-py-xs">
        <div class="text-h6">Filter</div>
      </q-card-section>
      <q-card-section>
        <q-select
          v-if="teams && teams.length > 1"
          v-model="selectedTeam"
          :loading="isFetchingTeams"
          :options="teams"
          class="q-mb-lg"
          clearable
          label="Team auswählen"
          option-label="name"
        />

        <div class="row">
          <DateTimeInputField
            v-model="startDateString"
            :mask="defaultDateMask"
            :rules="[val => !!val || 'Bitte gib ein Datum an']"
            class="col-sm-auto col-6"
            label="von"
            type="date"
            @update:model-value="filter.setSelectedStatsRange()"
          />

          <DateTimeInputField
            v-model="endDateString"
            :mask="defaultDateMask"
            class="col-sm-auto col-6"
            clearable
            label="bis"
            type="date"
            @update:model-value="filter.setSelectedStatsRange()"
          />
          <div class="q-pa-sm ">
            <TeamStatsRangeSelect :filter=filter />
          </div>
        </div>


        <q-option-group
          v-model="selectedEventTypes"
          :options="TeamEventTypeOptions"
          class="row"
          color="accent"
          type="checkbox"
        >
          <template v-slot:label="opt">
            <div class="q-mr-md">
              <q-icon :name="opt.icon" class="q-ml-sm" size="1.5em" />
              <span>{{ opt.label }}</span>
            </div>
          </template>
        </q-option-group>
      </q-card-section>
    </q-card>
  </template>

</template>

<script lang="ts" setup>

  import { EventStatsFilter } from 'src/composables/useEventStatsFilter';
  import { TeamEventTypeOptions } from 'src/models/TeamEvent';
  import { defaultDateMask } from 'src/i18n';
  import DateTimeInputField from 'src/components/DateTimeInputField.vue';
  import TeamStatsRangeSelect from 'src/components/stats/TeamStatsRangeSelect.vue';

  const props = defineProps<{
    filter: EventStatsFilter
  }>();

  const {
    teams,
    isErrorTeams,
    isLoadingTeams,
    isFetchingTeams,
    selectedTeam,
    startDateString,
    endDateString,
    selectedEventTypes,
  } = props.filter;

</script>

<style lang="scss" scoped></style>
