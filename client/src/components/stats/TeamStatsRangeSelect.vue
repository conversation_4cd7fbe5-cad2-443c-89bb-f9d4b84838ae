<template>

  <q-btn
    :disable="!filter.selectedTeam.value  || (statsRangesData.length === 0 && !hasPermissionForTeam(<Team>filter.selectedTeam.value, 'team.stats.range.create'))"
    :icon-right="statsRangesData.length > 0 ? 'navigate_next' : undefined"
    :loading="filter.selectedTeam.value && !isInitialLoadedAfterTeamChanged"
    no-caps
    @click="showRangeSelectDialog = true"
  >
    <span v-if="!filter.selectedTeam.value">
      Zeitraum: Bitte Team auswählen
    </span>

    <span v-else-if="filter.selectedStatsRange.value">
      Zeitraum: {{ filter.selectedStatsRange.value.name }}
    </span>

    <template v-else-if="statsRangesData.length === 0">
      <span v-if="hasPermissionForTeam(<Team>filter.selectedTeam.value, 'team.stats.range.create')">
        vordefinierten Zeitraum erstellen
      </span>
      <span v-else>
        Kein vordefinierter Zeitraum vorhanden
      </span>
    </template>
    <span v-else>
      Zeitraum auswählen
    </span>

  </q-btn>
  <InfoIcon class="q-pl-sm">
    <strong>Zeiträume erstellen geht nur mit der Rolle "{{ TeamRole.getDisplayName('manager') }}"</strong><br />
    Ein Zeitraum ist nützlich um den Filter auf eine Saison zu setzen.<br>
    Wenn das heutige Datum in einem vordefinierten Zeitraum liegt, wird dieser automatisch ausgewählt.<br>
    <br>
    Bei den Aufgaben Statistiken innerhalb von einem Termin wird automatisch der zum Termin passende Zeitraum betrachtet.
    Gibt es mehrere Zeiträume zu einem Tag, hat immer der mit dem spätesten Enddatum Vorrang.
    Die Reihenfolge ist hier in der Auswahlliste sichtbar.

  </InfoIcon>

  <CustomDialog v-model="showRangeSelectDialog" title="Wähle eine Zeitraum für die Statistiken aus.">
    <q-item-label>Diese Zeiträume gehören zum Team: <b>{{ filter.selectedTeam.value.name }}</b></q-item-label>
    <template v-if="!isInitialLoadedAfterTeamChanged">
      <q-item-label class="q-pt-sm">
        Zeiträume werden geladen
        <q-spinner />
      </q-item-label>
    </template>
    <template v-else-if="isStatsRangesError">
      <q-item-label class="q-pt-sm">
        Fehler beim Laden der Zeiträume
      </q-item-label>
    </template>

    <template v-else>
      <q-list separator>
        <q-item class=" text-subtitle2 no-padding">
          <q-item-section>Name</q-item-section>
          <q-item-section>Start</q-item-section>
          <q-item-section>Ende</q-item-section>
          <q-item-section side>
            <q-btn
              v-if="hasPermissionForTeam(<Team>filter.selectedTeam.value, 'team.stats.range.delete')"
              :icon="isListEditable ? 'check' : 'edit'"
              class="full-height"
              flat
              @click="isListEditable = !isListEditable"
            />
          </q-item-section>
        </q-item>

        <template
          v-for="(statsRange) in statsRangesData"
          :key="statsRange.id"
        >
          <q-item class="no-padding">
            <q-item-section>{{ statsRange.name }}</q-item-section>
            <q-item-section class="q-mx-sm">{{ statsRange.startDateFormatted }}</q-item-section>
            <q-item-section class="q-mx-sm">{{ statsRange.endDateFormatted }}</q-item-section>
            <q-item-section side>
              <q-btn
                v-if="isListEditable"
                :loading="statsRange.isDeletingStarted"
                class="full-height"
                flat
                icon="delete"
                @click="confirmBeforeAction({
                      dialogOptions: {
                        title: 'Zeitraum '+statsRange.name+' entfernen?',
                        message: 'Soll der Zeitraum vom '+statsRange.startDateFormatted+' bis '+statsRange.endDateFormatted+' wirklich entfernt werden? Es werden dabei keine Termine gelöscht.',
                      },
                      onOk: () => deleteRange(<TeamStatsRange>statsRange),
                    })
              "
              />
              <q-btn
                v-else
                v-close-popup
                class="full-height"
                flat
                icon-right="navigate_next"
                no-caps
                no-wrap
                @click="selectStatsRange(<TeamStatsRange>statsRange)"
              />
            </q-item-section>
          </q-item>
        </template>

        <template v-if="hasPermissionForTeam(<Team>filter.selectedTeam.value, 'team.stats.range.create')">
          <q-item v-if="!showNewStatsRangeForm" class="no-padding">
            <q-item-section>
              <q-btn
                color="primary"
                icon="add"
                label="Zeitraum hinzufügen"
                outline
                @click="showNewStatsRangeForm = true"
              />
            </q-item-section>
          </q-item>

          <q-form v-else @submit="saveNewRange">
            <div class="q-py-md text-bold">Zeitraum hinzufügen</div>
            <q-input
              v-model="newStatsRange.name"
              :rules="[
                        val => !!val || 'Bitte gib einen Namen ein',
                        val => statsRangesData.find((statsRange) => statsRange.name === val) === undefined || 'Ein Zeitraum mit diesem Namen existiert schon'
                        ]"
              label="Name"
              lazy-rules="ondemand"
            />
            <DateTimeInputField
              v-model="newStatsRange.startDateFormatted"
              :mask="defaultDateMask"
              :rules="[val => !!val || 'Bitte gib ein Datum an']"
              class="col-sm-auto col-6"
              label="von"
              type="date"
            />
            <DateTimeInputField
              v-model="newStatsRange.endDateFormatted"
              :mask="defaultDateMask"
              :rules="[val => !!val || 'Bitte gib ein Datum an']"
              class="col-sm-auto col-6"
              label="bis"
              type="date"
            />
            <q-btn
              :loading="newStatsRange.isSaving || isStatsRangesRefetching"
              color="primary"
              icon="check"
              label="speichern"
              type="submit"
            />
          </q-form>
        </template>

      </q-list>
    </template>
  </CustomDialog>

</template>

<script lang="ts" setup>

  import { ref, watch } from 'vue';
  import TeamStatsRange from 'src/models/TeamStatsRange';
  import Team from 'src/models/Team';
  import { useMutation } from '@tanstack/vue-query';
  import { saveTeamStatsRangeOnServer } from 'src/queries/mutations/teams';
  import { defaultDateMask } from 'src/i18n';
  import DateTimeInputField from 'src/components/DateTimeInputField.vue';
  import { EventStatsFilter } from 'src/composables/useEventStatsFilter';
  import { useTeamStatsRangeFilter } from 'src/composables/useTeamStatsRangeFilter';
  import { confirmBeforeAction } from 'src/helpers/dialogs';
  import CustomDialog from 'src/components/CustomDialog.vue';
  import InfoIcon from 'src/components/InfoIcon.vue';
  import TeamRole from '../../models/TeamRole';

  const props = defineProps<{
    filter: EventStatsFilter
  }>();

  const {
    isInitialLoadedAfterTeamChanged,
    isStatsRangesError,
    statsRangesData,
    isStatsRangesRefetching,
    refetchStatsRanges,
  } = useTeamStatsRangeFilter(props.filter);

  const showRangeSelectDialog = ref(false);
  const isListEditable = ref(false);

  const showNewStatsRangeForm = ref(false);
  const newStatsRange = ref<TeamStatsRange>(TeamStatsRange.createForTeam(<Team>props.filter.selectedTeam.value));

  function resetNewStatsRange() {
    newStatsRange.value = TeamStatsRange.createForTeam(<Team>props.filter.selectedTeam.value);
  }

  watch(showRangeSelectDialog, () => {
      showNewStatsRangeForm.value = false;
      resetNewStatsRange();
    },
  );
  watch(() => props.filter.selectedTeam.value, () => {
      resetNewStatsRange();
    },
  );


  const { mutateAsync: saveNewRangeOnServer } =
    useMutation({ mutationFn: saveTeamStatsRangeOnServer });

  async function saveNewRange() {
    await saveNewRangeOnServer(<TeamStatsRange>newStatsRange.value);
    await refetchStatsRanges();
    props.filter.refetchTeams();
    resetNewStatsRange();
    showNewStatsRangeForm.value = false;
  }

  const { mutateAsync: deleteTaskOnServer } = useMutation({
    mutationFn: (statsRange: TeamStatsRange) => statsRange.destroy(),
  });

  async function deleteRange(statsRange: TeamStatsRange) {
    if (statsRange.id === props.filter.selectedStatsRange.value?.id) {
      props.filter.setSelectedStatsRange();
    }
    await deleteTaskOnServer(statsRange);
    await refetchStatsRanges();
    props.filter.refetchTeams();
  }

  function selectStatsRange(statsRange: TeamStatsRange) {
    props.filter.setSelectedStatsRange(statsRange);
  }
</script>

<style lang="scss" scoped></style>
