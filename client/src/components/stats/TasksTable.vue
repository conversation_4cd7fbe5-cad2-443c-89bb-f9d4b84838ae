<template>

  <template v-if="!isInitialLoadedAfterTeamChanged">
    <q-card-section>
      Aufgaben werden geladen
      <q-spinner />
    </q-card-section>
  </template>

  <template v-else-if="!hasTaskConfigs">
    <q-card-section>
      <PERSON><PERSON><PERSON> dieses Team wurden noch keine Aufgaben erstellt
    </q-card-section>
  </template>

  <template v-else>
    <q-card-section v-if="title" class="q-py-none">
      <span v-html="title"></span>
    </q-card-section>
    <q-card-section v-if="showLastEntry && latestEntryForSelectedConfig" class="q-py-none">
      zuletzt: {{ latestEntryForSelectedConfig?.teamMember.name }} am {{ latestEntryForSelectedConfig?.teamEvent.dateBeginFormatted }}
    </q-card-section>
    <q-select
      v-if="showTaskConfigSelection"
      v-model="selectedTaskConfig"
      :options="taskConfigQueryData"
      filled
      option-label="title"
      option-value="id"
      square
    />

    <q-table
      v-model:selected="selected"
      :columns="columns"
      :loading="isFetchingTasks"
      :pagination="{rowsPerPage:0, sortBy: 'sum', descending: true}"
      :rows="rows"
      binary-state-sort
      hide-pagination
      row-key="id"
      table-class="sticky-header sticky-first-column"
    >

      <template v-slot:body-cell-member="props">
        <q-td :props="props" auto-width>
          {{ props.value }}
          <StatusRoleBadge :member="props.row.member" />
        </q-td>
      </template>

      <template v-slot:body-cell-sum="props">
        <q-td :props="props" auto-width>
          {{ props.value }}
        </q-td>
      </template>

      <template v-slot:body-cell-vote="props">
        <q-td :props="props" auto-width>
          <span class="voting">
                        <q-icon
                          v-if="props.value === 'yes'"
                          class="vote-yes"
                          name="thumb_up"
                        />
                        <q-icon
                          v-else-if="props.value === 'no'"
                          class="vote-no"
                          name="thumb_down"
                        />
                        <q-icon
                          v-else-if="props.value === 'maybe'"
                          class="vote-maybe"
                          name="question_mark"
                        />
                        <q-icon
                          v-else-if="props.value === 'none'"
                          class="vote-none"
                          name="o_circle"
                        />
                      </span>
        </q-td>
      </template>

      <template v-slot:body-cell-entries="props">
        <q-td :props="props">
          <template v-for="task in props.value as TeamEventTask[]" :key="task.id">
            <q-btn
              :icon="TeamEventTypeOptions.find((option) => option.value === task.teamEvent.eventType)?.icon"
              :to="{name: $routeNames.EVENTS_SHOW, params: {eventId: task.teamEvent.id}}"
              class="text-grey"
              dense
              flat
              no-caps
              size="12px"
              stack
            >
              <!--              {{ task.teamEvent.getDateBeginFormatted('DD.MM.') }}-->
              <q-tooltip>
                {{ task.teamEvent.getPageTitle() }} am {{ task.teamEvent.dateBeginFormatted }}
              </q-tooltip>
            </q-btn>
          </template>
        </q-td>
      </template>
    </q-table>
  </template>

</template>

<script lang="ts" setup>

  import { useEventStatsTasks } from 'src/composables/useEventStatsTasks';
  import { EventStatsFilter } from 'src/composables/useEventStatsFilter';
  import TeamEventTask from 'src/models/TeamEventTask';
  import TeamEvent, { TeamEventTypeOptions } from 'src/models/TeamEvent';
  import { ROUTE_NAMES } from 'src/router/route-names';
  import { useRouter } from 'vue-router';
  import { toRef, watch } from 'vue';
  import StatusRoleBadge from 'src/components/teams/StatusRoleBadge.vue';

  const props = withDefaults(defineProps<{
    filter: EventStatsFilter,
    title?: string,
    selectedTaskConfigId?: string,
    enableRouteChange?: boolean,
    showTaskConfigSelection?: boolean,
    showLastEntry?: boolean,
    eventForVotes?: TeamEvent
  }>(), {
    enableRouteChange: false,
    showTaskConfigSelection: true,
    showLastEntry: false,
    eventForVotes: undefined,
  });

  defineExpose({
    refetchTaskList,
  });

  function refetchTaskList() {
    refetchTasks();
  }

  const {
    isInitialLoadedAfterTeamChanged,
    taskConfigQueryData,
    hasTaskConfigs,
    isFetchingTasks,
    refetchTasks,
    columns,
    rows,
    selected,
    selectedTaskConfig,
    latestEntryForSelectedConfig,
  } = useEventStatsTasks(props.filter, props.selectedTaskConfigId, toRef(props, 'eventForVotes'));

  const router = useRouter();

  if (props.enableRouteChange) {
    watch(selectedTaskConfig, () => {
      router.replace({
        name: ROUTE_NAMES.STATS, params: {
          teamId: props.filter.selectedTeam.value?.id,
          tab: 'tasks',
          elementId: selectedTaskConfig.value?.id,
        },
      });
    });
  }

</script>

<style lang="scss" scoped></style>
