<template>
  <q-editor
    ref="editorRef"
    :model-value="modelValue || ''"
    :toolbar="[
              ['bold', 'italic', 'underline', 'strike', 'size-5'],
              ['link', 'hr', 'unordered', 'ordered'],
              ['removeFormat', 'undo', 'redo'],
            ]"
    min-height="5rem"
    @focusout="checkValue"
    @paste="onPaste"
    @update:model-value="(newValue) => modelValue = newValue"
  />
</template>

<script lang="ts" setup>

  import { ref } from 'vue';
  import { QEditor } from 'quasar';
  import BaseModel from 'src/models/BaseModel';

  const modelValue = defineModel<string | null>();

  function checkValue(): void {
    if (modelValue.value) {
      const strippedValue = BaseModel.stripHtml(modelValue.value);
      if (strippedValue.length == 0) {
        modelValue.value = null;
      }
    }
  }

  const editorRef = ref<QEditor>();

  /**
   * Capture the <CTL-V> paste event, only allow plain-text, no images.
   * See: https://quasar.dev/vue-components/editor#plaintext-pasting
   */
  function onPaste(evt) {
    if (editorRef.value) {
      // Let inputs do their thing, so we don't break pasting of links.
      if (evt.target.nodeName === 'INPUT') return;
      let text;
      // let onPasteStripFormattingIEPaste
      evt.preventDefault();
      evt.stopPropagation();
      if (evt.originalEvent && evt.originalEvent.clipboardData.getData) {
        text = evt.originalEvent.clipboardData.getData('text/plain');
        editorRef.value.runCmd('insertText', text);
      } else if (evt.clipboardData && evt.clipboardData.getData) {
        text = evt.clipboardData.getData('text/plain');
        editorRef.value.runCmd('insertText', text);
      }
      // else if (window.clipboardData && window.clipboardData.getData) {
      //   if (!onPasteStripFormattingIEPaste) {
      //     onPasteStripFormattingIEPaste = true
      //     detailsEditorRef.value.runCmd('ms-pasteTextOnly', text)
      //   }
      //   onPasteStripFormattingIEPaste = false
      // }
    }
  }
</script>

<style lang="scss" scoped>

</style>
