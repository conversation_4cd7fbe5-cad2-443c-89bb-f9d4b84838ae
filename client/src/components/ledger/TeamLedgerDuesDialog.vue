<template>
    <template v-if="dense">
        <q-btn
            :class="props.class"
            :icon="props.icon"
            :label="props.label"
            no-caps
            dense
            size="sm"
            @click="showDialog = true"
        />
    </template>
    <template v-else>
        <q-card class="partial-width" flat>
            <q-card-actions vertical>
                <q-btn
                    :icon="props.icon"
                    :label="props.label"
                    no-caps
                    outline
                    @click="showDialog = true"
                />
            </q-card-actions>
        </q-card>
    </template>

    <CustomDialog v-model="showDialog" :title="title">
        <q-form
            @submit="save"
            v-if="newDues"
            style="max-width: 365px"
        >
            <div class="q-py-md text-bold">Ein Beitrag kann ein Geldbetrag oder ein Gegenstand sein. Einmal festgelegt, kann das nicht mehr geändert werden</div>
            <q-btn-toggle
                v-if="!editDues"
                v-model="isMoneyTypeSelected"
                :options="[
                    {label: 'Betrag', value: true},
                    {label: 'Gegenstand', value: false},
                ]"
                class="full-width q-mb-md"
                no-caps
                spread
                stack
                text-color="primary"
                toggle-color="secondary"
                toggle-text-color="primary"
            />
            <q-input
                v-model="newDues.title"
                :rules="[val => !!val || 'Bitte gib einen Titel ein',]"
                label="Titel"
                lazy-rules="ondemand"
                outlined
            />
            <MoneyInput
                v-if="isMoneyTypeSelected"
                v-model="newDues.amount"
                label="Betrag"
                clearable
                no-zero-allowed
            />
            <q-input
                v-else
                v-model="newDues.item"
                :rules="[val => !!val || 'Bitte gib einen Gegenstand ein',]"
                label="Gegenstand"
                lazy-rules="ondemand"
                outlined
            />

            <template v-if="editDues && editDues.isRecurring()">
                Beitrag besteht seit: {{editDues.dateBeginFormatted}}<br>
<!--                <q-field-->
<!--                    v-model="isCreateFollowUp"-->
<!--                    :rules="[val => val !== undefined || 'Bitte wähle eine Option aus']"-->
<!--                    borderless-->
<!--                    tag="div"-->
<!--                >-->
<!--                    <q-list class="q-pr-sm">-->
<!--                        <q-item class="q-px-none">-->
<!--                            <q-item-section>-->
<!--                                <q-item-label class="text-bold">Ab wann gilt die Änderung?</q-item-label>-->
<!--                                <q-item-label caption>-->
<!--                                    Beitrag besteht seit: {{editDues.dateBeginFormatted}}-->
<!--                                </q-item-label>-->
<!--                            </q-item-section>-->
<!--                            <q-item-section side class="q-mr-xs">-->
<!--                                <TeamLedgerChangeClaimInfoIcon :claimableType="TeamLedgerDues.jsonapiType" />-->
<!--                            </q-item-section>-->
<!--                        </q-item>-->

<!--                        <q-item tag="label" v-ripple class="q-px-none">-->
<!--                            <q-item-section avatar>-->
<!--                                <q-radio v-model="isCreateFollowUp" :val="false" checked-icon="task_alt" />-->
<!--                            </q-item-section>-->
<!--                            <q-item-section>-->
<!--                                <q-item-label>Ab Startdatum</q-item-label>-->
<!--                                <q-item-label caption>Beitrag wird geändert und alle bestehenden Forderungen angepasst.</q-item-label>-->
<!--                            </q-item-section>-->
<!--                        </q-item>-->

<!--                        <q-item tag="label" v-ripple class="q-px-none">-->
<!--                            <q-item-section avatar>-->
<!--                                <q-radio v-model="isCreateFollowUp" :val="true" checked-icon="task_alt"/>-->
<!--                            </q-item-section>-->
<!--                            <q-item-section>-->
<!--                                <q-item-label>Datum auswählen</q-item-label>-->
<!--                                <q-item-label caption>ältere Beiträge vor dem gewählten Datum bleiben unverändert</q-item-label>-->
<!--                            </q-item-section>-->
<!--                        </q-item>-->
<!--                    </q-list>-->
<!--                </q-field>-->
            </template>

            <DateTimeInputField
                v-if="!editDues || !editDues.isRecurring()"
                v-model="newDues.dateBeginFormatted"
                :mask="defaultDateMask"
                :rules="[val => !!val || 'Bitte gib ein Datum an']"
                class="col-sm-auto col-6"
                :label="newDues.isRecurring() ? 'Startdatum' : 'Fällig am'"
                :options="(date: string) => editDues === undefined || !editDues.parent || editDues.isAfterDateBegin(date)"
                type="date"
            />
            <RecurringEditor
                v-model="newDues.rruleString"
                :date-begin="newDues.getDateStart()"
                :rule-editable="!editDues"
                class="q-mb-md"
                flat
            />
            <q-btn
                class="full-width"
                :loading="newDues?.isSaving"
                color="primary"
                icon="check"
                label="speichern"
                type="submit"
            />

            <q-btn
                v-if="props.editDues"
                class="full-width q-mt-md"
                :disable="newDues.isSaving"
                :loading="props.editDues.isDeletingStarted"
                color="negative"
                icon="delete"
                label="Löschen"
                @click="deleteFineDialogVisible = true"
            />

        </q-form>
    </CustomDialog>

    <CustomDialog
        v-model="deleteFineDialogVisible"
        title="Beitrag löschen"
    >
        <span v-if="isClaimCountLoading">lade Infos zur Strafe...<q-spinner /></span>
        <template v-else>

            <q-form @submit="deleteDues"
                    v-if="editDues"
                    style="max-width: 365px"
            >
                Titel: {{editDues.title}}<br>
                <template v-if="editDues.isMoneyType()">
                    Betrag: {{editDues.amount.formatted}}<br>
                </template>
                <template v-else>
                    Gegenstand: {{editDues.item}}<br>
                </template>
                <template v-if="editDues?.isRecurring()">
                    Fällig: {{useRecurring(editDues.getRRule()).getRecurrenceText()}}<br>
                    Zeitraum: {{useRecurring(editDues.getRRule()).getRangeString()}}<br>
                </template>
                <template v-else>
                    Fällig: {{ editDues.dateBeginFormatted}}<br>
                </template>
                vergeben: <strong>{{ claimCount }}x</strong><br>
<!--                <template v-if="lastClaim">-->
<!--                    zuletzt am {{lastClaim.dueDateFormatted}}-->
<!--                </template>-->

                <q-field
                    v-model="deleteFineChoiceIsSoftDelete"
                    :rules="[val => val !== undefined || 'Bitte wähle eine Option aus']"
                    borderless
                    tag="div"
                >
                    <q-list class="q-pr-sm">

<!--                        <template v-if="editDues?.parent">-->
<!--                            <q-item class="q-px-none">-->
<!--                                <q-item-section>-->
<!--                                    <q-item-label class="text-bold">Vorherig Version vorhanden:</q-item-label>-->
<!--                                    <q-item-label caption>-->
<!--                                        Titel: {{editFine.parent.title}}<br>-->
<!--                                        <template v-if="editFine.parent.isMoneyType()">-->
<!--                                            Betrag: {{editFine.parent.amount.formatted}}<br>-->
<!--                                        </template>-->
<!--                                        <template v-else>-->
<!--                                            Gegenstand: {{editFine.parent.item}}<br>-->
<!--                                        </template>-->
<!--                                        Gültig seit: {{editFine.parent.dateBeginFormatted}}-->

<!--                                    </q-item-label>-->
<!--                                </q-item-section>-->
<!--                            </q-item>-->

<!--                            <q-item tag="label" v-ripple class="q-px-none">-->
<!--                                <q-item-section avatar>-->
<!--                                    <q-radio v-model="deleteFineChoiceIsSoftDelete" :val="false" checked-icon="task_alt"/>-->
<!--                                </q-item-section>-->
<!--                                <q-item-section>-->
<!--                                    <q-item-label>Vorherige Version wiederherstellen</q-item-label>-->
<!--                                    <q-item-label caption v-if="claimCount > 0">aktuelle Strafe wird gelöscht und alle Forderungen auf die vorherige Version angepasst.</q-item-label>-->
<!--                                </q-item-section>-->
<!--                                <q-item-section side class="q-mr-xs">-->
<!--                                    <TeamLedgerChangeClaimInfoIcon :claimableType="TeamLedgerFine.jsonapiType" />-->
<!--                                </q-item-section>-->
<!--                            </q-item>-->

<!--                            <q-item tag="label" v-ripple class="q-px-none">-->
<!--                                <q-item-section avatar>-->
<!--                                    <q-radio v-model="deleteFineChoiceIsSoftDelete" :val="true" checked-icon="task_alt"/>-->
<!--                                </q-item-section>-->
<!--                                <q-item-section>-->
<!--                                    <q-item-label>Strafe aus Liste löschen</q-item-label>-->
<!--                                    <q-item-label caption v-if="claimCount > 0">Forderungen bleiben unverändert, die Strafe kann aber nicht mehr vergeben werden.</q-item-label>-->
<!--                                </q-item-section>-->
<!--                            </q-item>-->
<!--                        </template>-->

<!--                        <template v-else-if="claimCount > 0"> &lt;!&ndash; no parent &ndash;&gt;-->
                            <q-item tag="label" v-ripple class="q-px-none">
                                <q-item-section avatar>
                                    <q-radio v-model="deleteFineChoiceIsSoftDelete" :val="false" checked-icon="task_alt"/>
                                </q-item-section>
                                <q-item-section>
                                    <q-item-label>Vollständig mit Forderungen löschen</q-item-label>
                                    <q-item-label caption>
                                        Alle bereits vergebenen Forderungen zu diesem Beitrag werden gelöscht.
                                        <template v-if="editDues?.isMoneyType()">Bereits bezahlte Forderungen werden als Guthaben notiert.</template>
                                    </q-item-label>
                                </q-item-section>
                            </q-item>

                            <q-item tag="label" v-ripple class="q-px-none"
                                v-if="editDues?.isEnded() || editDues?.isStarted()"
                            >
                                <q-item-section avatar>
                                    <q-radio v-model="deleteFineChoiceIsSoftDelete" :val="true" checked-icon="task_alt"/>
                                </q-item-section>
                                <q-item-section>
                                    <q-item-label>Beitrag aus Katalog löschen</q-item-label>
                                    <q-item-label caption v-if="editDues?.isEnded()">
                                        Der Beitrag liegt in der Vergangenheit.
                                    </q-item-label>
                                    <q-item-label caption v-else>
                                        Das Enddatum wird auf heute gesetzt, zukünftige Forderungen werden somit gelöscht.<br>
                                    </q-item-label>
                                    <q-item-label caption>
                                        Vergangene Forderungen bleiben unverändert, der Beitrag wird hier aber nicht mehr aufgeführt.
                                    </q-item-label>
                                </q-item-section>
                            </q-item>

<!--                        </template>-->

<!--                        <template v-else> &lt;!&ndash; no parent, no associated claims  &ndash;&gt;-->
<!--                            <q-item tag="label" v-ripple class="q-px-none">-->
<!--                                <q-item-section avatar>-->
<!--                                    <q-radio v-model="deleteFineChoiceIsSoftDelete" :val="false" checked-icon="task_alt"/>-->
<!--                                </q-item-section>-->
<!--                                <q-item-section>-->
<!--                                    <q-item-label>Strafe aus Liste löschen</q-item-label>-->
<!--                                    <q-item-label caption>die Strafe kann danach nicht mehr vergeben werden.</q-item-label>-->
<!--                                </q-item-section>-->
<!--                            </q-item>-->
<!--                        </template>-->
                    </q-list>
                </q-field>

                <q-btn
                    class="full-width q-mt-sm"
                    :loading="props.editDues.isDeletingStarted || props.editDues.isSavingStarted"
                    color="negative"
                    icon="check"
                    label="Bestätigen"
                    type="submit"
                />
            </q-form>
        </template>
    </CustomDialog>
</template>

<script setup lang="ts">
    import TeamLedger from 'src/models/TeamLedger';
    import { Ref, ref, watch } from 'vue';
    import CustomDialog from 'src/components/CustomDialog.vue';
    import MoneyInput from 'src/components/MoneyInput.vue';
    import { useMutation, useQuery } from '@tanstack/vue-query';
    import { saveDuesOnServer } from 'src/queries/mutations/ledgers';
    import TeamLedgerDues from 'src/models/TeamLedgerDues';
    import RecurringEditor from 'src/components/RecurringEditor.vue';
    import DateTimeInputField from 'src/components/DateTimeInputField.vue';
    import { defaultDateMask } from 'src/i18n';
    import { queries } from 'src/queries';
    import { isEmpty } from 'lodash';
    import { useRecurring } from 'src/composables/useRecurring';

    const props = withDefaults(defineProps<{
        ledger?: TeamLedger | null
        isLedgerLoading: boolean
        icon?: string
        label?: string
        class?: string
        dense?: boolean
        editDues?: TeamLedgerDues
    }>(), {
        icon: 'add',
        label: 'Neuen Beitrag erstellen',
        dense: false
    });

    const emit = defineEmits<{
        saved: []
    }>();

    const isMoneyTypeSelected = ref(true)
    // const isCreateFollowUp = ref<boolean>();
    const title = ref('')

    const newDues = ref<TeamLedgerDues>(new TeamLedgerDues()) as Ref<TeamLedgerDues>
    const showDialog = ref(false);
    watch(showDialog, (newValue) => {
        if (newValue && props.ledger) {
            // isCreateFollowUp.value = undefined
            deleteFineChoiceIsSoftDelete.value = undefined
            if(props.editDues){
                title.value = 'Beitrag bearbeiten'
                newDues.value = TeamLedgerDues.createFollowUp(props.editDues, props.ledger)
                newDues.value = props.editDues
                deleteFineChoiceIsSoftDelete.value = !props.editDues.isStarted() ? false: undefined
                // if(props.editDues.amount) {
                //     newDues.value.amount = MoneyClass.fromServer(props.editDues.amount)
                // }

                // if(!props.editDues.isRecurring()){
                //     isCreateFollowUp.value = false
                // }

                // not the best solution but has to be done to get correct tracking of changes
                // set isPersisted and reset before save if follow up is created
                // newDues.value.isPersisted = true

                isMoneyTypeSelected.value = newDues.value.isMoneyType()
            } else {
                title.value = 'Neuen Beitrag erstellen'
                newDues.value = TeamLedgerDues.createForLedger(props.ledger)
            }
        }
    })

    // watch(isCreateFollowUp, (newValue) => {
    //     if(newValue){
    //         newDues.value.setDateBeginToday()
    //     } else {
    //         newDues.value.setDateBeginFromParent()
    //     }
    // })

    const {
        mutateAsync: saveAsync,
    } =
        useMutation({
            mutationFn: saveDuesOnServer,
            onSuccess() {
                showDialog.value = false
                emit('saved')
            },
        })

    function save() {
        if(isMoneyTypeSelected.value) {
            newDues.value.item = null
        } else {
            newDues.value.amount = null
        }

        // if(isCreateFollowUp.value) {
        //     newDues.value.isPersisted = false
        // } else {
        //     newDues.value.removeParent(false)
        // }

        saveAsync(newDues.value);
    }

    const deleteFineDialogVisible = ref(false);
    const deleteFineChoiceIsSoftDelete = ref<boolean>();

    const {
        isLoading: isClaimCountLoading,
        data: claimCountData,
    } = useQuery({
        ... queries.ledgers.duesClaimsCount(props.editDues?.id),
        enabled: () => !isEmpty(props.editDues) && deleteFineDialogVisible.value,
    });

    const claimCount = ref<number>()
    // const lastClaim = ref<TeamLedgerClaim<TeamLedgerDues>>()
    watch(claimCountData, (data) => {
        claimCount.value = data?.meta.page.total;
        // lastClaim.value = data?.data[0];
        if(!props.editDues.parent && claimCount.value === 0) {
            deleteFineChoiceIsSoftDelete.value = false
        }
    })

    const { mutateAsync: deleteDuesOnServer } = useMutation({
        mutationFn: (dues: TeamLedgerDues) => dues.destroy(),
    });

    const { mutateAsync: softDeleteDuesOnServer } = useMutation({
        mutationFn: (dues: TeamLedgerDues) => {
            dues.deletedAt = (new Date()).toISOString()
            return dues.save();
        },
    });

    function deleteDues() {

        if(deleteFineChoiceIsSoftDelete.value) {
            // soft delete
            softDeleteDuesOnServer(<TeamLedgerDues>props.editDues).then(() => {
                deleteFineDialogVisible.value = false
                showDialog.value = false
                emit('saved')
            });
        } else {
            // hard delete
            deleteDuesOnServer(<TeamLedgerDues>props.editDues).then(() => {
                deleteFineDialogVisible.value = false
                showDialog.value = false
                emit('saved')
            });
        }
    }
</script>

<style lang="scss" scoped></style>
