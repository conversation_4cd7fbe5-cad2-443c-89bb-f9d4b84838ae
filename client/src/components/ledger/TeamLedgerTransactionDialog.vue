<template>
    <template v-if="dense">
        <q-btn
            :class="props.class"
            :icon="props.icon"
            :label="props.label"
            no-caps
            dense
            size="sm"
            @click="showDialog = true"
        />
    </template>
    <template v-else>
        <q-card class="partial-width" :class="props.class" flat>
            <q-card-actions vertical>
                <q-btn
                    :icon="props.icon"
                    :label="props.label"
                    no-caps
                    outline
                    @click="showDialog = true"
                />
            </q-card-actions>
        </q-card>
    </template>

    <CustomDialog v-model="showDialog" title="Neue Ein- / Auszahlung erfassen">
        <q-form @submit="save" v-if="newTransaction">
            <div class="q-py-md text-bold">Mitglied oder Verwendungszweck eintragen</div>
            <q-btn-toggle
                v-model="isDeposit"
                :options="[
                    {label: 'Einzahlung', value: true},
                    {label: 'Auszahlung', value: false},
                ]"
                class="full-width q-mb-md"
                no-caps
                spread
                stack
                text-color="primary"
                toggle-color="secondary"
                toggle-text-color="primary"
            />
            <q-select v-if="ledger"
                class="q-mb-md"
                v-model="newTransaction.teamMember"
                :options="ledger.team.membersSortedByName"
                clearable
                outlined
                label="Mitglied (Optional)"
                option-label="name"
                :hint="creditBalanceHint"
            />
            <q-input
                v-model="newTransaction.title"
                :rules="[val => (!!newTransaction?.teamMember || !!val) || 'Verwendungszweck eintragen oder Mitglied auswählen',]"
                label="Verwendungszweck (Optional)"
                lazy-rules="ondemand"
                outlined
            />
            <MoneyInput
                v-model="newTransaction.amount"
                label="Betrag"
                clearable
                autofocus
                :rules="[validateMoneyInput]"
                class="q-mb-lg"
            />

            <!--            TODO edit form with date-->
            <!--            <DateTimeInputField-->
            <!--                v-model="newTransaction.startDateFormatted"-->
            <!--                :mask="defaultDateMask"-->
            <!--                :rules="[val => !!val || 'Bitte gib ein Datum an']"-->
            <!--                class="col-sm-auto col-6"-->
            <!--                label="von"-->
            <!--                type="date"-->
            <!--            />-->
            <q-btn
                class="full-width"
                :loading="newTransaction?.isSaving"
                color="primary"
                icon="check"
                label="speichern"
                type="submit"
            />
        </q-form>
    </CustomDialog>
</template>

<script setup lang="ts">
    import TeamLedger from 'src/models/TeamLedger';
    import { computed, ref, watch } from 'vue';
    import CustomDialog from 'src/components/CustomDialog.vue';
    import TeamLedgerTransaction from 'src/models/TeamLedgerTransaction';
    import MoneyInput from 'src/components/MoneyInput.vue';
    import { saveTransactionOnServer } from 'src/queries/mutations/ledgers';
    import TeamMember from 'src/models/TeamMember';
    import { MoneyClass } from 'src/helpers/money';
    import type { DefaultError } from '@tanstack/query-core';
    import { queries } from 'src/queries';
    import { useMutation, useQuery } from '@tanstack/vue-query';

    const props = withDefaults(defineProps<{
        ledger?: TeamLedger | null
        member?: TeamMember
        isLedgerLoading: boolean
        icon?: string
        label?: string
        class?: string
        dense?: boolean
    }>(), {
        icon: 'attach_money',
        label: 'Ein- / Auszahlung erfassen',
        dense: false
    });


    const emit = defineEmits<{
        transactionSaved: []
    }>();


    const newTransaction = ref<TeamLedgerTransaction>()
    const isDeposit = ref(true)
    const showDialog = ref(false);
    watch(showDialog, (newValue) => {
        if (newValue && props.ledger) {
            newTransaction.value = TeamLedgerTransaction.createForLedger(props.ledger)
            newTransaction.value.teamMember = props.member
            isDeposit.value = true
        }
    })


    const {
        mutateAsync: saveTransaction,
    } = useMutation({
        mutationFn: saveTransactionOnServer,
        onSuccess() {
            showDialog.value = false
            emit('transactionSaved')
        },
    })

    /**
     * Make sure the amount is set correct based on `isDeposit`.
     * Even when entered negative value as deposit or withdrawal
     */
    function getRevisedAmount(amount: number | string): number {
        if (isDeposit.value) {
            return Math.abs(Number(amount))
        } else {
            return Math.abs(Number(amount)) * -1
        }
    }

    function save() {
        if(newTransaction.value) {
            newTransaction.value.amount.amount = getRevisedAmount(newTransaction.value.amount.amount)
            saveTransaction(newTransaction.value)
        }
    }

    const selectedTeamMemberId = computed(() => newTransaction.value?.teamMember?.id);

    const {
        isLoading: isCreditBalanceLoading,
        data: creditBalance,
    } = useQuery<MoneyClass, DefaultError, MoneyClass>({
        ... queries.ledgers.creditBalance(props.ledger?.id, selectedTeamMemberId),
        enabled: () => selectedTeamMemberId.value && showDialog.value,
    });


     const balanceAfterTransaction = computed(() => {
         if(!newTransaction.value?.amount?.amount || !selectedTeamMemberId.value || creditBalance.value === undefined) {
             return 0
         }
         return creditBalance.value.amount + getRevisedAmount(newTransaction.value.amount.amount);
    })

    const creditBalanceHint = computed(() => {
        if(newTransaction.value?.teamMember){
            if(isCreditBalanceLoading.value) {
                return 'Guthaben: wird geladen...'
            } else if (creditBalance.value) {
                return 'Guthaben: ' + creditBalance.value.formatted
            }
        }
        return undefined
    })

    function validateMoneyInput(value) {
        if(!newTransaction.value?.amount?.amount || newTransaction.value?.amount.amount === 0) {
            return 'Bitte gib einen Betrag größer 0 ein.'
        } else if(newTransaction.value?.amount.amount < 0) {
            return 'Bitte gib einen Betrag größer 0 ein. Für Auszahlung oben den Button auswählen.'
        } else if(!isDeposit.value && balanceAfterTransaction.value < 0) {
            return 'Die Auszahlung darf nicht höher als das Guthaben sein.'
        }
        return true
    }
</script>

<style lang="scss" scoped></style>
