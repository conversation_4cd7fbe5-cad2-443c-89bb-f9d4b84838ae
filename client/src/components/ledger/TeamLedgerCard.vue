<template>
    <q-card :class="{isManager: hasPermissionForTeam(team, 'team.ledger.manage')}">
        <q-card-section class="bg-primary q-pa-none">
            <q-item class="q-pr-none">
                <q-item-section class="text-white">
                    <div class="text-h6">{{ team.name }}</div>
                </q-item-section>
                <q-item-section side top v-if="team.ledger"
                    class="no-wrap ">
                    <q-btn class="q-pl-sm q-pt-sm no-wrap text-white"
                           flat
                           dense
                           label="Zur Kasse"
                           no-caps
                           icon-right="navigate_next"
                           :to="{name: $routeNames.LEDGER, params: {teamId: team.id}}"
                    />
                </q-item-section>
            </q-item>
        </q-card-section>

        <q-separator />

        <template v-if="team.ledger === null">
            <q-card-section>
                Für dieses Team ist noch keine Ka<PERSON> vorhanden. Behalte den Überblick über:
                <ul>
                    <li><strong>Strafenkatalog</strong> (Betrag oder Gegenstand)</li>
                    <li><strong>Strafen aller Mitglieder</strong></li>
                    <li><strong>einmalige und regelmäßige Beiträge</strong> (Betrag oder Gegenstand)</li>
                    <li><strong>Geld Ein- und Ausgänge</strong> (Mitglieder oder Nichtmitglieder)</li>
                    <li><strong>offene Forderungen</strong></li>
                    <li><strong>Guthaben</strong> (wird automatisch mit neuen Forderungen verrechnet)</li>
                </ul>
            </q-card-section>

            <q-card-actions align="right">
                <q-btn
                    :href="ledger_info_url"
                    target="_blank"
                    outline
                >weitere Infos
                </q-btn>
                <q-space/>
                <q-btn v-if="hasPermissionForTeam(team, 'team.ledger.manage')"
                    @click="createLedger"
                    :loading="newLedger?.isSavingStarted"
                    color="primary"
                >Kasse anlegen
                </q-btn>
                <template v-else>
                    Nur euer Kassenwart kann die Kasse anlegen.
                </template>
            </q-card-actions>
        </template>
        <template v-else>
            <q-card-section class="no-padding">
                <TeamLedgerBalanceOverview
                    class="partial-width"
                    :ledger="team.ledger!"
                    dense
                    :is-loading="isLoading"
                    :show-due-item-claims="false"
                    :show-ledger-balance="showLedgerBalance"
                    @refresh-ledger="$emit('refreshLedger')"
                />
            </q-card-section>
        </template>

    </q-card>
</template>

<script lang="ts" setup>

    import Team from 'src/models/Team';
    import { useRouter } from 'vue-router';
    import { useMutation } from '@tanstack/vue-query';
    import { saveLedgerOnServer } from 'src/queries/mutations/ledgers';
    import { ROUTE_NAMES } from 'src/router/route-names';
    import TeamLedger from 'src/models/TeamLedger';
    import { ref } from 'vue';
    import TeamLedgerBalanceOverview from 'src/components/ledger/TeamLedgerBalanceOverview.vue';

    const props = defineProps<{
        team: Team;
        isLoading: boolean
        showLedgerBalance:boolean
    }>();

    const emit = defineEmits<{
        refreshLedger: []
    }>();

    const ledger_info_url = process.env.WEBSITE_URL + '/features#teamkasse'

    const router = useRouter();

    const {
        mutateAsync: saveOnServer,
    } = useMutation({mutationFn: saveLedgerOnServer})

    const newLedger = ref<TeamLedger>()
    function createLedger() {
        newLedger.value = new TeamLedger()
        newLedger.value.team = props.team

        saveOnServer(newLedger.value).then(async () => {
            // navigate to ledger
            await router.push({
                name: ROUTE_NAMES.LEDGER,
                params: {teamId: props.team.id}
            });
        });
    }
</script>

<style lang="scss" scoped>
    .isManager {
        border-left: 0.3rem solid;
        border-color: $accent;
    }
</style>
