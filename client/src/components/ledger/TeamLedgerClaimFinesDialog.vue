<template>
    <template v-if="ledger.fines.length == 0">
        <TeamLedgerFineDialog
            :ledger="ledger"
            :dense="dense"
            :icon-only="iconOnly"
            :is-ledger-loading="isLedgerLoading"
            @saved="$emit('saved')"
        />
    </template>
    <template v-else>
        <template v-if="dense">
            <q-btn
                :class="props.class"
                :icon="icon"
                :label="iconOnly ? '' : label"
                no-caps
                dense
                size="sm"
                @click="showDialog = true"
            />
        </template>
        <template v-else>
            <q-card class="partial-width" :class="props.class" flat>
                <q-card-actions vertical>
                    <q-btn
                        :icon="icon"
                        :label="iconOnly ? '' : label"
                        no-caps
                        outline
                        @click="showDialog = true"
                    />
                </q-card-actions>
            </q-card>
        </template>

        <CustomDialog v-model="showDialog" :title="title">
            <q-form @submit="() => newClaim ? saveClaim(newClaim): null"
                    v-if="newClaim"
                    style="max-width: 365px"
            >

                <template v-if="editClaim">
                    <div class="q-pb-sm">
                        Forderung: <b>{{editClaim.getClaimText()}}</b><br>
                        an: <b>{{editClaim.teamMember.name}}</b> <br>
                        für: <b>{{editClaim.title}}</b><br>
                        <br>
                        <span v-if="editClaim.isMoneyClaim()">
                            Wenn die Höhe der Strafe zur neuen Fälligkeit eine andere ist, wird der Betrag hier entsprechend angepasst.<br>
                            <span v-if="editClaim.isStatus('fulfilled')">
                                <br><strong>Die Strafe ist bereits bezahlt</strong>
                                <br>Ist der neue Betrag niedriger, wird die Differenz als Guthaben notiert.
                                <br>
                                <br>Ist der neue Betrag höher, wird vorhandenes Guthaben verrechnet. Ansonsten wird die Strafe als offen markiert und der jetzige Betrag als Guthaben notiert.
                            </span>
                        </span>
                    </div>
                </template>
                <template v-else>
                    <div class="q-py-md text-bold">Mitglied und Strafe eintragen</div>
                    <q-select v-if="ledger"
                        class="q-mb-md"
                        v-model="newClaim.teamMember"
                        :options="ledger.team.membersSortedByName"
                        clearable
                        outlined
                        label="Mitglied"
                        option-label="name"
                        :rules="[val => !!val || 'Bitte ein Mitglied auswählen',]"
                        lazy-rules="ondemand"
                    />
                    <q-select v-if="ledger"
                              ref="fineSelectRef"
                              class="q-mb-md"
                              v-model="newClaim.claimable"
                              :options="ledger.fines"
                              clearable
                              outlined
                              label="Strafe"
                              option-label="title"
                              :rules="[val => !!val || 'Bitte eine Strafe auswählen',]"
                              lazy-rules="ondemand"
                    />
                </template>


                <DateTimeInputField
                    v-model="newClaim.dueDateFormatted"
                    :mask="defaultDateMask"
                    :rules="[val => !!val || 'Bitte gib ein Datum an']"
                    class="col-sm-auto col-6"
                    label="Fälligkeit"
                    type="date"
                />
                <q-btn
                    class="full-width"
                    :loading="newClaim?.isSaving"
                    color="primary"
                    icon="check"
                    label="speichern"
                    type="submit"
                />
                <q-btn
                    v-if="editClaim"
                    class="full-width q-mt-md"
                    :loading="editClaim.isDeletingStarted"
                    color="negative"
                    icon="delete"
                    label="Löschen"
                    @click="confirmBeforeAction({
                           dialogOptions: {
                                title: 'Soll die Strafe wirklich gelöscht werden?',
                                message: editClaim.isStatus('fulfilled') && editClaim.isMoneyClaim() ?
                                    'Die Strafe wurde bereits bezahlt, der Betrag wird als Guthaben notiert' : undefined

                             },
                           onOk: deleteClaim
                         })"
                />
            </q-form>
        </CustomDialog>
    </template>
</template>

<script setup lang="ts">
    import TeamLedger from 'src/models/TeamLedger';
    import { ref, watch } from 'vue';
    import CustomDialog from 'src/components/CustomDialog.vue';
    import { useMutation } from '@tanstack/vue-query';
    import { saveClaimOnServer } from 'src/queries/mutations/ledgers';
    import TeamLedgerClaim from 'src/models/TeamLedgerClaim';
    import TeamLedgerFine from 'src/models/TeamLedgerFine';
    import DateTimeInputField from 'src/components/DateTimeInputField.vue';
    import { defaultDateMask } from 'src/i18n';
    import TeamMember from 'src/models/TeamMember';
    import { QSelect } from 'quasar';
    import { confirmBeforeAction } from 'src/helpers/dialogs';
    import TeamLedgerFineDialog from 'src/components/ledger/TeamLedgerFineDialog.vue';

    const props = withDefaults(defineProps<{
        ledger: TeamLedger
        member?: TeamMember
        isLedgerLoading: boolean
        icon?: string
        label?: string
        class?: string
        dense?: boolean
        iconOnly?: boolean
        editClaim?: TeamLedgerClaim<TeamLedgerFine>
    }>(), {
        icon: 'gavel',
        iconOnly: false,
        label: 'Strafe erfassen',
        dense: false
    });

    const emit = defineEmits<{
        saved: []
    }>();

    const fineSelectRef = ref<QSelect>()
    const title = ref('')

    const newClaim = ref<TeamLedgerClaim<TeamLedgerFine>>()
    const showDialog = ref(false);
    watch(showDialog, (newValue) => {
        if (newValue && props.ledger) {
            if(props.editClaim) {
                title.value = 'Strafe bearbeiten'
                newClaim.value = props.editClaim
            } else {
                title.value = 'Neue Strafe erfassen'
                newClaim.value = TeamLedgerClaim.createForLedger<TeamLedgerFine>(props.ledger)
                newClaim.value.teamMember = props.member
            }
        }
    })

    watch(fineSelectRef, () => {
        fineSelectRef.value?.showPopup()
    })

    const {
        mutateAsync: saveClaim,
    } =
        useMutation({
            mutationFn: saveClaimOnServer,
            onSuccess() {
                showDialog.value = false
                emit('saved')
            },
        })

    const { mutateAsync: deleteClaimOnServer } = useMutation({
        mutationFn: (claim: TeamLedgerClaim) => claim.destroy(),
    });

    function deleteClaim() {
        deleteClaimOnServer(<TeamLedgerClaim>props.editClaim).then(() => {
            emit('saved')
        });
    }
</script>

<style lang="scss" scoped></style>
