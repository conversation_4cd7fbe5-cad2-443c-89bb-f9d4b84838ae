<template>
    <template v-if="dense">
        <q-btn
            :class="props.class"
            :icon="props.icon"
            :label="iconOnly ? '' : label"
            no-caps
            dense
            size="sm"
            @click="showDialog = true"
        />
    </template>
    <template v-else>
        <q-card class="partial-width" flat>
            <q-card-actions vertical>
                <q-btn
                    :icon="props.icon"
                    :label="iconOnly ? '' : label"
                    no-caps
                    outline
                    @click="showDialog = true"
                />
            </q-card-actions>
        </q-card>
    </template>



    <CustomDialog v-model="showDialog" :title="title">
        <q-form @submit="save"
                v-if="newFine"
                style="max-width: 365px"
        >
            <div class="q-py-md text-bold">Eine Strafe kann ein Geldbetrag oder ein Gegenstand sein. Einmal festgelegt, kann das nicht mehr geändert werden</div>
            <q-btn-toggle
                v-if="!editFine"
                v-model="isMoneyTypeSelected"
                :options="[
                    {label: 'Betrag', value: true},
                    {label: 'Gegenstand', value: false},
                ]"
                class="full-width q-mb-md"
                no-caps
                spread
                stack
                text-color="primary"
                toggle-color="secondary"
                toggle-text-color="primary"
            />

            <q-input
                v-model="newFine.title"
                :rules="[val => !!val || 'Bitte gib einen Titel ein',]"
                label="Titel"
                lazy-rules="ondemand"
                outlined
            />

            <MoneyInput
                v-if="isMoneyTypeSelected"
                v-model="newFine.amount"
                label="Betrag"
                clearable
                no-zero-allowed
            />
            <q-input
                v-else
                v-model="newFine.item"
                :rules="[val => !!val || 'Bitte gib einen Gegenstand ein',]"
                label="Gegenstand"
                lazy-rules="ondemand"
                outlined
            />

            <template v-if="editFine">
                <q-field
                    v-model="isCreateFollowUp"
                    :rules="[val => val !== undefined || 'Bitte wähle eine Option aus']"
                    borderless
                    tag="div"
                >
                    <q-list class="q-pr-sm">
                        <q-item class="q-px-none">
                            <q-item-section>
                                <q-item-label class="text-bold">Ab wann gilt die Änderung?</q-item-label>
                                <q-item-label caption>
                                    Strafe besteht seit: {{editFine!.dateBeginFormatted}}
                                </q-item-label>
                            </q-item-section>
                            <q-item-section side class="q-mr-xs">
                                <TeamLedgerChangeClaimInfoIcon :claimableType="TeamLedgerFine.jsonapiType" />
                            </q-item-section>
                        </q-item>

                        <q-item tag="label" v-ripple class="q-px-none">
                            <q-item-section avatar>
                                <q-radio v-model="isCreateFollowUp" :val="false" checked-icon="task_alt" />
                            </q-item-section>
                            <q-item-section>
                                <q-item-label>Von Beginn an</q-item-label>
                                <q-item-label caption>Strafe wird geändert und alle bestehenden Forderungen angepasst.</q-item-label>
                            </q-item-section>
                        </q-item>

                        <q-item tag="label" v-ripple class="q-px-none">
                            <q-item-section avatar>
                                <q-radio v-model="isCreateFollowUp" :val="true" checked-icon="task_alt"/>
                            </q-item-section>
                            <q-item-section>
                                <q-item-label>Datum auswählen</q-item-label>
                                <q-item-label caption>ältere Strafen vor dem gewählten Datum bleiben unverändert</q-item-label>
                            </q-item-section>
                        </q-item>
                    </q-list>
                </q-field>

                <DateTimeInputField
                    v-if="isCreateFollowUp"
                    v-model="newFine.dateBeginFormatted"
                    :mask="defaultDateMask"
                    :rules="[val => !!val || 'Bitte ein Datum auswählen']"
                    label="Gültig ab"
                    :options="(date: string) => editFine!.isAfterDateBegin(date)"
                    type="date"
                />
            </template>

            <q-btn
                class="full-width"
                :loading="newFine?.isSaving"
                color="primary"
                icon="check"
                label="speichern"
                type="submit"
            />

            <q-btn
                v-if="props.editFine"
                class="full-width q-mt-md"
                :disable="newFine.isSaving"
                :loading="props.editFine.isDeletingStarted"
                color="negative"
                icon="delete"
                label="Löschen"
                @click="deleteFineDialogVisible = true"
            />

        </q-form>
    </CustomDialog>

    <CustomDialog
        v-model="deleteFineDialogVisible"
        title="Strafe löschen"
    >
        <span v-if="isClaimCountLoading">lade Infos zur Strafe...<q-spinner /></span>
        <template v-else>

            <q-form @submit="deleteFine"
                    v-if="editFine"
                    style="max-width: 365px"
            >
                Titel: {{editFine.title}}<br>
                <template v-if="editFine.isMoneyType()">
                    Betrag: {{editFine.amount.formatted}}<br>
                </template>
                <template v-else>
                    Gegenstand: {{editFine.item}}<br>
                </template>
                Gültig seit: {{editFine.dateBeginFormatted}}<br>
                vergeben: <strong>{{ claimCount }}x</strong><br>
                <template v-if="lastClaim">
                    zuletzt am {{lastClaim.dueDateFormatted}}
                </template>

                <q-field
                    v-model="deleteFineChoiceIsSoftDelete"
                    :rules="[val => val !== undefined || 'Bitte wähle eine Option aus']"
                    borderless
                    tag="div"
                >
                    <q-list class="q-pr-sm">

                        <template v-if="editFine?.parent">
                            <q-item class="q-px-none">
                                <q-item-section>
                                    <q-item-label class="text-bold">Vorherig Version vorhanden:</q-item-label>
                                    <q-item-label caption>
                                        Titel: {{editFine.parent.title}}<br>
                                        <template v-if="editFine.parent.isMoneyType()">
                                            Betrag: {{editFine.parent.amount.formatted}}<br>
                                        </template>
                                        <template v-else>
                                            Gegenstand: {{editFine.parent.item}}<br>
                                        </template>
                                        Gültig seit: {{editFine.parent.dateBeginFormatted}}

                                    </q-item-label>
                                </q-item-section>
                            </q-item>

                            <q-item tag="label" v-ripple class="q-px-none">
                                <q-item-section avatar>
                                    <q-radio v-model="deleteFineChoiceIsSoftDelete" :val="false" checked-icon="task_alt"/>
                                </q-item-section>
                                <q-item-section>
                                    <q-item-label>Vorherige Version wiederherstellen</q-item-label>
                                    <q-item-label caption v-if="claimCount > 0">aktuelle Strafe wird gelöscht und alle Forderungen auf die vorherige Version angepasst.</q-item-label>
                                </q-item-section>
                                <q-item-section side class="q-mr-xs">
                                    <TeamLedgerChangeClaimInfoIcon :claimableType="TeamLedgerFine.jsonapiType" />
                                </q-item-section>
                            </q-item>

                            <q-item tag="label" v-ripple class="q-px-none">
                                <q-item-section avatar>
                                    <q-radio v-model="deleteFineChoiceIsSoftDelete" :val="true" checked-icon="task_alt"/>
                                </q-item-section>
                                <q-item-section>
                                    <q-item-label>Strafe aus Katalog löschen</q-item-label>
                                    <q-item-label caption v-if="claimCount > 0">Forderungen bleiben unverändert, die Strafe kann aber nicht mehr vergeben werden.</q-item-label>
                                </q-item-section>
                            </q-item>
                        </template>

                        <template v-else-if="claimCount > 0"> <!-- no parent -->
                            <q-item tag="label" v-ripple class="q-px-none">
                                <q-item-section avatar>
                                    <q-radio v-model="deleteFineChoiceIsSoftDelete" :val="false" checked-icon="task_alt"/>
                                </q-item-section>
                                <q-item-section>
                                    <q-item-label>Vollständig mit Forderungen löschen</q-item-label>
                                    <q-item-label caption>
                                        Alle bereits vergebenen Forderungen mit dieser Strafe werden gelöscht.
                                        <template v-if="editFine?.isMoneyType()">Bereits bezahlte Forderungen werden als Guthaben notiert.</template>
                                    </q-item-label>
                                </q-item-section>
                            </q-item>

                            <q-item tag="label" v-ripple class="q-px-none">
                                <q-item-section avatar>
                                    <q-radio v-model="deleteFineChoiceIsSoftDelete" :val="true" checked-icon="task_alt"/>
                                </q-item-section>
                                <q-item-section>
                                    <q-item-label>Strafe aus Liste löschen</q-item-label>
                                    <q-item-label caption>Forderungen bleiben unverändert, die Strafe kann aber nicht mehr vergeben werden.</q-item-label>
                                </q-item-section>
                            </q-item>

                        </template>

                        <template v-else> <!-- no parent, no associated claims  -->
                            <q-item tag="label" v-ripple class="q-px-none">
                                <q-item-section avatar>
                                    <q-radio v-model="deleteFineChoiceIsSoftDelete" :val="false" checked-icon="task_alt"/>
                                </q-item-section>
                                <q-item-section>
                                    <q-item-label>Strafe aus Liste löschen</q-item-label>
                                    <q-item-label caption>die Strafe kann danach nicht mehr vergeben werden.</q-item-label>
                                </q-item-section>
                            </q-item>
                        </template>
                    </q-list>
                </q-field>

                <q-btn
                    class="full-width q-mt-sm"
                    :loading="props.editFine.isDeletingStarted || props.editFine.isSavingStarted"
                    color="negative"
                    icon="check"
                    label="Bestätigen"
                    type="submit"
                />
            </q-form>
        </template>
    </CustomDialog>
</template>

<script setup lang="ts">
    import TeamLedger from 'src/models/TeamLedger';
    import { Ref, ref, watch } from 'vue';
    import CustomDialog from 'src/components/CustomDialog.vue';
    import MoneyInput from 'src/components/MoneyInput.vue';
    import { useMutation, useQuery } from '@tanstack/vue-query';
    import { saveFineOnServer } from 'src/queries/mutations/ledgers';
    import TeamLedgerFine from 'src/models/TeamLedgerFine';
    import DateTimeInputField from 'src/components/DateTimeInputField.vue';
    import { defaultDateMask } from 'src/i18n';
    import TeamLedgerClaim from 'src/models/TeamLedgerClaim';
    import { queries } from 'src/queries';
    import { isEmpty } from 'lodash';
    import TeamLedgerChangeClaimInfoIcon from 'src/components/ledger/TeamLedgerChangeClaimInfoIcon.vue';

    const props = withDefaults(defineProps<{
        ledger?: TeamLedger | null
        isLedgerLoading: boolean
        icon?: string
        label?: string
        class?: string
        dense?: boolean
        iconOnly?: boolean
        editFine?: TeamLedgerFine
    }>(), {
        icon: 'add',
        iconOnly: false,
        label: 'Neue Strafe erstellen',
        dense: false
    });

    const emit = defineEmits<{
        saved: []
    }>();

    const isMoneyTypeSelected = ref(true)
    const isCreateFollowUp = ref<boolean>();
    const title = ref('')

    const newFine = ref<TeamLedgerFine>(new TeamLedgerFine()) as Ref<TeamLedgerFine>
    const showDialog = ref(false);
    watch(showDialog, (newValue) => {
        if (newValue && props.ledger) {
            isCreateFollowUp.value = undefined
            deleteFineChoiceIsSoftDelete.value = undefined
            if(props.editFine) {
                title.value = 'Strafe bearbeiten'
                newFine.value = TeamLedgerFine.createFollowUp(props.editFine, props.ledger)

                // not the best solution but has to be done to get correct tracking of changes
                // set isPersisted and reset before save if follow up is created
                newFine.value.isPersisted = true

                isMoneyTypeSelected.value = newFine.value.isMoneyType()
            } else {
                title.value = 'Neue Strafe erstellen'
                newFine.value = TeamLedgerFine.createForLedger(props.ledger)
            }
        }
    })

    const {
        mutateAsync: saveAsync,
    } =
        useMutation({
            mutationFn: saveFineOnServer,
            onSuccess() {
                showDialog.value = false
                emit('saved')
            },
        })

    function save() {

        if(isMoneyTypeSelected.value) {
            newFine.value.item = null
        } else {
            newFine.value.amount = null
        }

        if(isCreateFollowUp.value) {
            newFine.value.isPersisted = false
        } else {
            newFine.value.removeParent()
        }

        saveAsync(newFine.value);
    }


    const deleteFineDialogVisible = ref(false);
    const deleteFineChoiceIsSoftDelete = ref<boolean>();

    const {
        isLoading: isClaimCountLoading,
        data: claimCountWithLastEntry,
    } = useQuery({
        ... queries.ledgers.fineClaimsCountWithLastEntry(props.editFine?.id),
        enabled: () => !isEmpty(props.editFine) && deleteFineDialogVisible.value,
    });

    const claimCount = ref<number>()
    const lastClaim = ref<TeamLedgerClaim<TeamLedgerFine>>()
    watch(claimCountWithLastEntry, (data) => {
        claimCount.value = data?.meta.page.total;
        lastClaim.value = data?.data[0];
        if(!props.editFine.parent && claimCount.value === 0) {
            deleteFineChoiceIsSoftDelete.value = false
        }
    })

    const { mutateAsync: deleteFineOnServer } = useMutation({
        mutationFn: (fine: TeamLedgerFine) => fine.destroy(),
    });

    const { mutateAsync: softDeleteFineOnServer } = useMutation({
        mutationFn: (fine: TeamLedgerFine) => {
            fine.deletedAt = (new Date()).toISOString()
            return fine.save();
        },
    });

    function deleteFine() {

        if(deleteFineChoiceIsSoftDelete.value) {
            // soft delete
            softDeleteFineOnServer(<TeamLedgerFine>props.editFine).then(() => {
                deleteFineDialogVisible.value = false
                showDialog.value = false
                emit('saved')
            });
        } else {
            // hard delete
            deleteFineOnServer(<TeamLedgerFine>props.editFine).then(() => {
                deleteFineDialogVisible.value = false
                showDialog.value = false
                emit('saved')
            });
        }
    }
</script>

<style lang="scss" scoped></style>
