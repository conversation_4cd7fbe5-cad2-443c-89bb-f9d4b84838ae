<template>
    <template v-if="hasPermissionForTeam(ledger.team, 'team.ledger.manage')">
        <TeamLedgerTransactionDialog
            :ledger="ledger"
            :is-ledger-loading="isLedgerLoading"
            @transaction-saved="$emit('refreshLedger')"
        />
        <TeamLedgerClaimFinesDialog
            :ledger="ledger"
            :is-ledger-loading="isLedgerLoading"
            @saved="$emit('refreshLedger')"
        />
    </template>

    <q-table
        :rows="rowsPerMember"
        :columns="columns"
        :pagination="{rowsPerPage:0, sortBy: 'member', descending: false}"
        hide-pagination
        row-key="id"
        v-model:selected="selected"
        binary-state-sort
        column-sort-order="da"
        :table-class="['sticky-header', {
            'sticky-first-column': !hasPermissionForTeam(ledger.team, 'team.ledger.manage'),
            'sticky-second-column': hasPermissionForTeam(ledger.team, 'team.ledger.manage')
            },]"
    >

        <template v-slot:body="props">
            <q-tr :props="props">
                <q-td
                    v-for="col in props.cols"
                    :key="col.name"
                    :props="props"
                    :auto-width="['action', 'member'].includes(col.name)"
                >
                    <template v-if="col.name === 'action' && hasPermissionForTeam(ledger.team, 'team.ledger.manage')">
                        <TeamLedgerTransactionDialog
                            v-if="hasPermissionForTeam(ledger.team, 'team.ledger.manage')"
                            class="q-mr-sm"
                            :ledger="ledger"
                            :is-ledger-loading="isLedgerLoading"
                            label=""
                            dense
                            :member="props.row.member"
                            @transaction-saved="$emit('refreshLedger')"
                        />

                        <TeamLedgerClaimFinesDialog
                            v-if="hasPermissionForTeam(ledger.team, 'team.ledger.manage')"
                            class="q-mr-xs"
                            :ledger="ledger"
                            :is-ledger-loading="isLedgerLoading"
                            icon-only
                            dense
                            :member="props.row.member"
                            @saved="$emit('refreshLedger')"
                        />
                    </template>

                    <template v-else-if="col.name === 'member'">
                        {{ col.value }}
                        <br><StatusRoleBadge :member="props.row.member" />
                    </template>

                    <template v-else-if="col.name === 'claimBalance'">
                        <q-item-section>
                            <q-item-label v-if="col.value?.amount > 0">
                                {{ col.value.formatted }}
                            </q-item-label>
                            <q-item-label caption v-if="props.row.status?.dueItemClaimIds.length > 0">
                                {{props.row.status?.dueItemClaimIds.length}}
                                <q-btn class="q-ml-xs"
                                       text-color="primary"
                                       size="sm"
                                       dense
                                       :icon="props.expand ? 'mdi-package-variant' : 'mdi-package-variant-closed'"
                                       @click="props.expand = !props.expand"
                                />
                            </q-item-label>
                            <template v-if="col.value?.amount == 0 && props.row.status?.dueItemClaimIds.length == 0">
                                -
                            </template>
                        </q-item-section>
                    </template>

                    <template v-else-if="['futureClaimBalance', 'totalClaimBalance', 'availableBalance', 'creditBalance'].includes(col.name)">
                        <template v-if="col.value?.amount > 0">
                            {{ col.value.formatted }}
                        </template>
                        <template v-else>
                            -
                        </template>
                    </template>

                    <template v-else>
                        {{ col.value }}
                    </template>
                </q-td>
            </q-tr>
            <q-tr v-show="props.expand" :props="props" v-if="props.row.status">
                <q-td colspan="100%">
                    <div class="text-left">
                        <q-list separator dense bordered class="q-mx-md q-mb-md">
                            <q-item v-for="itemClaim in props.row.dueItemClaims as TeamLedgerClaim<TeamLedgerDues>[]"
                                :key="itemClaim.id"
                            >
                                <q-item-section side>
                                    <span v-if="itemClaim.hasRelatedFine()">Strafe:</span>
                                    <span v-else>Beitrag:</span>
                                </q-item-section>
                                <q-item-section>
                                    {{itemClaim.item}}
                                </q-item-section>
                                <q-item-section side>
                                    ({{itemClaim.title}} vom {{itemClaim.dueDateFormatted}})
                                </q-item-section>
                                <q-item-section side v-if="hasPermissionForTeam(ledger.team, 'team.ledger.manage')">
                                    <TeamLedgerClaimFulfillDialog
                                        :claim="itemClaim"
                                        :ledger="ledger"
                                        :is-ledger-loading="isLedgerLoading"
                                        @saved="$emit('refreshLedger')"
                                    />
                                </q-item-section>
                            </q-item>
                        </q-list>
                    </div>
                </q-td>
            </q-tr>
        </template>

        <template v-slot:bottom-row>
            <q-tr  class="dense-header-row bg-grey">
                <q-td v-if="hasPermissionForTeam(ledger.team, 'team.ledger.manage')"></q-td>
                <q-td class="bg-grey">
                    Summen Mitglieder
                </q-td>
                <q-td>{{props.ledger.claimBalance.formatted}}</q-td>
                <q-td>{{props.ledger.futureClaimBalance.formatted}}</q-td>
                <q-td>{{props.ledger.totalClaimBalance.formatted}}</q-td>
                <q-td>{{props.ledger.availableBalanceFromMembers.formatted}}</q-td>
                <q-td>{{props.ledger.creditBalance.formatted}}</q-td>
            </q-tr>
            <q-tr  class="dense-header-row">
                <q-td v-if="hasPermissionForTeam(ledger.team, 'team.ledger.manage')"></q-td>
                <q-td class="noMemberText">
                    Einnahmen {{TeamLedger.noMemberText}}
                </q-td>
                <q-td></q-td>
                <q-td></q-td>
                <q-td></q-td>
                <q-td>{{props.ledger.nonMemberSumIn.formatted}}</q-td>
                <q-td></q-td>
            </q-tr>
            <q-tr  class="dense-header-row">
                <q-td v-if="hasPermissionForTeam(ledger.team, 'team.ledger.manage')"></q-td>
                <q-td>
                    Ausgaben
                </q-td>
                <q-td></q-td>
                <q-td></q-td>
                <q-td></q-td>
                <q-td style="border-bottom: 1px solid">{{props.ledger.nonMemberSumOut.formatted}}</q-td>
                <q-td></q-td>
            </q-tr>
            <q-tr class="dense-header-row">
                <q-td v-if="hasPermissionForTeam(ledger.team, 'team.ledger.manage')"></q-td>
                <q-td>
                    Verfügbar
                </q-td>
                <q-td></q-td>
                <q-td></q-td>
                <q-td></q-td>
                <q-td>{{props.ledger.availableBalance.formatted}}</q-td>
                <q-td></q-td>
            </q-tr>
        </template>
    </q-table>

    <q-inner-loading :showing="isFetching" />
</template>

<script setup lang="ts">
    import TeamLedger, { TeamLedgerMemberStatus } from 'src/models/TeamLedger';
    import TeamLedgerTransactionDialog from 'src/components/ledger/TeamLedgerTransactionDialog.vue';
    import { useQuery } from '@tanstack/vue-query';
    import { queries } from 'src/queries';
    import { isEmpty } from 'lodash';
    import { computed, watch } from 'vue';
    import { QTable, QTableColumn } from 'quasar';
    import TeamMember from 'src/models/TeamMember';
    import TeamLedgerClaimFinesDialog from 'src/components/ledger/TeamLedgerClaimFinesDialog.vue';
    import { useAuthStore } from 'src/stores/auth';
    import TeamLedgerDues from 'src/models/TeamLedgerDues';
    import TeamLedgerClaim from 'src/models/TeamLedgerClaim';
    import Team from 'src/models/Team';
    import StatusRoleBadge from 'src/components/teams/StatusRoleBadge.vue';
    import TeamLedgerClaimFulfillDialog from 'src/components/ledger/TeamLedgerClaimFulfillDialog.vue';

    const props = defineProps<{
        ledger: TeamLedger
        isLedgerLoading: boolean
    }>();

    const emit = defineEmits<{
        refreshLedger: []
    }>();

    watch(() => props.isLedgerLoading, (ledgerLoaded) => {
        if(ledgerLoaded) {
            refetch()
        }
    } )

    type MemberStatusRow = {
        id: string,
        name: string,
        member: TeamMember
        selected: boolean,
        status?: TeamLedgerMemberStatus
    }

    const columns = [
        ...(useAuthStore().hasPermissionForTeam(props.ledger.team, 'team.ledger.manage') ?
                [{name: 'action', label: '', field: '', align: 'left'}]: []
        ),
        { name: 'member', label: 'Mitglied', field: 'name', sortable: true, align: 'left'},
        { name: 'claimBalance', label: 'Fällig', field: row => row.status?.claimBalance, sortable: true, align: 'left' },
        { name: 'futureClaimBalance', label: 'Zukünftig', field: row => row.status?.futureClaimBalance, sortable: true, align: 'left' },
        { name: 'totalClaimBalance', label: 'Offen', field: row => row.status?.totalClaimBalance, sortable: true, align: 'left' },
        { name: 'availableBalance', label: 'Bezahlt', field: row => row.status?.availableBalance, sortable: true, align: 'left' },
        { name: 'creditBalance', label: 'Guthaben', field: row => row.status?.creditBalance, sortable: true, align: 'left' },
    ] as QTableColumn<MemberStatusRow>[]



    const authStore = useAuthStore();
    const rowsPerMember = computed<MemberStatusRow[]>(() => {
            return [
                    ...props.ledger.team.membersSortedByName.map(member =>
                        buildRowForMember(member, memberStatusData.value?.getActiveMemberStatus(member.id))
                    ),
                    ...Team.orderMembersByName(props.ledger!.formerMembers).map(member =>
                        buildRowForMember(member, memberStatusData.value?.getFormerMemberStatus(member.id))
                    ),
            ]
        }
    )
    function buildRowForMember(member: TeamMember, status: TeamLedgerMemberStatus): MemberStatusRow {
        return {
            id: member.id,
            name: member.name,
            member: member,
            selected: authStore.isOwnMember(member),
            status: status,
            dueItemClaims: props.ledger.dueItemClaims.filter(itemClaim => status?.dueItemClaimIds.includes(itemClaim.id)),
        } as MemberStatusRow;
    }

    const selected = computed(() => rowsPerMember.value?.filter((row) => row.selected));


    const {
        isFetching,
        data: memberStatusData,
        refetch,
    } = useQuery({
        ... queries.ledgers.memberStatus(props.ledger.id as string),
        enabled: !isEmpty(props.ledger),
        refetchOnWindowFocus: false,
    });


</script>

<style lang="scss" scoped></style>
