<template>
    <TeamLedgerTransactionDialog
        v-if="hasPermissionForTeam(ledger.team, 'team.ledger.manage')"
        :ledger="ledger"
        :is-ledger-loading="isLedgerLoading"
        @transaction-saved="$emit('refreshLedger')"
    />

    <q-table
             :rows="rowData"
             :columns="columns"
             :rowsPerPageOptions="[10, 25, 50, 100]"
             row-key="id"
             v-model:pagination="pagination"
             binary-state-sort
             @request="onRequest"
    >
        <template v-slot:body="props">
            <q-tr :props="props">
                <q-td
                    v-for="col in props.cols"
                    :key="col.name"
                    :props="props"
                    style="border-bottom: none; padding-bottom: 0"
                    class="full-height"
                >
                    <template v-if="col.name === 'member'">
                        <template v-if="col.value">
                            {{ col.value.name }}
                        </template>
                        <template v-else>
                            <span class="noMemberText">{{TeamLedger.noMemberText}}</span>
                        </template>
                    </template>
                    <template v-else>
                        {{ col.value }}
                    </template>
                </q-td>
            </q-tr>
            <q-tr :props="props">
                <q-td colspan="100%" class="full-height" style="padding-top: 0">
                    <q-item-label caption>{{ props.row.title }}</q-item-label>
                </q-td>
            </q-tr>
        </template>
    </q-table>

    <q-inner-loading :showing="isFetching" />
</template>

<script setup lang="ts">
    import TeamLedger from 'src/models/TeamLedger';
    import TeamLedgerTransactionDialog from 'src/components/ledger/TeamLedgerTransactionDialog.vue';
    import { useQuery } from '@tanstack/vue-query';
    import { queries } from 'src/queries';
    import { isEmpty } from 'lodash';
    import { ref, watch } from 'vue';
    import { QTable, QTableColumn } from 'quasar';
    import TeamLedgerTransaction from 'src/models/TeamLedgerTransaction';
    import { PaginationProps, useServerTable } from 'src/composables/useServerTable';

    const props = defineProps<{
        ledger: TeamLedger
        isLedgerLoading: boolean
    }>();

    const emit = defineEmits<{
        refreshLedger: []
    }>();

    watch(() => props.isLedgerLoading, (ledgerLoaded) => {
        if(ledgerLoaded) {
            queryResult.refetch()
        }
    } )

    const columns = [
        {name: 'createdAt', label: 'Datum', field: row => row.getCreatedAtFormatted(), sortable: true, align: 'left'},
        {name: 'member', label: 'Mitglied', field: 'teamMember', align: 'left'},
        {name: 'amount', label: 'Betrag', field: row => row.amount.formatted, sortable: true },

    ] as QTableColumn<TeamLedgerTransaction>[]

    const pagination = ref(new PaginationProps(
        {
            page: 1,
            rowsPerPage: 10,
            sortBy: 'createdAt',
            descending: true,
        }
    ));

    const queryResult = useQuery({
        ... queries.ledgers.transactions(props.ledger.id as string, pagination),
        enabled: !isEmpty(props.ledger),
        refetchOnWindowFocus: false,
    });

    const {
        isFetching,
    } = queryResult;


    const {
        onRequest,
        rowData
    } = useServerTable<TeamLedgerTransaction>(queryResult, pagination)

</script>

<style lang="scss" scoped></style>
