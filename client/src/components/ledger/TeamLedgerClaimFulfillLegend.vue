<template>
    <q-card>
        <template v-for="statusInfo in TeamLedgerClaimStatusOptions" :key="statusInfo.value">
                <q-btn
                    class="q-mr-md q-pl-none"
                    :icon="statusInfo.icon"
                    :color="statusInfo.iconColor"
                    :label="statusInfo.label"
                    dense
                    no-caps
                    flat
                    :ripple="false"
                />
        </template>
    </q-card>
</template>

<script setup lang="ts">
    import { TeamLedgerClaimStatusOptions } from 'src/models/TeamLedgerClaim';
</script>

<style lang="scss" scoped></style>
