<template>
    <TeamLedgerClaimFinesDialog
        v-if="hasPermissionForTeam(ledger.team, 'team.ledger.manage')"
        :ledger="ledger"
        :is-ledger-loading="isLedgerLoading"
        @saved="$emit('refreshLedger')"
    />

    <q-table
             :rows="rowData"
             :columns="columns"
             :rowsPerPageOptions="[10, 25, 50, 100]"
             row-key="id"
             v-model:pagination="pagination"
             binary-state-sort
             @request="onRequest"
    >
        <template v-slot:body="props">
            <q-tr :props="props">
                <q-td
                    v-for="col in props.cols"
                    :key="col.name"
                    :props="props"
                    style="border-bottom: none; padding-bottom: 0"
                    class="full-height"
                    :auto-width="['edit', 'status'].includes(col.name)"
                >
                    <template v-if="col.name === 'edit'">
                        <TeamLedgerClaimFinesDialog
                            v-if="hasPermissionForTeam(ledger.team, 'team.ledger.manage')"
                            class="q-mr-sm"
                            :ledger="ledger"
                            :is-ledger-loading="isLedgerLoading"
                            :edit-claim="props.row"
                            label=""
                            icon="edit"
                            dense
                            @saved="$emit('refreshLedger')"
                        />
                    </template>
                    <template v-else-if="col.name === 'status'">
                        <TeamLedgerClaimFulfillDialog
                            :claim="props.row"
                            :ledger="ledger"
                            :is-ledger-loading="isLedgerLoading"
                            @saved="$emit('refreshLedger')"
                        />
                    </template>
                    <template v-else>
                        {{ col.value }}
                    </template>
                </q-td>
            </q-tr>
            <q-tr :props="props">
                <q-td auto-width class="full-height" v-if="hasPermissionForTeam(ledger.team, 'team.ledger.manage')"></q-td>
                <q-td colspan="100%" class="full-height" style="padding-top: 0">
                    <q-item-section class="float-left">
                        <q-item-label caption>{{ props.row.title }}</q-item-label>
                    </q-item-section>
                    <q-item-section side>
                        <q-item-label caption>{{ props.row.getClaimText() }}</q-item-label>
                    </q-item-section>
                </q-td>
            </q-tr>
        </template>

    </q-table>
    <TeamLedgerClaimFulfillLegend />

    <q-inner-loading :showing="isFetching" />
</template>

<script setup lang="ts">
    import TeamLedger from 'src/models/TeamLedger';
    import { useQuery } from '@tanstack/vue-query';
    import { queries, withRefCopy } from 'src/queries';
    import { isEmpty } from 'lodash';
    import { ref, watch } from 'vue';
    import { QTable, QTableColumn } from 'quasar';
    import TeamMember from 'src/models/TeamMember';
    import { PaginationProps, useServerTable } from 'src/composables/useServerTable';
    import TeamLedgerClaimFinesDialog from 'src/components/ledger/TeamLedgerClaimFinesDialog.vue';
    import TeamLedgerClaim from 'src/models/TeamLedgerClaim';
    import TeamLedgerFine from 'src/models/TeamLedgerFine';
    import TeamLedgerClaimFulfillDialog from 'src/components/ledger/TeamLedgerClaimFulfillDialog.vue';
    import TeamLedgerClaimFulfillLegend from 'src/components/ledger/TeamLedgerClaimFulfillLegend.vue';
    import { useAuthStore } from 'src/stores/auth';

    const props = defineProps<{
        ledger: TeamLedger
        isLedgerLoading: boolean
    }>();

    const emit = defineEmits<{
        refreshLedger: []
    }>();

    watch(() => props.isLedgerLoading, (ledgerLoaded) => {
        if(ledgerLoaded) {
            queryResult.refetch()
        }
    } )

    const columns = [
        ...(useAuthStore().hasPermissionForTeam(props.ledger.team, 'team.ledger.manage') ?
            [{name: 'edit', label: '', field: '', align: 'left'}]: []
        ),
        {name: 'dueDate', label: 'Datum', field: row => row.dueDateFormatted, sortable: true, align: 'left'},
        {name: 'member', label: 'Mitglied', field: 'teamMember', align: 'left', format: (member: TeamMember) => member.name },
        {name: 'status', label: 'Erfüllt', field: 'status' },

    ] as QTableColumn<TeamLedgerClaim<TeamLedgerFine>>[]

    const pagination = ref(new PaginationProps(
        {
            page: 1,
            rowsPerPage: 10,
            sortBy: 'dueDate',
            descending: true,
        }
    ));

    const editableData = ref([])
    const queryResult = useQuery({
        ... withRefCopy(
            queries.ledgers.fineClaims(props.ledger.id as string, pagination),
            editableData,
            false
        ),
        enabled: !isEmpty(props.ledger),
        refetchOnWindowFocus: false,
    });

    const {
        isFetching,
        refetch,
    } = queryResult;

    const {
        onRequest,
        rowData
    } = useServerTable<TeamLedgerClaim<TeamLedgerFine>>(
        queryResult,
        pagination,
        editableData,
    )

</script>

<style lang="scss" scoped></style>
