<template>
    <TeamLedgerDuesDialog
        v-if="hasPermissionForTeam(ledger.team, 'team.ledger.manage')"
        :ledger="ledger"
        :is-ledger-loading="isLedgerLoading"
        @saved="$emit('refreshLedger')"
    />

    <q-table
             :rows="rowsPerMember"
             :columns="columns"
             row-key="id"
             :pagination="{rowsPerPage:0, sortBy: 'member', descending: false}"
             hide-pagination
             v-model:selected="selected"
             binary-state-sort
             table-class="sticky-header sticky-first-column"
    >

        <template v-slot:header-cell="props">
            <q-th :props="props" class="vertical-bottom ellipsis" style="max-width: 80px">

                <template v-if="props.col.dues">
                    {{props.col.label}}<br>
                    {{props.col.sublabel}}
                    <q-tooltip>
                        {{props.col.claim.dueDateFormatted}}<br>
                        {{props.col.sublabel}}<br>
                        {{props.col.dues.title}}<br>
                        {{props.col.recurringText}}
                    </q-tooltip>
                </template>

                <template v-else>
                    {{ props.col.label }}
                </template>

            </q-th>
        </template>

        <template v-slot:body-cell-member="props">
            <q-td :props="props" auto-width>
                {{ props.value }}
            </q-td>
        </template>

        <template v-slot:body-cell="props">
            <q-td :props="props">
                <template v-if="props.value">
                    <TeamLedgerClaimFulfillDialog
                        :claim="props.value"
                        :ledger="ledger"
                        :is-ledger-loading="isLedgerLoading"
                        @saved="$emit('refreshLedger')"
                    />
                </template>
                <template v-else>
                    <InfoIcon icon-name="horizontal_rule">
                        nicht im Team
                    </InfoIcon>
                </template>
            </q-td>
        </template>

    </q-table>
    <TeamLedgerClaimFulfillLegend />

    <q-inner-loading :showing="isFetching" />
</template>

<script setup lang="ts">
    import TeamLedger from 'src/models/TeamLedger';
    import { useQuery } from '@tanstack/vue-query';
    import { queries } from 'src/queries';
    import { isEmpty } from 'lodash';
    import { computed, ComputedRef, watch } from 'vue';
    import { QTable, QTableColumn } from 'quasar';
    import TeamMember from 'src/models/TeamMember';
    import { useServerTable } from 'src/composables/useServerTable';
    import TeamLedgerClaim from 'src/models/TeamLedgerClaim';
    import TeamLedgerDuesDialog from 'src/components/ledger/TeamLedgerDuesDialog.vue';
    import TeamLedgerDues from 'src/models/TeamLedgerDues';
    import { useAuthStore } from 'src/stores/auth';
    import { useRecurring } from 'src/composables/useRecurring';
    import TeamLedgerClaimFulfillDialog from 'src/components/ledger/TeamLedgerClaimFulfillDialog.vue';
    import TeamLedgerClaimFulfillLegend from 'src/components/ledger/TeamLedgerClaimFulfillLegend.vue';
    import InfoIcon from 'src/components/InfoIcon.vue';
    import Team from 'src/models/Team';

    const props = defineProps<{
        ledger: TeamLedger
        isLedgerLoading: boolean
    }>();

    const emit = defineEmits<{
        refreshLedger: []
    }>();

    watch(() => props.isLedgerLoading, (ledgerLoaded) => {
        if(ledgerLoaded) {
            queryResult.refetch()
        }
    } )

    interface DuesClaimQTableColumn<Row extends Record<string, unknown>> extends QTableColumn<Row> {
        dues?: TeamLedgerDues;
        claim?: TeamLedgerClaim<TeamLedgerDues>
        recurringText?: string
    }

    type DuesMemberRow = {
        id: string,
        name: string,
        member: TeamMember
        selected: boolean,
        claims: { [key: string]: TeamLedgerClaim<TeamLedgerDues> }
    }

    function getUniqueDueClaimsForColumnsFromDueClaims() {
        const seen = new Set()
        return rowData.value.filter(duesClaim => {
            const key = duesClaim.getKey()
            if (seen.has(key)) {
                return false;
            } else {
                seen.add(key);
                return true;
            }
        })
    }

    const columns: ComputedRef<DuesClaimQTableColumn<DuesMemberRow>[]> = computed(() => {
        if(rowData.value) {
            return [
                { name: 'member', label: 'Mitglied', field: 'name', sortable: true, align: 'left' },
                ...getUniqueDueClaimsForColumnsFromDueClaims().map((duesClaim) => {
                    return {
                        name: duesClaim.id,
                        dues: duesClaim.claimable,
                        claim: duesClaim,
                        label: duesClaim.getDueDateFormatted('DD.MM.'),
                        sublabel: duesClaim.getClaimText(),
                        align: 'center',
                        recurringText: useRecurring(duesClaim.claimable?.getRRule()).getRecurrenceText(),
                        field: row => row['claims'][duesClaim.getKey()]
                    } as DuesClaimQTableColumn<DuesMemberRow>
                })
            ]
        }
        return [];
    })

    function getDuesClaimsForMember(member: TeamMember) {
        return rowData.value?.filter((duesClaim) => duesClaim.teamMember.id === member.id)
    .reduce((newArray: { [key: string]: TeamLedgerClaim<TeamLedgerDues> }, duesClaim) => {
            newArray[duesClaim.getKey()] = duesClaim as TeamLedgerClaim<TeamLedgerDues>;
            return newArray;
        }, {});
    }

    const authStore = useAuthStore();
    const rowsPerMember = computed<DuesMemberRow[]>(() => {
            return [
                ...props.ledger.team.membersSortedByName.map((member => (buildRowForMember(member)))),
                ...Team.orderMembersByName(props.ledger.formerMembers).map((member => (buildRowForMember(member)))),
            ]
        }
    )
    function buildRowForMember(member: TeamMember): DuesMemberRow {
        return {
            id: member.id,
                name: member.name,
                member: member,
                selected: authStore.isOwnMember(member),
                claims: getDuesClaimsForMember(member),
        } as DuesMemberRow
    }
    const selected = computed(() => rowsPerMember.value?.filter((row) => row.selected));

    const queryResult = useQuery({
        ... queries.ledgers.duesClaims(props.ledger.id as string),
        enabled: !isEmpty(props.ledger),
        refetchOnWindowFocus: false,
    });

    const {
        isFetching,
    } = queryResult;

    const {
        rowData
    } = useServerTable<TeamLedgerClaim<TeamLedgerDues>>(queryResult)

</script>

<style lang="scss" scoped></style>
