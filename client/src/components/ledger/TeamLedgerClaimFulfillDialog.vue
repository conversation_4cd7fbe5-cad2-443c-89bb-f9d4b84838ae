<template>

    <q-btn
        v-if="hasPermissionForTeam(ledger.team, 'team.ledger.manage')"
        :icon="statusInfo?.icon"
        :color="statusInfo?.iconColor"
        no-caps
        dense
        flat
        @click="showDialog = true"
    />
    <q-icon
        v-else
        :name="statusInfo?.icon"
        :color="statusInfo?.iconColor"
        size="sm"
    />

    <CustomDialog v-model="showDialog" title="Zustand ändern" v-if="hasPermissionForTeam(ledger.team, 'team.ledger.manage')">

            <div class="q-pb-sm">
                Forderung: <b>{{claim.getClaimText()}}</b><br>
                an: <b>{{claim.teamMember.name}}</b> <br>
                für: <b>{{claim.title}}</b><br>
                Datum: <b>{{claim.dueDateFormatted}}</b><br>
                <template v-if="ledger && claim.isMoneyClaim()">
                    Guthaben:
                    <q-spinner v-if="isCreditBalanceLoading" />
                    <b v-else>
                        <span v-if="creditBalance?.amount != 0">{{creditBalance?.formatted}}</span>
                        <span v-else>Nicht vorhanden</span>
                    </b>
                </template>

            </div>

            <q-list bordered separator>
                <q-item
                    v-if="!claim.isStatus('fulfilled') && (canBeCompletelyFulfilledByCreditBalance || canBePartiallyFulfilledByCreditBalance)"
                    clickable
                    v-ripple
                    @click="setClaimStatusFulfilled(true)"
                >
                    <q-item-section avatar><q-icon :name="claimStatusIcon.fulfilled" :color="claimStatusIconColor.fulfilled" /></q-item-section>
                    <q-item-section>
                        <q-item-label>Bezahlt mit Guthaben</q-item-label>
                        <q-item-label caption v-if="!canBeCompletelyFulfilledByCreditBalance">{{remainingAmountAfterCreditBalance.formatted}} Einzahlung wird erstellt</q-item-label>
                    </q-item-section>
                </q-item>

                <q-item :clickable="!claim.isStatus('fulfilled')"
                        @click="setClaimStatusFulfilled()"
                        :v-ripple="!claim.isStatus('fulfilled')"
                        :active="claim.isStatus('fulfilled')"
                >
                    <q-item-section avatar><q-icon :name="claimStatusIcon.fulfilled" :color="claimStatusIconColor.fulfilled" /></q-item-section>
                    <q-item-section>
                        <q-item-label>{{claim.isItemClaim() ? 'Erfüllt' : 'Bezahlt'}}</q-item-label>
                        <q-item-label caption v-if="claim.isMoneyClaim() && !claim.isStatus('fulfilled')">{{claim.amount.formatted}} Einzahlung wird erstellt</q-item-label>
                    </q-item-section>
                </q-item>



                <q-item
                    :clickable="!claim.isUnfulfilled()"
                    @click="setClaimStatusUnfulfilled()"
                    :v-ripple="!claim.isUnfulfilled()"
                    :active="claim.isUnfulfilled()"
                    :disable="canBeCompletelyFulfilledByCreditBalance"
                >
                    <q-item-section avatar><q-icon :name="claimStatusIcon.unfulfilled" :color="claimStatusIconColor.unfulfilled_overdue" /></q-item-section>
                    <q-item-section>
                        Nicht {{claim.isItemClaim() ? 'Erfüllt' : 'Bezahlt'}}
                        <q-item-label v-if="claim.isStatus('unfulfilled_overdue')" caption>
                            Überfällig
                        </q-item-label>
                        <q-item-label v-else-if="canBeCompletelyFulfilledByCreditBalance" caption>
                            Zustand unmöglich, da Guthaben vorhanden
                        </q-item-label>
                        <q-item-label v-else-if="claim.isStatus('fulfilled') && claim.isMoneyClaim()" caption>
                            {{claim.amount.formatted}} Auszahlung wird erstellt
                        </q-item-label>
                    </q-item-section>
                </q-item>



                <q-item
                    :clickable="!claim.isStatus('exempt')"
                    @click="setClaimStatusExempt(true)"
                    :v-ripple="!claim.isStatus('exempt')"
                    :active="claim.isStatus('exempt')"
                >
                    <q-item-section avatar><q-icon :name="claimStatusIcon.exempt" :color="claimStatusIconColor.exempt" /></q-item-section>
                    <q-item-section>
                        <q-item-label>Befreit</q-item-label>
                        <q-item-label caption
                                      v-if="claim.isStatus('fulfilled') && claim.isMoneyClaim()"
                        >{{claim.amount.formatted}} Auszahlung wird erstellt</q-item-label>
                    </q-item-section>
                </q-item>

                <q-item
                    v-if="claim.isStatus('fulfilled') && claim.isMoneyClaim()"
                    :clickable="!claim.isStatus('exempt')"
                    @click="setClaimStatusExempt(false)"
                    :v-ripple="!claim.isStatus('exempt')"
                    :active="claim.isStatus('exempt')"
                >
                    <q-item-section avatar><q-icon :name="claimStatusIcon.exempt" :color="claimStatusIconColor.exempt" /></q-item-section>
                    <q-item-section>
                        <q-item-label>Befreit</q-item-label>
                        <q-item-label caption>{{claim.amount.formatted}} verbleibt als Guthaben</q-item-label>
                    </q-item-section>
                </q-item>
            </q-list>
        <q-inner-loading :showing="claimSaving"/>
    </CustomDialog>
</template>

<script setup lang="ts">
    import  TeamLedger from 'src/models/TeamLedger';
    import { computed, ref } from 'vue';
    import CustomDialog from 'src/components/CustomDialog.vue';
    import { useMutation, useQuery } from '@tanstack/vue-query';
    import { setClaimStatusOnServer } from 'src/queries/mutations/ledgers';
    import TeamLedgerClaim, { claimStatusIcon, claimStatusIconColor } from 'src/models/TeamLedgerClaim';
    import { queries } from 'src/queries';
    import { isEmpty } from 'lodash';
    import { Money, MoneyClass } from 'src/helpers/money';
    import type { DefaultError } from '@tanstack/query-core';
    import TeamLedgerDues from 'src/models/TeamLedgerDues';
    import TeamLedgerFine from 'src/models/TeamLedgerFine';

    const props = withDefaults(defineProps<{
        claim: TeamLedgerClaim<TeamLedgerDues | TeamLedgerFine>
        ledger: TeamLedger
        isLedgerLoading: boolean
    }>(), {
    });

    const emit = defineEmits<{
        saved: []
    }>();

    const statusInfo = computed(() => props.claim.getStatusInfo())

    const showDialog = ref(false);

    const {
        mutateAsync: saveClaim,
        isPending: claimSaving
    } =
        useMutation({
            mutationFn: setClaimStatusOnServer,
            onSuccess() {
                showDialog.value = false
                emit('saved')
            },
        })


    const {
        isLoading: isCreditBalanceLoading,
        data: creditBalance,
    } = useQuery<MoneyClass, DefaultError, MoneyClass>({
       ... queries.ledgers.creditBalance(props.ledger.id, props.claim.teamMember?.id),
        enabled: () => !isEmpty(props.ledger) && !isEmpty(props.claim.teamMember) && props.claim.isMoneyClaim() && showDialog.value,
   });

    const canBeCompletelyFulfilledByCreditBalance = computed(() => props.claim.isMoneyClaim() && creditBalance.value?.amount >= Number(props.claim.amount?.amount))
    const canBePartiallyFulfilledByCreditBalance = computed(() => !canBeCompletelyFulfilledByCreditBalance.value && props.claim.isMoneyClaim() && creditBalance.value?.amount > 0)
    const remainingAmountAfterCreditBalance = computed(() => {
        if(canBeCompletelyFulfilledByCreditBalance.value) {
            return new MoneyClass(0)
        } else if (canBePartiallyFulfilledByCreditBalance.value) {
            return creditBalance.value?.subtractFrom(props.claim.amount as Money);
        } else {
            return null
        }
    })


    function setClaimStatusFulfilled(useAvailableCreditBalance: boolean = false) {
        let createTransactionAmount = null
        if (props.claim.isMoneyClaim()) {
            if (useAvailableCreditBalance) {
                if(!canBeCompletelyFulfilledByCreditBalance.value) {
                    createTransactionAmount = remainingAmountAfterCreditBalance.value?.amount
                }
            } else {
                // Bezahlt mit Guthaben (complete) erstellt aktuell auch die Transaktion
                createTransactionAmount = Number(props.claim.amount?.amount)
            }
        }

        saveClaim({
            claim: props.claim,
            claimStatus: 'fulfilled',
            createTransactionAmount
        })
    }

   function setClaimStatusUnfulfilled() {
       const currentClaimStatus = props.claim.status

       let createTransactionAmount = null
       if(props.claim.isMoneyClaim() && currentClaimStatus === 'fulfilled') {
           createTransactionAmount = Number(props.claim.amount?.amount) * -1
       }

        saveClaim({
            claim: props.claim,
            claimStatus: 'unfulfilled',
            createTransactionAmount
        })
    }

    function setClaimStatusExempt(createNegativeTransactionIfFulfilledMoneyClaim: boolean) {
        const currentClaimStatus = props.claim.status

        let createTransactionAmount = null
        if (createNegativeTransactionIfFulfilledMoneyClaim && props.claim.isMoneyClaim() && currentClaimStatus === 'fulfilled') {
            createTransactionAmount = Number(props.claim.amount?.amount) * -1
        }

        saveClaim({
            claim: props.claim,
            claimStatus: 'exempt',
            createTransactionAmount
        })
    }

</script>

<style lang="scss" scoped></style>
