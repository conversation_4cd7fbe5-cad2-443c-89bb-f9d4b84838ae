<template>
  <InfoIcon>
    Bereits bestehende {{labelPlural}} werden automatisch angepasst, die Forderungen pro Spieler können sich also ändern.<br>
    <br>
    <strong>Bei Geldbeträgen:</strong><br>
    Wird der Betrag niedriger, kann ein vorhandenes Guthaben {{labelSingular}} automatisch begleichen.<br>
    <br>
    <strong>Bereits bezahlte {{labelPlural}} werden ebenfalls angepasst.</strong><br>
    Wird der Betrag niedriger, wird die Differenz als Guthaben angerechnet.<br>
    Wird der Betrag höher, wird vorhandenes Guthaben verrechnet und der Rest als offene Forderung eingetragen.
  </InfoIcon>
</template>
<script setup lang="ts">

    import InfoIcon from 'src/components/InfoIcon.vue';
    import { computed } from 'vue';
    import TeamLedgerFine from 'src/models/TeamLedgerFine';
    import TeamLedgerDues from 'src/models/TeamLedgerDues';

    const props = defineProps<{
        claimableType:
            | TeamLedgerFine['jsonapiType']
            | TeamLedgerDues['jsonapiType']
    }>()

    const labelPlural = computed(() => {
        switch(props.claimableType) {
            case TeamLedgerFine.jsonapiType: return 'Strafen';
            case TeamLedgerDues.jsonapiType: return 'Beiträge';
            default: return 'Forderungen'
        }
    })

    const labelSingular = computed(() => {
        switch(props.claimableType) {
            case TeamLedgerFine.jsonapiType: return 'die Strafe';
            case TeamLedgerDues.jsonapiType: return 'den Beitrag';
            default: return 'die Forderung'
        }
    })

</script>
