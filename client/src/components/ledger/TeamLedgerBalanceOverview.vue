<template>
    <q-card>
        <q-card-section class="no-padding">
            <q-list class="full-width" separator :dense="dense">

                <q-item class="bg-secondary">
                    <q-item-section class="text-h6">
                        <q-item-label>Verfügbar:</q-item-label>
                    </q-item-section>
                    <q-item-section side class="text-h6 text-primary" :class="{'blur-text': !showLedgerBalance}">
                        {{ledger.availableBalance.formatted}}
                    </q-item-section>
                </q-item>

                <q-item>
                    <q-item-section class="text-subtitle1">
                        <q-item-label>Fällige Forderungen:</q-item-label>
                    </q-item-section>
                    <q-item-section side class="text-primary">
                        {{ledger.claimBalance.formatted}}
                    </q-item-section>
                </q-item>

                <q-expansion-item
                    header-class="q-pa-none"
                    :hide-expand-icon="!dueItemClaimListExpandable"
                    :dense="dense"
                >
                    <template v-slot:header>
                        <q-item class="full-width" :dense="dense">
                            <q-item-section class="text-subtitle1">
                                <q-item-label>Fällige Forderungen (Gegenstände):</q-item-label>
                            </q-item-section>
                            <q-item-section side class="text-primary">
                                {{ props.ledger.dueItemClaims.length }} Einträge
                            </q-item-section>
                        </q-item>
                    </template>

                    <q-list separator dense bordered class="q-mx-md q-mb-md" v-if="dueItemClaimListExpandable">
                        <q-item v-for="itemClaim in props.ledger.dueItemClaims"
                                :key="itemClaim.id"
                        >
                            <q-item-section top>
                                {{itemClaim.teamMember.name}}
                            </q-item-section>
                            <q-item-section side top>
                                <q-item-label><strong>{{itemClaim.item}}</strong></q-item-label>
                                <q-item-label caption>({{itemClaim.title}} vom {{itemClaim.dueDateFormatted}})</q-item-label>
                            </q-item-section>
                            <q-item-section side v-if="hasPermissionForTeam(ledger.team, 'team.ledger.manage')">
                                <TeamLedgerClaimFulfillDialog
                                    :claim="itemClaim"
                                    :ledger="ledger"
                                    :is-ledger-loading="isLoading"
                                    @saved="$emit('refreshLedger')"
                                />
                            </q-item-section>
                        </q-item>
                    </q-list>
                </q-expansion-item>

                <q-expansion-item
                    label="Weitere Infos"
                    :dense="dense"
                >
                    <q-list separator dense bordered class="q-mx-md bg-white">
                        <q-item>
                            <q-item-section>
                                <q-item-label>Geld vorhanden:</q-item-label>
                            </q-item-section>
                            <q-item-section side>
                                {{ledger.balance.formatted}}
                            </q-item-section>
                        </q-item>

                        <q-item>
                            <q-item-section>
                                <q-item-label>davon Guthaben:</q-item-label>
                            </q-item-section>
                            <q-item-section side>
                                {{ledger.creditBalance.formatted}}
                            </q-item-section>
                        </q-item>

                        <q-item>
                            <q-item-section>
                                <q-item-label>zukünftige Einnahmen aus Beiträgen:</q-item-label>
                            </q-item-section>
                            <q-item-section side>
                                {{ledger.futureClaimBalance.formatted}}
                            </q-item-section>
                        </q-item>
                    </q-list>
                    <div class="q-pb-md"></div>
                </q-expansion-item>

                <!--                            Fehler bei expansion item, margin bottom in list wird nicht angezeigt, wenn danach nichts mehr kommt -->
                <q-separator/>
            </q-list>

        </q-card-section>
        <q-inner-loading :showing="isLoading" />
    </q-card>
</template>

<script setup lang="ts">
    import TeamLedger from 'src/models/TeamLedger';
    import TeamLedgerClaimFulfillDialog from 'src/components/ledger/TeamLedgerClaimFulfillDialog.vue';
    import { computed } from 'vue';

    const props = withDefaults(defineProps<{
        ledger: TeamLedger
        isLoading: boolean
        showDueItemClaims?: boolean
        showLedgerBalance?:boolean
        dense?: boolean
    }>(), {
        showDueItemClaims: true,
        showLedgerBalance: true,
        dense: false
    });

    const dueItemClaimListExpandable = computed(() => props.showDueItemClaims && props.ledger.dueItemClaims.length > 0)

    const emit = defineEmits<{
        refreshLedger: []
    }>();
</script>

<style lang="scss" scoped>
    .blur-text {
        filter: blur(5px)
    }
</style>
