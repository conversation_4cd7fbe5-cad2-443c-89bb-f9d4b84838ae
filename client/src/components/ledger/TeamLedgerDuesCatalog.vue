<template>
    <TeamLedgerDuesDialog
        v-if="hasPermissionForTeam(ledger.team, 'team.ledger.manage')"
        :ledger="ledger"
        :is-ledger-loading="isLedgerLoading"
        @saved="$emit('refreshLedger')"
    />

    <q-table
        :rows="rowData"
        :columns="columns"
        row-key="id"
        v-model:pagination="pagination"
        binary-state-sort
        @request="onRequest"
        hide-pagination
    >

        <template v-slot:body="props">
            <q-tr :props="props">
                <q-td
                    v-for="col in props.cols"
                    :key="col.name"
                    :props="props"
                    style="border-bottom: none; padding-bottom: 0"
                    class="full-height"
                    :auto-width="['edit'].includes(col.name)"
                >
                    <template v-if="col.name === 'edit'">
                        <TeamLedgerDuesDialog
                            class="q-mr-sm"
                            :ledger="ledger"
                            :is-ledger-loading="isLedgerLoading"
                            :edit-dues="props.row"
                            label=""
                            icon="edit"
                            dense
                            @saved="$emit('refreshLedger')"
                        />
                    </template>
                    <template v-else>
                        {{ col.value }}
                    </template>
                </q-td>
            </q-tr>
            <q-tr :props="props">
                <q-td auto-width class="full-height" v-if="hasPermissionForTeam(ledger.team, 'team.ledger.manage')"></q-td>
                <q-td colspan="100%" class="full-height" style="padding-top: 0">
                    <q-item-section class="float-left">
                        <q-item-label caption>Fällig: {{ props.row.isRecurring() ? useRecurring(props.row.getRRule()).getRangeString() : props.row.dateBeginFormatted }}</q-item-label>
                    </q-item-section>
                    <q-item-section side>
                        <q-item-label caption>{{ props.row.isRecurring() ? useRecurring(props.row.getRRule()).getRecurrenceText() : defaultNonRecurringOptionTemplate.label }}</q-item-label>
                    </q-item-section>
                </q-td>
            </q-tr>
        </template>

    </q-table>

    <q-inner-loading :showing="isFetching" />
</template>

<script setup lang="ts">
    import TeamLedger from 'src/models/TeamLedger';
    import { ref, watch } from 'vue';
    import { QTable, QTableColumn } from 'quasar';
    import { PaginationProps, useServerTable } from 'src/composables/useServerTable';
    import { useQuery } from '@tanstack/vue-query';
    import { queries, withRefCopy } from 'src/queries';
    import { isEmpty } from 'lodash';
    import TeamLedgerDuesDialog from 'src/components/ledger/TeamLedgerDuesDialog.vue';
    import TeamLedgerDues from 'src/models/TeamLedgerDues';
    import { defaultNonRecurringOptionTemplate, useRecurring } from 'src/composables/useRecurring';
    import { useAuthStore } from 'src/stores/auth';

    const props = defineProps<{
        ledger: TeamLedger
        isLedgerLoading: boolean
    }>();

    const emit = defineEmits<{
        refreshLedger: []
    }>();

    watch(() => props.isLedgerLoading, (ledgerLoaded) => {
        if(ledgerLoaded) {
            queryResult.refetch()
        }
    } )

    const columns = [
        ...(useAuthStore().hasPermissionForTeam(props.ledger.team, 'team.ledger.manage') ?
                [{name: 'edit', label: '', field: '', align: 'left'}]: []
        ),
        {name: 'title', label: 'Titel', field: 'title', align: 'left', sortable: true},
        {name: 'amount', label: 'Betrag / Gegenstand', field: row => row.amount ? row.amount.formatted : row.item},

    ] as QTableColumn<TeamLedgerDues>[]

    const pagination = ref(new PaginationProps<TeamLedgerDues>(
        {
            page: 1,
            rowsPerPage: 0,
            sortBy: 'title',
            descending: false,
        }
    ));

    const editableData = ref([])
    const queryResult = useQuery({
        ... withRefCopy(
                queries.ledgers.dues(props.ledger.id as string, pagination),
                editableData,
                false
        ),
        enabled: !isEmpty(props.ledger),
        refetchOnWindowFocus: false,
    });

    const {
        isFetching,
    } = queryResult;


    const {
        onRequest,
        rowData
    } = useServerTable<TeamLedgerDues>(
        queryResult,
        pagination,
        editableData,
    )

</script>

<style lang="scss" scoped></style>
