declare namespace App.Types {
export type AbsenceType = 'vacation' | 'sick' | 'inactive' | 'other';
export type EventReminderType = 'none' | 'hours_before_event';
export type EventResponseType = 'none' | 'auto_yes';
export type EventSeriesType = 'single' | 'recurring_child' | 'recurring_parent';
export type EventVoteReminderType = 'none' | 'hours_after_creation' | 'hours_before_event';
export type FeatureType = 'admin.stats' | 'team.create' | 'device.list' | 'notifications.enabled' | 'subscription' | 'live_demo';
export type SubscriptionPriceType = 'team_yearly' | 'team_half_yearly' | 'team_monthly';
export type SubscriptionStatusType = 'active' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'past_due' | 'paused' | 'trialing' | 'unpaid';
export type TeamEventMatchType = 'home' | 'away';
export type TeamEventType = 'none' | 'training' | 'match' | 'tournament' | 'event';
export type TeamEventVoteType = 'none' | 'yes' | 'no' | 'maybe';
export type TeamLedgerClaimStatusType = 'fulfilled' | 'exempt' | 'unfulfilled' | 'unfulfilled_overdue';
export type TeamPermissionType = 'team.member.create' | 'team.member.delete' | 'team.member.update' | 'team.event.create' | 'team.event.delete' | 'team.event.vote.self' | 'team.event.vote.other' | 'team.event.task.create' | 'team.event.task.delete' | 'team.event.task.assign.self' | 'team.event.task.assign.other' | 'team.member.invite.create.self' | 'team.member.invite.create.other' | 'team.invite.create' | 'team.update' | 'team.stats.range.create' | 'team.stats.range.delete' | 'team.subscription.manage' | 'team.ledger.manage' | 'team.ledger.view';
export type TeamRoleType = 'manager' | 'member' | 'inactive' | 'treasurer';
export type WaitListMailType = 'unknown' | 'alreadyRegistered' | 'alreadyOnInviteList' | 'alreadyOnWaitList' | 'addedToWaitList';
}
