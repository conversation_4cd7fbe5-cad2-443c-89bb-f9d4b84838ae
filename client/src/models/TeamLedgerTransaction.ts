import { Attr, BelongsTo, Model } from 'spraypaint';
import TeamLedger from 'src/models/TeamLedger';
import Person from 'src/models/Person';
import TeamMember from 'src/models/TeamMember';
import { Money } from 'src/helpers/money';
import BaseModelWithTimestamps from 'src/models/BaseModelWithTimestamps';


export interface TeamLedgerTransactionFilter {
    perPage: number,
    page: number,
    sortBy: string | null,
    descending: boolean
}

@Model()
export default class TeamLedgerTransaction extends BaseModelWithTimestamps {
    static jsonapiType = 'teamLedgerTransactions';

    @Attr() id: string;
    @Attr() title: string;
    @Attr() amount: Money;
    @BelongsTo('teamMember') teamMember: TeamMember | null;
    @BelongsTo('person') readonly author: Person;
    @BelongsTo('teamLedger') ledger: TeamLedger;

    static createForLedger(ledger: TeamLedger){
        return new TeamLedgerTransaction({ledger})
    }

}
