import BaseModel from 'src/models/BaseModel';
import { Attr, HasOne, Model } from 'spraypaint';
import User from 'src/models/User';
import TeamPermissionType = App.Types.TeamPermissionType;
import { defaultDateMask, defaultDateTimeMask } from 'src/i18n';

type TeamPermissionNamesPerTeamId = Readonly<Record<string, TeamPermissionType[]>>;

@Model()
export default class DeviceInfo extends BaseModel {
    static jsonapiType = 'deviceInfos';

    @Attr() id: string;
    @Attr() name: '';
    @Attr() displayModel: string;
    @Attr() pushNotificationsEnabled: boolean;
    @Attr() lastSeen: string;
    @HasOne() user: User;

    getLastSeenFormatted(mask: string = defaultDateTimeMask) {
        return this.formatDateTimeLocalized(this.lastSeen, mask);
    }
}
