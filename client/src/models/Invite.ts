import BaseModel from 'src/models/BaseModel';
import { Attr, BelongsTo, Model } from 'spraypaint';
import { ROUTE_NAMES } from 'src/router/route-names';
import { useAuthStore } from 'src/stores/auth';
import Team from 'src/models/Team';
import TeamMember from 'src/models/TeamMember';

export interface Invitable<InvitableType extends Invitable<InvitableType>> extends BaseModel {
  invite?: Invite<InvitableType>;
}


@Model()
export default class Invite<InvitableType extends Invitable<InvitableType>> extends BaseModel {
  static jsonapiType = 'invites';

  @Attr() id: string;
  @Attr() token: string;
  @BelongsTo() invitable: InvitableType;
  @Attr() team: Readonly<Team>;


  getInviteLink(): string {
    const inviteUrl = `${process.env.INVITE_URL}`;
    const route = useAuthStore().router.resolve({ name: ROUTE_NAMES.INVITE, params: { token: this.token } });
    return `${inviteUrl}/${route.href.slice(1)}`;
  }

  isTeamInvite(): boolean {
    return this.invitable.isType(Team.jsonapiType);
  }

  isTeamMemberInvite(): boolean {
    return this.invitable.isType(TeamMember.jsonapiType);
  }

}
