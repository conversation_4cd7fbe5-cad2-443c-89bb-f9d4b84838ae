import { Attr, Has<PERSON><PERSON>, Model } from 'spraypaint';
import BaseModel, { dirtyChecker, SelectOptionWithIcon } from 'src/models/BaseModel';
import TeamMember from 'src/models/TeamMember';
import Person from 'src/models/Person';
import { RRule } from 'rrule';
import AbsenceType = App.Types.AbsenceType;

export const AbsenceTypeOptions: SelectOptionWithIcon<AbsenceType>[] = [
  {
    label: 'Urlaub',
    value: 'vacation',
    icon: 'o_beach_access',
  },
  {
    label: 'Krank',
    value: 'sick',
    icon: 'o_sick',
  },
  {
    label: 'Inaktiv',
    value: 'inactive',
    icon: 'o_do_disturb_on',
  },
  {
    label: 'Sonstiges',
    value: 'other',
    icon: 'help_outline',
  },
];

@Model()
export default class Absence extends BaseModel {
  static jsonapiType = 'absences';

  @Attr() id: string;
  @Attr() name: string;
  @Attr() absenceType: AbsenceType;
  @Attr() rruleString: string | null = null;
  @Attr() author: Person;
  @HasMany('teamMembers') teamMembers: TeamMember[] = [];
  @HasMany('persons') persons: Person[] = [];
  @Attr({ persist: false, dirtyChecker: dirtyChecker }) readonly targetTeamMemberStrings: string[] = [];
  @Attr() private dateBegin: string | null = null;
  @Attr() private dateEnd: string | null = null;

  get dateBeginFormatted(): string {
    return this.convertDateStringFromApi(this.dateBegin);
  }

  set dateBeginFormatted(value: string | null) {
    this.dateBegin = this.convertDateStringToApi(value);
    // this.resetWeekdayAndMonthOptions()
  }

  get dateEndFormatted(): string {
    return this.convertDateStringFromApi(this.dateEnd);
  }

  set dateEndFormatted(value: string | null) {
    this.dateEnd = this.convertDateStringToApi(value);
  }

  get rangeString(): string {
    if (this.dateEnd === null) {
      return 'ab ' + this.dateBeginFormatted;
    } else {
      return this.dateBeginFormatted + ' - ' + this.dateEndFormatted;
    }
  }

  get absenceTypeOption() {
    return AbsenceTypeOptions.find((option) => option.value === this.absenceType) as SelectOptionWithIcon<AbsenceType>;
  }

  isRecurring(): boolean {
    return this.rruleString !== null;
  }

  getRRule() {
    return this.rruleString !== null ? RRule.fromString(this.rruleString) : null;
  }

  getDateStart(): Date | null {
    if (this.dateBegin === null) {
      return null;
    }

    const date = new Date();
    const dateBegin = this.getDateObjectFromMask(this.dateBegin);
    date.setFullYear(dateBegin.getFullYear(), dateBegin.getMonth(), dateBegin.getDate());
    date.setHours(0, 0, 0, 0);
    return date;
  }

  getDateEnd(): Date | null {
    if (this.dateEnd === null) {
      return null;
    }

    const date = new Date();
    const dateEnd = this.getDateObjectFromMask(this.dateEnd);
    date.setFullYear(dateEnd.getFullYear(), dateEnd.getMonth(), dateEnd.getDate());
    date.setHours(0, 0, 0, 0);
    return date;
  }

  isPersonSelected(person: Person): boolean {
    return this.persons.find(
      (entry) => entry.id === person.id,
    ) !== undefined;
  }
}
