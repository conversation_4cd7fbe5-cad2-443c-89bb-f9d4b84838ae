import BaseModel from 'src/models/BaseModel';
import { Attr, BelongsTo, Model } from 'spraypaint';
import Team from 'src/models/Team';

@Model()
export default class TeamStatsRange extends BaseModel {
  static jsonapiType = 'teamStatsRanges';

  @Attr() id: string | undefined;
  @BelongsTo() team: Team;
  @Attr() name: string | undefined;
  @Attr() private startDate: string | null = null;
  @Attr() private endDate: string | null = null;

  get startDateFormatted(): string {
    return this.convertDateStringFromApi(this.startDate);
  }

  set startDateFormatted(value: string | null) {
    this.startDate = this.convertDateStringToApi(value);
  }

  get endDateFormatted(): string {
    return this.convertDateStringFromApi(this.endDate);
  }

  set endDateFormatted(value: string | null) {
    this.endDate = this.convertDateStringToApi(value);
  }

  static createForTeam(team: Team) {
    return new TeamStatsRange({ team });
  }

  getStartDate(): Date {
    return this.getDateObjectFromMask(<string>this.startDate);
  }

  getEndDate(): Date {
    return this.getDateObjectFromMask(<string>this.endDate);
  }
}
