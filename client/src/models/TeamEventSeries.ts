import { At<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Model } from 'spraypaint';
import BaseModel from 'src/models/BaseModel';
import TeamEvent from 'src/models/TeamEvent';
import { RRule } from 'rrule';


@Model()
export default class TeamEventSeries extends BaseModel {
  static jsonapiType = 'teamEventSeries';

  @Attr() id: string | undefined;
  @Attr() rruleString: string | null = null;

  @HasOne() eventData: TeamEvent;
  @HasMany() events: TeamEvent[];

  isRecurring(): boolean {
    return this.rruleString !== null;
  }

  getRRule() {
    return this.rruleString !== null ? RRule.fromString(this.rruleString) : null;
  }

}
