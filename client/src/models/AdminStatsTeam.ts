import { Attr, Model } from 'spraypaint';
import { Invitable } from 'src/models/Invite';
import BaseModelWithTimestamps from 'src/models/BaseModelWithTimestamps';
import { isEmpty } from 'lodash';

@Model()
export default class AdminStatsTeam extends BaseModelWithTimestamps implements Invitable<AdminStatsTeam> {
  static jsonapiType = 'adminStatsTeams';

  @Attr() id: string;
  @Attr() name: string;
  @Attr() trialEndsAt: string;
  @Attr() isSubscribed: boolean;
  @Attr() subscriptionEndsAd: string;
  @Attr() isActive: boolean;
  @Attr() membersCount: number;
  @Attr() registeredMembersCount: number;
  @Attr() eventsCount: number;
  @Attr() votesCountAutomatic: number;
  @Attr() votesCountManual: number;
  @Attr() ledgerId: string | null;
  @Attr() ledgerFineCount: number | null;
  @Attr() ledgerDuesCount: number | null;
  @Attr() ledgerTransactionCount: number | null;

    hasTrialEndDate(): boolean {
        return !isEmpty(this.trialEndsAt)
    }

    getTrialEndsAtFormatted(): string {
        return this.convertDateStringFromApi(this.trialEndsAt);
    }

    getSubscriptionEndsAtFormatted(): string {
        return this.convertDateStringFromApi(this.subscriptionEndsAd);
    }
}
