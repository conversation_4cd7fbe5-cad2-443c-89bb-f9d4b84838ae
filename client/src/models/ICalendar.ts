import { Attr, <PERSON><PERSON><PERSON>, Model } from 'spraypaint';
import BaseModelWithTimestamps from 'src/models/BaseModelWithTimestamps';
import Person from 'src/models/Person';
import TeamMember from 'src/models/TeamMember';
import { dirtyChecker } from 'src/models/BaseModel';

@Model()
export default class ICalendar extends BaseModelWithTimestamps {
    static jsonapiType = 'iCalendars';

    @Attr() internalName: string;
    @Attr() author: Person;
    @Attr() readonly publicLink: string;
    @Attr() readonly name: string;
    @HasMany('teamMembers') teamMembers: TeamMember[] = [];
    @HasMany('persons') persons: Person[] = [];
    @Attr({ persist: false, dirtyChecker: dirtyChecker }) readonly targetTeamMemberStrings: string[] = [];

    isPersonSelected(person: Person): boolean {
        return this.persons.find(
            (entry) => entry.id === person.id,
        ) !== undefined;
    }
}
