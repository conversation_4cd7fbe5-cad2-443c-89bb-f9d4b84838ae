import { Attr, Model } from 'spraypaint';
import BaseModelWithTimestamps from 'src/models/BaseModelWithTimestamps';
import { useAuthStore } from 'src/stores/auth';

@Model()
export default class Subscription extends BaseModelWithTimestamps {
  static jsonapiType = 'subscriptions';

  @Attr() id: string;
  @Attr() userId: string;
  @Attr() sponsorName: string;

  isCurrentUserSubscriptionSponsor(): boolean {
    return this.userId === useAuthStore().userId;
  }
}
