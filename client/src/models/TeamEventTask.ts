import { Attr, BelongsTo, HasOne, Model } from 'spraypaint';
import BaseModel from 'src/models/BaseModel';
import TeamEvent from 'src/models/TeamEvent';
import TeamEventTaskConfig from 'src/models/TeamEventTaskConfig';
import TeamMember from 'src/models/TeamMember';
import { defaultDateTimeMask } from 'src/i18n';

@Model()
export default class TeamEventTask extends BaseModel {
  static jsonapiType = 'teamEventTasks';

  @Attr() id: string;
  @BelongsTo() teamEvent: TeamEvent;
  @BelongsTo() config: TeamEventTaskConfig;
  @HasOne() teamMember?: TeamMember;
  @Attr() private readonly updatedAt: string;

  static createForTeamEvent(teamEvent: TeamEvent) {
    return new TeamEventTask({ teamEvent });
  }

  getUpdatedAtDateFormatted(mask: string = defaultDateTimeMask) {
    return this.formatDateTimeLocalized(this.updatedAt, mask);
  }
}
