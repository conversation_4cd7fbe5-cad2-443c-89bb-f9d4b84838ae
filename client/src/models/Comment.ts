import { Attr, BelongsTo, Model } from 'spraypaint';
import BaseModel from 'src/models/BaseModel';
import Person from 'src/models/Person';
import { defaultDateTimeMask } from 'src/i18n';

export interface Commentable {
  comment: Comment | undefined;
  readonly hasSavedComment: boolean;

  getOrCreateComment(): Comment;

  get commentText(): string;

  set commentText(value: string);
}

@Model()
export default class Comment extends BaseModel {
  static jsonapiType = 'comments';

  @Attr() id: string;
  @Attr() text = '';
  @Attr() author: Person;
  @BelongsTo() parent: BaseModel & Commentable;
  @Attr() private createdAt = '';
  @Attr() private updatedAt = '';

  getCreatedAtDateFormatted(mask: string = defaultDateTimeMask) {
    return this.formatDateTimeLocalized(this.createdAt, mask);
  }

  getUpdatedAtDateFormatted(mask: string = defaultDateTimeMask) {
    return this.formatDateTimeLocalized(this.updatedAt, mask);
  }

  isTextEmpty() {
    return this.text === null || this.text.trim().length === 0;
  }
}
