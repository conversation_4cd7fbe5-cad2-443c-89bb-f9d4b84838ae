import BaseModel from 'src/models/BaseModel';
import { Attr, BelongsTo, HasMany, Model } from 'spraypaint';
import Team from 'src/models/Team';
import TeamEventTask from 'src/models/TeamEventTask';

@Model()
export default class TeamEventTaskConfig extends BaseModel {
  static jsonapiType = 'teamEventTaskConfigs';

  @Attr() id: string;
  @BelongsTo() team: Team;
  @Attr() title: string;
  @HasMany() tasks: TeamEventTask[] = [];

  static createForTeam(team: Team) {
    return new TeamEventTaskConfig({ team });
  }
}
