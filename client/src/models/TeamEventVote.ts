import { Attr, BelongsTo, HasOne, Model } from 'spraypaint';
import BaseModel from 'src/models/BaseModel';
import TeamEvent from 'src/models/TeamEvent';
import TeamMember from 'src/models/TeamMember';
import Comment, { Commentable } from 'src/models/Comment';
import { defaultDateTimeMask } from 'src/i18n';
import Person from 'src/models/Person';
import TeamEventVoteType = App.Types.TeamEventVoteType;

@Model()
export default class TeamEventVote extends BaseModel implements Commentable {
  static jsonapiType = 'teamEventVotes';

  @Attr() id: string;
  @BelongsTo() teamEvent: TeamEvent;
  @HasOne() teamMember: TeamMember;
  @Attr() vote: TeamEventVoteType = 'none';
  @HasOne() comment: Comment | undefined;
  @Attr() readonly hasSavedComment: boolean = false;
  @BelongsTo() readonly author?: Person;
  @Attr() private readonly updatedAt: string;

  get commentText(): string {
    return this.getOrCreateComment().text;
  }

  set commentText(value: string) {
    this.getOrCreateComment().text = value;
  }

  getOrCreateComment() {
    if ((this.comment === undefined || this.comment.isDeletingStarted)) {
      this.comment = new Comment({ parent: this });
    }
    return this.comment;
  }

  getUpdatedAtDateFormatted(mask: string = defaultDateTimeMask) {
    return this.formatDateTimeLocalized(this.updatedAt, mask);
  }

  isManualVote() {
    return this.author !== undefined;
  }
}
