import { Attr, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Model, SpraypaintBase } from 'spraypaint';
import BaseModel, { SelectOptionWithIcon, SelectOptionWithInfo } from 'src/models/BaseModel';
import Team from 'src/models/Team';
import TeamEventVote from 'src/models/TeamEventVote';
import TeamMember from 'src/models/TeamMember';
import { SaveOptions } from 'spraypaint/lib-esm/model';
import { useAuthStore } from 'src/stores/auth';
import TeamEventSeries from 'src/models/TeamEventSeries';
import { date, QSelectOption } from 'quasar';
import TeamRole from 'src/models/TeamRole';
import TeamEventTask from 'src/models/TeamEventTask';
import TeamStatsRange from 'src/models/TeamStatsRange';
import TeamEventType = App.Types.TeamEventType;
import TeamEventVoteType = App.Types.TeamEventVoteType;
import EventSeriesType = App.Types.EventSeriesType;
import EventResponseType = App.Types.EventResponseType;
import TeamEventMatchType = App.Types.TeamEventMatchType;
import TeamRoleType = App.Types.TeamRoleType;
import EventVoteReminderType = App.Types.EventVoteReminderType;
import EventReminderType = App.Types.EventReminderType;
import { ModelAttributeChangeSet } from 'spraypaint/lib/model';

export const TeamEventTypeOptions: SelectOptionWithIcon<TeamEventType>[] = [
    {
        label: 'Training',
        value: 'training',
        icon: 'mdi-traffic-cone',
    },
    {
        label: 'Wettkampf',
        value: 'match',
        icon: 'mdi-whistle',
    },
    {
        label: 'Turnier',
        value: 'tournament',
        icon: 'mdi-trophy-variant',
    },
    {
        label: 'Event',
        value: 'event',
        icon: 'mdi-grill',
    },
];

export const EventResponseTypeOptions: SelectOptionWithInfo<EventResponseType>[] = [
    {
        label: 'Teilnehmer müssen zusagen',
        value: 'none',
        info: 'Teilnehmer werden als unbeantwortet eingetragen und müssen Zu- oder Absagen',
    },
    {
        label: 'Teilnehmer müssen absagen',
        value: 'auto_yes',
        info: 'Teilnehmer stehen automatisch auf "Zugesagt" und müssen Absagen',
    },
];

export const EventVoteReminderOptions: SelectOptionWithInfo<EventVoteReminderType>[] = [
    {
        label: 'Nach Erstellung',
        value: 'hours_after_creation',
        info: 'Erinnerung wird nach x Stunden an alle Teilnehmer gesendet, die noch nicht abgestimmt haben.'
    },
    {
        label: 'Vor Termin',
        value: 'hours_before_event',
        info: 'Erinnerung wird x Stunden vor Terminbeginn an alle Teilnehmer gesendet, die noch nicht abgestimmt haben.'
    },
    {
        label: 'Keine Erinnerung',
        value: 'none',
        info: 'Es wird keine Erinnerung zur Abstimmung an die Teilnehmer gesendet.'
    }
];

export const EventReminderOptions: SelectOptionWithInfo<EventReminderType>[] = [
    {
        value: 'hours_before_event',
        label: 'Vor Termin',
        info: 'Erinnerung wird x Stunden vor Terminbeginn an alle zugesagten Teilnehmer gesendet.'
    },
    {
        value: 'none',
        label: 'Keine Erinnerung',
        info: 'Es wird keine Erinnerung an den Termin gesendet.'
    }
];

export const EventMatchTypeOptions: QSelectOption<TeamEventMatchType>[] = [
    {
        label: 'Heimspiel',
        value: 'home',
    },
    {
        label: 'Auswärtsspiel',
        value: 'away',
    },
];

@Model()
export default class TeamEvent extends BaseModel {
    static jsonapiType = 'teamEvents';

    @Attr() id: string | undefined;
    @BelongsTo() team: Team;

    @Attr() eventType: TeamEventType;
    @Attr() title = '';
    @Attr() subText = '';
    @Attr() details: string | null = null;
    @Attr() detailsManager: string | null = null;
    @Attr() responseType: EventResponseType = 'none';
    @Attr() addressString = null;
    @Attr() matchType: TeamEventMatchType | undefined;
    @Attr() matchOpponentName: string;
    @Attr() seriesType: EventSeriesType = 'single';
    @Attr() voteReminderType: EventVoteReminderType = 'hours_after_creation';
    @Attr() voteReminderHours: number = 2;
    @Attr() eventReminderType: EventReminderType = 'none';
    @Attr() eventReminderHours: number = 12;
    @Attr() sendChangeNotification: boolean = true;
    @BelongsTo() series: TeamEventSeries = new TeamEventSeries({ eventData: this });
    @HasMany(TeamEventVote) votes: TeamEventVote[] = [];
    @HasMany() tasks: TeamEventTask[] = [];
    @Attr() private cancelledAt = '';
    @Attr() private createdAt: string | null = null;
    @Attr() private dateBegin: string | null = null;
    @Attr() private timeBegin: string | null = null;
    @Attr() private timeEnd: string | null = null;
    @Attr() private timeMeet: string | null = null;
    private votesBeingUpdated: Record<string, TeamEventVoteType> = {};

    get isCancelled(): boolean {
        return date.isValid(this.cancelledAt);
    }

    get activeStatsRange(): TeamStatsRange | null {
        return this.team.statsRanges.find(range => {
            const dateBegin = this.getDateTimeStart();
            return dateBegin
                && range.getEndDate() >= dateBegin
                && range.getStartDate() <= dateBegin;
        }) || null;
    }

    get dateBeginFormatted(): string {
        return this.convertDateStringFromApi(this.dateBegin);
    }

    set dateBeginFormatted(value: string | null) {
        this.dateBegin = this.convertDateStringToApi(value);
    }

    get timeBeginFormatted(): string {
        return this.convertTimeStringFromApi(this.timeBegin);
    }

    set timeBeginFormatted(value: string | null) {
        this.timeBegin = this.convertTimeStringToApi(value);
    }

    get timeEndFormatted(): string {
        return this.convertTimeStringFromApi(this.timeEnd);
    }

    set timeEndFormatted(value: string | null) {
        this.timeEnd = this.convertTimeStringToApi(value);
    }

    get timeMeetFormatted(): string {
        return this.convertTimeStringFromApi(this.timeMeet);
    }

    set timeMeetFormatted(value: string | null) {
        this.timeMeet = this.convertTimeStringToApi(value);
    }

    get matchTypeOption() {
        return EventMatchTypeOptions.find((option) => option.value === this.matchType);
    }

    static createCopy(initialEvent: TeamEvent) {
        const event = initialEvent;
        event.isPersisted = false;
        event.id = undefined;
        event.seriesType = 'single';
        event.series = new TeamEventSeries({ eventData: event });
        event.unsetCancelled();
        event.dateBeginFormatted = null;
        event.reset(); // reset dirty tracking
        return event;
    }

    save<I extends SpraypaintBase>(options?: SaveOptions<I>): Promise<boolean> {
        if (!this.isPersisted && this.series && this.series.isRecurring()) {
            this.seriesType = 'recurring_parent';
            // save series first
            return this.series.save().then(() => super.save(options));
        } else {
            return super.save(options);
        }
    }

    resetValuesOnTypeChange() {
        this.title = '';
        this.subText = '';
        this.matchType = undefined;
        this.matchOpponentName = '';
        this.series = new TeamEventSeries({ eventData: this });
    }

    setCancelled(): void {
        const now = new Date();
        this.cancelledAt = now.toISOString();
    }

    unsetCancelled(): void {
        this.cancelledAt = '';
        this.isDirty()
    }

    getDateTimeStart(): Date | null {
        if (this.dateBegin === null || this.timeBegin === null) {
            return null;
        }
        const date = new Date();
        date.setSeconds(0, 0);
        if (this.dateBegin !== null) {
            const dateBegin = this.getDateObjectFromMask(this.dateBegin);
            date.setFullYear(dateBegin.getFullYear(), dateBegin.getMonth(), dateBegin.getDate());
        }
        if (this.timeBegin !== null) {
            const timeBegin = this.getTimeObjectFromMask(this.timeBegin);
            date.setHours(timeBegin.getHours(), timeBegin.getMinutes());
        }
        return date;
    }

    getDateBeginFormatted(format: string, fallback = '-'): string {
        return this.hasDateBegin() ? this.convertDateStringFromApi(this.dateBegin, format) : fallback;
    }

    getTimeBeginFormatted(format = 'HH:mm', fallback = '-:-'): string {
        return this.hasTimeBegin() ? this.convertTimeStringFromApi(this.timeBegin, format) : fallback;
    }

    getTimeEndFormatted(format = 'HH:mm', fallback = '-:-'): string {
        return this.hasTimeEnd() ? this.convertTimeStringFromApi(this.timeEnd, format) : fallback;
    }

    hasTimeEnd(): boolean {
        return this.timeEndFormatted !== '';
    }

    getTimeMeetFormatted(format = 'HH:mm', fallback = '-:-'): string {
        return this.hasTimeMeet() ? this.convertTimeStringFromApi(this.timeMeet, format) : fallback;
    }

    hasTimeMeet(): boolean {
        return this.timeMeetFormatted !== '';
    }


    getVotes(includeInactive = false) {
        return this.votes.filter((vote) => {
            if (includeInactive) {
                return true;
            }
            return this.team.getMembers(includeInactive)
                .find((member) => vote.teamMember.id === member.id) !== undefined;
        });
    }

    getVoteSum(type: TeamEventVoteType, includeInactive = false): number {
        if (type === 'none') {
            const totalVoteCount = this.getVotes(includeInactive).length;
            return this.team.getMembersCount(includeInactive) - totalVoteCount;
        } else {
            return this.getVotes(includeInactive)
                .filter((vote) => vote.vote === type)
                .length;
        }
    }

    getStatusRolesWithVotes(type: TeamEventVoteType, includeInactive = false) {
        return TeamRole.statusRoleVoteOrder
            .filter((roleName: TeamRoleType) => this.getVoteSumForStatusRole(type, roleName, includeInactive) > 0);
    }

    getVoteSumForStatusRole(type: TeamEventVoteType, role: TeamRoleType, includeInactive = false) {
        return this.getMembersByVoteType(type, includeInactive)
            .filter((member: TeamMember) => member.statusRole.name === role).length;
    }

    getVotePercent(type: TeamEventVoteType, includeInactive = false) {
        return (this.getVoteSum(type, includeInactive) / this.team.getMembersCount(includeInactive) * 100);
    }

    getVote(teamMember: TeamMember): TeamEventVote | undefined {
        return this.votes.find(vote => {
            return vote.teamMember.id === teamMember.id;
        });
    }

    getVoteType(teamMember: TeamMember): TeamEventVoteType {
        const eventVote = this.getVote(teamMember);
        return eventVote === undefined ? 'none' : eventVote.vote;
    }

    hasVoted(teamMember: TeamMember, vote: TeamEventVoteType | null = null) {
        const eventVote = this.getVote(teamMember);

        if (vote === null) {
            return eventVote !== undefined;
        } else if (vote === 'none') {
            return eventVote === undefined;
        } else {
            return eventVote !== undefined && eventVote.vote === vote;
        }
    }

    hasVoteComment(teamMember: TeamMember): boolean {
        const vote = this.getVote(teamMember);
        return vote !== undefined && vote.comment && vote.comment.isPersisted;
    }

    canUserVoteForMember(teamMember: TeamMember) {
        return useAuthStore().hasPermissionForTeam(this.team, 'team.event.vote.other')
            || (
                useAuthStore().person?.teamMembers.find(member => member.id === teamMember.id) !== undefined
                && useAuthStore().hasPermissionForTeam(this.team, 'team.event.vote.self')
            );
    }

    isVoteEnabled(teamMember: TeamMember) {
        if (!this.canUserVoteForMember(teamMember)) {
            return false;
        }
        const eventVote = this.getVote(teamMember);
        return eventVote === undefined || (!eventVote.isSaving && !eventVote.isDeleting);
    }

    setVoteUpdating(teamMember: TeamMember, vote: TeamEventVoteType, updating = true) {
        if (updating) {
            this.votesBeingUpdated[teamMember.id] = vote;
        } else {
            delete this.votesBeingUpdated[teamMember.id];
        }
    }

    isVoteUpdating(teamMember: TeamMember, vote: TeamEventVoteType) {
        const eventVote = this.getVote(teamMember);
        return this.votesBeingUpdated[teamMember.id] === vote ||
            (
                eventVote !== undefined && eventVote.vote === vote &&
                (eventVote.isSaving || eventVote.isDeleting)
            );
    }

    getMembersByVoteType(type: TeamEventVoteType, includeInactive = false) {
        return this.team.getMembers(includeInactive).filter((member) => {
            return this.hasVoted(member, type);
        });
    }

    getMembersByVoteTypeSortedByName(type: TeamEventVoteType, includeInactive = false) {
        return Team.orderMembersByName(this.getMembersByVoteType(type, includeInactive));
    }

    getAddressUrlForGoogleMaps() {
        if (this.addressString) {
            return 'https://www.google.com/maps/search/?api=1&query=' + encodeURIComponent(this.addressString);
        }
    }

    getPageTitle() {
        switch (this.eventType) {
            case 'match':
                return this.matchTypeOption?.label + ' gegen ' + this.matchOpponentName;
            default:
                return this.title;
        }
    }

    getDisplayTitle() {
        switch (this.eventType) {
            case 'match':
                return this.matchTypeOption?.label;
            default:
                return this.title;
        }
    }

    getDisplaySubtitle() {
        switch (this.eventType) {
            case 'match':
                return this.matchOpponentName;
            default:
                return this.subText;
        }
    }

    hasDisplaySubtitle() {
        return !!this.getDisplaySubtitle();
    }

    private hasDateBegin(): boolean {
        return this.dateBeginFormatted !== '';
    }

    private hasTimeBegin(): boolean {
        return this.timeBeginFormatted !== '';
    }

    /**
     * We override the changes() function here to always transmit the sendChangeNotification attribute,
     * if added by the frontend. This attribute is only being used for commands and is not persisted.
     */
    changes(): ModelAttributeChangeSet<never> {
        const changes = super.changes();
        changes['sendChangeNotification'] = [null, this.sendChangeNotification];
        return changes;
    }
}
