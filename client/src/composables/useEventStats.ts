import { keepPreviousData, useQuery } from '@tanstack/vue-query';
import { queries } from 'src/queries';
import { computed, ComputedRef, Ref, ref, watch } from 'vue';
import { EventStatsFilter } from 'src/composables/useEventStatsFilter';
import { SelectOptionWithIcon } from 'src/models/BaseModel';
import TeamEventType = App.Types.TeamEventType;

interface EventStats {
  isInitialLoadedAfterTeamChanged: Ref<boolean>,
  isFetching: Ref<boolean>,
  isError: Ref<boolean>,
  totalEvents: Ref<number>,
  eventTypeOptionsNotZero: ComputedRef<SelectOptionWithIcon<TeamEventType>[]>,

  getEventSumByType(type: TeamEventType): number,
}

export function useEventStats(filter: EventStatsFilter): EventStats {
  const isInitialLoadedAfterTeamChanged = ref(false);
  watch(() => filter.selectedTeam.value, () => {
    isInitialLoadedAfterTeamChanged.value = false;
  });

  const {
    data: eventsQueryData,
    isFetching,
    isError,
  } = useQuery({
    ...queries.events.allForStatSums(filter),
    placeholderData: filter.isFilterValid ? keepPreviousData : [],
    enabled: filter.isFilterValid,
  });

  watch(() => isFetching.value, (value, oldValue) => {
    if (!value && oldValue) {
      isInitialLoadedAfterTeamChanged.value = true;
    }
  });

  const totalEvents = computed(() => {
    if (eventsQueryData.value && filter.selectedEventTypes.value.length > 0) {
      return eventsQueryData.value.filter((event) => filter.selectedEventTypes.value.includes(event.eventType)).length;
    }
    return eventsQueryData.value?.length ?? 0;
  });

  function getEventSumByType(type: TeamEventType) {
    if (eventsQueryData.value) {
      return eventsQueryData.value.filter((event) => event.eventType === type).length;
    }
    return 0;
  }

  const eventTypeOptionsNotZero = computed(() => {
    return filter.selectedEventTypeOptions.value.filter((option => getEventSumByType(option.value) > 0));
  });


  return {
    isInitialLoadedAfterTeamChanged,
    isFetching,
    isError,
    getEventSumByType,
    totalEvents,
    eventTypeOptionsNotZero,
  };
}
