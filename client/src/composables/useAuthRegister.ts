import { useMutation } from '@tanstack/vue-query';
import { api } from 'src/boot/axios';
import { LoginResponse } from 'src/stores/auth';
import { AxiosError } from 'axios';
import { APIError, isAlreadyRegistered, isNotInAllowlist, isUserCreationFailed } from 'src/queries/api-errors';
import { ref, Ref, toValue } from 'vue';
import { MaybeRef } from '@vueuse/core';

/**
 * This composeable handles the registration of a new user. The required data
 * for registration is passed in as refs from the component.
 */
export function useAuthRegister(
  firstname: Ref<string>,
  lastname: Ref<string>,
  email: Ref<string>,
  password: Ref<string>,
  passwordConfirmation: Ref<string>,
  inviteToken: MaybeRef<string | undefined>,
  noLogin: boolean,
  origin: Ref<string>,
  competition: Ref<string>,
  onSuccess: (response: LoginResponse) => void,
) {

  const successfullyRegistered = ref(false);
  const errorNotOnAllowList = ref(false);
  const registerErrorMessage = ref('');
  const registerErrors = ref<{
    firstname?: string[];
    lastname?: string[];
    email?: string[];
    password?: string[];
    passwordConfirmation?: string[];
  }>({});

  async function registerMutation(): Promise<LoginResponse> {
    return await api.post<LoginResponse>('/auth/register', {
      firstname: firstname.value,
      lastname: lastname.value,
      email: email.value,
      password: password.value,
      password_confirmation: passwordConfirmation.value,
      device_name: 'browser',
      no_login: noLogin,
      info_origin: origin.value,
      info_competition: competition.value,
      invite_token: toValue(inviteToken),
    }, {
        withCredentials: true
    }).then(data => data.data);
  }

  const { mutate: register, isPending: isRegistering } = useMutation({
    mutationFn: () => registerMutation(),
    onError: (error) => {
      successfullyRegistered.value = false;

      if (error instanceof AxiosError && error.response !== undefined) {
        const data = error.response.data as APIError;
        registerErrorMessage.value = '';
        registerErrors.value = data.errors;
        // We check the error here and show our own message instead of using the one from the backend,
        // such that it can be changed via i18n later.
        if (isNotInAllowlist(data)) {
          registerErrorMessage.value = 'Registrierung ist aktuell nur mit Einladung möglich.';
          errorNotOnAllowList.value = true;
        } else if (isAlreadyRegistered(data)) {
          registerErrorMessage.value = 'E-Mail Adresse ist bereits registriert, wurde aber nachträglich geändert.';
        } else if (isUserCreationFailed(data)) {
          registerErrorMessage.value = 'Es ist ein Fehler aufgetreten.';
        }
      }
      if (registerErrorMessage.value == '') {
        registerErrorMessage.value = 'Bitte prüfe deine Eingaben.';
      }
    },
    onSuccess: async (response) => {
      successfullyRegistered.value = true;
      registerErrors.value = {};
      registerErrorMessage.value = '';

      onSuccess(response);
    },
  });

  return {
    register,
    isRegistering,
    successfullyRegistered,
    registerErrors,
    registerErrorMessage,
    errorNotOnAllowList,
  };
}
