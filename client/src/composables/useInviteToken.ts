import Invite from 'src/models/Invite';
import { useQuery } from '@tanstack/vue-query';
import { queries } from 'src/queries';
import { messageInviteNotExistingOrAlreadyUsed } from 'src/composables/useInvite';
import { computed, reactive, Ref, ref, watch } from 'vue';
import { PersistedSpraypaintRecord } from 'spraypaint';

export interface InviteTokenInfo {
    invite: Ref<PersistedSpraypaintRecord<Invite<never>> | null>;
    isLoading: Ref<boolean>;
    errorMessage: Ref<string | undefined>;
}

export function useInviteToken(token: Ref<string>): InviteTokenInfo {
  const errorMessage = ref<string | undefined>();

  const enabled = computed(() => token.value.length > 0)
  const q = useQuery({
    ...queries.invites.forToken(token),
    initialData: null,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    enabled: enabled,
  });

  watch(() => q.isFetching.value, () => {
    if (!q.isFetching.value && q.data.value === null) {
      errorMessage.value = messageInviteNotExistingOrAlreadyUsed;
    }
  });

  return reactive({
    invite: q.data,
    isLoading: q.isFetching,
    errorMessage,
  });
}
