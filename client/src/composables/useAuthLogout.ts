import { useMutation } from '@tanstack/vue-query';
import { api } from 'src/boot/axios';
import { ROUTE_NAMES } from 'src/router/route-names';
import { useAuthStore } from 'src/stores/auth';
import { useRouter } from 'vue-router';

export function useAuthLogout() {

    const authStore = useAuthStore();
    const router = useRouter();

    async function logoutMutation() {
        await api.post('/auth/logout');
    }

    async function logoutDeviceMutation(deviceId: string) {
        await api.post('/auth/logout', {
            'device_id': deviceId,
        });
    }

    const { isPending: isLoggingOut, mutate: logout } = useMutation({
        mutationFn: () => logoutMutation,
        onSettled: async () => {
            await authStore.logout();

            await router.push({
                name: ROUTE_NAMES.LOGIN,
            });
        },
    });

    const logoutDevice = async (deviceId: string): boolean => {
        try {
            await api.post('/auth/logout', {
                'device_id': deviceId,
            });

            // Check if the device being logged out is the current device. If so,
            // clear the local auth information and redirect to the login page.
            if (authStore.installationId === deviceId) {
                await authStore.logout();

                await router.push({
                    name: ROUTE_NAMES.LOGIN,
                });
            }
        } catch (error) {
            console.error('Error logging out device', deviceId, error);
            return false;
        }

        return true;
    }

    return {
        logout,
        logoutDevice,
        isLoggingOut,
    };
}
