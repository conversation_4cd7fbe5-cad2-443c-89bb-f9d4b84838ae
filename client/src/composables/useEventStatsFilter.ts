import { computed, ComputedRef, ref, Ref, UnwrapRef, watch, watchEffect } from 'vue';
import Team from 'src/models/Team';
import { SelectOptionWithIcon } from 'src/models/BaseModel';
import { QueryObserverResult, useQuery } from '@tanstack/vue-query';
import { queries } from 'src/queries';
import { date, Quasar } from 'quasar';
import { defaultDateMask } from 'src/i18n';
import { TeamEventTypeOptions } from 'src/models/TeamEvent';
import TeamStatsRange from 'src/models/TeamStatsRange';
import { PersistedSpraypaintRecord } from 'spraypaint';
import TeamEventType = App.Types.TeamEventType;

export interface EventStatsFilter {
  selectedTeam: Ref<Team | null | undefined>,
  startDate: Ref<Date | undefined>,
  endDate: Ref<Date | undefined>,
  endDateOrToday: Ref<Date>,
  readonly selectedStatsRange: Ref<TeamStatsRange | null | undefined>,
  selectedEventTypes: Ref<TeamEventType[]>,
  teams: Ref<UnwrapRef<Team[] | undefined>>,
  isErrorTeams: Ref<boolean>,
  isLoadingTeams: Ref<boolean>,
  isFetchingTeams: Ref<boolean>,
  startDateString: Ref<string>,
  endDateString: Ref<string>,
  selectedEventTypeOptions: ComputedRef<SelectOptionWithIcon<TeamEventType>[]>,
  isFilterValid: Ref<boolean>,
  includeEndDateForLatestEntry: Ref<boolean>,

  setSelectedStatsRange(range?: TeamStatsRange | null): void,

  refetchTeams(): Promise<QueryObserverResult<PersistedSpraypaintRecord<Team>[], unknown>>

  getStartDateFormatted(mask?: string): string,

  getEndDateFormatted(mask?: string): string,

  getEndDateOrTodayFormatted(mask?: string): string,
}

export function useEventStatsFilter(selectedTeamId: string | undefined, autoSelectActiveTeamStatsRange = true): EventStatsFilter {

  const selectedTeam: Ref<Team | null | undefined> = ref<Team | null>();
  watch(() => selectedTeam.value, (newValue) => {
    selectedStatsRange.value = null;
    if (autoSelectActiveTeamStatsRange && newValue instanceof Team && newValue.activeStatsRange instanceof TeamStatsRange) {
      setSelectedStatsRange(newValue.activeStatsRange);
    }
  });
  const selectedStatsRange: Ref<TeamStatsRange | null | undefined> = ref<TeamStatsRange | null>();

  const {
    data: teams,
    isError: isErrorTeams,
    isLoading: isLoadingTeams,
    isFetching: isFetchingTeams,
    refetch: refetchTeams,
  } = useQuery({
    ...queries.teams.all(true, false, true),
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });

  watch(() => teams.value, (data) => {
    if (data) {
      const selectedTeamAsArray = data.filter((config) => config.id === selectedTeamId);
      const isSelectedTeamIncluded = selectedTeamAsArray.length > 0;

      if (isSelectedTeamIncluded) {
        selectedTeam.value = <Team>selectedTeamAsArray[0];
      } else if (data.length == 1) {
        selectedTeam.value = <Team>data[0];
      }
    }
  });

  const startDateString = ref(date.formatDate(date.buildDate({ month: 1, day: 1 }), defaultDateMask));
  const endDateString = ref('');
  const startDate = ref<Date>();
  const endDate = ref<Date>();
  watchEffect(() => {
    startDate.value = startDateString.value ? date.extractDate(startDateString.value, defaultDateMask) : undefined;
    endDate.value = endDateString.value ? date.extractDate(endDateString.value, defaultDateMask) : undefined;
  });

  const endDateOrToday = computed(() => {
    let endDateOrToday = new Date();
    if (endDate?.value instanceof Date) {
      endDateOrToday = date.getMinDate(endDateOrToday, endDate.value);
    }
    return date.adjustDate(endDateOrToday, { hour: 23, minutes: 59, seconds: 59 });
  });

  const selectedEventTypes = ref<TeamEventType[]>(TeamEventTypeOptions.map((option) => option.value));
  const selectedEventTypeOptions = computed(() => TeamEventTypeOptions.filter((option) => selectedEventTypes.value.includes(option.value)));

  const isFilterValid = computed(() => selectedTeam.value instanceof Team);

  function getStartDateFormatted(mask: string = defaultDateMask) {
    return date.formatDate(startDate.value, mask, Quasar.lang.date);
  }

  function getEndDateFormatted(mask: string = defaultDateMask) {
    return date.formatDate(endDate.value, mask, Quasar.lang.date);
  }

  function getEndDateOrTodayFormatted(mask: string = defaultDateMask) {
    return date.formatDate(endDateOrToday.value, mask, Quasar.lang.date);
  }

  function setSelectedStatsRange(range: TeamStatsRange | null = null) {
    selectedStatsRange.value = range;
    if (range instanceof TeamStatsRange) {
      startDateString.value = range.startDateFormatted;
      endDateString.value = range.endDateFormatted;
    }
  }

  const includeEndDateForLatestEntry = ref(false);

  return {
    teams,
    isErrorTeams,
    isLoadingTeams,
    isFetchingTeams,
    refetchTeams,
    selectedTeam,
    startDateString,
    startDate,
    endDateString,
    endDate,
    endDateOrToday,
    selectedStatsRange,
    setSelectedStatsRange,
    selectedEventTypes,
    selectedEventTypeOptions,
    isFilterValid,
    includeEndDateForLatestEntry,
    getStartDateFormatted,
    getEndDateFormatted,
    getEndDateOrTodayFormatted,
  };
}
