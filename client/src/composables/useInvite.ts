import Invite, { Invitable } from 'src/models/Invite';
import { useMutation, useQuery } from '@tanstack/vue-query';
import { api } from 'src/boot/axios';
import { AxiosError } from 'axios';
import { APIError, isError } from 'src/queries/api-errors';
import { ROUTE_NAMES } from 'src/router/route-names';
import { useAuthStore } from 'src/stores/auth';
import { useRouter } from 'vue-router';
import { computed, Ref, ref, toValue, watch } from 'vue';
import TeamMember from 'src/models/TeamMember';
import { QInput } from 'quasar';
import { queries } from 'src/queries';
import { confirmBeforeAction } from 'src/helpers/dialogs';

type AcceptInviteQuery = {
  token: string
}

type AcceptInviteResponse = {
  message: string
}
type AcceptInviteQueryTeamWithExistingMember = AcceptInviteQuery & {
  memberId: string
}
type AcceptInviteQueryTeamWithNewMember = AcceptInviteQuery & {
  newMemberName: string
}
type AcceptInviteQueryTeam = AcceptInviteQueryTeamWithExistingMember | AcceptInviteQueryTeamWithNewMember

export const messageInviteNotExistingOrAlreadyUsed = 'Das Einladungstoken existiert nicht oder wurde schon verwendet.';

export function useInvite<InvitableType extends Invitable<InvitableType>>(invite: Invite<InvitableType>) {

  const inviteInternal = ref(toValue(invite));
  const enabled = invite.isTeamInvite();

  const {
    data,
    isPending: isLoadingTeamDetails,
    isError: isErrorTeamDetails,
    refetch: refetchTeamDetails,
  } = useQuery({
    ...queries.invites.forToken(invite.token, true),
    enabled: enabled,
  });

  watch(() => data.value, (data) => {
    if (data) {
      inviteInternal.value = data;
    }
  });


  const authStore = useAuthStore();
  const router = useRouter();
  const errorMessage = ref('');

  const isLoadingAcceptingInvite = ref(false);
  const loadingAcceptingMember = ref<TeamMember>();
  const isLoadingAcceptingNewMember = ref(false);
  const isTokenNotFound = (errorData: APIError) => isError(errorData, 'invalid_invite_token');
  const isAcceptingInviteFailed = (errorData: APIError) => isError(errorData, 'accepting_invite_failed');

  function isLoadingAcceptingMember(member?: TeamMember) {
    if (member === undefined) {
      return loadingAcceptingMember.value !== undefined;
    } else {
      return loadingAcceptingMember.value === member;
    }
  }

  function acceptInvite() {
    isLoadingAcceptingInvite.value = true;
    const data: AcceptInviteQuery = {
      token: inviteInternal.value.token,
    };
    acceptInviteMutation.mutate(data);
  }

  function acceptInviteWithMember(member: TeamMember) {
    loadingAcceptingMember.value = member;
    const data: AcceptInviteQueryTeamWithExistingMember = {
      token: inviteInternal.value.token,
      memberId: member.id,
    };
    acceptInviteMutation.mutate(data);
  }

  const newMemberNameInput = ref<QInput>();
  const newMemberName = ref('');

  function acceptInviteWithNewMember() {
    if (newMemberNameInput.value) {
      newMemberNameInput.value.validate();
      if (!newMemberNameInput.value.hasError) {
        isLoadingAcceptingNewMember.value = true;
        const data: AcceptInviteQueryTeamWithNewMember = {
          token: inviteInternal.value.token,
          newMemberName: newMemberName.value,
        };
        acceptInviteMutation.mutate(data);
      }
    }
  }

  const acceptInviteMutation = useMutation<
    AcceptInviteResponse,
    unknown,
    AcceptInviteQuery | AcceptInviteQueryTeam
  >({
    mutationFn: (data) =>
      api.post<AcceptInviteResponse>('/invite/accept', {
        ...data,
      }).then(data => data.data),
    onError: (error) => {
      // TODO(fabzo): Display error message

      // console.log('onError: - accepting invite failed', error, data)
      const defaultMessage =
        'Das hat leider nicht funktioniert, wenn der Fehler weiterhin auftritt, bitte deinen Trainer den Link neu zu erstellen';

      if (error instanceof AxiosError && error.response !== undefined) {
        const data = error.response.data as APIError;

        // We check the error here and show our own message instead of using the one from the backend,
        // such that it can be changed via i18n later.
        if (isTokenNotFound(data)) {
          errorMessage.value = messageInviteNotExistingOrAlreadyUsed;
        } else if (isAcceptingInviteFailed(data)) {
          errorMessage.value = defaultMessage;
        }
      }
      if (errorMessage.value == '') {
        errorMessage.value = defaultMessage; // unknown error
      }
    },
    onSuccess: async () => {
      await authStore.fetchUserData();
      if (invite.isTeamInvite()) {
        await confirmBeforeAction({
          dialogOptions: {
            title: 'Einladung angenommen',
            message: 'Möchtest du noch ein weiteres Teammitglied hinzufügen?',
            cancel: 'Nein',
            ok: 'Ja',
          },
          onCancel: () => router.push({ name: ROUTE_NAMES.HOME }),
          onOk: () => {
            newMemberName.value = '';
            newMemberNameInput.value?.resetValidation();
            refetchTeamDetails();
            return Promise.resolve();
          },
        });
      } else {
        await router.push({ name: ROUTE_NAMES.HOME });
      }
    },
    onSettled: () => {
      isLoadingAcceptingInvite.value = false;
      loadingAcceptingMember.value = undefined;
      isLoadingAcceptingNewMember.value = false;
    },
  });

  const invitable = computed(() => inviteInternal.value.invitable);

  return {
    invitable: invitable as Ref<InvitableType>,
    acceptInvite,
    isLoadingAcceptingInvite,
    errorMessage,
    messageInviteNotExistingOrAlreadyUsed,
    teamInvite: {
      acceptInviteWithMember,
      acceptInviteWithNewMember,
      newMemberNameInput,
      newMemberName,
      isLoadingTeamDetails,
      isErrorTeamDetails,
      isLoadingAcceptingMember,
      isLoadingAcceptingNewMember,
    },
  };
}
