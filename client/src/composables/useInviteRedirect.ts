import { InviteTokenInfo, useInviteToken } from 'src/composables/useInviteToken';
import { useRouter } from 'vue-router';
import { ROUTE_NAMES } from 'src/router/route-names';
import { computed, ComputedRef, Ref } from 'vue';

interface InviteRedirect {
  isRedirectToInvite: ComputedRef<boolean>;
  token: ComputedRef<string>,
  inviteInfo: InviteTokenInfo
}

export function useInviteRedirect(redirectUrl: Ref<string>): InviteRedirect {
  const router = useRouter();

  const location = computed(() => router.resolve({ path: redirectUrl.value }));
  const isRedirectToInvite = computed(() => location.value.name === ROUTE_NAMES.INVITE);
  const token = computed(() => isRedirectToInvite.value ? location.value.params.token as string : '');

  const inviteInfo = useInviteToken(token);

  return {
    isRedirectToInvite,
    token,
    inviteInfo,
  };

}

