import { useAuthStore } from 'src/stores/auth';
import { Capacitor } from '@capacitor/core';
import { Device } from '@capacitor/device';
import { api } from 'src/boot/axios';

export function useDeviceInfo() {

  const authStore = useAuthStore();

  async function updateRemote() {
    let deviceInfo;
    let languageTag;

    //console.log('Plugin: ' + Capacitor.isPluginAvailable('Device'))

    if (Capacitor.isPluginAvailable('Device')) {
      deviceInfo = await Device.getInfo();
      languageTag = await Device.getLanguageTag();
    }

    const deviceRequestData = deviceInfo ? {
      device_name: deviceInfo.name,
      device_model: deviceInfo.model,
      device_platform: deviceInfo.platform,
      device_os: deviceInfo.operatingSystem,
      device_os_version: deviceInfo.osVersion,
      device_language: languageTag?.value ?? '',
      device_manufacturer: deviceInfo.manufacturer,
    } : {};

    const fcmToken = authStore.fcmToken;
    const installation_id = authStore.installationId;

    try {
      await api.post('/user/update-device-info', {
        ...deviceRequestData,
        fcm_token: fcmToken,
        installation_id: installation_id,
      }, {
        timeout: 2000, // 2 seconds
      });
    } catch (error) {
      console.error('Error updating device info', error);
    }
  }

  return {
    updateRemote,
  };
}
