import { useMutation } from '@tanstack/vue-query';
import { api } from 'src/boot/axios';
import { LoginResponse, useAuthStore } from 'src/stores/auth';
import { useRouter } from 'vue-router';
import { AxiosError } from 'axios';
import { APIError } from 'src/queries/api-errors';
import { ref, Ref, toValue } from 'vue';
import { Device } from '@capacitor/device';
import { Capacitor } from '@capacitor/core';
import { MaybeRef } from '@vueuse/core';

// eslint-disable-next-line @typescript-eslint/no-empty-function
export function useAuthLogin(email: Ref<string>, password: Ref<string>, redirectTo: MaybeRef<string>, onError: () => void = () => {
}) {

    const authStore = useAuthStore();
    const router = useRouter();

    const loginErrors = ref<{
        email?: string[];
    }>({});

    async function loginMutation(): Promise<LoginResponse> {
        const baseRequestData = {
            email: email.value,
            password: password.value,
            fcm_token: authStore.fcmToken,
            installation_id: authStore.installationId,
        };

        let deviceInfo;
        let languageTag;

        if (Capacitor.isPluginAvailable('PushNotifications')) {
            deviceInfo = await Device.getInfo();
            languageTag = await Device.getLanguageTag();
        }

        const deviceRequestData = deviceInfo ? {
            device_name: deviceInfo.name,
            device_model: deviceInfo.model,
            device_platform: deviceInfo.platform,
            device_os: deviceInfo.operatingSystem,
            device_os_version: deviceInfo.osVersion,
            device_language: languageTag?.value ?? '',
            device_manufacturer: deviceInfo.manufacturer,
        } : {};

        const requestData = { ...baseRequestData, ...deviceRequestData };

        return api.post<LoginResponse>('/auth/login', requestData).then(data => data.data);
    }

    const { isPending: isLoggingIn, mutate: login } = useMutation({
        mutationFn: () => loginMutation(),
        onError: (error) => {
            console.log(error);
            if (error instanceof AxiosError && error.response !== undefined) {
                const data = error.response.data as APIError;
                loginErrors.value = data.errors;
            }

            onError();
        },
        onSuccess: async (response) => {
            loginErrors.value = {};
            await useAuthStore().initializeUserSession(response.token, response.userId);
            await router.push({
                path: toValue(redirectTo),
            });
        },
    });

    return {
        login,
        loginErrors,
        isLoggingIn,
    };
}
