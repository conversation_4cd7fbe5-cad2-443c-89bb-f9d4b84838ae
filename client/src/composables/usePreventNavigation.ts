import { onBeforeMount, onBeforeUnmount, Ref } from 'vue';
import { onBeforeRouteLeave } from 'vue-router';
import { confirmBeforeAction } from 'src/helpers/dialogs';
import { QDialogOptions } from 'quasar';

export function usePreventNavigation(
  preventNavigation: Ref<boolean>,
  dialogOptions?: QDialogOptions,
) {
  const defaultOptions = {
    title: 'Änderungen wurden nicht gespeichert, Seite verlassen?',
  };

  onBeforeMount(() => {
    window.addEventListener('beforeunload', preventNav);
  });

  onBeforeUnmount(() => {
    window.removeEventListener('beforeunload', preventNav);
  });

  onBeforeRouteLeave((to, from, next) => {
    if (preventNavigation.value) {
      confirmBeforeAction<boolean>({
        dialogOptions: { ...defaultOptions, ...dialogOptions },
        onOk: () => true,
        onCancel: () => false,
        onDismiss: () => false,
      }).then(allowNavigation => {
        if (allowNavigation) {
          next();
        }
      });
    } else {
      next();
    }
  });

  function preventNav(event: BeforeUnloadEvent) {
    if (preventNavigation.value) {
      event.preventDefault();
      event.returnValue = '';
    }
  }
}
