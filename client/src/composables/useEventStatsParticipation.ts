import { computed, ComputedRef, ref, Ref, watch } from 'vue';
import TeamMember from 'src/models/TeamMember';
import TeamEventVote from 'src/models/TeamEventVote';
import { useAuthStore } from 'src/stores/auth';
import { QTableColumn } from 'quasar';
import TeamEvent from 'src/models/TeamEvent';
import { keepPreviousData, useQuery } from '@tanstack/vue-query';
import { queries } from 'src/queries';
import { EventStatsFilter } from 'src/composables/useEventStatsFilter';
import TeamEventType = App.Types.TeamEventType;
import TeamEventVoteType = App.Types.TeamEventVoteType;

export interface ExtendedQTableColumn<Row extends Record<string, unknown>> extends QTableColumn<Row> {
  eventType?: TeamEventType;
  eventTooltip?: string;
  voteSumYes: number;
  voteSumPerStatusRoleName: { TeamRoleType: number }
}

export type StatisticRow = {
  id: string,
  name: string,
  member: TeamMember
  participation: number,
  selected: boolean,
  eventVotes: { [key: string]: TeamEventVoteType }
}

export interface EventParticipationStats {
  isInitialLoadedAfterTeamChanged: Ref<boolean>,
  hasTeamEvents: ComputedRef<boolean>,
  isFetching: Ref<boolean>,
  isError: Ref<boolean>,
  columns: ComputedRef<ExtendedQTableColumn<StatisticRow>[]>,
  rows: ComputedRef<StatisticRow[]>,
  selected: ComputedRef<StatisticRow[]>,
  showSumYesPerStatusRole: Ref<boolean>

  getParticipationPercent(participation: number): number,
}

export function useEventStatsParticipation(filter: EventStatsFilter): EventParticipationStats {

  const isInitialLoadedAfterTeamChanged = ref(false);
  watch(() => filter.selectedTeam.value, () => {
    isInitialLoadedAfterTeamChanged.value = false;
  });

  const {
    data: eventsQueryData,
    isFetching,
    isError,
  } = useQuery({
    ...queries.events.allForStatsParticipation(filter),
    placeholderData: filter.isFilterValid ? keepPreviousData : [],
    enabled: filter.isFilterValid,
  });

  watch(() => isFetching.value, (value, oldValue) => {
    if (!value && oldValue) {
      isInitialLoadedAfterTeamChanged.value = true;
    }
  });

  const hasTeamEvents = computed(() => eventsQueryData.value !== undefined && eventsQueryData.value.length > 0);
  const showSumYesPerStatusRole = ref(false);

  function getParticipationForMember(member: TeamMember) {
    if (eventsQueryData.value) {
      return eventsQueryData.value?.filter((event) => event.hasVoted(member, 'yes')).length;
    }
    return 0;
  }

  function getParticipationPercent(participation: number) {
    if (eventsQueryData.value && eventsQueryData.value.length > 0) {
      return Math.round(participation / eventsQueryData.value.length * 100);
    }
    return 0;
  }

  function getVotesPerEventForMember(member: TeamMember) {
    return eventsQueryData.value?.reduce((newArray: { [key: string]: TeamEventVoteType }, event) => {
      newArray[event.id] = event.getVoteType(member);
      return newArray;
    }, {});
  }

  function getVoteCommentsPerEventForMember(member: TeamMember) {
    return eventsQueryData.value?.reduce((newArray: { [key: string]: string }, event) => {
      if (event.hasVoteComment(member)) {
        newArray[event.id] = (event.getVote(member) as TeamEventVote).comment?.text as string;
      }
      return newArray;
    }, {});
  }


  function getVoteSumPerStatusRoleForEvent(event: TeamEvent, includeInactive = true) {
    return event.getStatusRolesWithVotes('yes', includeInactive).reduce((newArray: { [key: string]: number }, roleName) => {
      newArray[roleName] = event.getVoteSumForStatusRole('yes', roleName, includeInactive);
      return newArray;
    }, {});
  }

  const authStore = useAuthStore();

  const columns: ComputedRef<ExtendedQTableColumn<StatisticRow>[]> = computed(() => {
    if (eventsQueryData.value) {
      return [
        { name: 'member', label: 'Mitglied', field: 'name', sortable: true, align: 'left' },
        { name: 'participation', label: 'Teilnahme', field: 'participation', sortable: true, align: 'left' },
        ...eventsQueryData.value.map((event) => {
          return {
            name: event.id,
            eventType: event.eventType,
            eventTooltip: event.getPageTitle(),
            voteSumYes: event.getVoteSum('yes', true),
            voteSumPerStatusRoleName: getVoteSumPerStatusRoleForEvent(event),
            label: event.getDateBeginFormatted('DD.MM.'),
            align: 'center',
            field: row => row['eventVotes'][event.id],
          } as ExtendedQTableColumn<StatisticRow>;
        }),
      ];
    }
    return [];
  });

  const rows = computed(() =>
    filter.selectedTeam.value?.membersSortedByName.map((member => ({
      id: member.id,
      name: member.name,
      member: member,
      participation: getParticipationForMember(member),
      selected: authStore.isOwnMember(member),
      eventVotes: getVotesPerEventForMember(member),
      voteComments: getVoteCommentsPerEventForMember(member),
    }))) as StatisticRow[]);

  const selected = computed(() => rows.value?.filter((row) => row.selected));


  return {
    isInitialLoadedAfterTeamChanged,
    hasTeamEvents,
    isFetching,
    isError,
    getParticipationPercent,
    columns,
    rows,
    selected,
    showSumYesPerStatusRole,
  };
}
