import { useMutation } from '@tanstack/vue-query';
import { api } from 'src/boot/axios';
import { AxiosError } from 'axios';
import { APIError } from 'src/queries/api-errors';
import { computed, ref, Ref, watch } from 'vue';
import WaitListMailType = App.Types.WaitListMailType;

export type AddToWaitListResponse = {
  mailState: WaitListMailType
}

export function useAuthWaitList(
  email: Ref<string>,
) {
  const mailState: Ref<WaitListMailType> = ref('unknown');
  const isRegistrationAllowed = computed(() => mailState.value === 'alreadyOnInviteList');

  watch(email, () => {
    mailState.value = 'unknown';
  });

  const errorMessage = ref('');
  const errors = ref<{
    email?: string[];
  }>({});

  async function addToWaitListMutation(): Promise<AddToWaitListResponse> {
    return await api.post<AddToWaitListResponse>('/auth/addToWaitList', {
      email: email.value,
    }).then(data => data.data);
  }

  const {
    mutate: checkMailAndAddToWaitList,
    isPending: isAddingToWaitList,
  } = useMutation({
    mutationFn: () => addToWaitListMutation(),
    onError: (error) => {
      mailState.value = 'unknown';
      if (error instanceof AxiosError && error.response !== undefined) {
        const data = error.response.data as APIError;
        errorMessage.value = '';
        errors.value = data.errors;
        // We check the error here and show our own message instead of using the one from the backend,
        // such that it can be changed via i18n later.
      }
      if (errorMessage.value == '') {
        errorMessage.value = 'Bitte prüfe deine Eingaben.';
      }
    },
    onSuccess: async (response) => {
      mailState.value = response.mailState;

      errors.value = {};
      errorMessage.value = '';
    },
  });

  return {
    checkMailAndAddToWaitList,
    isLoading: isAddingToWaitList,
    mailState,
    isRegistrationAllowed,
    errors,
    errorMessage,
  };
}
