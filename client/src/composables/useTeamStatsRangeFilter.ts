import { EventStatsFilter } from 'src/composables/useEventStatsFilter';
import { computed, ref, watch } from 'vue';
import { useQuery } from '@tanstack/vue-query';
import { queries } from 'src/queries';
import TeamStatsRange from 'src/models/TeamStatsRange';
import Team from 'src/models/Team';

export function useTeamStatsRangeFilter(filter: EventStatsFilter) {
  const isInitialLoadedAfterTeamChanged = ref(false);
  watch(() => filter.selectedTeam.value, () => {
    isInitialLoadedAfterTeamChanged.value = false;
  });

  const statsRangesData = ref<TeamStatsRange[]>([]);

  const {
    isError: isStatsRangesError,
    refetch: refetchStatsRanges,
    isRefetching: isStatsRangesRefetching,
    isFetching,
  } = useQuery({
    ...queries.teams.allStatsRanges(filter.selectedTeam),
    select: (data) => {
      statsRangesData.value = data;
    },
    enabled: computed(() => filter.selectedTeam.value instanceof Team),
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });

  watch(() => isFetching.value, (value, oldValue) => {
    if (!value && oldValue) {
      isInitialLoadedAfterTeamChanged.value = true;
    }
  });

  return {
    isInitialLoadedAfterTeamChanged,
    isStatsRangesError,
    statsRangesData,
    isStatsRangesRefetching,
    refetchStatsRanges,
  };
}
