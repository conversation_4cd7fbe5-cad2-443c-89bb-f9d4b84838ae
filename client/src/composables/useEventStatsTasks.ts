import { EventStatsFilter } from 'src/composables/useEventStatsFilter';
import { computed, ComputedRef, Ref, ref, watch } from 'vue';
import { keepPreviousData, useQuery } from '@tanstack/vue-query';
import { queries } from 'src/queries';
import TeamEventTaskConfig from 'src/models/TeamEventTaskConfig';
import { PersistedSpraypaintRecord } from 'spraypaint';
import TeamMember from 'src/models/TeamMember';
import TeamEventTask from 'src/models/TeamEventTask';
import { useAuthStore } from 'src/stores/auth';
import { QTableColumn } from 'quasar';
import { groupBy, orderBy } from 'lodash';
import TeamEvent from 'src/models/TeamEvent';
import TeamEventVoteType = App.Types.TeamEventVoteType;

type StatisticRow = {
  id: string,
  name: string,
  member: TeamMember
  sum: number,
  selected: boolean,
  eventTasks: TeamEventTask[]
  voteType?: TeamEventVoteType,
}

export interface EventTasksStats {
  isInitialLoadedAfterTeamChanged: Ref<boolean>,
  taskConfigQueryData: Ref<PersistedSpraypaintRecord<TeamEventTaskConfig>[]> | Ref<undefined>,
  hasTaskConfigs: ComputedRef<boolean>,
  isFetchingTaskConfigs: Ref<boolean>,
  isErrorTaskConfigs: Ref<boolean>,
  isFetchingTasks: Ref<boolean>,
  isErrorTasks: Ref<boolean>,
  refetchTasks: CallableFunction,
  columns: ComputedRef<QTableColumn<StatisticRow>[]>,
  rows: ComputedRef<StatisticRow[]>,
  selected: ComputedRef<StatisticRow[]>,
  selectedTaskConfig: Ref<TeamEventTaskConfig | null | undefined>,
  latestEntryForSelectedConfig: ComputedRef<TeamEventTask | null>,
}

export function useEventStatsTasks(filter: EventStatsFilter, selectedTaskConfigId?: string, eventForVotes?: Ref<TeamEvent | undefined>): EventTasksStats {

  const isInitialLoadedAfterTeamChanged = ref(false);
  watch(() => filter.selectedTeam.value, () => {
    isInitialLoadedAfterTeamChanged.value = false;
  });

  const selectedTaskConfig: Ref<TeamEventTaskConfig | null | undefined> = ref<TeamEventTaskConfig | null>();

  const {
    data: taskConfigQueryData,
    isFetching: isFetchingTaskConfigs,
    isError: isErrorTaskConfigs,
  } = useQuery({
    ...queries.eventTasks.allConfigsForTeam(filter.selectedTeam.value?.id as string),
    placeholderData: filter.isFilterValid ? keepPreviousData : [],
    enabled: filter.isFilterValid,
  });

  watch(() => taskConfigQueryData.value, (data) => {
    if (data) {
      const selectedTaskConfigAsArray = data.filter((config) => config.id === selectedTaskConfigId);
      const isSelectedTaskConfigIncluded = selectedTaskConfigAsArray.length > 0;

      if (isSelectedTaskConfigIncluded) {
        selectedTaskConfig.value = selectedTaskConfigAsArray[0];
      } else {
        selectedTaskConfig.value = data[0];
      }
      isInitialLoadedAfterTeamChanged.value = true;
    }
  });

  const hasTaskConfigs = computed(() => taskConfigQueryData.value !== undefined && taskConfigQueryData.value.length > 0);

  const {
    data: tasksQueryData,
    isFetching: isFetchingTasks,
    isError: isErrorTasks,
    refetch: refetchTasks,
  } = useQuery({
    ...queries.eventTasks.allForStatsTasks(filter),
    placeholderData: hasTaskConfigs.value ? keepPreviousData : [],
    enabled: hasTaskConfigs,
  });

  watch(() => isFetchingTasks.value, (value, oldValue) => {
    if (!value && oldValue) {
      isInitialLoadedAfterTeamChanged.value = true;
    }
  });

  function getTasksForMember(member: TeamMember) {
    if (tasksQueryData.value) {
      return tasksQueryData.value.filter((task) => {
        return task.teamMember?.id === member.id;
      });
    }
    return [];
  }

  function getTasksPerTaskConfigForMember(member: TeamMember): { [key: string]: TeamEventTask[] } {
    return groupBy(getTasksForMember(member), function(task) {
      return task.config.id;
    });
  }

  function getTasksForTaskConfigAndMember(config: TeamEventTaskConfig, member: TeamMember) {
    const tasksPerTaskConfigForMember = getTasksPerTaskConfigForMember(member);
    if (tasksPerTaskConfigForMember[config.id]) {
      return orderBy(
        tasksPerTaskConfigForMember[config.id],
        task => task.teamEvent.getDateTimeStart(),
        'desc');
    }
    return [];
  }

  function getTaskSumForTaskConfigAndMember(config: TeamEventTaskConfig, member: TeamMember) {
    const tasksPerTaskConfigForMember = getTasksPerTaskConfigForMember(member);
    return tasksPerTaskConfigForMember[config.id]?.length ?? 0;
  }

  const authStore = useAuthStore();

  const columns: ComputedRef<QTableColumn<StatisticRow>[]> = computed(() => {
    if (tasksQueryData.value) {
      const columns = [
        { name: 'member', label: 'Mitglied', field: 'name', sortable: true, align: 'left' },
        { name: 'sum', label: 'Anzahl', field: 'sum', sortable: true, align: 'left' },
        eventForVotes?.value ? { name: 'vote', label: 'Teilnahme', field: 'voteType', sortable: false, align: 'left' } : undefined,
        { name: 'entries', label: 'Einträge', field: 'eventTasks', sortable: false, align: 'left' },
      ] as QTableColumn<StatisticRow>[];
      return columns.filter((column) => column !== undefined);
    }
    return [];
  });

  const rows = computed(() => {
    if (selectedTaskConfig.value) {
      return filter.selectedTeam.value?.membersSortedByName.map((member => {
        if (selectedTaskConfig.value) {
          return ({
            id: member.id,
            name: member.name,
            member: member,
            sum: getTaskSumForTaskConfigAndMember(selectedTaskConfig.value, member),
            selected: authStore.isOwnMember(member),
            eventTasks: getTasksForTaskConfigAndMember(selectedTaskConfig.value, member),
            voteType: eventForVotes?.value ? eventForVotes.value.getVoteType(member) : undefined,
          });
        }
      })) as StatisticRow[];
    }
    return [];
  });

  const selected = computed(() => rows.value?.filter((row) => row.selected));

  const latestEntryForSelectedConfig = computed(() => {
    if (selectedTaskConfig.value) {

      const endDateOrTodayStartOfDay = new Date(filter.endDateOrToday.value);
      endDateOrTodayStartOfDay.setHours(0, 0, 0, 0);

      const tasks = tasksQueryData.value?.filter((task) => {
        return task.config.id === selectedTaskConfig.value?.id
          && (filter.includeEndDateForLatestEntry.value || <Date>task.teamEvent.getDateTimeStart() <= endDateOrTodayStartOfDay);
      });

      if (tasks && tasks.length > 0) {
        const tasksOrderedByDate = orderBy(
          tasks,
          task => task.teamEvent.getDateTimeStart(),
          'desc');

        return tasksOrderedByDate[0];
      }
    }
    return null;
  });


  return {
    isInitialLoadedAfterTeamChanged,
    taskConfigQueryData,
    selectedTaskConfig,
    hasTaskConfigs,
    isFetchingTaskConfigs,
    isErrorTaskConfigs,
    isFetchingTasks,
    isErrorTasks,
    refetchTasks,
    columns,
    rows,
    selected,
    latestEntryForSelectedConfig,
  };
}
