import { Frequency, RRule, Weekday } from 'rrule';
import { Options as RRuleOptions } from 'rrule/dist/esm/types';
import { ComputedRef, toValue, watchEffect } from 'vue';
import { date, Quasar, useQuasar } from 'quasar';
import { isDate, isEmpty, sortBy } from 'lodash';
import { defaultDateMask } from 'src/i18n';


type RecurringMergeOption = {
  label: string,
  mergeOptions: Partial<RRuleOptions>
}

export type RecurringMonthMergeOption = RecurringMergeOption & {
  value: 'sameNthDay' | 'sameNthWeekday',
}

export type RecurringFrequencyMergeOption = RecurringMergeOption & {
  value: Frequency,
}

export type RecurringOption = {
  label: string
  options: Partial<RRuleOptions>
}

export const defaultNonRecurringOptionTemplate: RecurringOption = {
  label: 'Einmalig',
  options: {},
};
export const CustomRecurringOptionLabel = 'Benutzerdefiniert';

export const RecurringWeekDayOptions: Weekday[] = [
  RRule.MO,
  RRule.TU,
  RRule.WE,
  RRule.TH,
  RRule.FR,
  RRule.SA,
  RRule.SU,
];

export function useRecurring(
  rruleRefOrGetter: ComputedRef<RRule | null | undefined> | RRule | null | undefined,
) {

  let options: Partial<RRuleOptions>;
  watchEffect(() => {
    const rrule = toValue(rruleRefOrGetter) || new RRule();
    options = rrule.origOptions;
  });

  function getRecurringFrequencyOptions(): RecurringFrequencyMergeOption[] {
    const count = options.interval;
    const mergeOptions: Partial<RRuleOptions> = {
      bymonthday: null,
      byweekday: [],
    };
    return [
      {
        value: Frequency.DAILY,
        label: count === 1 ? 'Tag' : 'Tage',
        mergeOptions: { freq: Frequency.DAILY, ...mergeOptions },
      },
      {
        value: Frequency.WEEKLY,
        label: count === 1 ? 'Woche' : 'Wochen',
        mergeOptions: { freq: Frequency.WEEKLY, ...mergeOptions },
      },
      {
        value: Frequency.MONTHLY,
        label: count === 1 ? 'Monat' : 'Monate',
        mergeOptions: { freq: Frequency.MONTHLY, ...mergeOptions },
      },
    ];
  }

  function getSelectedRecurringFrequencyOption(): RecurringFrequencyMergeOption | undefined {
    const availableOptions = getRecurringFrequencyOptions();
    if (options.freq !== undefined) {
      return availableOptions.find(
        element => element.value === options.freq,
      );
    } else {
      return undefined;
    }
  }

  function getRangeString(): string {
    if (options.dtstart) {
      const dateBeginFormatted = date.formatDate(options.dtstart, defaultDateMask, Quasar.lang.date);
      if (!options.until) {
        return 'ab ' + dateBeginFormatted;
      } else {
        const dateEndFormatted = date.formatDate(options.until, defaultDateMask, Quasar.lang.date);
        return dateBeginFormatted + ' - ' + dateEndFormatted;
      }
    }
    return '';
  }

  function getRecurrenceText(): string {
    // could be much more complex like this which is only available in english: https://github.com/jkbrzt/rrule/blob/master/src/nlp/totext.ts
    const $q = useQuasar();
    const text: string[] = [];

    if (options.freq === Frequency.WEEKLY) {
      if (options.interval === undefined || options.interval === 1) {
        text.push('Jede Woche');
      } else {
        text.push('Alle');
        text.push(options.interval.toString());
        text.push('Wochen');
      }

      if (Array.isArray(options.byweekday)) {
        sortBy(options.byweekday, 'weekday')
          .forEach(function(day, index, array) {
            const isLastElement = index === array.length - 1;
            let maybeComma = '';
            if (!isLastElement) {
              maybeComma = ',';
            }
            const weekday: Weekday = day as Weekday;
            text.push($q.lang.date.daysShort[weekday.getJsWeekday()] + maybeComma);
          });
      }
    } else if (options.freq === Frequency.MONTHLY) {
      if (options.interval === undefined || options.interval === 1) {
        text.push('Jeden Monat');
      } else {
        text.push('Alle');
        text.push(options.interval.toString());
        text.push('Monate');
      }
      text.push('am');
      text.push(getSelectedRecurringMonthOption()?.label as string);
    }

    return text.join(' ');
  }

  function getRecurringMonthOptions(): RecurringMonthMergeOption[] {
    const day = options.dtstart as Date;
    if (day === undefined) {
      return [];
    }
    const dayNumberStartingMonday = (day.getDay() || 7) - 1;
    const sameDayString = +day.getDate() + '.';
    const nthWeekday = Math.ceil((day.getDate() / 7));
    const nth = nthWeekday > 4 ? -1 : nthWeekday;
    let sameWeekdayString;
    // TODO: letzten XY ggf. als eigene Option immer wenn es der letzte ist.
    if (nthWeekday > 4) {
      sameWeekdayString = 'letzten ' + useQuasar().lang.date.days[day.getDay()];
    } else {
      sameWeekdayString = nthWeekday + '. ' + useQuasar().lang.date.days[day.getDay()];
    }
    return [
      {
        value: 'sameNthDay',
        label: sameDayString,
        mergeOptions: {
          bymonthday: day.getDate(),
          byweekday: [],
        },
      },
      {
        value: 'sameNthWeekday',
        label: sameWeekdayString,
        mergeOptions: {
          bymonthday: undefined,
          byweekday: RecurringWeekDayOptions[dayNumberStartingMonday]
            .nth(nth),
        },
      },
    ];
  }

  function getSelectedRecurringMonthOption(): RecurringMonthMergeOption | undefined {
    const availableOptions = getRecurringMonthOptions();
    if (options.bymonthday !== null && options.bymonthday !== undefined) {
      return availableOptions.find(
        element => element.value === 'sameNthDay',
      );
    } else if (!isEmpty(options.byweekday)) {
      return availableOptions.find(
        element => element.value === 'sameNthWeekday',
      );
    } else {
      return undefined;
    }
  }

  function isDateBeginSet() {
    return isDate(options.dtstart);
  }

  function getRecurringOptionTemplates(): RecurringOption[] {
    // return static array for now so we can add specific options anytime
    return [
      defaultNonRecurringOptionTemplate,
      {
        label: 'Jede Woche',
        options: {
          freq: Frequency.WEEKLY,
          byweekday: [],
        },
      },
      {
        label: 'Alle zwei Wochen',
        options: {
          freq: Frequency.WEEKLY,
          interval: 2,
          byweekday: [],
        },
      },
      {
        label: 'Jeden Monat',
        options: {
          freq: Frequency.MONTHLY,
        },
      },
      {
        label: CustomRecurringOptionLabel,
        options: {
          freq: Frequency.WEEKLY,
          interval: 1,
          byweekday: [],
        },
      },
    ];
  }

  return {
    isDateBeginSet,
    getRangeString,
    getRecurrenceText,
    getRecurringMonthOptions,
    getSelectedRecurringMonthOption,
    getRecurringFrequencyOptions,
    getSelectedRecurringFrequencyOption,
    getRecurringOptionTemplates,
  };
}
