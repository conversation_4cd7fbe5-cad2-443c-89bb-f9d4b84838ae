import { Ref, ref, watch } from 'vue';
import { UseQueryReturnType } from '@tanstack/vue-query';
import { QTableProps } from 'quasar';
import BaseModel from 'src/models/BaseModel';
import { CollectionProxy } from 'spraypaint/lib/proxies';
import { PersistedSpraypaintR<PERSON>ord, Scope, SpraypaintBase } from 'spraypaint';
import { isEmpty } from 'lodash';

export class PaginationProps {
    descending: boolean;
    page: number;
    rowsNumber = 0;
    rowsPerPage: number;
    sortBy: string | null;

    constructor(props: QTableProps['pagination']) {
        Object.assign(this, props);
    }
}

export function applyPaginationPropsFilter<Model extends SpraypaintBase>(query: Scope<Model>, filter: PaginationProps) {
    query = query
        .per(filter.rowsPerPage)
        .page(filter.page)

    if(!isEmpty(filter.sortBy)) {
        query = query.order({ [filter.sortBy as string]: filter.descending ? 'desc' : 'asc'})

        if(filter.sortBy !== 'createdAt') {
            // sort by createdAt as second value, so entries with same primary sort value get sorted by created date
            query = query.order({ createdAt: 'desc' })
        }
    }
    return query
}

/**
 * props have no interface in QTableProps['onRequest'], so defined manually
 */
interface QTablePropsForRequest  {
    /**
     * Pagination object
     */
    pagination: {
        /**
         * Column name (from column definition)
         */
        sortBy: string;
        /**
         * Is sorting in descending order?
         */
        descending: boolean;
        /**
         * Page number (1-based)
         */
        page: number;
        /**
         * How many rows per page? 0 means Infinite
         */
        rowsPerPage: number;
    };
    /**
     * String/Object to filter table with (the 'filter' prop)
     */
    filter?: string | never;
    /**
     * Function to get a cell value
     * @param col Column name from column definitions
     * @param row The row object
     * @returns Parsed/Processed cell value
     */
    getCellValue: (col: never, row: never) => never;
}

export function useServerTable<TData extends BaseModel, TError>(
    // @ts-expect-error TS2344: Type TData does not satisfy the constraint SpraypaintBase Type BaseModel is not assignable to type SpraypaintBase
    queryResult: UseQueryReturnType<CollectionProxy<TData>, TError>,
    pagination?: Ref<PaginationProps>,
    queryResultData?: Ref<PersistedSpraypaintRecord<TData>[]>,
    ) {


    const rowData = ref<PersistedSpraypaintRecord<TData>[]>([]) as Ref<PersistedSpraypaintRecord<TData>[]>

    function onRequest(tableProps: QTablePropsForRequest) {
        const { page, rowsPerPage, sortBy, descending } = tableProps.pagination
        // const tableFilter = tableProps.filter  // TODO add filter by member

        if(pagination) {
            pagination.value.sortBy = sortBy
            pagination.value.descending = descending
            pagination.value.rowsPerPage = rowsPerPage
            pagination.value.page = page

        }
        queryResult.refetch()
    }

    watch(queryResult.data, (data) => {
        if(data) {
            rowData.value = queryResultData?.value ?? data.data

            if(pagination){
                pagination.value.rowsNumber = data.meta.page.total as number
                pagination.value.page= data.meta.page.currentPage as number
                pagination.value.rowsPerPage= data.meta.page.perPage as number
            }
        }

    })

  return {
      onRequest,
      rowData
  };
}
