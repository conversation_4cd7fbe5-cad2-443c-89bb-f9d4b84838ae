import { onMounted, ref } from 'vue';
import { Capacitor } from '@capacitor/core';
import { AndroidSettings, IOSSettings, NativeSettings } from 'capacitor-native-settings';
import { PushNotifications } from '@capacitor/push-notifications';

export function usePushNotificationSettings() {
  const pushPermissionGranted = ref(false);
  const pushNotificationsAvailable = ref(false);

  onMounted(async () => {
    if (!Capacitor.isPluginAvailable('PushNotifications')) {
      console.warn('PushNotifications plugin not available');
      pushPermissionGranted.value = false;
      return;
    }
    pushNotificationsAvailable.value = true;

    const permission = await PushNotifications.checkPermissions();
    pushPermissionGranted.value = permission.receive === 'granted';
  });

  // Function to open app settings for notifications
  const openNotificationSettings = async () => {
    if (!Capacitor.isPluginAvailable('NativeSettings')) {
      console.log('Cannot open push notification settings. NativeSettings plugin missing.');
      return;
    }

    if (Capacitor.getPlatform() === 'ios') {
      await NativeSettings.openIOS({
        option: IOSSettings.App,
      });
    } else if (Capacitor.getPlatform() === 'android') {
      await NativeSettings.openAndroid({
        option: AndroidSettings.ApplicationDetails,
      });
    } else {
      console.log('Cannot open push notification settings for platform', Capacitor.getPlatform());
    }
  };

  return {
    pushPermissionGranted,
    pushNotificationsAvailable,
    openNotificationSettings,
  };
}
