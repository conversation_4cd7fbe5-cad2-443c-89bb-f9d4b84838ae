export class APIError {
  message: string;
  errors: object;
}

export const isError = (errorData: APIError, errorType: string) => {
  return errorType in errorData?.errors;
};

export const isNotInAllowlist = (errorData: APIError) => isError(errorData, 'not_allowlisted');
export const isAlreadyRegistered = (errorData: APIError) => isError(errorData, 'already_registered');
export const isUserCreationFailed = (errorData: APIError) => isError(errorData, 'user_creation_failed');
