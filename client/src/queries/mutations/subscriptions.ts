import { api } from 'src/boot/axios';
import SubscriptionPriceType = App.Types.SubscriptionPriceType;

export function cancelTeamSubscription(teamId: string) {
  return api.post(`/subscription/team/${teamId}/cancel`);
}

export function resumeTeamSubscription(teamId: string) {
  return api.post(`/subscription/team/${teamId}/resume`);
}

export async function createTeamCheckoutSession(teamId: string, priceType: SubscriptionPriceType) {
  const response = await api.post(`/subscription/team/${teamId}/create-checkout-session/${priceType}`);
  return response.data;
}

