import TeamLedger from 'src/models/TeamLedger';
import TeamLedgerTransaction from 'src/models/TeamLedgerTransaction';
import TeamLedgerFine from 'src/models/TeamLedgerFine';
import TeamLedgerClaim from 'src/models/TeamLedgerClaim';
import TeamLedgerDues from 'src/models/TeamLedgerDues';
import { api } from 'src/boot/axios';
import TeamLedgerClaimStatusType = App.Types.TeamLedgerClaimStatusType;
import TeamLedgerClaimable from 'src/models/abstracts/TeamLedgerClaimable';

export function saveLedgerOnServer(ledger: TeamLedger) {
    const options = { with: 'team.id' };
    return ledger.save(options);
}

export function saveTransactionOnServer(transaction: TeamLedgerTransaction) {
    const options = { with: ['ledger.id', 'teamMember.id']};
    return transaction.save(options);
}

export function saveFineOnServer(fine: TeamLedgerFine) {
    const options = { with: ['ledger.id', 'parent.id']};
    return fine.save(options);
}

export function saveDuesOnServer(dues: TeamLedgerDues) {
    const options = { with: ['ledger.id', 'parent.id']};
    return dues.save(options);
}

export function saveClaimOnServer<ClaimableType extends TeamLedgerClaimable<ClaimableType>>(claim: TeamLedgerClaim<ClaimableType>) {
    const options = { with: ['ledger.id', 'claimable.id', 'teamMember.id']};
    return claim.save(options);
}
export function setClaimStatusOnServer(
    {
        claim,
        claimStatus,
        createTransactionAmount
    }: {
        claim: TeamLedgerClaim<TeamLedgerDues | TeamLedgerFine>,
        claimStatus: TeamLedgerClaimStatusType,
        createTransactionAmount?: number
    }) {
    return api.post('teamLedger/setClaimStatus/' + claim.id + '/' + claimStatus, {
        createTransactionAmount: createTransactionAmount
    })
        .then(data => data.data)
}
