import TeamEvent from 'src/models/TeamEvent';
import TeamEventSeries from 'src/models/TeamEventSeries';
import TeamEventTaskConfig from 'src/models/TeamEventTaskConfig';
import TeamEventTask from 'src/models/TeamEventTask';

export function saveTeamEventOnServer(teamEvent: TeamEvent) {
  const options = { with: ['team.id'] };
  if (teamEvent.series && teamEvent.series.isRecurring()) {
    options.with.push('series.id');
  }
  return teamEvent.save(options);
}

export function saveTeamEventSeriesOnServer(teamEventSeries: TeamEventSeries) {
  return teamEventSeries.save();
}

export async function saveTeamEventTaskConfigOnServer(teamEventTaskConfig: TeamEventTaskConfig) {
  return teamEventTaskConfig.save({ with: ['team.id'] });
}

export async function saveTeamEventTaskOnServer(teamEventTask: TeamEventTask) {
  return teamEventTask.save({ with: ['teamEvent.id', 'config.id', 'teamMember.id'] });
}


