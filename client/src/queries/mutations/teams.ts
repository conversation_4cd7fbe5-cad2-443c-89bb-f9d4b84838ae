// import { createMutationKeys } from '@lukemorales/query-key-factory';
import Team from 'src/models/Team';
import TeamMember from 'src/models/TeamMember';
import TeamStatsRange from 'src/models/TeamStatsRange';


// export const teamMutations = createMutationKeys('teams', {
//   addTeamWithMembers: () => ({
//     mutationKey: [''],
//     mutationFn: saveTeamWithMembersOnServer,
//   })
// })


export function saveTeamMemberOnServer(teamMember: TeamMember) {
  return teamMember.save({ with: ['team.id', 'statusRole.id', 'roles.id'] });
}

export function saveTeamOnServer(team: Team) {
  return team.save();
}

export function saveTeamWithMembersOnServer(team: Team) {
  return team.save().then(() => {
    const memberCreationPromises = team.members.map((teamMember) => {
        teamMember.team = team;
        return saveTeamMemberOnServer(teamMember);
      },
    );
    return Promise.all(memberCreationPromises)
      .then(() => team);
  });
}

export async function saveTeamStatsRangeOnServer(teamStatsRange: TeamStatsRange) {
  return teamStatsRange.save({ with: ['team.id'] });
}
