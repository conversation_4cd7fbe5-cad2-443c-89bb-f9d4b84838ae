import ICalendar from 'src/models/ICalendar';

export const iCalendarsQueries = {
  all: {
    queryKey: null,
    queryFn: () => {
      return ICalendar.all().then(data => data.data);
    },
  },
  detail: (iCalendarId: string) => ({
    queryKey: [iCalendarId] as const,
    queryFn: () =>
        ICalendar.includes(['persons', 'teamMembers'])
        .find(iCalendarId)
        .then(data => data.data.detachWhenEmpty(['persons', 'teamMembers'])),
  }),
};
