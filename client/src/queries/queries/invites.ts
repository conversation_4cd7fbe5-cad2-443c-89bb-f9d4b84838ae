import Invite from 'src/models/Invite';
import { toValue } from 'vue';
import { MaybeRef } from '@vueuse/core';
import { Scope } from 'spraypaint';

// TODO(fabzo): This might be problematic,
//  as it will include all team data that the person might not yet be allowed to see(?)
const InviteWithInvitable = Invite.includes('invitable') as Scope<Invite<never>>;

export const inviteQueries = {
  forToken(token: MaybeRef<string>, includeTeamMembers = false) {
    return {
      queryKey: [token, includeTeamMembers] as const,
      queryFn: () => {
        let query = InviteWithInvitable.where({ token: toValue(token) });

        if (includeTeamMembers) {
          query = query.includes({ invitable: ['roles', { members: ['person', 'statusRole', 'invite'] }] });
        }
        return query.first().then((data) => data.data);
      },
    };
  },
};
