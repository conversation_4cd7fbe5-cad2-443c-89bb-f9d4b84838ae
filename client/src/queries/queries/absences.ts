import Absence from 'src/models/Absence';

export const absencesQueries = {
  all: {
    queryKey: null,
    queryFn: () => {
      return Absence.all().then(data => data.data);
    },
  },
  detail: (absenceId: string) => ({
    queryKey: [absenceId] as const,
    queryFn: () =>
      Absence.includes(['persons', 'teamMembers'])
        .find(absenceId)
        .then(data => data.data.detachWhenEmpty(['persons', 'teamMembers'])),
  }),
};
