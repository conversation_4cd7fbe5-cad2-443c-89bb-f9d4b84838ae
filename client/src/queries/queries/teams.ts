import Team from 'src/models/Team';
import { Ref } from 'vue';
import TeamStatsRange from 'src/models/TeamStatsRange';

const TeamWithMembers = Team.includes({ members: 'statusRole' });

export const teamQueries = {
  all: (withMembers = true, withPersons = false, withActiveStatsRanges = false) => ({
    queryKey: [withMembers, withPersons, withActiveStatsRanges] as const,
    queryFn: () => {
      let query = Team.scope();
      if (withMembers) {
        query = TeamWithMembers;
      }
      if (withPersons) {
        query = query.includes({ members: ['person', 'statusRole'] });
      }
      if (withActiveStatsRanges) {
        query = query.includes('activeStatsRanges');
      }
      return query.all().then(data => data.data);
    },
  }),
    allWithLedger: {
        queryKey: null,
        queryFn: () => {
            return Team.includes({ledger: ['dueItemClaims']})
                .all().then(data => data.data);
        },
    },
    withLedgerDetail: (teamId: string) => ({
        queryKey: [teamId] as const,
        queryFn: () => {
            return TeamWithMembers
                .includes({ledger: ['formerMembers', 'fines', {dueItemClaims: ['claimable', 'teamMember']}]})
                .find(teamId).then(data => {
                    const result = data.data;
                    if(result.ledger) {
                        // link team to ledger to access information more easily => structuralSharing has to be set to false on useQuery
                        result.ledger.team = data.data
                    }
                    return result
                });
        },
    }),

  allPaginated: (page = 1, perPage = 10) => ({
    queryKey: [page, perPage] as const,
    queryFn: () => TeamWithMembers.page(page).per(perPage).all(),
  }),

  detail: (teamId: string) => ({
    queryKey: [teamId] as const,
    queryFn: () =>
        TeamWithMembers
            .includes([
                'invite', 'roles', 'activeSubscription',
                { members: ['person', 'statusRole', 'roles', 'invite'] }
            ])
            .find(teamId)
            .then(data => {
                data.data.members.forEach(member => {
                    member.detachWhenEmpty(['roles'])
                })
                return data.data;
            }),
  }),

  allStatsRanges: (team: Ref<Team | null | undefined>) => ({
    queryKey: [team] as const,
    queryFn: () => TeamStatsRange.where({ teamId: team.value?.id as string }).all().then(data => data.data),
  }),
};

