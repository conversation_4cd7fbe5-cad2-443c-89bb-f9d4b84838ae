import TeamLedgerTransaction from 'src/models/TeamLedgerTransaction';
import { MaybeRef } from '@vueuse/core';
import { unref } from 'vue';
import { applyPaginationPropsFilter, PaginationProps } from 'src/composables/useServerTable';
import TeamLedgerFine from 'src/models/TeamLedgerFine';
import TeamLedgerClaim from 'src/models/TeamLedgerClaim';
import TeamLedgerDues from 'src/models/TeamLedgerDues';
import TeamLedger from 'src/models/TeamLedger';
import { api } from 'src/boot/axios';
import { Money, MoneyClass } from 'src/helpers/money';

export const ledgerQueries = {
    transactions: (ledgerId: string, filter: MaybeRef<PaginationProps>) => ({
        queryKey: [ledgerId, unref(filter)] as const,
        queryFn: () => {
            const query = TeamLedgerTransaction
                .includes('teamMember')
                .where({ ledgerId: ledgerId })

            return applyPaginationPropsFilter(query, unref(filter))
                .all();
        },
    }),
    fines: (ledgerId: string, filter: MaybeRef<PaginationProps>) => ({
        queryKey: [ledgerId, unref(filter)] as const,
        queryFn: () => {
            const query = TeamLedgerFine
                .includes('parent')
                .where({ ledgerId: ledgerId })

            return applyPaginationPropsFilter(query, unref(filter))
                .all();
        },
    }),
    dues: (ledgerId: string, filter: MaybeRef<PaginationProps>) => ({
        queryKey: [ledgerId, unref(filter)] as const,
        queryFn: () => {
            const query = TeamLedgerDues
                .includes('parent')
                .where({ ledgerId: ledgerId })

            return applyPaginationPropsFilter(query, unref(filter))
                .all();
        },
    }),
    fineClaims: (ledgerId: string, filter: MaybeRef<PaginationProps>) => ({
        queryKey: [ledgerId, unref(filter), TeamLedgerFine.jsonapiType] as const,
        queryFn: () => {
            const query = TeamLedgerClaim
                .includes('teamMember')
                .where({ ledgerId: ledgerId })
                .where({ claimableType: TeamLedgerFine.jsonapiType })

            return applyPaginationPropsFilter(query, unref(filter))
                .all();
        },
    }),
    fineClaimsCountWithLastEntry: (teamLedgerFineId: MaybeRef<string | undefined>) => ({
        queryKey: [teamLedgerFineId, TeamLedgerFine.jsonapiType] as const,
        queryFn: () => {
            return TeamLedgerClaim
                .where({ claimableId: teamLedgerFineId })
                .where({ claimableType: TeamLedgerFine.jsonapiType })
                .per(1)
                .order({ dueDate: 'desc'})
                .stats({total: 'count'})
                .all()
        },
    }),
    duesClaims: (ledgerId: string) => ({
        queryKey: [ledgerId, TeamLedgerDues.jsonapiType] as const,
        queryFn: () => {
            let query = TeamLedgerClaim
                .includes(['teamMember', 'claimable'])
                .where({ ledgerId: ledgerId })
                .where({ claimableType: TeamLedgerDues.jsonapiType })

            query = query.order('dueDate')
            return query.all();
        },
    }),
    duesClaimsCount: (teamLedgerDuesId: MaybeRef<string | undefined>) => ({
        queryKey: [teamLedgerDuesId, TeamLedgerDues.jsonapiType] as const,
        queryFn: () => {
            return TeamLedgerClaim
                .where({ claimableId: teamLedgerDuesId })
                .where({ claimableType: TeamLedgerDues.jsonapiType })
                .per(0)
                .order({ dueDate: 'desc'})
                .stats({total: 'count'})
                .all()
        },
    }),

    memberStatus: (ledgerId: string) => ({
        queryKey: [ledgerId] as const,
        queryFn: () => TeamLedger
                .select(['memberStatus'])
                .find(ledgerId)
                .then(data => data.data),
    }),

    creditBalance: (ledgerId: string, teamMemberId?: MaybeRef<string | undefined>) => ({
        queryKey: [ledgerId, teamMemberId] as const,
        queryFn: () =>
            api.get<Money>('teamLedger/getCreditBalance/' + ledgerId + '/' + (unref(teamMemberId) || ''))
                .then(data => MoneyClass.fromServer(data.data)),
    }),
};
