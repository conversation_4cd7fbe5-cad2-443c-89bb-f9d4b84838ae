import TeamEventTask from 'src/models/TeamEventTask';
import TeamEventTaskConfig from 'src/models/TeamEventTaskConfig';
import { EventStatsFilter } from 'src/composables/useEventStatsFilter';
import { applyScopeFromEventStatsFilter, EventsWithoutSeriesParent } from 'src/queries/queries/events';

const TeamEventTaskWithConfigAndMembers = TeamEventTask
  .includes(['config', 'teamMember']);

export const eventTaskQueries = {
  allForEvent: (eventId: string) => ({
    queryKey: [eventId] as const,
    queryFn: () => TeamEventTaskWithConfigAndMembers
      .where({ teamEventId: eventId })
      .all()
      .then(data => {
        return data.data.map(task => task.detachWhenEmpty(['teamMember']));
      }),
  }),
  allForStatsTasks: (filter: EventStatsFilter) => ({
    queryKey: [filter.startDate, filter.endDate, filter.selectedTeam, filter.selectedEventTypes] as const,
    queryFn: () => {
      return TeamEventTaskWithConfigAndMembers
        .includes('teamEvent')
        .where({ teamMember: true })
        .merge({ teamEvent: applyScopeFromEventStatsFilter(EventsWithoutSeriesParent, filter) })
        .all()
        .then(data => data.data);
    },
  }),
  allConfigsForTeam: (teamId: string) => ({
    queryKey: [teamId] as const,
    queryFn: () => TeamEventTaskConfig.where({ teamId: teamId }).all().then(data => data.data),
  }),
};
