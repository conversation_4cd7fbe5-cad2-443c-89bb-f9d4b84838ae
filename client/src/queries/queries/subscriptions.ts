import { api } from 'src/boot/axios';
import Team from 'src/models/Team';

export const subscriptionQueries = {
  teamWithSubscription: (teamId: string) => ({
    queryKey: [teamId] as const,
    queryFn: () => Team
      .includes('activeSubscription')
      .find(teamId)
      .then(data => data.data)
  }),

  teamInvoices: (teamId: string) => ({
    queryKey: [teamId] as const,
    queryFn: async () => {
      const response = await api.get(`/subscription/team/${teamId}/invoices`);
      return response.data;
    },
  }),

  teamPrices:  (teamId: string) => ({
    queryKey: [teamId] as const,
    queryFn: async () => {
      const response = await api.get(`/subscription/team/${teamId}/prices`);
      return response.data;
    },
  }),
};
