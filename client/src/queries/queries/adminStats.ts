import { unref } from 'vue';
import { MaybeRef } from '@vueuse/core';
import { applyPaginationPropsFilter, PaginationProps } from 'src/composables/useServerTable';
import AdminStatsTeam from 'src/models/AdminStatsTeam';
import User from 'src/models/User';

export const adminStatsQueries = {
  allAdminStatsTable: (filter: MaybeRef<PaginationProps>) => ({
      queryKey: [unref(filter)] as const,
    queryFn: () => {
      const query = AdminStatsTeam.scope()
        return applyPaginationPropsFilter(query, unref(filter))
            .all();
    },
  }),
  usersTable: (filter: MaybeRef<PaginationProps>) => ({
      queryKey: [unref(filter)] as const,
    queryFn: () => {
      const query = User
          .includes({person: [{teamMembers: ['team']}]})
        return applyPaginationPropsFilter(query, unref(filter))
            .all();
    },
  }),
};

