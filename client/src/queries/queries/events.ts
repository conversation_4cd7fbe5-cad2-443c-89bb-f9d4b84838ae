import TeamEvent from 'src/models/TeamEvent';
import { sortBy } from 'lodash';
import BaseModel from 'src/models/BaseModel';
import { EventStatsFilter } from 'src/composables/useEventStatsFilter';
import { Scope } from 'spraypaint';
import { MaybeRef } from '@vueuse/core';
import { unref } from 'vue';

export const EventsWithoutSeriesParent = TeamEvent
  .where({ events: true });
export const EventsWithSeriesParent = TeamEvent
  .where({ seriesParent: true });

export const EventsWithTeamAndMembers = EventsWithoutSeriesParent
  .includes([{ team: { members: 'statusRole' } }, { votes: ['teamMember', 'comment', 'author'] }]);

export const EventsDetail = EventsWithoutSeriesParent
  .includes([{ team: ['statsRanges', { members: 'statusRole' }] }, { votes: ['teamMember', 'comment', 'author'] }]);

export function getKeyFromModelArray(teams: BaseModel[]) {
  return sortBy(teams, 'id')
    .map((team: BaseModel) => team.id)
    .join(',');
}

export function applyScopeFromEventStatsFilter(query: Scope<TeamEvent>, filter: EventStatsFilter) {
  query = query
    .where({ notCancelled: true });

  if (filter.startDate?.value instanceof Date) {
    query = query.where({ 'startDate': filter.startDate.value.toISOString() });
  }

  query = query.where({ 'endDate': filter.endDateOrToday.value.toISOString() });

  if (filter.selectedTeam.value) {
    query = query.where({ team: filter.selectedTeam.value.id });
  }
  if (filter.selectedEventTypes.value.length > 0) {
    query = query.where({ eventType: filter.selectedEventTypes.value });
  } else {
    query = query.where({ eventType: false });
  }
  return query;
}

export const eventQueries = {
  all: {
    queryKey: null,
    queryFn: () => EventsWithoutSeriesParent.includes(['team']).all().then(data => data.data),
  },
  allWithTeamAndMembersAndVotes: (initialStartDate: Date, initialEndDate: Date) => ({
    queryKey: [initialStartDate, initialEndDate] as const,
    queryFn: ({ pageParam = { startDate: initialStartDate, endDate: initialEndDate } }) =>
      EventsWithTeamAndMembers
        .where({ 'startDate': pageParam.startDate.toISOString() })
        .where({ 'endDate': pageParam.endDate.toISOString() })
        .all()
        .then(data => data.data),
  }),
  allForStatSums: (filter: EventStatsFilter) => ({
    queryKey: [filter.startDate, filter.endDate, filter.selectedTeam, filter.selectedEventTypes] as const,
    queryFn: () => {
      return applyScopeFromEventStatsFilter(EventsWithoutSeriesParent, filter)
        .select(['eventType'])
        .all()
        .then(data => data.data);
    },
  }),
  allForStatsParticipation: (filter: EventStatsFilter) => ({
    queryKey: [filter.startDate, filter.endDate, filter.selectedTeam, filter.selectedEventTypes] as const,
    queryFn: () => {
      return applyScopeFromEventStatsFilter(EventsWithTeamAndMembers, filter)
        .order({ dateBegin: 'desc' })
        .all()
        .then(data => data.data);
    },
  }),
  detail: (eventId: MaybeRef<string>) => ({
    queryKey: [eventId] as const,
    queryFn: () => EventsDetail
      .find(unref(eventId)).then(data => data.data),
  }),
  forEdit: (eventId: string, forCopy = false) => ({
    queryKey: [eventId, forCopy] as const,
    queryFn: () => TeamEvent.includes(['team', 'series']).find(eventId).then(data => data.data),
  }),
};
