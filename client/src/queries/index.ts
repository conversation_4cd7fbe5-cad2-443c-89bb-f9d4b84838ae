import { createQueryKeyStore } from '@lukemorales/query-key-factory';
import { teamQueries } from 'src/queries/queries/teams';
import { eventQueries } from 'src/queries/queries/events';
import { inviteQueries } from 'src/queries/queries/invites';
import { absencesQueries } from 'src/queries/queries/absences';
import { eventTaskQueries } from 'src/queries/queries/eventTasks';
import { deviceInfoQueries } from 'src/queries/queries/deviceInfo';
import { Ref } from 'vue';
import { ledgerQueries } from 'src/queries/queries/ledgers';
import { QueryObserverBaseResult } from '@tanstack/vue-query';
import { adminStatsQueries } from 'src/queries/queries/adminStats';
import { iCalendarsQueries } from 'src/queries/queries/iCalendars';
import { subscriptionQueries } from 'src/queries/queries/subscriptions';

export const queries = createQueryKeyStore({
  adminStats: adminStatsQueries,
  teams: teamQueries,
  events: eventQueries,
  eventTasks: eventTaskQueries,
  invites: inviteQueries,
  absences: absencesQueries,
  devices: deviceInfoQueries,
  ledgers: ledgerQueries,
  iCalendars: iCalendarsQueries,
  subscriptions: subscriptionQueries,
})

// export const mutations = mergeQueryKeys(teamMutations)

// withRefCopy takes a TanStack query object and adds a select function that copies the data into a ref, and returns the
// new query object this can be used to extract the data from a query before it's inserted into the cache and made read-only.
//
// Notes: This is required when using Spraypaint to be able to utilize the save() function on the model objects.
export const withRefCopy = (query: unknown, listRef: Ref<unknown>, dataAlreadyExtracted = true) => {
    return {
        ...query,
        select: (data: QueryObserverBaseResult<never> | never) => {
            const list = [];
            let listData: []
            if(!dataAlreadyExtracted && data.data) {
                listData = data.data
            } else {
                listData = data as never as []
            }
            listData.map((item: never) => {
                list.push(item);
            })
            listRef.value = list;
            return data;
        },
    }
};
