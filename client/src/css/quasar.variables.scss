// Quasar SCSS (& Sass) Variables
// --------------------------------------------------
// To customize the look and feel of this app, you can override
// the Sass/SCSS variables found in Quasar's source Sass/SCSS files.

// Check documentation for full list of Quasar variables

// Your own variables (that are declared here) and Quasar's own
// ones will be available out of the box in your .vue/.scss/.sass files

// It's highly recommended to change the default colors
// to match your app's branding.
// Tip: Use the "Theme Builder" on Quasar's documentation website.

$primary: #444e61;
$secondary: #fdefbc;
$accent: #fbd758;

$dark: #2f3643;
$dark-page: #59667f;

$positive: #43a047;
$negative: #e53935;
$info: #4a8ebf;
$warning: #eba433;
