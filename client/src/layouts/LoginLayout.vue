<template>
    <q-layout view="hHh LpR fff">
      <!-- Header with conditional safe-area-top -->
      <q-header bordered class="bg-primary text-white safe-area-top">
        <q-toolbar class="q-pr-none">
          <HeaderToolbarLogoName />

          <q-space />

          <!-- Login button shown when not logged-in & not on auth routes -->
          <q-btn
            v-if="!useAuthStore().loggedIn
              && $route.name !== $routeNames.LOGIN
              && $route.name !== $routeNames.REGISTRATION
              && $route.name !== $routeNames.FORGOT_PASSWORD
              && $route.name !== $routeNames.VERIFY_EMAIL"
            :to="{ name: $routeNames.LOGIN }"
            flat
            label="Login"
            stretch
          />

          <!-- Logout button when logged in -->
          <q-btn
            v-if="useAuthStore().loggedIn"
            :loading="isLoggingOut"
            flat
            data-cy="logout-button"
            label="Ausloggen"
            stretch
            @click="logout"
          />
        </q-toolbar>
      </q-header>

      <!-- Page container with conditional style-fn -->
      <q-page-container class="q-pb-safe-bottom">
        <router-view />
      </q-page-container>

      <FooterToolbar />
    </q-layout>
  </template>

  <script lang="ts" setup>
  import { useQuasar } from 'quasar';
  import { Capacitor } from '@capacitor/core';

  import FooterToolbar from 'src/layouts/FooterToolbar.vue';
  import HeaderToolbarLogoName from 'src/layouts/HeaderToolbarLogoName.vue';
  import { useAuthStore } from 'src/stores/auth';
  import { useAuthLogout } from 'src/composables/useAuthLogout';

  const { logout, isLoggingOut } = useAuthLogout();
  </script>

  <style lang="scss" scoped>
  </style>
