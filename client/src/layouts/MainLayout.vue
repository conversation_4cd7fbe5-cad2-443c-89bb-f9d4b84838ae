<template>
    <q-layout view="hHh lpR fFf" :class="{ 'blurred': isWindowBlurred }">


        <q-header bordered class="bg-primary text-white safe-area-top">
            <q-toolbar class="q-px-none">
                <q-btn flat icon="menu"
                       stretch
                       @click="menuVisible = !menuVisible"
                />

                <q-separator dark inset vertical />
                <HeaderToolbarLogoName />

                <!--        <q-space/>-->

                <!--        <q-select-->
                <!--          filled-->
                <!--          dense-->
                <!--          clearable-->
                <!--          v-model="authStore.person"-->
                <!--          :options="personRepo.all()"-->
                <!--          option-value="id"-->
                <!--          option-label="firstname"-->
                <!--          style="width: 250px"-->
                <!--          label="Select person">-->

                <!--          <template v-slot:before>-->
                <!--            <q-icon name="person" />-->
                <!--          </template>-->

                <!--        </q-select>-->


                <div v-if="isFetching">
                    <q-spinner
                        :thickness="5"
                        size="1.5em"
                    />
                </div>

                <!--        <div v-if="isMutating">-->
                <!--          <q-spinner-->
                <!--            size="1.5em"-->
                <!--            :thickness="5"-->
                <!--          />-->
                <!--        </div>-->

                <div v-if="!isOnline">
                    <q-icon
                        name="warning"
                        style="font-size: 1.5em;"
                    />
                    Offline
                </div>


                <q-btn class="q-pr-sm" flat no-caps stretch data-cy="user-dropdown">
                    <q-avatar class="q-mr-xs" color="white" size="24px" text-color="primary">
                        {{ authStore.avatarLetters }}
                    </q-avatar>
                    {{ authStore.avatarName }}
                    <q-icon name="arrow_drop_down" size="16px" />
                    <q-menu auto-close>
                        <q-list style="min-width: 150px">
                            <q-item>
                                <q-item-section>
                                    <q-item-label caption>Angemeldet als:</q-item-label>
                                    <q-item-label>{{ authStore.fullName }}</q-item-label>
                                </q-item-section>
                            </q-item>
                            <q-separator />

                            <q-item :to="{name: $routeNames.MY_PROFILE}">
                                <q-item-section>
                                    Mein Profil
                                </q-item-section>
                            </q-item>

                            <q-separator />
                            <q-item clickable @click="logout" data-cy="logout-button">
                                <q-item-section>
                                    Ausloggen
                                </q-item-section>
                                <q-item-section v-if="isLoggingOut" avatar>
                                    <q-spinner size="sm" />
                                </q-item-section>
                            </q-item>

                        </q-list>
                    </q-menu>
                </q-btn>

            </q-toolbar>

        </q-header>

        <q-drawer
            v-model="menuVisible"
            :breakpoint="700"
            :width="200"
            bordered
            show-if-above
            class="safe-area-top"
        >
            <q-scroll-area class="fit">

                <q-list>
                    <template v-for="(menuItem, index) in visibleMenuList" :key="index">
                        <q-item v-ripple :to="{name: menuItem.routeName}" exact :data-cy="'menu-' + menuItem.routeName">
                            <q-item-section avatar>
                                <q-icon :name="menuItem.icon" />
                            </q-item-section>
                            <q-item-section>
                                {{ menuItem.label }}
                            </q-item-section>
                        </q-item>
                        <q-separator />
                    </template>
                </q-list>

                <q-item />

                <q-list>
                    <template v-for="(menuItem, index) in visibleBottomList" :key="index">
                        <q-item v-ripple :to="{name: menuItem.routeName}" exact>
                            <q-item-section avatar>
                                <q-icon :name="menuItem.icon" />
                            </q-item-section>
                            <q-item-section>
                                {{ menuItem.label }}
                            </q-item-section>
                        </q-item>
                        <q-separator />
                    </template>
                </q-list>

                <q-item />

                <q-list v-if="showEnvironmentSwitch">
                    <q-item v-ripple :class="{'bg-secondary': currentEnvironment === 'production'}" class="q-pa-md" clickable @click="toggleProduction">
                        <q-item-section avatar>
                            <q-icon name="factory" />
                        </q-item-section>
                        <q-item-section>
                            Production
                        </q-item-section>
                    </q-item>
                    <q-item v-ripple :class="{'bg-secondary': currentEnvironment === 'staging'}" class="q-pa-md" clickable @click="toggleStaging">
                        <q-item-section avatar>
                            <q-icon name="settings" />
                        </q-item-section>
                        <q-item-section>
                            Staging
                        </q-item-section>
                    </q-item>
                    <q-item v-ripple :class="{'bg-secondary': currentEnvironment === 'testing' || currentEnvironment === 'dev'}" class="q-pa-md" clickable
                            @click="toggleTesting">
                        <q-item-section avatar>
                            <q-icon name="bug_report" />
                        </q-item-section>
                        <q-item-section>
                            Testing
                        </q-item-section>
                    </q-item>
                    <q-separator />
                </q-list>

                <q-item />

                <q-card v-if="!$q.platform.is.nativeMobile" class="q-mt-lg">
                    <q-card-actions align="center" vertical>
                        <q-btn class="fit" flat
                               href="https://play.google.com/store/apps/details?id=com.numoapp.numo.app"
                               target="_blank">
                            <img alt="Link zum Google Play Store" class="fit" src="/badge-google-playstore.png" />
                        </q-btn>

                        <q-btn class="fit" flat
                               href="https://apps.apple.com/de/app/numo/id6451087523"
                               target="_blank">
                            <img alt="Link zum Apple Appstore" class="fit" src="/badge-apple-appstore.svg" />
                        </q-btn>
                    </q-card-actions>
                </q-card>

            </q-scroll-area>
        </q-drawer>

        <q-page-container class="q-pb-safe-bottom">
            <router-view />
        </q-page-container>

        <!--  <FooterToolbar/>-->

    </q-layout>
</template>

<script lang="ts" setup>

    import { computed, ComputedRef, ref } from 'vue';
    import { ROUTE_NAMES } from '../router/route-names';
    import { useAuthStore } from 'src/stores/auth';
    import { onlineManager, useIsFetching } from '@tanstack/vue-query';
    import HeaderToolbarLogoName from 'src/layouts/HeaderToolbarLogoName.vue';
    import { useAuthLogout } from 'src/composables/useAuthLogout';
    import { useRouter } from 'vue-router';
    import {
        checkRouteRequiresFeatureValid,
        checkRouteRequiresPermissionForAnyTeamValid,
        checkRouteRequiresTeamsValid,
    } from 'src/boot/check-auth';
    import { useWindowFocus } from '@vueuse/core';

    const authStore = useAuthStore();

    const {
        logout,
        isLoggingOut,
    } = useAuthLogout();

    const isWindowBlurred = computed(() => authStore.hasFeature('live_demo') && !useWindowFocus().value);



    const menuVisible = ref(false);
    const isFetching = useIsFetching();
    // const isMutating = useIsMutating()
    const isOnline = ref(onlineManager.isOnline());
    onlineManager.subscribe(() => {
        isOnline.value = onlineManager.isOnline();
    });

    const currentEnvironment = process.env.ENVIRONMENT;
    const showEnvironmentSwitch = authStore.hasFeature('admin.stats');

    function toggleEnvironment(url: string) {
            window.location.href = url;
    }

    function toggleProduction() {
        toggleEnvironment('https://app.numo-app.com');
    }

    function toggleStaging() {
        toggleEnvironment('https://app-staging.numo-app.com');
    }

    function toggleTesting() {
        toggleEnvironment('https://app-testing.numo-app.com');
    }

    type MenuItem = {
        icon: string,
        label: string,
        routeName: keyof typeof ROUTE_NAMES,
    }

    const menuList: MenuItem[] = [
        {
            icon: 'supervisor_account',
            label: 'Startseite',
            routeName: ROUTE_NAMES.HOME,
        },
        {
            icon: 'event',
            label: 'Termine',
            routeName: ROUTE_NAMES.EVENTS,
        },
        {
            icon: 'groups',
            label: 'Teams',
            routeName: ROUTE_NAMES.TEAMS,
        },
        {
            icon: 'o_medical_services',
            label: 'Urlaub / Krank',
            routeName: ROUTE_NAMES.ABSENCES,
        },
        {
            icon: 'bar_chart',
            label: 'Statistik',
            routeName: ROUTE_NAMES.STATS,
        },
        {
            icon: 'attach_money',
            label: 'Teamkasse',
            routeName: ROUTE_NAMES.LEDGERS,
        },
    ];

    const bottomList: MenuItem[] = [
        {
            icon: 'query_stats',
            label: 'Admin Stats',
            routeName: ROUTE_NAMES.ADMIN_STATS,
        },
        {
            icon: 'info_outline',
            label: 'Impressum',
            routeName: ROUTE_NAMES.IMPRESSUM,
        },
        {
            icon: 'lock_outline',
            label: 'Datenschutz',
            routeName: ROUTE_NAMES.PRIVACY,
        },
        {
            icon: 'mail_outline',
            label: 'Kontakt',
            routeName: ROUTE_NAMES.CONTACT,
        },
    ];

    const routes = useRouter().getRoutes();

    const visibleMenuList: ComputedRef<MenuItem[]> = computed(() => {
        return menuList.filter(menuItem => {
            const route = routes.find(record => record.name === menuItem.routeName);
            return route && checkRouteRequiresTeamsValid(route.meta) && checkRouteRequiresFeatureValid(route.meta) && checkRouteRequiresPermissionForAnyTeamValid(route.meta);
        });
    });

    const visibleBottomList: ComputedRef<MenuItem[]> = computed(() => {
        return bottomList.filter(menuItem => {
            const route = routes.find(record => record.name === menuItem.routeName);
            return route && checkRouteRequiresFeatureValid(route.meta);
        });
    });

</script>

<style lang="scss" scoped>
  .blurred:after {
    pointer-events: none; /* optional: block interaction while blurred */
    content: "";
    position: fixed;
    inset: 0;
    background: rgba(255, 255, 255, 0.4);
    backdrop-filter: saturate(50%) brightness(50%);
    z-index: 9999;
    transition: opacity 0.3s ease;
  }
</style>
