<template>
  <PageContainer>

    <FirstTeamCreate v-if="useAuthStore().teamsCount == 0" />

    <template v-else>
      <EventList
        :end-date="endDate"
        :show-all-events-button="true"
        :start-date="startDate"
        title="Aktuelle Termine"
      />
    </template>
  </PageContainer>
</template>

<script lang="ts" setup>
  import EventList from 'src/components/events/EventList.vue';
  import { date } from 'quasar';
  import PageContainer from 'src/components/global/PageContainer.vue';
  import { useAuthStore } from 'src/stores/auth';
  import FirstTeamCreate from 'src/components/teams/FirstTeamCreate.vue';
  import subtractFromDate = date.subtractFromDate;
  import addToDate = date.addToDate;

  const startDate = subtractFromDate(new Date(), { days: 1 });
  const endDate = addToDate(new Date(), { days: 10 });

</script>

<style lang="scss" scoped>

</style>
