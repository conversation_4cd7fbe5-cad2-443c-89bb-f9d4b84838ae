<template>
    <div class="q-pa-md container">
        <q-card class="registration-card">
            <q-card-section class="bg-filled bg-primary text-white">
                <div class="text-h6 text-center">E-Mail Adresse bestätigen</div>
            </q-card-section>

            <template v-if="token">
                <q-card-section v-if="!errorMessage">
                    <div class="text-h6">
                        E-Mail Adresse wird bestätigt
                        <q-spinner />
                    </div>
                </q-card-section>

                <q-card-section v-else>
                    <div class="text-h6 text-red">
                        {{ errorMessage }}
                    </div>
                </q-card-section>
            </template>

            <template v-else>
                <q-card-section data-cy="verify-email-request-message">
                    Hallo {{ authStore.person?.firstname }},<br />
                    danke für deine Anmeldung bei {{ $appName }}.<br />
                    Bitte bestätige zunächst deine E-Mail Adresse, indem du auf den Link
                    klickst, den wir an
                    <span class="text-weight-bold">{{ authStore.user?.email }}</span> geschickt haben.
                </q-card-section>

                <q-card-section>
                    Wenn du keine E-Mail erhalten hast, schicken wir dir gerne noch eine.<br />
                    Bitte prüfe auch deinen Spam Ordner.
                </q-card-section>
            </template>


            <q-card-section v-if="isResendSuccess">
                Ein neuer Link wurde an
                <span class="text-weight-bold">{{ resendEmail }}</span> versendet.
            </q-card-section>
            <q-card-actions v-if="!isResendSuccess" align="left">
                <q-btn
                    :loading="isResendLoading"
                    class="q-mb-sm"
                    color="primary"
                    label="Bestätigungslink erneut senden"
                    no-caps
                    @click="resendMailOnServer"
                />
            </q-card-actions>

            <q-card-section v-if="isResendError">
                <div class="text-red">
                    {{ errorMessage }}<br />
                    Sollte der Fehler weiterhin auftreten, melde dich gerne bei uns:
                    <router-link :to="{name: $routeNames.CONTACT}">Kontakt</router-link>
                </div>
            </q-card-section>

        </q-card>
    </div>
</template>

<script lang="ts" setup>

    import { onMounted, ref, watchEffect } from 'vue';
    import { useMutation } from '@tanstack/vue-query';
    import { api } from 'src/boot/axios';
    import { useAuthStore } from 'src/stores/auth';
    import { AxiosError } from 'axios';
    import { useRouter } from 'vue-router';
    import { ROUTE_NAMES } from 'src/router/route-names';
    import { APIError } from 'src/queries/api-errors';

    const props = withDefaults(
        defineProps<{
            redirectTo?: string;
            redirectQuery?: string;
            token?: string;
            inviteToken?: string;
            email?: string;
        }>(),
        {
            redirectTo: '/',
            redirectQuery: '/',
        },
    );
    const router = useRouter();
    const authStore = useAuthStore();

    const errorMessage = ref('');
    const resendEmail = ref('');

    function redirect() {
        if (props.inviteToken) {
            router.replace({ name: ROUTE_NAMES.INVITE, params: { token: props.inviteToken } });
        } else {
            router.replace({
                path: props.redirectTo,
            });
        }
    }

    onMounted(() => {
        watchEffect(() => {
            if (authStore.emailVerified) {
                redirect();
            } else if (props.token) {
                confirmMailOnServer();
            }
        });
    });

    const {
        mutate: resendMailOnServer,
        isPending: isResendLoading,
        isSuccess: isResendSuccess,
        isError: isResendError,
    } = useMutation<{ email: string }>({
        mutationFn: () =>
            api.post('/verify-mail/resend').then(data => data.data),
        onError: (error) => {
            if (error instanceof AxiosError && error.response !== undefined) {
                const data = error.response.data as APIError;
                errorMessage.value = data.message;
            }
            if (errorMessage.value == '') {
                errorMessage.value = 'Es ist ein unbekannter Fehler aufgetreten, bitte versuche es noch einmal.';
            }
        },
        onSuccess: async (data) => {
            resendEmail.value = data.email;
        },
    });


    const {
        mutate: confirmMailOnServer,
    } = useMutation({
        mutationFn: () =>
            api.post('/verify-mail/confirm', {
                email: props.email,
                token: props.token,
                // invite_token: token,
            }).then(data => data.data),
        onError: (error) => {
            if (error instanceof AxiosError && error.response !== undefined) {
                const data = error.response.data as APIError;
                errorMessage.value = data.message;
            }
            if (errorMessage.value == '') {
                errorMessage.value = 'Der Link scheint ungültig oder abgelaufen zu sein.';
            }
        },
        onSuccess: async () => {
            await useAuthStore().fetchUserData();
            redirect();
        },
    });


</script>

<style lang="scss" scoped>
    .container {
        max-width: 400px;
        margin: auto;

        .q-card {
            border-radius: 10px;
        }
    }

    .error-message {
        margin-top: 1rem;
        padding: 0.5rem;
    }
</style>
