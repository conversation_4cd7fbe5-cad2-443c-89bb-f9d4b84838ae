<template>
  <div class="q-pa-md login-container">
    <LoginInviteInfoCard :redirect-to="props.redirectTo" />

    <q-card class="login-card">
      <q-card-section class="bg-filled bg-primary text-white">
        <div class="text-h6 text-center">Passwort vergessen</div>
      </q-card-section>

      <q-form @submit.prevent="sendResetEmail">
        <q-card-section>
          <q-input
            v-model="email"
            :rules="[
              (val, rules) =>
                rules.email(val) ||
                'Bitte eine richtige E-Mail Adresse angeben',
            ]"
            label="E-Mail"
            lazy-rules
            outlined
            type="email"
          />
        </q-card-section>

        <q-card-actions align="center" class="q-mt-md">
          <q-btn
            :loading="sendResetEmailMutation.isPending.value"
            class="q-mb-sm"
            color="primary"
            label="Absenden"
            type="submit"
          />
          <q-btn
            :disable="sendResetEmailMutation.isPending.value"
            class="q-mb-sm"
            color="primary"
            flat
            label="Zum Login"
            @click="redirectPreserveQuery(ROUTE_NAMES.LOGIN)"
          />
          <q-btn
            :disable="sendResetEmailMutation.isPending.value"
            class="q-mb-sm"
            color="primary"
            flat
            label="Registrieren"
            @click="redirectPreserveQuery(ROUTE_NAMES.REGISTRATION)"
          />
        </q-card-actions>
      </q-form>
    </q-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { useMutation } from '@tanstack/vue-query';
  import { Notify } from 'quasar';
  import { useRouter } from 'vue-router';
  import { ROUTE_NAMES } from 'src/router/route-names';
  import LoginInviteInfoCard from 'src/components/invites/LoginInviteInfoCard.vue';
  import { useRouting } from 'src/composables/useRouting';
  import { sendPasswordResetToServer } from 'src/queries/mutations/auth';

  const props = withDefaults(
    defineProps<{
      redirectTo?: string;
    }>(),
    {
      redirectTo: '/',
    },
  );

  const email = ref('');

  const router = useRouter();

  const { redirectPreserveQuery } = useRouting();

  const sendResetEmailMutation = useMutation({
    mutationFn: () => sendPasswordResetToServer(email.value),
    onSuccess: (response) => {
      email.value = '';
      notifyMessage('Passwort zurücksetzen Email gesendet.', 'positive');
      router.push({ name: ROUTE_NAMES.LOGIN });
    },
    onError: (error) => {
      notifyMessage('Senden fehlgeschlagen.', 'negative');
    },
  });

  function sendResetEmail() {
    sendResetEmailMutation.mutate();
  }

  function notifyMessage(message: string, color: string) {
    Notify.create({
      message: message,
      color: color,
      position: 'top',
    });
  }
</script>

<style lang="scss" scoped>
  .login-container {
    max-width: 400px;
    margin: auto;

    .q-card {
      border-radius: 10px;
    }
  }
</style>
