<template>
  <div class="q-pa-md registration-container">
    <q-card class="registration-card">
      <q-card-section class="bg-filled bg-primary text-white">
        <div class="text-h6 text-center">Warteliste</div>
      </q-card-section>

      <q-form @submit.prevent="checkMailAndAddToWaitList">
        <q-card-section>
          <q-item>
            <q-item-section avatar>
              <q-icon name="fa-solid fa-users" />
            </q-item-section>

            <q-item-section>
              <q-item-label>Du bist Spieler?</q-item-label>
              <q-item-label caption>
                Registrierung ist aktuell nur mit Einladung von deinem Trainer möglich.
              </q-item-label>
            </q-item-section>
          </q-item>

          <q-item>
            <q-item-section avatar>
              <q-icon name="fa-solid fa-user" />
            </q-item-section>

            <q-item-section>
              <q-item-label>Du bist Trainer?</q-item-label>
              <q-item-label caption>
                Du kannst dich in die Warteliste eintragen. Wir informieren dich, sobald du dein erstes Team anlegen kannst.
              </q-item-label>
            </q-item-section>
          </q-item>
        </q-card-section>
        <q-card-section>

          <q-input
            v-model="email"
            :error="errors.email !== undefined"
            :error-message="errors.email?.join(' ')"
            :rules="[
              (val, rules) =>
                rules.email(val) || 'Bitte gib eine gültige E-Mail Adresse ein',
            ]"
            label="E-Mail"
            lazy-rules
            outlined
            type="email"
          />

          <q-card v-if="mailState === 'alreadyRegistered'" class="bg-secondary error-message" flat>
            Für deine E-Mail Adresse wurde bereits ein Account angelegt. Du kannst dich also schon einloggen.
          </q-card>
          <q-card v-else-if="mailState === 'alreadyOnInviteList'" class="bg-secondary error-message" flat>
            Deine E-Mail Adresse ist bereits freigeschaltet, du kannst die Registrierung jetzt abschließen.
          </q-card>
          <q-card v-else-if="mailState === 'alreadyOnWaitList'" class="bg-secondary error-message" flat>
            Deine E-Mail Adresse steht bereits auf der Warteliste.
          </q-card>
          <q-card v-else-if="mailState === 'addedToWaitList'" class="bg-secondary error-message" flat>
            Deine E-Mail Adresse wurde der Warteliste hinzugefügt.
          </q-card>

          <q-card v-if="errorMessage" class="bg-red-1 error-message" flat>
            {{ errorMessage }}
          </q-card>
          <template v-else-if="mailState !== 'unknown'">
            <q-card class="q-pa-sm" flat>
              Folge uns, um schon jetzt spannende Einblicke in numo zu erhalten:
              <q-card-section class="row justify-evenly full-width">
                <q-btn flat href="https://www.instagram.com/numo_app/" icon="fa-brands fa-instagram" size="lg" target="_blank" />
                <q-btn flat href="https://www.facebook.com/app.numo/" icon="fa-brands fa-square-facebook" size="lg" target="_blank" />
                <q-btn flat href="https://twitter.com/app_numo" icon="fa-brands fa-x-twitter" size="lg" target="_blank" />
                <q-btn flat href="https://www.linkedin.com/company/numo-app/" icon="fa-brands fa-linkedin" size="lg" target="_blank" />
              </q-card-section>
            </q-card>
          </template>

        </q-card-section>

        <q-card-actions align="center">
          <q-btn
            v-if="isRegistrationAllowed"
            :loading="isLoading"
            class="q-mb-sm"
            color="primary"
            label="Registrieren"
            @click="router.push({
              name: ROUTE_NAMES.REGISTRATION,
              query: {'email': email}
            })"
          />
          <q-btn
            v-else
            :loading="isLoading"
            class="q-mb-sm"
            color="primary"
            label="In Warteliste eintragen"
            type="submit"
          />
          <q-btn
            :disable="isLoading"
            class="q-mb-sm"
            color="primary"
            flat
            label="Zum Login"
            @click="redirectPreserveQuery(ROUTE_NAMES.LOGIN)"
          />
        </q-card-actions>
      </q-form>
    </q-card>
  </div>
</template>

<script lang="ts" setup>
  import { onBeforeMount, ref } from 'vue';
  import { useRouting } from 'src/composables/useRouting';
  import { ROUTE_NAMES } from 'src/router/route-names';
  import { useAuthWaitList } from 'src/composables/useAuthWaitList';
  import { useRouter } from 'vue-router';

  const props = withDefaults(
    defineProps<{
      email?: string;
    }>(),
    {
      email: '',
    },
  );

    onBeforeMount(() => {
        if (process.env.ALLOW_REGISTRATION_STRING === 'true') {
            router.push({ name: ROUTE_NAMES.REGISTRATION });
        }
    });

  const router = useRouter();

  const email = ref(props.email);

  const {
    checkMailAndAddToWaitList,
    isLoading,
    mailState,
    isRegistrationAllowed,
    errors,
    errorMessage,
  } = useAuthWaitList(email);

  const { redirectPreserveQuery } = useRouting();

</script>

<style lang="scss" scoped>
  .registration-container {
    max-width: 400px;
    margin: auto;

    .q-card {
      border-radius: 10px;
    }
  }

  .error-message {
    margin-top: 1rem;
    padding: 0.5rem;
  }
</style>
