<template>
    <div class="q-pa-md login-container">
        <LoginInviteInfoCard :redirect-to="props.redirectTo" />
        <LoginEmailVerifyInfoCard :redirect-to="props.redirectTo" />

        <q-card>
            <q-card-section class="bg-filled bg-primary text-white">
                <div class="text-h6 text-center">Login</div>
            </q-card-section>

            <q-form @submit.prevent="login">
                <q-card-section>
                    <q-input
                        v-model="email"
                        :error="loginErrors.email !== undefined"
                        :error-message="loginErrors.email?.join(' ')"
                        :rules="[
              (val, rules) =>
                rules.email(val) || 'Bitte gib eine gültige E-Mail Adresse ein',
            ]"
                        label="E-Mail"
                        lazy-rules
                        outlined
                        type="email"
                        name="email"
                    />
                    <q-input
                        v-model="password"
                        :rules="[(val) => !!val || 'Bitte ein Passwort eingeben']"
                        :type="isPwd ? 'password' : 'text'"
                        label="Password"
                        lazy-rules
                        outlined
                        name="password"
                    >
                        <template v-slot:append>
                            <q-icon
                                :name="isPwd ? 'visibility_off' : 'visibility'"
                                class="cursor-pointer"
                                @click="isPwd = !isPwd"
                            />
                        </template>
                    </q-input>
                    <a
                        class="forgot-password-link q-mt-sm"
                        href=""
                        @click.prevent="redirectPreserveQuery($routeNames.FORGOT_PASSWORD)"
                    >Passwort vergessen?</a
                    >
                </q-card-section>

                <q-card-actions align="center">
                    <q-btn
                        :loading="isLoggingIn"
                        class="q-mb-sm"
                        color="primary"
                        label="Einloggen"
                        type="submit"
                        name="login"
                    />
                    <q-btn
                        :disable="isLoggingIn"
                        class="q-mb-sm"
                        color="primary"
                        flat
                        label="Registrieren"
                        @click="redirectPreserveQuery(ROUTE_NAMES.REGISTRATION)"
                        name="register"
                    />
                </q-card-actions>
            </q-form>
        </q-card>

        <q-card v-if="!$q.platform.is.nativeMobile" class="q-mt-lg">
            <q-card-actions align="center">
                <q-btn flat
                       href="https://play.google.com/store/apps/details?id=com.numoapp.numo.app"
                       style="width: 50%"
                       target="_blank">
                    <img alt="Link zum Google Play Store" class="fit" src="/badge-google-playstore.png" />
                </q-btn>

                <q-btn flat
                       href="https://apps.apple.com/de/app/numo/id6451087523"
                       style="width: 45%"
                       target="_blank">
                    <img alt="Link zum Apple Appstore" class="fit" src="/badge-apple-appstore.svg" />
                </q-btn>
            </q-card-actions>
        </q-card>
    </div>
</template>

<script lang="ts" setup>
    import { computed, onBeforeMount, ref } from 'vue';
    import { useRouter } from 'vue-router';
    import { useAuthStore } from 'src/stores/auth';
    import { useMeta } from 'quasar';
    import { ROUTE_NAMES } from 'src/router/route-names';
    import { useRouting } from 'src/composables/useRouting';
    import LoginInviteInfoCard from 'src/components/invites/LoginInviteInfoCard.vue';
    import { useAuthLogin } from 'src/composables/useAuthLogin';
    import LoginEmailVerifyInfoCard from 'src/components/invites/LoginEmailVerifyInfoCard.vue';

    const props = withDefaults(
        defineProps<{
            redirectTo?: string;
        }>(),
        {
            redirectTo: '/',
        },
    );
    useMeta({ title: 'Login' });

    const router = useRouter();

    const email = ref('');
    const password = ref('');
    const isPwd = ref(true);

    onBeforeMount(() => {
        if (useAuthStore().loggedIn) {
            router.push({
                path: props.redirectTo,
            });
        }
    });

    const { redirectPreserveQuery } = useRouting();
    const { login, loginErrors, isLoggingIn } = useAuthLogin(email, password, computed(() => props.redirectTo));
</script>

<style lang="scss" scoped>
    a {
        text-decoration: none;
        color: blue;
    }

    a:hover {
        text-decoration: none;
        color: darkblue;
    }

    a:active,
    a:focus {
        outline: none;
    }

    .login-container {
        max-width: 400px;
        margin: auto;

        .q-card {
            border-radius: 10px;
        }
    }


    .forgot-password-link {
        display: inline-block;
    }
</style>
