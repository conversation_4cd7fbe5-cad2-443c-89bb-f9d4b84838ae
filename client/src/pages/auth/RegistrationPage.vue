<template>
    <div class="q-pa-md registration-container">
        <LoginInviteInfoCard :redirect-to="props.redirectTo" />

        <q-card class="registration-card">
            <q-card-section class="bg-filled bg-primary text-white">
                <div class="text-h6 text-center">Registrieren</div>
            </q-card-section>

            <q-form @submit.prevent="register">
                <q-card-section>
                    <q-input
                        v-model="email"
                        :error="registerErrors.email !== undefined"
                        :error-message="registerErrors.email?.join(' ')"
                        :rules="[
              (val, rules) =>
                rules.email(val) || 'Bitte gib eine gültige E-Mail Adresse ein',
            ]"
                        label="E-Mail"
                        lazy-rules
                        outlined
                        type="email"
                    />
                    <q-input
                        v-model="firstname"
                        :error="registerErrors.firstname !== undefined"
                        :error-message="registerErrors.firstname?.join(' ')"
                        :rules="[(val) => !!val || 'Bitte gib deinen Vornamen an']"
                        label="Vorname"
                        lazy-rules
                        outlined
                    />
                    <q-input
                        v-model="lastname"
                        :error="registerErrors.lastname !== undefined"
                        :error-message="registerErrors.lastname?.join(' ')"
                        :rules="[(val) => !!val || 'Bitte gib deinen Nachnamen an']"
                        label="Nachname"
                        lazy-rules
                        outlined
                    />

                    <q-input
                        v-model="password"
                        :error="registerErrors.password !== undefined"
                        :error-message="registerErrors.password?.join(' ')"
                        :rules="[(val) => !!val || 'Bitte ein Passwort eingebend']"
                        :type="isPasswordHidden ? 'password' : 'text'"
                        label="Passwort"
                        lazy-rules
                        outlined
                    >
                        <template v-slot:append>
                            <q-icon
                                :name="isPasswordHidden ? 'visibility_off' : 'visibility'"
                                class="cursor-pointer"
                                @click="isPasswordHidden = !isPasswordHidden"
                            />
                        </template>
                    </q-input>
                    <q-input
                        v-model="passwordConfirmation"
                        :error="registerErrors.passwordConfirmation !== undefined"
                        :error-message="registerErrors.passwordConfirmation?.join(' ')"
                        :rules="[
              (val) =>
                val === password || 'Die Passwörter stimmen nicht überein',
            ]"
                        :type="isPasswordHidden ? 'password' : 'text'"
                        label="Passwort wiederholen"
                        lazy-rules
                        outlined
                    >
                        <template v-slot:append>
                            <q-icon
                                :name="
                  isPasswordHidden ? 'visibility_off' : 'visibility'
                "
                                class="cursor-pointer"
                                @click="isPasswordHidden = !isPasswordHidden"
                            />
                        </template>
                    </q-input>

                    Optionale Angaben
                    <select-with-manual-other-option
                        class="q-field--with-bottom"
                        label="Wo hast du von numo erfahren?"
                        v-model="origin"
                        :options="originOptions"
                    />
                    <select-with-manual-other-option
                        label="Was benutzt du bisher?"
                        v-model="competition"
                        :options="competitionOptions"
                    />


                    <q-card v-if="registerErrorMessage" class="bg-red-1 error-message" flat>
                        {{ registerErrorMessage }}
                    </q-card>

                </q-card-section>

                <q-card-actions align="center">

                    <q-btn
                        v-if="errorNotOnAllowList"
                        class="q-mb-sm"
                        color="primary"
                        label="Zur Warteliste"
                        @click="router.push({
              name: ROUTE_NAMES.ADD_TO_WAIT_LIST,
              query: {'email': email}
            })"
                    />
                    <q-btn
                        v-else
                        :loading="isLoading"
                        class="q-mb-sm"
                        color="primary"
                        label="Registrieren"
                        type="submit"
                    />


                    <q-btn
                        :disable="isLoading"
                        class="q-mb-sm"
                        color="primary"
                        flat
                        label="Zum Login"
                        @click="redirectPreserveQuery(ROUTE_NAMES.LOGIN)"
                    />
                </q-card-actions>
                <q-card-section>
                    Mit der Registrierung stimme ich den
                    <router-link :to="{ name: $routeNames.PRIVACY }" target="_blank"
                    >Datenschutzbestimmungen
                    </router-link
                    >
                    zu.
                </q-card-section>
            </q-form>
        </q-card>
    </div>
</template>

<script lang="ts" setup>
    import { computed, onBeforeMount, ref } from 'vue';
    import { useRouter } from 'vue-router';
    import { useAuthStore } from 'src/stores/auth';
    import { useRouting } from 'src/composables/useRouting';
    import LoginInviteInfoCard from 'src/components/invites/LoginInviteInfoCard.vue';
    import { ROUTE_NAMES } from 'src/router/route-names';
    import { useInviteRedirect } from 'src/composables/useInviteRedirect';
    import { useAuthRegister } from 'src/composables/useAuthRegister';
    import { useAuthLogin } from 'src/composables/useAuthLogin';
    import SelectWithManualOtherOption from 'src/components/SelectWithManualOtherOption.vue';

    const props = withDefaults(
        defineProps<{
            redirectTo?: string;
            token?: string;
            email?: string;
        }>(),
        {
            redirectTo: '/',
            email: '',
        },
    );

    const router = useRouter();

    const {
        token,
        isRedirectToInvite,
    } = useInviteRedirect(computed(() => props.redirectTo));

    const isRegistrationAllowed = computed(() =>
        process.env.ALLOW_REGISTRATION_STRING === 'true'
        || isRedirectToInvite.value
        || props.email.length >= 5
    );

    const isLoading = computed(() => isRegistering.value || isLoggingIn.value);

    // Password field visibility toggles
    const isPasswordHidden = ref(true);

    const firstname = ref('');
    const lastname = ref('');
    const email = ref(props.email);
    const password = ref('');
    const passwordConfirmation = ref('');

    const origin = ref<string>();
    const originOptions =[
        'Einladung vom Trainer',
        'Google',
        'Facebook',
        'Instagram',
        'Empfehlung',
        'Bandenwerbung',
        'Flyer',
        'Flyer Fussballschule Grenzland',
    ]
    const competition = ref<string>();
    const competitionOptions =[
        'WhatsApp Umfragen',
        'Spielerplus',
        'Spond',
        'Sportmember',
    ]

    onBeforeMount(() => {
        if (useAuthStore().loggedIn) {
            router.push({
                path: props.redirectTo,
            });
        } else if (!isRegistrationAllowed.value) {
            router.push({ name: ROUTE_NAMES.ADD_TO_WAIT_LIST });
        }
    });

    // We register the user, if successful we try an additional login call. If it is successful the useAuthLogin will redirect to the props.redirectTo page. If
    // it fails we will redirect the user to the login page.
    const {
        login,
        loginErrors,
        isLoggingIn,
    } = useAuthLogin(email, password, computed(() => props.redirectTo),
        () => {
            redirectPreserveQuery(ROUTE_NAMES.LOGIN);
        });

    const {
        register,
        isRegistering,
        registerErrors,
        registerErrorMessage,
        errorNotOnAllowList,
    } = useAuthRegister(firstname, lastname, email, password, passwordConfirmation, token, true, origin, competition,
        (response) => login());


    const { redirectPreserveQuery } = useRouting();

</script>

<style lang="scss" scoped>
    .registration-container {
        max-width: 400px;
        margin: auto;

        .q-card {
            border-radius: 10px;
        }
    }

    .error-message {
        margin-top: 1rem;
        padding: 0.5rem;
    }
</style>
