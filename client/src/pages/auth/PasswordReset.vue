<template>
  <div class="q-pa-md reset-password-container">
    <q-card class="reset-password-card">
      <q-card-section class="bg-filled bg-primary text-white text-center">
        <div class="text-h6">Passwort zurücksetzen</div>
          {{props.email}}
      </q-card-section>

      <q-card-section v-if="useAuthStore().loggedIn && props.email !== useAuthStore().user?.email">
          <span class="text-negative text-bold">Achtung:</span>
          Du bist aktuell mit einem anderen Benutzer angemeldet:
          <strong>{{useAuthStore().user?.email}}</strong>. Das Passwort wird aber für die oben angegebene E-Mail Adresse geändert!
           Im Anschluss bleibst du weiterhin mit deinem aktuellen Nutzer angemeldet.
    </q-card-section>
      <q-form @submit.prevent="resetPassword">

        <q-card-section>
          <q-input
            v-model="password"
            autocompletetype="password"
            :rules="[val => !!val || 'Bitte ein Passwort eingebend']"
            :type="isPasswordHidden ? 'password' : 'text'"
            label="Neues Passwort"
            lazy-rules
            outlined
          >
              <template v-slot:append>
                  <q-icon
                      :name="isPasswordHidden ? 'visibility_off' : 'visibility'"
                      class="cursor-pointer"
                      @click="isPasswordHidden = !isPasswordHidden"
                  />
              </template>
          </q-input>
          <q-input
            v-model="passwordConfirmation"
            autocompletetype="password"
            :rules="[
              (val) =>
                val === password || 'Die Passwörter stimmen nicht überein',
            ]"
            :type="isPasswordHidden ? 'password' : 'text'"
            label="Passwort Bestätigung"
            outlined
          >
              <template v-slot:append>
                  <q-icon
                      :name="isPasswordHidden ? 'visibility_off' : 'visibility'"
                      class="cursor-pointer"
                      @click="isPasswordHidden = !isPasswordHidden"
                  />
              </template>
          </q-input>

          <div class="q-mt-md">
            <q-btn
              class="q-mb-sm"
              color="primary"
              label="Passwort zurücksetzen"
              type="submit"
            />
            <q-btn
              class="q-mb-sm"
              flat
              :label="useAuthStore().loggedIn ? 'Zur Startseite' : 'Zurück zum Login'"
              @click="useAuthStore().loggedIn ? $router.push('/') : $router.push({name: ROUTE_NAMES.LOGIN}) "
            />
          </div>
        </q-card-section>
      </q-form>
    </q-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { useMutation } from '@tanstack/vue-query';
  import { api } from 'src/boot/axios';
  import { Notify } from 'quasar';
  import { useRouter } from 'vue-router';
  import { ROUTE_NAMES } from 'src/router/route-names';
  import { useAuthStore } from 'src/stores/auth';

  const props = withDefaults(defineProps<{
    email?: string,
    token?: string
  }>(), {
    email: '',
    token: '',
  });

  // Password field visibility toggles
  const isPasswordHidden = ref(true);

  const password = ref('');
  const passwordConfirmation = ref('');

  const router = useRouter();

  const {
      mutate: resetPassword
  } = useMutation({
    mutationFn: () =>
      api.post('/auth/password/reset', {
        email: props.email,
        token: props.token,
        password: password.value,
        password_confirmation: passwordConfirmation.value,
      }).then(data => data.data),
    onSuccess: (response) => {
      password.value = '';
      passwordConfirmation.value = '';
      notifyMessage('Password zurücksetzen war erfolgreich.', 'positive');
      router.push({ name: ROUTE_NAMES.LOGIN });
    },
    onError: (error) => {
      console.log('error');
      notifyMessage('Passwort zurücksetzen fehlgeschlagen.', 'negative');
    },
  });

  function notifyMessage(message: string, color: string) {
    Notify.create({
      message: message,
      color: color,
      position: 'top',
    });
  }
</script>

<style lang="scss" scoped>
  .reset-password-container {
    max-width: 400px;
    margin: auto;
  }

  .reset-password-card {
    border-radius: 10px;
    margin-bottom: 1rem;
  }
</style>
