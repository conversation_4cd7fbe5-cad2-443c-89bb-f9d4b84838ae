<template>
  <PageContainer :key="eventId">
    <span v-if="isLoading">lade Daten... <q-spinner /></span>
    <span v-else-if="isError">Termin wurde nicht gefunden</span>
    <template v-else-if="event">
      <PageHeaderWithAction
        :sub-title="event.getDateBeginFormatted('dd. DD.MM.YYYY') + ' - ' + event.getTimeBeginFormatted()"
        :title="event.getPageTitle()"
      >
        <q-btn
          v-if="hasPermissionForTeam(event.team, 'team.event.create')"
          :to="{name: $routeNames.EVENTS_EDIT, params: {eventId: props.eventId}}"
          color="primary"
          icon="edit"
          label="Termin bearbeiten"
          size="12px"
        />
      </PageHeaderWithAction>

      <EventCard
        v-if="event"
        :is-fetching-team-event-data="isFetching"
        :show-team-header="useAuthStore().teamsCount > 1"
        :teamEvent="event"
        :with-detail-button="false"
      />

      <q-separator class="q-my-md" inset />

      <EventTaskCard
        v-if="event"
        :teamEvent="event"
      />

      <q-card class="q-mt-md" flat>
        <q-card-actions class="q-pt-none" vertical>
          <q-btn
            icon="content_copy"
            label="Link zum Termin kopieren"
            no-caps
            outline
            @click="copyEventLink()"
          />
        </q-card-actions>
      </q-card>
    </template>
  </PageContainer>
</template>

<script lang="ts" setup>
  import PageContainer from 'src/components/global/PageContainer.vue';
  import EventCard from 'src/components/events/EventCard.vue';
  import { useAuthStore } from 'src/stores/auth';
  import { useQuery } from '@tanstack/vue-query';
  import TeamEvent from 'src/models/TeamEvent';
  import { ref, toRef } from 'vue';
  import PageHeaderWithAction from 'src/components/global/PageHeaderWithAction.vue';
  import { Notify } from 'quasar';
  import { Clipboard } from '@capacitor/clipboard';
  import { queries } from 'src/queries';
  import EventTaskCard from 'src/components/events/EventTaskCard.vue';

  const props = defineProps<{
    eventId: string
  }>();

  const event = ref<TeamEvent>();


  const {
    isLoading,
    isFetching,
    isError,
  } =
    useQuery({
      ...queries.events.detail(toRef(props, 'eventId')),
      select: (data) => {
        event.value = data;
      },
    });

  function copyEventLink() {
    Clipboard.write({
      string: location.href,
    });
    notifyMessage(
      'Link zum Termin in die Zwischenablage kopiert.',
    );
  }

  function notifyMessage(message: string) {
    Notify.create({
      message: message,
      color: 'positive',
      position: 'bottom',
      timeout: 2000,
    });
  }

</script>

<style lang="scss" scoped>

</style>
