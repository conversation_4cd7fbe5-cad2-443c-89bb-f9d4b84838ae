<template>
  <PageContainer>
    <EditEvent
      page-title="Termin kopieren"
      v-bind:create-copy="true"
      v-bind:event-id="props.eventId"
    />
  </PageContainer>
</template>

<script lang="ts" setup>
  import PageContainer from 'src/components/global/PageContainer.vue';
  import EditEvent from 'src/components/events/EditEvent.vue';

  const props = defineProps<{
    eventId: string
  }>();

</script>

<style lang="scss" scoped>

</style>
