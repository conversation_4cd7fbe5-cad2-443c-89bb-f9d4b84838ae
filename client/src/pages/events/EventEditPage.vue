<template>
  <PageContainer>
    <EditEvent
      :event-id="props.eventId"
      :with-copy-button="true"
      :with-delete-button="true"
      page-title="Termin bearbeiten"
      edit-mode="edit"
    />
  </PageContainer>
</template>

<script lang="ts" setup>
  import PageContainer from 'src/components/global/PageContainer.vue';
  import EditEvent from 'src/components/events/EditEvent.vue';

  const props = defineProps<{
    eventId: string
  }>();
</script>

<style lang="scss" scoped>

</style>
