<template>
  <PageContainer>
    <EventList
      :end-date="endDate"
      :show-load-more-events-button="true"
      :show-subscribe-events-button="true"
      :start-date="startDate"
      :with-create-button="true"
    />
  </PageContainer>
</template>

<script lang="ts" setup>
  import EventList from 'src/components/events/EventList.vue';
  import PageContainer from 'src/components/global/PageContainer.vue';
  import { date } from 'quasar';
  import subtractFromDate = date.subtractFromDate;
  import addToDate = date.addToDate;

  const startDate = subtractFromDate(new Date(), { days: 1 });
  const endDate = addToDate(new Date(), { days: 20 });

</script>

<style lang="scss" scoped>

</style>
