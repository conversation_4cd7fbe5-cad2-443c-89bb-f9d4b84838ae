<template>
  <PageContainer>
    <PageHeaderWithAction title="Teamkasse">
        <q-toggle v-model="showLedgerBalance" v-if="teams && teams.length > 0">
            Kassenstände anzeigen
        </q-toggle>
    </PageHeaderWithAction>


      <template v-if="!isLoading">
          <template v-if="teams && teams.length > 0">

              <template v-if="teams.length > 2 && hasPermissionForAnyTeam('team.ledger.manage')">
                  <q-toggle v-model="showOnlyLedgerWhereUserIsManager" class="q-mb-sm">
                      Zeige nur Kassen wo ich {{TeamRole.getDisplayName('treasurer')}} bin
                  </q-toggle>
              </template>

              <template
                  v-for="(team) in teams"
                  :key="team.id"
              >
                  <div class="q-mb-lg">
                      <TeamLedgerCard
                          v-if="!showOnlyLedgerWhereUserIsManager || hasPermissionForTeam(team, 'team.ledger.manage')"
                          :team="team"
                          :is-loading="isFetching"
                          :show-ledger-balance="showLedgerBalance"
                          @refresh-ledger="ledgerQueryRefetch"
                      ></TeamLedgerCard>
                  </div>
              </template>
          </template>
          <template v-else>
              <!--
              else Teil sollte nicht mehr angezeigt werden, da durch
              check-auth.ts => checkRouteRequiresTeamsValid()
              die Seite nicht aufgerufen werden kann
              -->
              <q-card flat>
                  <q-card-section horizontal>
                      <q-space />
                      <div>Keine Teams gefunden</div>
                      <q-space />
                  </q-card-section>
              </q-card>
          </template>
      </template>
      <template v-else>
          <div>lade Teams...
              <q-spinner />
          </div>
      </template>

  </PageContainer>
</template>

<script lang="ts" setup>
  import PageContainer from 'src/components/global/PageContainer.vue';
  import { useQuery } from '@tanstack/vue-query';
  import { queries } from 'src/queries';
  import TeamLedgerCard from 'src/components/ledger/TeamLedgerCard.vue';
  import { ref, watch } from 'vue';
  import TeamRole from '../../models/TeamRole';
  import PageHeaderWithAction from 'src/components/global/PageHeaderWithAction.vue';

  const {
      data: teams,
      isLoading,
      isFetching,
      refetch: ledgerQueryRefetch,
  } = useQuery({
      ...queries.teams.allWithLedger,
      retry: false,
      refetchOnWindowFocus: false
  });

  const showOnlyLedgerWhereUserIsManager = ref(false)
  const showLedgerBalance = ref(false)

  watch(teams, (newValue, oldValue) => {
      if(oldValue === undefined && newValue && newValue.length <= 1) {
          showLedgerBalance.value = true
      }
  })

</script>

<style lang="scss" scoped>

</style>
