<template>
    <PageContainer full-width>
        <q-pull-to-refresh class="absolute-top full-height" @refresh="refresh" />
        <q-pull-to-refresh @refresh="refresh">
            <PageHeader title="Teamkasse" :sub-title="team?.name" />
            <span v-if="isInitialLoading">lade <PERSON>...<q-spinner /></span>
            <span v-else-if="isError"><PERSON><PERSON> beim <PERSON>den der Teamkasse</span>
            <template v-else>
                <TeamLedgerBalanceOverview
                    class="partial-width"
                    :ledger="team.ledger"
                    :is-loading="isFetching"
                    @refresh-ledger="ledgerQueryRefetch"
                />

                <q-card class="q-mt-md">
                    <q-card-section class="q-pa-none">
                        <q-tabs
                            :model-value="tab"
                            active-color="primary"
                            align="left"
                            class="text-grey"
                            dense
                            draggable="false"
                            indicator-color="primary"
                            narrow-indicator
                            no-caps
                            mobile-arrows
                            outside-arrows
                        >
                            <q-tab class="bg-transparent q-px-sm" label="Übersicht" name="overview"
                                   @click="setSelectedTab('overview')" />
                            <q-separator vertical inset />
                            <q-separator vertical inset />
                            <q-tab class="bg-transparent q-px-sm" label="Strafen" name="fines"
                                   @click="setSelectedTab('fines')" />
                            <q-separator vertical inset />
                            <q-tab class="bg-transparent q-px-sm" label="Beiträge" name="dues"
                                   @click="setSelectedTab('dues')" />
                            <q-separator vertical inset />
                            <q-tab class="bg-transparent q-px-sm" label="Strafenkatalog" name="fines-catalog"
                                   @click="setSelectedTab('fines-catalog')" />
                            <q-separator vertical inset />
                            <q-tab class="bg-transparent q-px-sm" label="Beitragskatalog" name="dues-catalog"
                                   @click="setSelectedTab('dues-catalog')" />
                            <q-tab class="bg-transparent q-px-sm" label="Umsätze" name="transactions"
                                   @click="setSelectedTab('transactions')" />
                        </q-tabs>
                        <q-separator />
                        <q-tab-panels :model-value="tab" animated>
                            <q-tab-panel class="q-pa-none" name="overview">
                                <TeamLedgerMemberOverview
                                    :ledger="team.ledger"
                                    :is-ledger-loading="isFetching"
                                    @refresh-ledger="ledgerQueryRefetch"
                                />
                            </q-tab-panel>
                            <q-tab-panel class="q-pa-none" name="fines">
                                <TeamLedgerClaimFinesOverview
                                    :ledger="team.ledger"
                                    :is-ledger-loading="isFetching"
                                    @refresh-ledger="ledgerQueryRefetch"
                                />
                            </q-tab-panel>
                            <q-tab-panel class="q-pa-none" name="dues">
                                <TeamLedgerClaimDuesOverview
                                    :ledger="team.ledger"
                                    :is-ledger-loading="isFetching"
                                    @refresh-ledger="ledgerQueryRefetch"
                                />
                            </q-tab-panel>
                            <q-tab-panel class="q-pa-none" name="fines-catalog">
                                <TeamLedgerFinesCatalog
                                    :ledger="team.ledger"
                                    :is-ledger-loading="isFetching"
                                    @refresh-ledger="ledgerQueryRefetch"
                                />
                            </q-tab-panel>
                            <q-tab-panel class="q-pa-none" name="dues-catalog">
                                <TeamLedgerDuesCatalog
                                    :ledger="team.ledger"
                                    :is-ledger-loading="isFetching"
                                    @refresh-ledger="ledgerQueryRefetch"
                                />
                            </q-tab-panel>
                            <q-tab-panel class="q-pa-none" name="transactions">
                                <TeamLedgerTransactionsOverview
                                    :ledger="team.ledger"
                                    :is-ledger-loading="isFetching"
                                    @refresh-ledger="ledgerQueryRefetch"
                                />
                            </q-tab-panel>
                        </q-tab-panels>
                    </q-card-section>
                </q-card>

            </template>
        </q-pull-to-refresh>
    </PageContainer>
</template>

<script setup lang="ts">
    import PageContainer from 'src/components/global/PageContainer.vue';
    import PageHeader from 'src/components/global/PageHeader.vue';
    import { useQuery } from '@tanstack/vue-query';
    import { queries } from 'src/queries';
    import TeamLedgerBalanceOverview from 'src/components/ledger/TeamLedgerBalanceOverview.vue';
    import { ROUTE_NAMES } from 'src/router/route-names';
    import { useRouter } from 'vue-router';
    import TeamLedgerTransactionsOverview from 'src/components/ledger/TeamLedgerTransactionsOverview.vue';
    import { watchEffect } from 'vue';
    import TeamLedgerFinesCatalog from 'src/components/ledger/TeamLedgerFinesCatalog.vue';
    import TeamLedgerClaimFinesOverview from 'src/components/ledger/TeamLedgerClaimFinesOverview.vue';
    import TeamLedgerClaimDuesOverview from 'src/components/ledger/TeamLedgerClaimDuesOverview.vue';
    import TeamLedgerMemberOverview from 'src/components/ledger/TeamLedgerMemberOverview.vue';
    import TeamLedgerDuesCatalog from 'src/components/ledger/TeamLedgerDuesCatalog.vue';

    const props = defineProps<{
        teamId: string,
        tab?: string,
    }>();


    const router = useRouter();

    function refresh(done: () => void) {
        ledgerQueryRefetch().then(() => done());
    }

    const {
        isRefetching: isLedgerQueryRefetching,
        isLoading: isInitialLoading,
        isFetching,
        refetch: ledgerQueryRefetch,
        isError,
        data: team
    } = useQuery({
        ...queries.teams.withLedgerDetail(props.teamId),
        enabled: true,
        refetchOnWindowFocus: false,
        structuralSharing: false
    });

    watchEffect(() => {
        if (!props.tab) {
            setSelectedTab('overview');
        }
    });

    function setSelectedTab(tab: string) {
        router.replace({
            name: ROUTE_NAMES.LEDGER, params: {
                teamId: props.teamId,
                tab,
            },
        });
    }

</script>

<style lang="scss" scoped></style>
