<template>
  <PageContainer>
    <EditICalendar
      :iCalendarId="props.iCalendarId"
      :with-delete-button="true"
      page-title="Kalender Link bearbeiten"
    />
  </PageContainer>
</template>

<script lang="ts" setup>
  import PageContainer from 'src/components/global/PageContainer.vue';
  import EditICalendar from 'src/components/icalendar/EditICalendar.vue';

  const props = defineProps<{
      iCalendarId: string
  }>();
</script>

<style lang="scss" scoped>

</style>
