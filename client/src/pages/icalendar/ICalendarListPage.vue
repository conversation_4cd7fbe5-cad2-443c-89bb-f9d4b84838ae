<template>
  <PageContainer>
    <PageHeaderWithAction
      title="Kalender Links">
      <q-btn
        :to="{ name: $routeNames.ICALENDAR_CREATE }"
        color="primary"
        icon="add"
        label="Link erstellen"
        size="12px"
      />
    </PageHeaderWithAction>

    <span v-if="isLoading">lade Daten... <q-spinner /></span>
    <span v-else-if="isError"><PERSON><PERSON></span>
    <template v-else>
      <template v-if="iCalendarData && iCalendarData.length > 0">
        <template
          v-for="(iCalendar) in iCalendarData"
          :key="iCalendar.id"
        >
          <div class="q-my-md">
            <ICalendarCard
              :iCalendar="iCalendar"
            />
          </div>
        </template>
      </template>
      <template v-else>
        <q-card flat>
          <q-card-section>
            <q-space />
              <div class="text-center"><PERSON><PERSON>lender Links gefunden.</div>
            <q-space />
          </q-card-section>
        </q-card>
      </template>
    </template>
      <q-card class="q-mt-md">
          <q-card-section class="bg-secondary q-py-xs">
              <div class="text-h6">Infos zu Kalender Links</div>
          </q-card-section>
          <q-card-section>
              Erstelle beliebig viele Links und entscheide selbst, welche Teams darin enthalten sind.<br>
              <strong>Achtung:</strong>
              Damit dein Kalender auf die Termine zugreifen kann, sind Details zu den Terminen über diesen Link öffentlich abrufbar.
              Falls du den Link unbeabsichtigt irgendwo geteilt hast, kannst du ihn hier einfach löschen und einen neuen erstellen.
          </q-card-section>
      </q-card>

  </PageContainer>
</template>

<script lang="ts" setup>
  import PageContainer from 'src/components/global/PageContainer.vue';
  import PageHeaderWithAction from 'src/components/global/PageHeaderWithAction.vue';
  import { useQuery } from '@tanstack/vue-query';
  import { computed, onMounted, ref } from 'vue';
  import TeamMember from 'src/models/TeamMember';
  import { queries } from 'src/queries';
  import { useAuthStore } from 'src/stores/auth';
  import Person from 'src/models/Person';
  import ICalendarCard from 'src/components/icalendar/ICalendarCard.vue';
  import ICalendar from 'src/models/ICalendar';

  type iCalendarData = ICalendar[]

  const {
    data: iCalendarData,
    isError,
    isLoading,
  } = useQuery(queries.iCalendars.all);


  // duplicated code in EditAbsence.vue, cleanup whit #308
  const hasMultipleTargetMembers = ref(false);
  const firstMember = ref<TeamMember>();
  const singleMemberSubtitle = computed(() => {
    if (hasMultipleTargetMembers.value || !firstMember.value) {
      return undefined;
    } else {
      return firstMember.value.name + ' (' + firstMember.value?.team.name + ')';
    }
  });

  onMounted(() => {
    const persons = [useAuthStore().person as Person];
    const memberSum = persons.reduce((count, person) => person.teamMembers.length, 0);
    hasMultipleTargetMembers.value = memberSum > 1;
    if (memberSum === 1) {
      firstMember.value = persons.find((person) => person.teamMembers.length > 0)?.teamMembers[0] as TeamMember;
    }
  });


</script>

<style lang="scss" scoped></style>
