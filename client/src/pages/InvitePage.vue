<template>
  <PageContainer>
    <PageHeader title="Team beitreten" />

    <q-card v-if="inviteInfo.isLoading">
      <q-card-section>
        <div class="text-h6">
          Prüfe Einladungslink
          <q-spinner />
        </div>
      </q-card-section>
    </q-card>

    <q-card v-else-if="inviteInfo.invite">
      <q-card-section class="bg-primary text-white" horizontal>
        <q-card-section class="q-py-xs">
          <div class="text-h6">{{ inviteInfo.invite.team.name }}</div>
          <div class="text-subtitle2">
            Du wurdest eingeladen diesem Team beizutreten.
          </div>
        </q-card-section>
      </q-card-section>

      <q-card-section class="bg-secondary">
        <div class="text-body1">
          Du bist eingeloggt als <span class="text-weight-bold">{{ authStore.fullName }}</span>.<br />
          Möchtest du die Einladung mit einem anderen Account annehmen? Klicke hier:
          <q-btn
            :loading="isLoggingOut"
            dense
            flat
            icon="logout"
            label="Account wechseln (Ausloggen)"
            no-caps
            @click="logout"
          />
        </div>
      </q-card-section>

      <q-card-section class="no-padding">
        <TeamInvite v-if="inviteInfo.invite?.isTeamInvite()" :invite="inviteInfo.invite as Invite<Team>" />
        <TeamMemberInvite v-else-if="inviteInfo?.invite?.isTeamMemberInvite()" :invite="inviteInfo.invite as Invite<TeamMember>" />
      </q-card-section>

    </q-card>

    <q-card v-if="inviteInfo.errorMessage">
      <q-card-section class="bg-primary text-white" horizontal>
        <q-card-section class="q-py-xs">
          <div class="text-h6">Fehler</div>
        </q-card-section>
      </q-card-section>
      <q-card-section>
        <div class="text-h6 text-red">{{ inviteInfo.errorMessage }}</div>
      </q-card-section>
    </q-card>

  </PageContainer>
</template>

<script lang="ts" setup>
  import { useAuthStore } from 'src/stores/auth';
  import PageContainer from 'src/components/global/PageContainer.vue';
  import PageHeader from 'src/components/global/PageHeader.vue';
  import Team from 'src/models/Team';
  import Invite from 'src/models/Invite';
  import TeamMember from 'src/models/TeamMember';
  import TeamInvite from 'src/components/invites/TeamInvite.vue';
  import TeamMemberInvite from 'src/components/invites/TeamMemberInvite.vue';
  import { useAuthLogout } from 'src/composables/useAuthLogout';
  import { useInviteToken } from 'src/composables/useInviteToken';
  import { computed } from 'vue';

  const props = withDefaults(defineProps<{
    token?: string
  }>(), {
    token: '',
  });

  const authStore = useAuthStore();

  const {
    logout,
    isLoggingOut,
  } = useAuthLogout();

  const inviteInfo = useInviteToken(computed(() => props.token));  // props aren't reactive by default

</script>

<style scoped>

</style>
