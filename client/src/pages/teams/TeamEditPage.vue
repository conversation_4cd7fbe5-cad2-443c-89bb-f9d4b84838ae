<template>
    <PageContainer full-width>
        <q-pull-to-refresh class="absolute-top full-height" @refresh="refresh" />
        <q-pull-to-refresh @refresh="refresh">
            <div v-if="team">
                <q-card class="q-my-sm">
                    <q-card-section class="bg-primary text-white" horizontal>
                        <q-card-section class="q-py-xs full-width">
                            <div class="text-h6">{{ team.name }}</div>
                            <div class="text-subtitle2">
                                {{ team?.getMembersCount(true) + ' Mitglieder' }}
                            </div>
                        </q-card-section>
                        <q-card-section v-if="hasPermissionForTeam(team, 'team.invite.create')"
                        >

                            <q-btn-group outline>
                                <q-btn
                                    v-if="team.hasPersistedInvite()"
                                    :loading="team.invite?.isDeletingStarted"
                                    dense
                                    icon="content_copy"
                                    outline
                                    @click="copyTeamInvite(team)"
                                />
                                <q-btn
                                    v-if="!team.hasPersistedInvite()"
                                    :loading="team.invite?.isSaving"
                                    dense
                                    icon="add_link"
                                    outline
                                    @click="createTeamInvite(team)"
                                />

                                <q-btn-dropdown
                                    :disable="team.invite?.isSaving || team.invite?.isDeletingStarted"
                                    dense
                                    outline
                                >
                                    <q-list>
                                        <q-item
                                            v-if="team.hasPersistedInvite()"
                                            v-close-popup
                                            clickable
                                            @click="copyTeamInvite(team)"
                                        >
                                            <q-item-section avatar>
                                                <q-icon name="content_copy" />
                                            </q-item-section>
                                            <q-item-section>
                                                <q-item-label>Einladungslink kopieren</q-item-label>
                                            </q-item-section>
                                        </q-item>

                                        <q-item
                                            v-if="team.hasPersistedInvite()"
                                            v-close-popup
                                            clickable
                                            @click="deleteTeamInvite(team)"
                                        >
                                            <q-item-section avatar>
                                                <q-icon name="clear" />
                                            </q-item-section>
                                            <q-item-section>
                                                <q-item-label>Einladungslink löschen</q-item-label>
                                            </q-item-section>
                                        </q-item>

                                        <q-item
                                            v-if="!team.hasPersistedInvite()"
                                            v-close-popup
                                            clickable
                                            @click="createTeamInvite(team)"
                                        >
                                            <q-item-section avatar>
                                                <q-icon name="add_link" />
                                            </q-item-section>
                                            <q-item-section>
                                                <q-item-label>Einladungslink erstellen</q-item-label>
                                            </q-item-section>
                                        </q-item>
                                    </q-list>
                                </q-btn-dropdown>
                            </q-btn-group>

                        </q-card-section>
                        <q-card-actions class="q-py-none" vertical>
                            <PopUpEditButton
                                v-if="hasPermissionForTeam(team, 'team.update')"
                                v-model="team.name"
                                :is-saving="team.isSaving"
                                class="full-height "
                                hint="Teamname ändern"
                                @save="saveTeam"
                            />
                        </q-card-actions>
                    </q-card-section>

                    <q-tabs
                        ref="tabsRef"
                        :model-value="tab"
                        active-color="primary"
                        align="left"
                        class="text-grey"
                        dense
                        draggable="false"
                        indicator-color="primary"
                        narrow-indicator
                    >
                        <q-tab class="bg-transparent" label="Mitglieder" name="members"
                               @click="setSelectedTab('members')" />
                        <q-tab class="bg-transparent" label="Abonnement" name="subscription"
                               v-if="hasSubscriptionPermission"
                               @click="setSelectedTab('subscription')" />
                    </q-tabs>

                    <q-separator />

                    <q-tab-panels :model-value="tab" animated>

                        <q-tab-panel class="q-px-none" name="members">
                             <TeamMemberList
                                :is-team-reloading="isTeamQueryRefetching"
                                :team="team"
                                @add-member="addMember"
                                @delete-member="deleteMember"
                                @save-member="saveMember"
                                @create-invite="createInvite"
                                @delete-invite="deleteInvite"
                            ></TeamMemberList>
                        </q-tab-panel>

                        <q-tab-panel class="q-pa-none" name="subscription" v-if="hasSubscriptionPermission">
                            <TeamSubscriptionSettings
                              :initialTeamData="team"
                            />
                        </q-tab-panel>
                    </q-tab-panels>
                </q-card>

                <q-card v-if="hasPermissionForTeam(team, 'team.invite.create')"
                        class="q-mt-md"
                >
                    <q-card-section class="bg-primary text-white">
                        <div class="text-subtitle2">
                            Einladungslink erstellen
                        </div>
                    </q-card-section>
                    <q-card-section>
                        Über den Link können deine Mitglieder deinem Team beitreten.<br />
                        Mitglieder mit der Rolle "{{ TeamRole.getDisplayName('manager') }}" können über diesen Link nicht ausgewählt werden.
                        <div
                            v-if="hasPermissionForTeam(team, 'team.member.invite.create.other')"
                            class="q-mt-sm">
                            Du kannst oben in der Liste auch einen individuellen Link für Mitglieder erstellen.
                            Das ist aus Sicherheitsgründen nötig für Mitglieder mit der Rolle "{{ TeamRole.getDisplayName('manager') }}",
                            kann aber auch für jedes andere Mitglied verwendet werden.
                        </div>
                    </q-card-section>
                    <q-card-actions vertical>
                        <q-btn
                            v-if="team.hasPersistedInvite()"
                            :loading="team.invite?.isDeletingStarted"
                            icon="content_copy"
                            label="Einladungslink kopieren"
                            no-caps
                            outline
                            @click="copyTeamInvite(team)"
                        />
                        <q-btn
                            v-if="team.hasPersistedInvite()"
                            :loading="team.invite?.isDeletingStarted"
                            icon="clear"
                            label="Einladungslink löschen"
                            no-caps
                            outline
                            @click="deleteTeamInvite(team)"
                        />
                        <q-btn
                            v-if="!team.hasPersistedInvite()"
                            :loading="team.invite?.isSaving"
                            icon="add_link"
                            label="Einladungslink erstellen"
                            no-caps
                            outline
                            @click="createTeamInvite(team)"
                        />

                    </q-card-actions>
                </q-card>

                <q-card v-if="hasPermissionForTeam(team, 'team.update') || hasFeature('admin.stats')" class="q-mt-xs" flat>
                    <q-card-actions align="left" vertical>
                        <q-btn
                            :loading="team?.isDeletingStarted"
                            class="q-my-md"
                            color="negative"
                            icon="delete"
                            label="Team Löschen"
                            @click="
                confirmBeforeAction({
                  dialogOptions: {
                    title: 'Soll das Team wirklich gelöscht werden?',
                    message:
                      'Es werden alle Mitglieder, Termine und Statistiken unwiderruflich gelöscht',
                  },
                  onOk: deleteTeam,
                })
              "
                        />
                    </q-card-actions>
                </q-card>

            </div>
        </q-pull-to-refresh>
    </PageContainer>
</template>

<script lang="ts" setup>

    import { onMounted, ref, watchEffect, onBeforeMount, computed } from 'vue';
    import TeamMemberList from 'src/components/teams/TeamMemberList.vue';
    import { useMutation, useQuery } from '@tanstack/vue-query';
    import { queries } from 'src/queries';
    import Team from 'src/models/Team';
    import TeamMember from 'src/models/TeamMember';
    import { saveTeamMemberOnServer } from 'src/queries/mutations/teams';
    import { useAuthStore } from 'src/stores/auth';
    import PageContainer from 'src/components/global/PageContainer.vue';
    import PopUpEditButton from 'src/components/PopUpEditButton.vue';
    import Invite from 'src/models/Invite';
    import { confirmBeforeAction } from 'src/helpers/dialogs';
    import { ROUTE_NAMES } from 'src/router/route-names';
    import { useRouter, useRoute } from 'vue-router';
    import { Clipboard } from '@capacitor/clipboard';
    import { Notify, QTabs, useMeta } from 'quasar';
    import TeamRole from '../../models/TeamRole';
    import TeamSubscriptionSettings from 'src/components/teams/TeamSubscriptionSettings.vue';
    import { Capacitor } from '@capacitor/core';

    const props = withDefaults(defineProps<{
        teamId: string
        tab?: string,
    }>(), {
        tab: 'members',
    });

    const team = ref<Team>();
    const tabsRef = ref<QTabs>();

    useMeta(() => ({
        title: team.value?.name,
    }));

    const router = useRouter();
    const route = useRoute();

    // relates to subscription feature, see FeatureType.php
    const isOldNativeApp = computed(() => Capacitor.isNativePlatform() && !Capacitor.isPluginAvailable('Browser'));

    const hasSubscriptionPermission = computed(() => {
      return team.value && useAuthStore().hasPermissionForTeam(team.value, 'team.subscription.manage')
        && (useAuthStore().hasFeature('subscription') || !isOldNativeApp.value);
    });

    watchEffect(() => {
        if (team.value && route.params.tab) {
            const currentTab = route.params.tab as string;

            if (currentTab === 'subscription' && !hasSubscriptionPermission.value) {
                setSelectedTab('members');
            }
        }
    });


    // Ensure tab is set correctly on page load/reload
    onBeforeMount(() => {
        if (!route.params.tab) {
            setSelectedTab('members');
        }
    });

    function refresh(done: () => void) {
        teamQueryRefetch().then(() => done());
    }

    onMounted(() => teamQueryRefetch());
    const {
        isRefetching: isTeamQueryRefetching,
        refetch: teamQueryRefetch,
    } = useQuery({
        ...queries.teams.detail(props.teamId),
        select: (data) => {
            // save data to ref since tanstack query makes returned value readonly for cache usage
            team.value = data;
            return data;
        },
        enabled: false,
    });

    function saveTeam() {
        saveTeamAsync();
    }

    const { mutateAsync: saveTeamAsync } = useMutation({
        mutationFn() {
            if (team.value) {
                return team.value.save();
            } else {
                return Promise.reject(false);
            }
        },
    });

    const { mutate: deleteTeamOnServer } = useMutation({

        mutationFn() {
            if (team.value) {
                return team.value.destroy();
            } else {
                return Promise.reject(false);
            }
        },
        async onSuccess() {
            await useAuthStore().loadPermissions();
            await router.push({
                name: ROUTE_NAMES.TEAMS,
            });
        },
    });

    function deleteTeam() {
        if(useAuthStore().hasFeature('team.create')) {
            deleteTeamOnServer()
        } else {
            confirmBeforeAction({
                dialogOptions: {
                    title: 'ACHTUNG',
                    message:
                        'Aktuell können leider keine neuen Teams erstellt werden. Bist du wirklich sicher, dass du dieses Team löschen möchtest?',
                },
                onOk: deleteTeamOnServer,
            })
        }
    }


    function addMember(name: string) {
        const member = new TeamMember({
            name: name,
            team: team.value,
            statusRole: team.value?.getMemberStatusRole(),
        });
        team.value?.members.push(member);
    }

    // const { mutateAsync: deleteTeamMemberOnServer } = useMutation({
    //     mutationFn: (member: TeamMember) => member.destroy(),
    // });

    const { mutateAsync: softDeleteTeamMemberOnServer } = useMutation({
        mutationFn: (member: TeamMember) => {
            member.deletedAt = (new Date()).toISOString()
            return member.save();
        },
    });

    async function deleteMember(member: TeamMember) {
        const index = team.value?.members.indexOf(member);
        if (member.isPersisted) {
            await softDeleteTeamMemberOnServer(member);
        }
        if (index && index >= 0) {
            team.value?.members.splice(index, 1);
        }
        return Promise.resolve(true);
    }

    const { mutateAsync: saveTeamMember } = useMutation({
        mutationFn: saveTeamMemberOnServer,
    });

    async function saveMember(member: TeamMember, force: boolean = false) {
        if (force || member.isDirty()) {
            await saveTeamMember(member);

            if (useAuthStore().isOwnMember(member)) {
                await useAuthStore().loadPermissions();
            }
        }
    }

    const { mutateAsync: createInviteOnServer } = useMutation({
        mutationFn: (invite: Invite) => invite.save({ with: ['invitable.id'] }),
    });

    const { mutateAsync: deleteInviteOnServer } = useMutation({
        mutationFn: (invite: Invite) => invite.destroy(),
    });


    async function createInvite(member: TeamMember) {
        member.invite = new Invite({ invitable: member });
        await createInviteOnServer(member.invite);
    }

    async function deleteInvite(member: TeamMember) {
        if (member.invite) {
            await deleteInviteOnServer(member.invite);
            member.invite = undefined;
        }
    }


    function createTeamInvite(team: Team) {
        team.invite = new Invite({ invitable: team });
        createInviteOnServer(team.invite);

        const unwatch = watchEffect(async () => {
            if (team.invite?.isPersisted) {
                copyTeamInvite(team);
                unwatch();
            }
        });
    }

    async function deleteTeamInvite(team: Team) {
        if (team.invite) {
            await deleteInviteOnServer(team.invite);
            team.invite = undefined;
        }
    }

    function copyTeamInvite(team: Team) {
        if (team.invite) {
            Clipboard.write({
                string: team.invite.getInviteLink(),
            });
            notifyMessage(
                'Einladungslink für ' + team.name + ' in die Zwischenablage kopiert.',
            );
        }
    }

    function notifyMessage(message: string) {
        Notify.create({
            message: message,
            color: 'positive',
            position: 'bottom',
            timeout: 2000,
        });
    }

    function setSelectedTab(tab: string) {
        router.replace({
            name: ROUTE_NAMES.TEAM, params: {
                teamId: props.teamId,
                tab,
            },
        });
    }

</script>

<style lang="scss" scoped></style>
