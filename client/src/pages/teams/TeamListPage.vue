<template>
  <PageContainer>
    <PageHeaderWithAction title="Meine Teams">
      <q-btn :to="{name: $routeNames.TEAMS_CREATE}"
             color="primary"
             icon="add"
             label="Team erstellen"
             size="12px"
      />
    </PageHeaderWithAction>

    <template v-if="!isLoading">
      <template v-if="teams && teams.length > 0">
        <template
          v-for="(team) in teams"
          :key="team.id"
        >
          <div class="q-mb-lg">
            <TeamCard
              :team="team"
            ></TeamCard>
          </div>
        </template>
      </template>
      <template v-else>
<!--
else Teil sollte nicht mehr angezeigt werden, da durch
check-auth.ts => checkRouteRequiresTeamsValid()
die Seite nicht aufgerufen werden kann
-->
        <q-card flat>
          <q-card-section horizontal>
            <q-space />
            <div>Keine Teams gefunden</div>
            <q-space />
          </q-card-section>
          <q-card-section class="q-pa-md" horizontal>
            <q-space />
            <q-btn :to="{name: $routeNames.TEAMS_CREATE}"
                   color="primary"
                   icon="add"
                   label="Team erstellen"
            />
            <q-space />
          </q-card-section>
        </q-card>
      </template>
    </template>
    <template v-else>
      <div>lade Teams...
        <q-spinner />
      </div>
    </template>
  </PageContainer>
</template>

<script lang="ts" setup>
  import PageContainer from 'src/components/global/PageContainer.vue';
  import PageHeaderWithAction from 'src/components/global/PageHeaderWithAction.vue';
  import { useQuery } from '@tanstack/vue-query';
  import { queries } from 'src/queries';
  import TeamCard from 'src/components/teams/TeamCard.vue';

  const {
    data: teams,
    isLoading,
  } = useQuery(queries.teams.all(false));
</script>

<style lang="scss" scoped>

</style>
