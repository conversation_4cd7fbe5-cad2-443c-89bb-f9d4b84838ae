<template>
  <PageContainer>
    <PageHeaderWithAction
      title="Team erstellen">
      <q-btn class="q-mr-md q-mb-xs" color="primary"
             dense
             icon="close"
             label="Abbrechen"
             size="12px"
             @click="router.back()"
      />
    </PageHeaderWithAction>

    <TeamCreate />
  </PageContainer>
</template>

<script lang="ts" setup>
  import PageContainer from 'src/components/global/PageContainer.vue';
  import PageHeaderWithAction from 'src/components/global/PageHeaderWithAction.vue';
  import { useRouter } from 'vue-router';
  import TeamCreate from 'src/components/teams/TeamCreate.vue';

  const router = useRouter();
</script>

<style lang="scss" scoped>

</style>
