<template>
  <PageContainer>
    <PageHeaderWithAction
      :sub-title="singleMemberSubtitle"
      title="Abwesenheiten">
      <q-btn
        :to="{ name: $routeNames.ABSENCE_CREATE }"
        color="primary"
        icon="add"
        label="Abwesenheit erstellen"
        size="12px"
      />
    </PageHeaderWithAction>

    <span v-if="isLoading">lade Daten... <q-spinner /></span>
    <span v-else-if="isError">Fehler</span>
    <template v-else>
      <template v-if="absenceData && absenceData.length > 0">
        <template
          v-for="(absence) in absenceData"
          :key="absence.id"
        >
          <div class="q-my-md">
            <AbsenceCard
              :absence="absence"
              :show-target-members="hasMultipleTargetMembers"
            />
          </div>
        </template>
      </template>
      <template v-else>
        <q-card flat>
          <q-card-section horizontal>
            <q-space />
            <div>Keine Abwesenheiten gefunden</div>
            <q-space />
          </q-card-section>
        </q-card>
      </template>
    </template>

  </PageContainer>
</template>

<script lang="ts" setup>
  import PageContainer from 'src/components/global/PageContainer.vue';
  import PageHeaderWithAction from 'src/components/global/PageHeaderWithAction.vue';
  import { useQuery } from '@tanstack/vue-query';
  import Absence from 'src/models/Absence';
  import AbsenceCard from 'src/components/absence/AbsenceCard.vue';
  import { computed, onMounted, ref } from 'vue';
  import TeamMember from 'src/models/TeamMember';
  import { queries } from 'src/queries';
  import { useAuthStore } from 'src/stores/auth';
  import Person from 'src/models/Person';

  type absenceData = Absence[]

  const {
    data: absenceData,
    isError,
    isLoading,
  } = useQuery(queries.absences.all);


  // duplicated code in EditAbsence.vue, cleanup whit #308
  const hasMultipleTargetMembers = ref(false);
  const firstMember = ref<TeamMember>();
  const singleMemberSubtitle = computed(() => {
    if (hasMultipleTargetMembers.value || !firstMember.value) {
      return undefined;
    } else {
      return firstMember.value.name + ' (' + firstMember.value?.team.name + ')';
    }
  });

  onMounted(() => {
    const persons = [useAuthStore().person as Person];
    const memberSum = persons.reduce((count, person) => person.teamMembers.length, 0);
    hasMultipleTargetMembers.value = memberSum > 1;
    if (memberSum === 1) {
      firstMember.value = persons.find((person) => person.teamMembers.length > 0)?.teamMembers[0] as TeamMember;
    }
  });


</script>

<style lang="scss" scoped></style>
