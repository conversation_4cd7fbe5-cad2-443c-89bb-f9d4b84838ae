<template>
  <PageContainer>
    <EditAbsence
      :absence-id="props.absenceId"
      :with-delete-button="true"
      page-title="Abwesenheit bearbeiten"
    />
  </PageContainer>
</template>

<script lang="ts" setup>
  import PageContainer from 'src/components/global/PageContainer.vue';
  import EditAbsence from 'src/components/absence/EditAbsence.vue';

  const props = defineProps<{
    absenceId: string
  }>();
</script>

<style lang="scss" scoped>

</style>
