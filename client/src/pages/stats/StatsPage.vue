<template>
  <PageContainer full-width>
    <PageHeader :sub-title="pageSubtitle" title="Statistik" />

    <FilterCard :filter="filter" />

    <q-card class="q-mt-lg">
      <q-card-section v-if="!filter.selectedTeam.value" class="bg-primary text-white">
        Bitte wähle ein Team aus
      </q-card-section>
      <q-card-section v-else-if="!isInitialLoadedAfterTeamChanged" class="bg-primary text-white">
        lade Termin Daten...
        <q-spinner />
      </q-card-section>
      <q-card-section v-else-if="isError" class="bg-primary text-white">
        <PERSON><PERSON> beim <PERSON>den der Termine
      </q-card-section>


      <template v-else>
        <q-card-section class="bg-primary text-white">
          <div>
            <span v-if="eventTypeOptionsNotZero.length !== 1">{{ totalEvents }} Termine</span>
            <span v-if="eventTypeOptionsNotZero.length > 1">: </span>
            <span v-for="(eventType, index) in eventTypeOptionsNotZero" :key="eventType.value">
              {{ getEventSumByType(eventType.value) }}x {{ eventType.label }}<span v-if="index < eventTypeOptionsNotZero.length - 1">, </span>
            </span>
          </div>
        </q-card-section>

        <q-card-section class="q-pa-none">
          <q-tabs
            :model-value="tab"
            active-color="primary"
            align="left"
            class="text-grey"
            dense
            draggable="false"
            indicator-color="primary"
            narrow-indicator
          >
            <q-tab class="bg-transparent" label="Teilnahme" name="participation"
                   @click="setSelectedTab('participation')" />
            <q-tab class="bg-transparent" label="Aufgaben" name="tasks"
                   @click="setSelectedTab('tasks')" />
            <!--            <q-tab name="tasks" label="Aufgaben" class="bg-transparent"-->
            <!--                         :to="{name: $routeNames.STATS, params: {tab: 'tasks'}}" />-->
          </q-tabs>

          <q-separator />

          <q-tab-panels :model-value="tab" animated>
            <q-tab-panel class="q-pa-none" name="participation">
              <ParticipationTable :filter="filter" />
            </q-tab-panel>
            <q-tab-panel class="q-pa-none" name="tasks">
              <TasksTable
                :enable-route-change="true"
                :filter="filter"
                :selected-task-config-id="elementId"
              />
            </q-tab-panel>
          </q-tab-panels>
        </q-card-section>

      </template>
    </q-card>

  </PageContainer>
</template>

<script lang="ts" setup>
  import PageContainer from 'src/components/global/PageContainer.vue';
  import PageHeader from 'src/components/global/PageHeader.vue';
  import { useEventStats } from 'src/composables/useEventStats';
  import { computed, watch, watchEffect } from 'vue';
  import ParticipationTable from 'src/components/stats/ParticipationTable.vue';
  import { useRouter } from 'vue-router';
  import { ROUTE_NAMES } from 'src/router/route-names';
  import TasksTable from 'src/components/stats/TasksTable.vue';
  import { useEventStatsFilter } from 'src/composables/useEventStatsFilter';
  import FilterCard from 'src/components/stats/FilterCard.vue';

  const props = defineProps<{
    teamId?: string,
    tab?: string,
    elementId?: string,
  }>();

  const filter = useEventStatsFilter(props.teamId);

  const pageSubtitle = computed(() => filter.teams.value?.length === 1 && filter.selectedTeam.value ? filter.selectedTeam.value.name : undefined);

  const {
    isInitialLoadedAfterTeamChanged,
    isError,
    getEventSumByType,
    totalEvents,
    eventTypeOptionsNotZero,
  } = useEventStats(filter);

  const router = useRouter();

  watch(filter.selectedTeam, () => {
    router.replace({
      name: ROUTE_NAMES.STATS, params: {
        teamId: filter.selectedTeam.value?.id,
        tab: router.currentRoute.value.params['tab'],
        elementId: router.currentRoute.value.params['elementId'],
      },
    });
  });

  watchEffect(() => {
    if (router.currentRoute.value.name === ROUTE_NAMES.STATS && filter.selectedTeam.value && !props.tab) {
      setSelectedTab('participation');
    }
  });

  function setSelectedTab(tab: string) {
    router.replace({
      name: ROUTE_NAMES.STATS, params: {
        teamId: filter.selectedTeam.value?.id,
        tab,
        elementId: router.currentRoute.value.params['elementId'],
      },
    });
  }

</script>

<style lang="scss" scoped>

</style>
