<template>
  <PageContainer>
    <PageHeader title="Mein Profil" />

    <q-tabs
      v-model="tab"
      active-color="primary"
      align="justify"
      indicator-color="primary"
      @input="onTabClick"
    >
      <q-tab label="Profil" name="profil" />
      <q-tab label="Teams" name="teams" />
      <q-tab name="devices" label="Einstellungen" />
    </q-tabs>

    <q-separator />

    <q-tab-panels v-model="tab">
      <q-tab-panel name="profil">
        <ProfilePersonalSettings />
      </q-tab-panel>
      <q-tab-panel name="teams">
        <ProfileTeamSettings />
      </q-tab-panel>
      <q-tab-panel name="notifications">
        <ProfileNotificationSettings />
      </q-tab-panel>
      <q-tab-panel name="devices">
        <ProfileDevicesSettings />
      </q-tab-panel>
    </q-tab-panels>
  </PageContainer>
</template>

<script lang="ts" setup>
  import PageHeader from 'src/components/global/PageHeader.vue';
  import PageContainer from 'src/components/global/PageContainer.vue';
  import { ref } from 'vue';
  import ProfilePersonalSettings from 'src/components/profile/ProfilePersonalSettings.vue';
  import ProfileNotificationSettings from 'src/components/profile/ProfileNotificationSettings.vue';
  import ProfileTeamSettings from 'src/components/profile/ProfileTeamSettings.vue';
  import ProfileDevicesSettings from 'src/components/profile/ProfileDevicesSettings.vue';
  import { useDeviceInfo } from 'src/composables/useDeviceInfo';

  const tab = ref('profil');

  const onTabClick = (newTab) => {
    if (newTab == 'devices') {
        useDeviceInfo().updateRemote();
    }
  };
</script>

<style lang="scss" scoped>

</style>
