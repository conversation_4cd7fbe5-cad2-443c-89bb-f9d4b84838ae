<template>
  <PageContainer full-width>
    <PageHeader title="<PERSON><PERSON>" />

    <q-card class="q-mb-md">
      <q-card-section class="bg-primary text-white" horizontal>
        <q-card-section class="q-py-xs">
          <div class="text-h6">Users</div>
        </q-card-section>
      </q-card-section>

      <span v-if="isUsersQueryLoading">Lade <PERSON>n</span>
      <span v-else-if="isUsersQueryError"><PERSON>hler</span>
      <template v-else-if="usersQueryResult">
        <q-card-section class="no-padding">
          <q-item class="no-padding">
              <q-table
                  :rows="usersRowData"
                  :columns="usersColumns"
                  :rowsPerPageOptions="[10, 25, 50, 100]"
                  row-key="id"
                  v-model:pagination="usersPagination"
                  binary-state-sort
                  dense
                  separator="cell"
                  @request="usersOnRequest"
              >
                  <template v-slot:body-cell-email="props">
                      <q-td :props="props" auto-width>
                          {{ props.value }}
                          <q-icon :name="props.row.hasVerifiedEmail? 'check' : 'close'"
                                  :color="props.row.hasVerifiedEmail? 'green' : 'red'"
                          />
                          <q-icon v-if="props.row.isProblemProvider"

                                  name="warning"
                                  color="red"
                          >
                              <q-tooltip>
                                  Problem mail provider for sendgrid
                              </q-tooltip>
                          </q-icon>
                      </q-td>
                  </template>

                  <template v-slot:body-cell-teams="props">
                      <q-td :props="props" auto-width>
                          <q-markup-table dense flat>
                              <tbody>
                                  <tr v-for="teamMember in (props.row.person.teamMembers as TeamMember[])" :key="teamMember.id">
                                      <td>
                                          {{ teamMember.team.name }}
                                          <q-btn :to="{name: $routeNames.TEAM, params: {teamId: teamMember.team.id}}" dense flat
                                                 icon="navigate_next"
                                                 size="sm" />
                                      </td>
                                      <td>{{ teamMember.name }}</td>
                                      <td>{{ teamMember.getCreatedAtFormatted() }}</td>
                                  </tr>
                              </tbody>
                          </q-markup-table>
                      </q-td>
                  </template>
              </q-table>
          </q-item>
        </q-card-section>
        <q-inner-loading :showing="isUsersQueryFetching" />
      </template>
    </q-card>


    <q-card class="q-mb-md">
      <q-card-section class="bg-primary text-white" horizontal>
        <q-card-section class="q-py-xs">
          <div class="text-h6">
            Teams - Events - Users
          <q-spinner v-if="isTeamsQueryFetching" />
          </div>
        </q-card-section>
<!--        <q-btn :to="{name: $routeNames.TEAMS}" class="absolute-right"-->
<!--               flat-->
<!--               icon="navigate_next"-->
<!--        />-->
      </q-card-section>

      <span v-if="isTeamsQueryLoading">Lade Daten</span>
      <span v-else-if="isTeamsQueryError">Fehler</span>
      <template v-else-if="teamsQueryResult">

          <q-card-section class="row q-gutter-lg items-start" >
              <q-card>
                  Neu im Zeitraum
                  <q-markup-table dense separator="cell">
                      <thead>
                      <tr>
                          <th class="text-left">Zeitraum</th>
                          <th class="text-right">2 Monate</th>
                          <th class="text-right">6 Monate</th>
                          <th class="text-right">12 Monate</th>
                          <th class="text-right">Gesamt</th>
                      </tr>
                      </thead>
                      <tbody>
                      <tr>
                          <td>Teams</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalTeams2Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalTeams6Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalTeams12Month }}</td>
                          <td class="text-right">{{ teamsPagination.rowsNumber }}</td>
                      </tr>
                      <tr>
                          <td>Mitglieder</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalTeamMembers2Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalTeamMembers6Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalTeamMembers12Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalTeamMembers }}</td>
                      </tr>
                      <tr>
                          <td>registrierte Mitglieder</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalRegisteredTeamMembers2Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalRegisteredTeamMembers6Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalRegisteredTeamMembers12Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.registeredTeamMembers }}</td>
                      </tr>
                      <tr>
                          <td>User</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalUsers2Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalUsers6Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalUsers12Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalUsers }}</td>
                      </tr>
                      </tbody>
                  </q-markup-table>
              </q-card>
              <q-card>
                  Aktiv im Zeitraum
                  <q-markup-table dense separator="cell">
                      <thead>
                      <tr>
                          <th class="text-left">Zeitraum</th>
                          <th class="text-right">2 Monate</th>
                          <th class="text-right">6 Monate</th>
                          <th class="text-right">12 Monate</th>
                      </tr>
                      </thead>
                      <tbody>
                      <tr>
                          <td>Teams</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalActiveTeams2Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalActiveTeams6Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalActiveTeams12Month }}</td>
                      </tr>
                      <tr>
                          <td>User</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalActiveUsers2Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalActiveUsers6Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalActiveUsers12Month }}</td>
                      </tr>
                      </tbody>
                  </q-markup-table>
              </q-card>
              <q-card>
                  Termine erstellt im Zeitraum
                  <q-markup-table dense separator="cell">
                      <thead>
                      <tr>
                          <th class="text-left">Zeitraum</th>
                          <th class="text-right">2 Monate</th>
                          <th class="text-right">6 Monate</th>
                          <th class="text-right">12 Monate</th>
                          <th class="text-right">Gesamt</th>
                      </tr>
                      </thead>
                      <tbody>
                      <tr>
                          <td>Serien</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEventSeries2Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEventSeries6Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEventSeries12Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEventSeries }}</td>
                      </tr>
                      <tr>
                          <td>Alle Arten</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEvents2Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEvents6Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEvents12Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEvents }}</td>
                      </tr>
                      <tr>
                          <td>Training</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEventsTraining2Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEventsTraining6Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEventsTraining12Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEventsTraining }}</td>
                      </tr>
                      <tr>
                          <td>Wettkampf</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEventsMatch2Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEventsMatch6Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEventsMatch12Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEventsMatch }}</td>
                      </tr>
                      <tr>
                          <td>Turnier</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEventsTournament2Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEventsTournament6Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEventsTournament12Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEventsTournament }}</td>
                      </tr>
                      <tr>
                          <td>Event</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEventsEvent2Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEventsEvent6Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEventsEvent12Month }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.totalEventsEvent }}</td>
                      </tr>
                      </tbody>
                  </q-markup-table>
              </q-card>

              <q-card>
                  Person (User) nach Anzahl Member
                  <q-markup-table dense separator="cell">
                      <thead>
                      <tr>
                          <th class="text-right">Member</th>
                          <th class="text-right">Personen</th>
                      </tr>
                      </thead>
                      <tbody>
                      <tr v-for="memberCount in Object.keys(teamsQueryResult.data.value.meta.personCountPerMemberCount).sort((a, b) => b - a)" :key="memberCount">
                          <td class="text-right">{{ memberCount }}</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.personCountPerMemberCount[memberCount] }}</td>
                      </tr>
                      <tr>
                          <td class="text-right">0</td>
                          <td class="text-right">{{ teamsQueryResult.data.value.meta.personCountWithoutMember }}</td>
                      </tr>
                      </tbody>
                  </q-markup-table>
              </q-card>
              <q-card>
                  Herkunft
                  <q-markup-table dense separator="cell">
                      <thead>
                      <tr>
                          <th class="text-right">Herkunft</th>
                          <th class="text-right">Anzahl</th>
                      </tr>
                      </thead>
                      <tbody>
                      <tr v-for="originCount in teamsQueryResult.data.value.meta.originCounts as {origin, count}[]" :key="originCount.origin">
                          <td class="text-right">{{ originCount.origin }}</td>
                          <td class="text-right">{{ originCount.count }}</td>
                      </tr>
                      </tbody>
                  </q-markup-table>
              </q-card>
              <q-card>
                  Bisher genutzt
                  <q-markup-table dense separator="cell">
                      <thead>
                      <tr>
                          <th class="text-right">Konkurrenz</th>
                          <th class="text-right">Anzahl</th>
                      </tr>
                      </thead>
                      <tbody>
                      <tr v-for="competitionCount in teamsQueryResult.data.value.meta.competitionCounts as {competition, count}[]" :key="competitionCount.competition">
                          <td class="text-right">{{ competitionCount.competition }}</td>
                          <td class="text-right">{{ competitionCount.count }}</td>
                      </tr>
                      </tbody>
                  </q-markup-table>
              </q-card>
              <q-card>
                  Landing page im Zeitraum (Besucher - <q-chip dense>davon Registriert</q-chip>)
                  <q-markup-table dense separator="cell">
                      <thead>
                      <tr>
                          <th class="text-right">Landing Page</th>
                          <th class="text-right">2 Monate</th>
                          <th class="text-right">6 Monate</th>
                          <th class="text-right">12 Monate</th>
                          <th class="text-right">Gesamt</th>
                      </tr>
                      </thead>
                      <tbody>
                      <tr v-for="landingPageCount in teamsQueryResult.data.value.meta.landingPageCountTotal as LandingPageCount[]" :key="landingPageCount.landing_page">
                          <td class="text-right">{{ landingPageCount.landing_page }}</td>
                          <td class="text-right">{{getLandingPageCountForMonth(landingPageCount.landing_page, 2)?.visit_count}} <q-chip dense>{{getLandingPageCountForMonth(landingPageCount.landing_page, 2)?.user_count}}</q-chip></td>
                          <td class="text-right">{{getLandingPageCountForMonth(landingPageCount.landing_page, 6)?.visit_count}} <q-chip dense>{{getLandingPageCountForMonth(landingPageCount.landing_page, 6)?.user_count}}</q-chip></td>
                          <td class="text-right">{{getLandingPageCountForMonth(landingPageCount.landing_page, 12)?.visit_count}} <q-chip dense>{{getLandingPageCountForMonth(landingPageCount.landing_page, 12)?.user_count}}</q-chip></td>                          <td class="text-right">{{ landingPageCount.visit_count }} <q-chip dense>{{ landingPageCount.user_count }}</q-chip></td>
                      </tr>
                      </tbody>
                  </q-markup-table>
              </q-card>
          </q-card-section>

        <q-card-section>
          Aktiv = Vote innerhalb der letzten 2 Monate<br>
          <q-item class="no-padding">
              <q-table
                  :rows="teamsRowData"
                  :columns="teamsColumns"
                  :rowsPerPageOptions="[10, 25, 50, 100]"
                  row-key="id"
                  v-model:pagination="teamsPagination"
                  binary-state-sort
                  dense
                  separator="cell"
                  :loading="isTeamsQueryFetching"
                  @request="teamsOnRequest"
              >
                  <template v-slot:body-cell-name="props">
                      <q-td :props="props" auto-width>
                          {{ props.value }}
                          <q-btn :to="{name: $routeNames.TEAM, params: {teamId: props.row.id}}" dense flat
                                 icon="navigate_next"
                                 size="sm" />
                      </q-td>
                  </template>
                  <template v-slot:body-cell-ledger="props">
                      <q-td :props="props" auto-width>
                          {{ props.value }}
                          <q-btn v-if="props.row.ledger"
                                :to="{name: $routeNames.LEDGER, params: {teamId: props.row.id}}" dense flat
                                 icon="navigate_next"
                                 size="sm" />
                      </q-td>
                  </template>
                  <template v-slot:body-cell-fines="props">
                      <q-td :props="props" auto-width>
                          {{ props.value }}
                          <span v-if="props.row.ledger">(x {{props.row.ledger.fineClaims.length}})</span>
                      </q-td>
                  </template>

                  <template v-slot:top-row>
                      <q-tr  class="dense-header-row bg-secondary">
                          <q-td>Total:</q-td>
                          <q-td></q-td>
                          <q-td>{{ teamsPagination.rowsNumber }} Teams</q-td>
                          <q-td class="text-right">{{ teamsQueryResult.data.value.meta.totalActive }}</q-td>
                          <q-td class="text-right">{{ teamsQueryResult.data.value.meta.totalTeamMembers }}</q-td>
                          <q-td class="text-right">{{ teamsQueryResult.data.value.meta.registeredTeamMembers }}</q-td>
                          <q-td class="text-right">{{ (teamsQueryResult.data.value.meta.registeredTeamMembers / teamsQueryResult.data.value.meta.totalTeamMembers * 100).toFixed(0) }}%</q-td>
                          <q-td class="text-right">{{ teamsQueryResult.data.value.meta.totalEvents }}</q-td>
                          <q-td class="text-right">{{ teamsQueryResult.data.value.meta.totalVotesManual }}</q-td>
                          <q-td class="text-right">{{ teamsQueryResult.data.value.meta.totalVotesAutomatic }}</q-td>
                          <q-td class="text-right">{{ teamsQueryResult.data.value.meta.totalLedgers }}</q-td>
                          <q-td class="text-right">{{ teamsQueryResult.data.value.meta.totalLedgerFines }}</q-td>
                          <q-td class="text-right">{{ teamsQueryResult.data.value.meta.totalLedgerDues }}</q-td>
                          <q-td class="text-right">{{ teamsQueryResult.data.value.meta.totalLedgerTransactions }}</q-td>
                      </q-tr>
                  </template>

              </q-table>
          </q-item>
        </q-card-section>
      </template>
    </q-card>

    <q-card class="q-mb-md">
      <q-card-section class="bg-primary text-white" horizontal>
        <q-card-section class="q-py-xs">
          <div class="text-h6">Push Notification Stats</div>
        </q-card-section>

      </q-card-section>
            Disabled weil Backen nicht mehr zur query passt und jeder Aufruf einen Fehler in Sentry erzeugt
<!--      <template>-->
<!--        <q-card-section class="no-padding bg-secondary">-->
<!--          <q-item-label header></q-item-label>-->
<!--        </q-card-section>-->

<!--        <q-card-section class="no-padding">-->
<!--          <q-list bordered class="rounded-borders" dense>-->
<!--            <q-item>-->
<!--              <q-item-section>Users: {{ pushStats.registeredUsers }}</q-item-section>-->
<!--            </q-item>-->
<!--            <q-item>-->
<!--              <q-item-section>Devices: {{ pushStats.devices }}</q-item-section>-->
<!--            </q-item>-->
<!--            <q-item>-->
<!--              <q-item-section>Tokens: {{ pushStats.pushNotificationCapableDevices }}</q-item-section>-->
<!--            </q-item>-->
<!--            <q-item>-->
<!--              <q-item-section>Missing Tokens: {{ pushStats.devices - pushStats.pushNotificationCapableDevices }}</q-item-section>-->
<!--            </q-item>-->
<!--          </q-list>-->
<!--        </q-card-section>-->
<!--      </template>-->
    </q-card>

      <q-card class="q-mb-md">
           Sentry aktiviert: {{isSentryActive()}}
        <q-card-actions>
          <q-btn label="Throw frontend test error"
                 no-caps
                 @click="throwFrontendTestError()"
          />
          <q-btn label="Throw backend test error"
                 no-caps
                 @click="throwBackendTestError()"
          />
        </q-card-actions>
      </q-card>

      <q-card class="q-mb-md">
           Demo Daten:
        <q-card-actions>
          <q-btn label="Zurücksetzen"
                 no-caps
                 @click="confirmBeforeAction({
                    dialogOptions: {
                        title: 'Demo Daten zurücksetzen?',
                        message: 'Möchtest du die Demo Daten wirklich zurücksetzen? Es werden alle Teams, Mitglieder, Termine, etc. gelöscht und die Demo Daten neu angelegt.',
                    },
                    onOk: resetDemoData
                 })"
          />
        </q-card-actions>
      </q-card>

  </PageContainer>
</template>

<script lang="ts" setup>
    import { keepPreviousData, useQuery } from '@tanstack/vue-query';
  import { ref } from 'vue';
  import PageContainer from 'src/components/global/PageContainer.vue';
  import PageHeader from 'src/components/global/PageHeader.vue';
  import Team from 'src/models/Team';
  import User from 'src/models/User';
  import { isSentryActive, throwBackendTestError, throwFrontendTestError } from 'src/boot/sentry';
  import { queries } from 'src/queries';
  import { PaginationProps, useServerTable } from 'src/composables/useServerTable';
    import { Notify, QTable, QTableColumn } from 'quasar';
    import AdminStatsTeam from 'src/models/AdminStatsTeam';
    import TeamMember from 'src/models/TeamMember';
    import { api } from 'src/boot/axios';
    import { confirmBeforeAction } from 'src/helpers/dialogs';

    type LandingPageCount = {landing_page, visit_count, user_count}

    function getLandingPageCountForMonth(landingPage: string, month: number) {
        return (teamsQueryResult.data.value.meta['landingPageCount'+month+'Month'] as LandingPageCount[]).find(item => item.landing_page === landingPage)
    }

  const teamsColumns = [
      {name: 'createdAt', label: 'Erstellt', field: row => row.getCreatedAtFormatted(), sortable: true, align: 'left'},
      {name: 'trialEndsAt', label: 'Abo / Test Ende', field: row => row.isSubscribed ? '(Abo) ' + row.getSubscriptionEndsAtFormatted() : (row.hasTrialEndDate() ? '(Test) ' + row.getTrialEndsAtFormatted() : '-'), sortable: true, align: 'left'},
      {name: 'name', label: 'Name', field: 'name', align: 'left', sortable: true },
      {name: 'isActive', label: 'Aktiv?', field: row => (row.isActive ? 'Ja' : 'Nein') },
      {name: 'membersCount', label: 'Mitglieder', field: row => row.membersCount },
      {name: 'registeredMembersCount', label: 'Registriert', field: row => row.registeredMembersCount},
      {name: 'percentage', label: '%', field: row => (row.registeredMembersCount / row.membersCount * 100).toFixed(0) + '%' },
      {name: 'eventsCount', label: 'Events', field: row => row.eventsCount },
      {name: 'votesCountManual', label: 'Votes M', field: row => row.votesCountManual },
      {name: 'votesCountAutomatic', label: 'Votes A', field: row => row.votesCountAutomatic },
      {name: 'ledger', label: 'Kasse?', field: row => (row.ledgerId !== null ? 'Ja' : 'Nein') },
      {name: 'fines', label: 'Strafen', field: row => (row.ledgerFineCount === null ? '' : row.ledgerFineCount) },
      {name: 'dues', label: 'Beiträge', field: row => (row.ledgerDuesCount === null ? '' : row.ledgerDuesCount) },
      {name: 'transactions', label: 'Umsätze', field: row => (row.ledgerTransactionCount === null ? '' : row.ledgerTransactionCount) },

  ] as QTableColumn<AdminStatsTeam>[]

  const teamsPagination = ref(new PaginationProps(
      {
          page: 1,
          rowsPerPage: 10,
          sortBy: 'trialEndsAt',
          descending: true,
      }
  ));

    const teamsQueryResult = useQuery({
        ... queries.adminStats.allAdminStatsTable(teamsPagination),
        placeholderData: keepPreviousData
    });

  const {
      isLoading: isTeamsQueryLoading,
      isFetching: isTeamsQueryFetching,
      isError: isTeamsQueryError,
  } = teamsQueryResult;

  const {
      onRequest: teamsOnRequest,
      rowData: teamsRowData
  } = useServerTable<Team>(teamsQueryResult, teamsPagination)






    const usersColumns = [
        {name: 'createdAt', label: 'Erstellt', field: row => row.getCreatedAtFormatted(), sortable: true, align: 'left'},
        {name: 'name', label: 'Name', field: row => row.person.name, align: 'left' },
        {name: 'email', label: 'Email', field: 'email', align: 'left', sortable: true },
        {name: 'origin', label: 'Herkunft', field: 'origin', align: 'left'},
        {name: 'competition', label: 'Bisher genutzt', field: 'competition', align: 'left'},
        {name: 'teams', label: 'Teams', field: row => row.person.teamMembers.length, align: 'left' },
    ] as QTableColumn<User>[]

  const usersPagination = ref(new PaginationProps(
      {
          page: 1,
          rowsPerPage: 10,
          sortBy: 'createdAt',
          descending: true,
      }
  ));

    const usersQueryResult = useQuery({
        ... queries.adminStats.usersTable(usersPagination),
        placeholderData: keepPreviousData
    });

  const {
      isLoading: isUsersQueryLoading,
      isFetching: isUsersQueryFetching,
      isError: isUsersQueryError,
  } = usersQueryResult;

  const {
      onRequest: usersOnRequest,
      rowData: usersRowData
  } = useServerTable<User>(usersQueryResult, usersPagination)


    const resetDemoData = () => {
      api.post('/admin/demo/resetData').then((response) => {
          Notify.create({
            type: 'positive',
            timeout: 0,
            message: 'Demo Daten zurückgesetzt.',
            actions: [
              { icon: 'close', color: 'white', round: true, handler: () => { /* ... */ } }
            ]
          });
      });
    }

  // interface PushStatsData {
  //   registeredUsers: number;
  //   pushNotificationCapableDevices: number;
  //   devices: number;
  // }
  //
  // const pushStats: Ref<PushStatsData> = ref({
  //   registeredUsers: 0,
  //   pushNotificationCapableDevices: 0,
  //   devices: 0,
  // });
  //
  // api.get<PushStatsData>('/push/stats').then((response) => {
  //   let data = response.data
  //   console.log('Push stats', data);
  //
  //   pushStats.value.registeredUsers = data.registeredUsers;
  //   pushStats.value.pushNotificationCapableDevices = data.pushNotificationCapableDevices;
  //   pushStats.value.devices = data.devices;
  // });


</script>




