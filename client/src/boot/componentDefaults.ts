import { boot } from 'quasar/wrappers';
import { ComponentConstructor, Platform, QTooltip, QTooltipProps } from 'quasar';


// based on https://github.com/quasarframework/quasar/issues/7379#issuecomment-1269501490
export default boot(() => {

  SetComponentDefaults<QTooltipProps>(QTooltip, {
    anchor: 'top left',
    self: 'top left',
    hideDelay: Platform.is.mobile ? 1000 : 0,
  });

});

/**
 * Set some default properties on a component
 */
const SetComponentDefaults = <T>(component: ComponentConstructor, defaults: Partial<T>): void => {
  /* eslint-disable @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access */
  Object.keys(defaults).forEach((prop: string) => {
    component.props[prop] =
      Array.isArray(component.props[prop]) === true || typeof component.props[prop] === 'function'
        ? { type: component.props[prop], default: (defaults as Record<string, never>)[prop] }
        : { ...component.props[prop], default: (defaults as Record<string, never>)[prop] };
  });
  /* eslint-enable @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access */
};
