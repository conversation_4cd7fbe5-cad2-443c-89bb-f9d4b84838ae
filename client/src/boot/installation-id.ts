import { boot } from 'quasar/wrappers';
import { Capacitor } from '@capacitor/core';
import { useAuthStore } from 'src/stores/auth';
import { v4 as uuidv4 } from 'uuid';

export default boot(async () => {
  let value: string | null = null;

  // Try to use Preferences first (newer plugin)
  if (Capacitor.isPluginAvailable('Preferences')) {
    const { Preferences } = await import('@capacitor/preferences');
    const { value: prefValue } = await Preferences.get({ key: 'installationId' });
    value = prefValue;
  }
  // Fallback to Storage (older plugin)
  else if (Capacitor.isPluginAvailable('Storage')) {
    const { Storage } = await import('@capacitor/storage');
    const { value: storageValue } = await Storage.get({ key: 'installationId' });
    value = storageValue;
  } else {
    console.warn('Neither Storage nor Preferences plugin is available');
    return;
  }

  if (!value) {
    value = uuidv4();
    // Save using the same plugin we used to read
    if (Capacitor.isPluginAvailable('Preferences')) {
      const { Preferences } = await import('@capacitor/preferences');
      await Preferences.set({ key: 'installationId', value });
    } else {
      const { Storage } = await import('@capacitor/storage');
      await Storage.set({ key: 'installationId', value });
    }
  }

  const authStore = useAuthStore();
  await authStore.setUniqueId(value);
});
