import { ActionPerformed as PushActionPerformed, PushNotifications, PushNotificationSchema, Token } from '@capacitor/push-notifications';
import { ActionPerformed as LocalActionPerformed, LocalNotifications } from '@capacitor/local-notifications';
import { boot } from 'quasar/wrappers';
import { Capacitor } from '@capacitor/core';
import { useAuthStore } from 'src/stores/auth';
import { api } from 'src/boot/axios';
import { toValue } from 'vue';
import { Router } from 'vue-router';

interface PushNotificationData {
    uuid: string;
    deepLink: string;
}

// isPushNotificationData checks if the supplied object is of type PushNotificationData by checking the expected fields for their type
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function isPushNotificationData(data: any): data is PushNotificationData {
    return data
        && typeof data.deepLink === 'string'
        && typeof data.uuid === 'string';
}

export default boot(async ({ router, app }) => {

    // ######################
    // Be sure to always check for the existence of the plugin before using it anywhere!!
    // ######################

    if (!Capacitor.isPluginAvailable('PushNotifications')) {
        console.warn('PushNotifications plugin not available');
        return;
    }

    // Request permission and register for push notifications
    PushNotifications.requestPermissions().then(result => {
        if (result.receive === 'granted') {
            console.log('Push notifications permission granted');
            PushNotifications.register();
        } else {
            console.log('Push notifications permission denied');
        }
    });

    if (await supportsLocalNotifications()) {
        LocalNotifications.requestPermissions().then(result => {
            if (result.display === 'granted') {
                console.log('Local notifications permission granted');
            } else {
                console.log('Local notifications permission denied');
            }
        });

        // This listener is called when someone interacts with a notification (eg: taps on it)
        LocalNotifications.addListener('localNotificationActionPerformed', (notification: LocalActionPerformed) => {
            processNotification(
                notification.notification.title ?? '',
                notification.notification.body ?? '',
                notification.notification.extra,
            );
        });
    }

    // Listener for registration success
    PushNotifications.addListener('registration', async (token: Token) => {
        const authStore = useAuthStore();
        await authStore.setFcmToken(token.value);
    });

    // Listener for registration error
    PushNotifications.addListener('registrationError', (error: unknown) => {
        console.log('Error on FCM registration: ' + JSON.stringify(error));
    });

    // This listener is being called if the app is running in foreground and a push notification is received
    PushNotifications.addListener('pushNotificationReceived', async (notification: PushNotificationSchema) => {
        markNotificationAsRead(notification);

        console.log('Push received: ' + JSON.stringify(notification));

        if (await supportsLocalNotifications()) {
            if (Capacitor.getPlatform() === 'android') {
                // Android will not deliver notifications without a proper channel, thus
                // we create a default channel if it doesn't exist. The notification will
                // automatically be assigned to the default channel.
                LocalNotifications.createChannel({
                    id: 'default',
                    name: 'Default',
                    description: 'This is the default channel',
                    // 1: Min notification importance: only shows in the shade, below the fold.
                    // 2: Low notification importance: Shows in the shade, and potentially in the status bar (see shouldHideSilentStatusBarIcons()), but is not audibly intrusive.
                    // 3: Default notification importance: shows everywhere, makes noise, but does not visually intrude.
                    // 4: Higher notification importance: shows everywhere, makes noise and peeks. May use full screen intents.
                    // 5: Max notification importance. Unused.
                    importance: 3,
                    // 1: private (show on lock screen, but hide sensitive information)
                    // 0: public (show on lock screen and show all information)
                    // -1: secret (don't show on lock screen)
                    visibility: 0,
                    // Overwritten by default vibration settings on phone as far as I could tell
                    vibration: false,
                }).then(() => LocalNotifications.schedule({
                    notifications: [
                        {
                            title: notification.title ?? '',
                            body: notification.body ?? '',
                            schedule: {
                                // On Android the notification needs to be at least 100ms in the future
                                at: new Date(Date.now() + 700),

                                // Can be used to show the notification will the app or phone is in doze mode
                                // Should be used sparingly as it can drain the battery and can only be used
                                // every 9 minutes.
                                allowWhileIdle: false,
                            },
                            extra: notification.data,
                            // Needs to be unique, otherwise it will overwrite the previous notification
                            id: Math.floor(Math.random() * 100000000),
                        },
                    ],
                }).then(r => {
                    console.log('Local channel notification scheduled: ' + JSON.stringify(r));
                }));
            } else if (Capacitor.getPlatform() === 'ios') {
                // iOS doesn't know channels, so we can just schedule the notification
                LocalNotifications.schedule({
                    notifications: [
                        {
                            title: notification.title ?? '',
                            body: notification.body ?? '',
                            schedule: {
                                // iOS is okay with a 10ms delay for local notifications
                                at: new Date(Date.now() + 700),
                            },
                            extra: notification.data,
                            // Needs to be unique, otherwise it will overwrite the previous notification
                            id: Math.floor(Math.random() * 100000000),
                        },
                    ],
                }).then(r => {
                    console.log('Local notification scheduled: ' + JSON.stringify(r));
                });
            }
        }
    });

    // This listener is called when someone interacts with a notification (eg: taps on it)
    PushNotifications.addListener('pushNotificationActionPerformed', async (notification: PushActionPerformed) => {
        markNotificationAsRead(notification.notification);

        processNotification(
            notification.notification.title ?? '',
            notification.notification.body ?? '',
            notification.notification.data,
        );
    });

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    function processNotification(title: string, body: string, data: unknown) {
        if (!isPushNotificationData(data)) {
            console.log('Received push notification without proper data: ' + JSON.stringify(notification.data));
            return;
        }

        const pnData = data as PushNotificationData;

        handleUrl(router, pnData.deepLink);
    }

    async function supportsLocalNotifications(): Promise<boolean> {
        if (!(Capacitor.getPlatform() === 'ios' || Capacitor.getPlatform() === 'android') || !Capacitor.isPluginAvailable('LocalNotifications')) {
            return false;
        }

        try {
            const permissionStatus = await LocalNotifications.checkPermissions();
            return permissionStatus.display === 'granted';
        } catch (error) {
            console.error('Error checking local notifications permissions:', error);
            return false;
        }
    }

    function markNotificationAsRead(notification: PushNotificationSchema): void {
        if (!isPushNotificationData(notification.data)) {
            console.log('Received push notification without proper data: ' + JSON.stringify(notification.data));
            return;
        }

        const data = notification.data as PushNotificationData;

        // Note: This function calling style allows us to run the function asynchronously and not wait for it to finish
        (async function sendPostRequest() {
            try {
                await api.post('/push/received', {
                    message_uuid: data.uuid,
                }, {
                    timeout: 2000, // 2 seconds
                });
            } catch (error) {
                console.error('Error sending push notification receipt', error);
            }
        })();
    }

    function handleUrl(router: Router, url: string) {
        try {
            const { origin, pathname, search, hash } = new URL(url);

            if (origin === window.location.origin) {
                // Internal URL
                router.push({ path: pathname + search + hash });
            } else {
                // External URL
                window.location.href = url;
            }
        } catch (error) {
            console.error('Invalid URL', error);
        }
    }
});
