import { ROUTE_NAMES } from '../router/route-names';
import { boot } from 'quasar/wrappers';

declare module '@vue/runtime-core' {
  // provide typings for `this.$routeNames`
  interface ComponentCustomProperties {
    $routeNames: typeof ROUTE_NAMES,
    $appName: string,
  }
}

export default boot(({ app }) => {
  app.config.globalProperties.$routeNames = ROUTE_NAMES;
  app.config.globalProperties.$appName = process.env.APP_NAME ?? 'numoDefault';
});
