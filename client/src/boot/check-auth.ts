import { boot } from 'quasar/wrappers';
import Team from 'src/models/Team';
import { ROUTE_NAMES } from 'src/router/route-names';
import { useAuthStore } from 'src/stores/auth';
import { RouteLocationNormalized, RouteMeta } from 'vue-router';
import { Loading } from 'quasar';
import { useDeviceInfo } from 'src/composables/useDeviceInfo';
import { errorsDynamicallyImportedModul } from 'src/boot/sentry';

declare module '@vue/runtime-core' {
    interface ComponentCustomProperties {
        hasPermissionForTeam: (team: Team, permissionName: App.Types.TeamPermissionType) => boolean,
        hasPermissionForAnyTeam: (permissionName: App.Types.TeamPermissionType) => boolean,
        hasFeature: (feature: App.Types.FeatureType) => boolean,
    }
}

const authStore = useAuthStore();

export function checkRouteRequiresAuthValid(meta: RouteMeta): boolean {
    return meta.requiresAuth !== true || authStore.loggedIn;
}

export function checkRouteRequiresFeatureValid(meta: RouteMeta): boolean {
    return meta.requiresFeature === undefined || authStore.hasFeature(meta.requiresFeature);
}

export function checkRouteRequiresPermissionForAnyTeamValid(meta: RouteMeta): boolean {
    return meta.requiresPermissionForAnyTeam === undefined || authStore.hasPermissionForAnyTeam(meta.requiresPermissionForAnyTeam);
}

export function checkRouteRequiresTeamsValid(meta: RouteMeta): boolean {
    return meta.requiresTeams !== true || authStore.teamsCount > 0;
}

function checkRouteRequiresAuthFailed(to: RouteLocationNormalized) {
    return !checkRouteRequiresAuthValid(to.meta);
}

function checkRouteRequiresMailAuthFailed(to: RouteLocationNormalized) {
    return authStore.loggedIn && !authStore.emailVerified && !to.path.startsWith('/site');
}

function checkRouteRequiresFeatureFailed(to: RouteLocationNormalized) {
    return !checkRouteRequiresFeatureValid(to.meta);
}

function checkRouteRequiresPermissionForAnyTeamFailed(to: RouteLocationNormalized) {
    return !checkRouteRequiresPermissionForAnyTeamValid(to.meta);
}

function checkRouteRequiresTeamsFailed(to: RouteLocationNormalized) {
    return !checkRouteRequiresTeamsValid(to.meta);
}

async function fetchUserDataOnAppStart() {
    const message = process.env.APP_NAME + ' wird gestartet';
    Loading.show({
        message: message,
    });

    if (useAuthStore().loggedIn) {
        await useAuthStore().fetchUserData();
        await useDeviceInfo().updateRemote();
    }
    Loading.hide();
}

export default boot(async ({ router, app }) => {

    await fetchUserDataOnAppStart();

    app.config.globalProperties.hasPermissionForTeam = authStore.hasPermissionForTeam;
    app.config.globalProperties.hasPermissionForAnyTeam = authStore.hasPermissionForAnyTeam;
    app.config.globalProperties.hasFeature = authStore.hasFeature;


    router.beforeEach(async (to, from) => {
        if (checkRouteRequiresAuthFailed(to)) {
            return { name: ROUTE_NAMES.LOGIN, query: { redirectTo: to.path } };
        } else if (checkRouteRequiresMailAuthFailed(to)) {
            return { name: ROUTE_NAMES.VERIFY_EMAIL, query: { redirectTo: to.path } };
        } else if (checkRouteRequiresFeatureFailed(to)
            || checkRouteRequiresTeamsFailed(to)
            // || checkRouteRequiresPermissionForAnyTeamFailed(to) // TODO activate
        ) {
            return { name: ROUTE_NAMES.HOME };
        } else {
            return true;
        }
    });

    router.onError((error, to) => {
        if(errorsDynamicallyImportedModul.some((errorMessage) => error.message.includes(errorMessage))) {
            // see https://github.com/eltini/clubmanager/issues/220
            window.location.href = to.fullPath;
        }
    });
});
