import { boot } from 'quasar/wrappers';
import { focusManager, VueQueryPlugin, VueQueryPluginOptions } from '@tanstack/vue-query';
// import BaseModel from 'src/models/BaseModel';

// function isModel(data: any) {
//   return data.data &&
//     (data.data instanceof BaseModel
//       || (Array.isArray(data.data) && (data.data.length == 0 || data.data[0] instanceof BaseModel)))
// }
// const defaultSelectFn = (data: any) => {
//   if (isModel(data)) {
//     return data.data
//   }
//   return data
// }
export default boot(({ app }) => {

  // provide the default query function to your app with defaultOptions
  const vueQueryPluginOptions: VueQueryPluginOptions = {
    queryClientConfig: {
      defaultOptions: {
        queries: {
          // select: defaultSelectFn
        },
      },
    },
  };

  focusManager.setEventListener(handleFocus => {
    // fix to use behaviour from tanstack query v4 where focus event is used to refetch queries
    // https://github.com/TanStack/query/pull/4805#issuecomment-1792500521

    // Listen to visibilitychange and focus
    if (typeof window !== 'undefined' && window.addEventListener) {
      window.addEventListener('visibilitychange', handleFocus, false);
      window.addEventListener('focus', handleFocus, false);
    }

    return () => {
      // Be sure to unsubscribe if a new handler is set
      window.removeEventListener('visibilitychange', handleFocus);
      window.removeEventListener('focus', handleFocus);
    };
  });

  app.use(VueQueryPlugin, vueQueryPluginOptions);
});
