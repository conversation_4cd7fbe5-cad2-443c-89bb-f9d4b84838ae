import { boot } from 'quasar/wrappers';
import axios from 'axios';
import { useAuthStore } from 'src/stores/auth';

const api = axios.create({
  baseURL: process.env.API_URL,
  // withCredentials: true
});

api.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

// add Token to requests if logged in
api.interceptors.request.use(function(config) {
  const authStore = useAuthStore();
  if (authStore.loggedIn) {
    config.headers.Authorization = 'Bearer ' + authStore.token;
  }
  return config;
});

api.interceptors.response.use(
  response => response,
  async error => {
    if (error.response && error.response.status === 401) {
      await useAuthStore().loadPermissions();
      // window.location.reload()
    }

    // get CSRF Token on error and send resend request
    else if (error.response && error.response.status === 419) {
      // Refresh csrf token
      await api.get('/sanctum/csrf-cookie');
      console.error('csrfResponse - should not be used, disabled for API requests');
      // Return a new request using the original request's configuration
      return axios(error.response.config);
    }
    return Promise.reject(error);
  },
);

export default boot(({ app }) => {
  // for use inside Vue files (Options API) through this.$axios and this.$api

  app.config.globalProperties.$axios = axios;
  // ^ ^ ^ this will allow you to use this.$axios (for Vue Options API form)
  //       so you won't necessarily have to import axios in each vue file

  app.config.globalProperties.$api = api;
  // ^ ^ ^ this will allow you to use this.$api (for Vue Options API form)
  //       so you can easily perform requests against your app's API
});

export { axios, api };
