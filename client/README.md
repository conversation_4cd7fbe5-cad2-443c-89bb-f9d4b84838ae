# numo (client)

Organize your club

# Topics
1. [Setup dev Environment](#setup)
2. [Mobile](#mobile)
3. [Frontend Tests](#tests)



# Setup dev Environment <div id="setup"/>
## Install the dependencies
```bash
#install
yarn install

#update dependencies
yarn upgrade-interactive
```

### Start the app in development mode (hot-code reloading, error reporting, etc.)
```bash
quasar dev
```


### Lint the files
```bash
yarn lint
```


### Format the files
```bash
yarn format
```

# Mobile <div id="mobile"/>

## Fastlane setup
The following is the recommended setup of fastlane, as it allows to pin versions
```bash
# Install ruby via rbenv
1. brew install rbenv
2. rbenv install 3.2.2
3. rbenv global 3.2.2
4. rbenv rehash
5. echo 'eval "$(rbenv init -)"' >> ~/.zshrc

# Install bundler
6. gem install bundler

# Install fastlane via bundler
7. cd client/src-capacitor/android
8. bundle update

# Ensure that you have openjdk 17 installed
# (required for compatibility with the capacitor gradle version)
9. brew install openjdk@17

# Running fastlane
10. bundle exec fastlane

# Note: Fastlane requires the locale to be set to UTF-8, if you get an error about it add the following to the .zshrc or .bashrc:
export LC_ALL=en_US.UTF-8
export LANG=en_US.UTF-8
```

## Virtual device setup
```
1. Create a new virtual device in Android Studio (Pixel 6 API 33, Android 13)
2. Terminate the virtual device if it is running
3. Start the device via the command line and allow the system partition to be made writeable:
  - `Library/Android/sdk/emulator/emulator -avd Pixel_6_API_33 -writable-system`
4.

```

## Build the Android app for production
```bash
cp client/src-capacitor/android/app/google-services-production.json client/src-capacitor/android/app/google-services.json
yarn build
quasar dev:android
make build-android
> Run release process in Android Studio
```

### Customize the configuration
See [Configuring quasar.config.js](https://v2.quasar.dev/quasar-cli-vite/quasar-config-js).

### Start storybook
Storybook is configured to work with quasar components:
https://dev.to/allanjeremy/using-quasar-with-vue3-storybook-4dl8
 > For now, storybook is not used and removed as dependency. The .storybook config folder is still present to see integration with quasar when it will be used again at any time
```bash
yarn storybook
```

# Mobile app development - Android

**Preparation:**
- Install Android Studio
- Install Android SDK (via Android Studio)
- Set the necessary environment variables (in `~/.zshrc` and reload via `source ~/.zshrc`)
  - `export ANDROID_HOME="$HOME/Library/Android/sdk"`
  - `export ANDROID_SDK_ROOT="$HOME/Library/Android/sdk"`
  - `export PATH=$PATH:$ANDROID_SDK_ROOT/tools`
  - `export PATH=$PATH:$ANDROID_SDK_ROOT/platform-tools`
  - `export PATH=$PATH:$ANDROID_SDK_ROOT/build-tools/33.0.2/`
  - `export PATH=$PATH:$ANDROID_SDK_ROOT/emulator/`
- Create a virtual device (Pixel 6 API 33)
  - Stop the virtual device if started
- Fix hosts file (alternative: use pihole/adguard local DNS, but that messes with local development)
  - Prepare hosts file with the following content:
    ```
    127.0.0.1       localhost
    ::1             ip6-localhost
    ********        api.server.ddev.site
    ********        app.server.ddev.site
    ```
  - Start emulator and allow system partition to be remounted writable:
    - `emulator -avd Pixel_6_API_33 -writable-system`
  - Remount system partition and push hosts file:
    - `adb root`
    - `adb remount`
    - `adb push hosts /system/etc/hosts`
- Add root CA (mkcert support)
  - Remount system partition and disable verification:
  - `adb push "/Users/<USER>/Library/Application Support/mkcert/rootCA.pem" /storage/emulated/0/Download`
  - Install root CA in Android via `Settings > Security > Install from SD card`: TBH
- Start the app (starts Android Studio, which then can be used to run the app)
  - `yarn dev:android`

**Signing the app**
- Download the keystore and locate it at
  - `~/.keystore/clubmanager-release-key.keystore`
- In the same location add a `config.ini` with the following contents:
  - [signingConfig]
    keyPassword=<password>
    storePassword=<password>
- TODO: Describe how to generate a signed AAB

**Remote Debugging**
- Open Chrome and go to `chrome://inspect/#devices`
- Click on `inspect` for the WebView containing the app
- In case you receive an 404 error
  - The error means that no remotely hosted DevTools distribution for the remote Chrome version is hosted by Google
  - Do the following:
    - Open the devtools in the browser and go to the `Network` tab
    - Open the network conditions sub tab and set the user agent to and older Chrome version
      - For example `Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/******** Safari/537.36`
    - Reload the page
      - The `inspect fallback` link should now be visible
      - This link can be used to open the devtools for the app while using a local devtools distribution

**Adding new capacitor plugins**
- Go into the client directory
- Add the plugin via `yarn add @capacitor/<plugin-name>`
- Run
  - `npx cap sync`
  - `npx cap sync android`

**App Links: Android**
- For app links to work in an emulator the local debug certificate fingerprint must be added to the assetlinks.json
- In Android Studio go to Tools -> App Links Assistant
- Go to step 3. Note down the sha256_cert_fingerprints value
- Add it to the assetlinks.json file for the com.numoapp.numo.app package within the frontend / client deployment
- Deploy that to production. Don't mess up the file or it will break app links from working globally
- Once deployed you can retrigger the verification process on the emulator via
  - adb shell pm verify-app-links --re-verify com.numoapp.numo.app
  - Wait a moment and run
  - adb shell pm get-app-links com.numoapp.numo.app
  - That should print "app.numo-app.com: verified"
- You can now open step 4 on the App Links Assistant and test it with various links
- Alternatively from the shell run the following:
  - adb shell am start -a "android.intent.action.VIEW" -d "https://app.numo-app.com/invite/cFyejk-ewxFkfeQBpeyOW"

**Firebase Messaging Setup**
- Creating a new google-services.json
  1. Go to the firebase project
  2. Add Android App
  3. Enter the package name (com.numoapp.numo.app)
  4. Click on "Register App"
  5. Download the google-services.json
  6. Copy the file to the client/src-capacitor/android/app directory
- ...


# Frontend Tests <div id="tests"/>

## Start test environment
We are using [Cypress](https://docs.cypress.io/guides/overview/why-cypress) for frontend tests.
The setup is based on the [quasar testing configuration](https://quasar.dev/quasar-cli-vite/testing-and-auditing) which has some custom [cypress commands](https://testing.quasar.dev/packages/e2e-cypress/)

These plugins are installed, too:
- [wait-until](https://github.com/NoriSte/cypress-wait-until)

To start the test environment, the backend server has to be running
```bash
# in main directory
ddev start
# in client directory
test:e2e
```

## Setup PHP Storm
- install Test Automation Plugin: https://plugins.jetbrains.com/plugin/20175-test-automation
- changing location of the command type file, so PHP Storm can generate the type definition of custom commands for code completion in the correct index.d.ts file: https://www.jetbrains.com/help/phpstorm/cypress-custom-commands.html#change_location_of_command_file

## How it works
The test environment can be used in parallel with the dev server (quasar dev) and is using the test database.

### Commands / Scripts
`test:e2e` is doing a few things to get the test environment up and running:
1. it is using [start-test](https://github.com/bahmutov/start-server-and-test) to prepare the frontend and backend server, waits for frontend and backend URL to be available and then starts cypress

Frontend
1. The frontend preparation is using [Concurrently](https://github.com/open-cli-tools/concurrently) to build the frontend and use `quasar serve` to start the frontend server
2. The frontend is using `quasar build`, since [cypress is really slow](https://github.com/cypress-io/cypress/issues/22968) when running with quasar/vite dev server. Even the login page takes >10 seconds to load and regularly runs into cypress timeout.
3. `quasar build` only builds the frontend once, but during development cypress should be capable of staying open. Vite has a `--watch` param for build mode, but [quasar does not](https://github.com/quasarframework/quasar/discussions/10180). So we are using [chokidar](https://github.com/open-cli-tools/chokidar-cli) as file watcher for the source files and clean + build the frontend whenever a file is changed. The rebuild is visible on the command line, so the tests can be started again, when the rebuild has finished.

Backend
1. The backend runs the migration on the `test` db. The seeding will be done during the tests
2. It also checks, if the backend is running and the api-test subdomain is available


### Using test database
Since we do not want the local db beeing used for automated tests, we are using the same test db that ist used for the unit tests and comes with DDEV. Since there is only one backend server running in DDEV that can be used for both frontends (quasar dev and cypresss) in parallel, the underlying database has to be changed for requests from cypress.
This is done with the following steps:
- server/.env file should have to API URLS configured:
  - APP_API_URL="https://api.server.ddev.site"
  - APP_API_TEST_URL="https://api-test.server.ddev.site"
- server/config/database.php has a connection 'mysql_test' configured for the test db
- server/app/Providers/FrontendTestServiceProvider.php looks for the api-test APP_API_TEST_URL and changes the default db connection to 'mysql_test'
  - it also sets Mail and Cache driver to array and Queue to sync so everything will stay inside the test environment
- server/app/Providers/RouteServiceProvider.php overwrites the api routes so the test URL is used when routes are generated by name
- client/.env.testing has the test API URL and other URLs with the port 9002:
  - APP_API_URL="https://api-test.server.ddev.site:8443"
- .ddev/config.yaml adds the subdomain as an additional_hostname
- client/cypress.config.ts has the baseURL set with port 9002
- client/quasar.extensions.json has the port 9002 set for @quasar/testing-e2e-cypress extension



