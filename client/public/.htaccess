<IfModule mod_speling.c>
    CheckSpelling off
</IfModule>

<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews
    </IfModule>

    # added for ionos
    RewriteBase /
    Options +FollowSymLinks
    # added for ionos

    RewriteEngine On

    # Redirect to https
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}/$1 [R=302,L]

    # Send Requests To Quasar App...
    RewriteCond %{REQUEST_URI} !^/assets
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.html [QSA,L]
</IfModule>

<FilesMatch "apple-app-site-association$">
    Header set Content-Type application/json
</FilesMatch>

<IfModule mod_headers.c>
    <Files "index.html">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </Files>
</IfModule>
