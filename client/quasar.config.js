/* eslint-env node */

/*
 * This file runs in a Node context (it's NOT transpiled by Babel), so use only
 * the ES6 features that are supported by your Node version. https://node.green/
 */

// Configuration for your app
// https://v2.quasar.dev/quasar-cli-vite/quasar-config-js

const { configure } = require('quasar/wrappers');
const { loadEnv } = require('vite');
const path = require('path');
const os = require('os');

// vite needs a prefix to prevent accidentally exposing variables into client code and will only load prefixes variables
const dotenv = loadEnv(process.env.MODE, process.cwd(), 'APP_');

console.log('Loading environment configuration for ' + process.env.MODE);
console.log('Environment configuration:');
console.log(dotenv);

if (dotenv.APP_API_URL === undefined) {
    throw new Error('APP_API_URL missing. Maybe .env file missing or variable not set');
}

if (dotenv.APP_INVITE_URL === undefined) {
    throw new Error('APP_INVITE_URL missing. Maybe .env file missing or variable not set');
}

let httpsConfig = undefined;
let listeningPort = undefined;
let strictListeningPort = undefined;

if (dotenv.APP_ENABLE_HTTPS === 'true') {
    /**
     * This will be enabled when running 'yarn devHttps'. It requires some setup. See docs/ssl_dev_server.md
     */

    listeningPort = 443;
    strictListeningPort = 443;

    httpsConfig = {
        key: '../private/certs/private.key',
        cert: '../private/certs/certificate.crt',
        ca: '../private/certs/ca_bundle.crt',
    };
}

module.exports = configure(function(/* ctx */) {


    return {
        eslint: {
            // fix: true,
            // include = [],
            // exclude = [],
            // rawOptions = {},
            warnings: true,
            errors: true,
        },

        // https://v2.quasar.dev/quasar-cli-vite/prefetch-feature
        preFetch: true,

        // app boot file (/src/boot)
        // --> boot files are part of "main.js"
        // https://v2.quasar.dev/quasar-cli-vite/boot-files
        boot: [
            'sentry',
            'axios',
            'i18n',
            'route-names',
            'tanstack-query',
            'gsap',
            'capacitor',
            'push-notifications',
            'installation-id',
            'componentDefaults',
            'check-auth',
        ],

        // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#css
        css: [
            'app.scss',
        ],

        // https://github.com/quasarframework/quasar/tree/dev/extras
        extras: [
            // 'ionicons-v4',
            'mdi-v7',
            'fontawesome-v6',
            // 'eva-icons',
            // 'themify',
            // 'line-awesome',
            // 'roboto-font-latin-ext', // this or either 'roboto-font', NEVER both!

            'roboto-font', // optional, you are not bound to it
            'material-icons', // optional, you are not bound to it
            'material-icons-outlined', // optional, you are not bound to it
        ],

        // Full list of options: https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#build
        build: {
            target: {
                browser: ['es2019', 'edge88', 'firefox78', 'chrome87', 'safari13.1'],
                node: 'node16',
            },
            vueRouterMode: 'history', // available values: 'hash', 'history'
            // vueRouterBase,
            // vueDevtools,
            // vueOptionsAPI: false,

            sourcemap: 'true',
            // rebuildCache: true, // rebuilds Vite/linter/etc cache on startup

            publicPath: process.env.PUBLIC_PATH !== undefined ? process.env.PUBLIC_PATH : '/',
            // analyze: true,
            env: {
                APP_NAME: dotenv.APP_APP_NAME,
                INVITE_URL: dotenv.APP_INVITE_URL,
                API_URL: dotenv.APP_API_URL,
                WEBSITE_URL: dotenv.APP_WEBSITE_URL,
                ENVIRONMENT: dotenv.APP_ENV,
                SENTRY_ENABLED_STRING: dotenv.APP_SENTRY_ENABLED === undefined ? 'true' : dotenv.APP_SENTRY_ENABLED,
                ALLOW_REGISTRATION_STRING: dotenv.APP_ALLOW_REGISTRATION === undefined ? 'true' : dotenv.APP_ALLOW_REGISTRATION,
            },
            // rawDefine: {}
            // ignorePublicFolder: true,
            // minify: false,
            // polyfillModulePreload: true,
            // distDir

            // extendViteConf (viteConf) {},
            // viteVuePluginOptions: {},

            vitePlugins: [
                [require('@sentry/vite-plugin').sentryVitePlugin, {
                    org: 'eltini',
                    project: 'javascript-vue',
                    // Auth tokens can be obtained from https://sentry.io/settings/account/api/auth-tokens/
                    // and need `project:releases` and `org:read` scopes
                    authToken: dotenv.APP_SENTRY_AUTH_TOKEN,
                },
                    'vite-plugin-checker', {
                    vueTsc: {
                        tsconfigPath: 'tsconfig.vue-tsc.json',
                    },
                    eslint: {
                        lintCommand: 'eslint "./**/*.{js,ts,mjs,cjs,vue}"',
                    },
                },
                    { server: false },
                ],
                // ['@intlify/unplugin-vue-i18n', {
                //   // if you want to use Vue I18n Legacy API, you need to set `compositionOnly: false`
                //   // compositionOnly: false,
                //
                //   // you need to set i18n resource including paths !
                //   include: path.resolve(__dirname, './src/i18n/**')
                // }]
            ],
        },

        // Full list of options: https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#devServer
        devServer: {
            // https: true,
            // host: 'app.server.ddev.site',
            // open: true, // opens browser window automatically
            open: process.env.MODE !== 'test', // opens browser window automatically
            https: httpsConfig,
            port: listeningPort,
            strictPort: strictListeningPort,
        },

        // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#framework
        framework: {
            config: {
                capacitor: {
                    'backButtonExit': ['#/', '/login'],
                    'iosStatusBarPadding': false,
                },
                brand: {
                    primary: '#444e61',
                    secondary: '#fdefbc',
                    accent: '#fbd758',

                    dark: '#2f3643',
                    'dark-page': '#59667f',

                    positive: '#43a047', // $green-7
                    negative: '#e53935', // $red-7
                    info: '#4a8ebf',
                    warning: '#eba433',
                },
            },

            // iconSet: 'material-icons', // Quasar icon set
            lang: 'de', // Quasar language pack

            // For special cases outside of where the auto-import strategy can have an impact
            // (like functional components as one of the examples),
            // you can manually specify Quasar components/directives to be available everywhere:
            //
            // components: [],
            // directives: [],

            // Quasar plugins
            plugins: [
                'SessionStorage',
                'Dialog',
                'Notify',
                'Loading',
                'Meta',
            ],
        },

        // https://v2.quasar.dev/options/animations
        // animations: 'all', // --- includes all animations
        animations: [],

        // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#sourcefiles
        // sourceFiles: {
        //   rootComponent: 'src/App.vue',
        //   router: 'src/router/index',
        //   store: 'src/store/index',
        //   registerServiceWorker: 'src-pwa/register-service-worker',
        //   serviceWorker: 'src-pwa/custom-service-worker',
        //   pwaManifestFile: 'src-pwa/manifest.json',
        //   electronMain: 'src-electron/electron-main',
        //   electronPreload: 'src-electron/electron-preload'
        // },

        // https://v2.quasar.dev/quasar-cli-vite/developing-ssr/configuring-ssr
        ssr: {
            // ssrPwaHtmlFilename: 'offline.html', // do NOT use index.html as name!
            // will mess up SSR

            // extendSSRWebserverConf (esbuildConf) {},
            // extendPackageJson (json) {},

            pwa: false,

            // manualStoreHydration: true,
            // manualPostHydrationTrigger: true,

            prodPort: 3000, // The default port that the production server should use
                            // (gets superseded if process.env.PORT is specified at runtime)

            middlewares: [
                'render', // keep this as last one
            ],
        },

        // https://v2.quasar.dev/quasar-cli-vite/developing-pwa/configuring-pwa
        pwa: {
            workboxMode: 'generateSW', // or 'injectManifest'
            injectPwaMetaTags: true,
            swFilename: 'sw.js',
            manifestFilename: 'manifest.json',
            useCredentialsForManifestTag: false,
            // extendGenerateSWOptions (cfg) {}
            // extendInjectManifestOptions (cfg) {},
            // extendManifestJson (json) {}
            // extendPWACustomSWConf (esbuildConf) {}
        },

        // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-cordova-apps/configuring-cordova
        cordova: {
            // noIosLegacyBuildFlag: true, // uncomment only if you know what you are doing
        },

        // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-capacitor-apps/configuring-capacitor
        capacitor: {
            hideSplashscreen: true,
        },
        bin: {
            // windowsAndroidStudio: 'C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Toolbox\\apps\\AndroidStudio\\ch-0\\213.7172.25.2113.9123335\\bin\\studio64.exe'
        },

        // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-electron-apps/configuring-electron
        electron: {
            // extendElectronMainConf (esbuildConf)
            // extendElectronPreloadConf (esbuildConf)

            inspectPort: 5858,

            bundler: 'packager', // 'packager' or 'builder'

            packager: {
                // https://github.com/electron-userland/electron-packager/blob/master/docs/api.md#options

                // OS X / Mac App Store
                // appBundleId: '',
                // appCategoryType: '',
                // osxSign: '',
                // protocol: 'myapp://path',

                // Windows only
                // win32metadata: { ... }
            },

            builder: {
                // https://www.electron.build/configuration/configuration

                appId: 'client',
            },
        },

        // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-browser-extensions/configuring-bex
        bex: {
            contentScripts: [
                'my-content-script',
            ],

            // extendBexScriptsConf (esbuildConf) {}
            // extendBexManifestJson (json) {}
        },
    };
});
