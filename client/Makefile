# Makefile for building and signing a Quasar Capacitor Android project
#
# Note: Make sure the following tools are installed:
# - apks<PERSON><PERSON>, which is part of the Android SDK
# - zipalign, which is part of the Android SDK
# - op, which is part of the 1Password CLI
#
# Note: Make sure the following environment variables are set:
# ~/Library/Android/sdk/build-tools/31.0.0/ is on your path
#
# Path should be
# export ANDROID_HOME="$HOME/Library/Android/sdk"
# export ANDROID_SDK_ROOT="$HOME/Library/Android/sdk"
# export PATH=$PATH:$ANDROID_SDK_ROOT/tools
# export PATH=$PATH:$ANDROID_SDK_ROOT/platform-tools
# export PATH=$PATH:$ANDROID_SDK_ROOT/build-tools/33.0.2/
# export PATH=$PATH:$ANDROID_SDK_ROOT/emulator
#

# Variables
#OPTOKEN := $(shell op signin --raw --account https://my.1password.com)
#KEYSTORE_PASSWORD = $(shell op --session $(OPTOKEN) item get --vault "Eltini - Shared" "Eltini - clubmanager-release-key-keystore - File" --fields Password)
#APP_NAME = Clubmanager
#KEYSTORE_NAME = $(shell pwd)/src-capacitor/clubmanager-release-key.keystore
#KEYSTORE_ALIAS = clubmanager
#ANDROID_APP_FOLDER = src-capacitor/android/app
#ANDROID_APK_FOLDER = src-capacitor/android/app/build/outputs/apk/release
#
#.PHONY: sign-android
#
#sign-android:
#	@echo Running apksigner
#	# Delete the old apk
#	@rm -f $(ANDROID_APK_FOLDER)/$(APP_NAME).apk
#	# Sign the app
#	@cd $(ANDROID_APK_FOLDER) && \
#	apksigner sign --ks $(KEYSTORE_NAME) --ks-pass pass:$(KEYSTORE_PASSWORD) --ks-key-alias clubmanager app-release-unsigned.apk
#
#	# Zipalign the signed app
#	cd $(ANDROID_APK_FOLDER) && \
#	zipalign -v 4 app-release-unsigned.apk $(APP_NAME).apk

current_dir = $(shell pwd)

build-android:
	@echo '############################################################'
	@echo 'Running quasar build process (app, icons, ...)'
	@echo '############################################################'
	MODE=android quasar build -m capacitor -T android

	@echo '############################################################'
	@echo 'Updating capacitor config'
	@echo '############################################################'
	@cd src-capacitor && NODE_ENV=production node update-capacitor-config.js android/app/src/main/assets/capacitor.config.json

	@echo '############################################################'
	@echo 'Remove public content'
	@echo '############################################################'
	rm -rf src-capacitor/android/app/src/main/assets/public/*

	@echo '############################################################'
	@echo 'Rebuilding capacitor app'
	@echo '############################################################'
	@cd src-capacitor/android && ./gradlew :app:bundleRelease

	@echo '############################################################'
	@echo 'Copying output to dist directory'
	@echo '############################################################'
	rm -rf dist/capacitor/android/*
	cp -R src-capacitor/android/app/build/outputs/* dist/capacitor/android/

	@echo 'Done'

gen-android-icons:
	icongenie generate -p icongenie-android.json

gen-ios-icons:
	icongenie generate -p icongenie-ios.json

ios-pre-build:
	@cd src-capacitor && NODE_ENV=production node update-capacitor-config.js ios/App/App/capacitor.config.json

# Requires imagemagick to be installed
gen-favicon:
	convert -density 256x256 -background transparent $(current_dir)/public/numo_favicon.svg -define icon:auto-resize -colors 256 $(current_dir)/public/favicon.ico
	@echo
	@echo Note: Update the version query parameter in client/index.html for browsers to refresh the icon

push-staging:
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'rm -rf staging/new_frontend'
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'cp -r staging/frontend staging/new_frontend'
	rclone --config blank.config -v --sftp-host access950750563.webspace-data.io --sftp-port 22 --sftp-pass ${IONOS_MAIN_ACCOUNT_PASS_RCLONE} --sftp-user ${IONOS_MAIN_ACCOUNT_USER} --sftp-path-override /kunden/homepages/18/d950750563/htdocs/staging/new_frontend/ sync dist/spa/ :sftp:staging/new_frontend/

update-staging:
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'mv staging/frontend staging/old_frontend'
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'mv staging/new_frontend staging/frontend'
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'rm -rf staging/old_frontend'

push-production:
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'rm -rf production/new_frontend'
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'cp -r production/frontend production/new_frontend'
	rclone --config blank.config -v --sftp-host access950750563.webspace-data.io --sftp-port 22 --sftp-pass ${IONOS_MAIN_ACCOUNT_PASS_RCLONE} --sftp-user ${IONOS_MAIN_ACCOUNT_USER} --sftp-path-override /kunden/homepages/18/d950750563/htdocs/production/new_frontend/ sync dist/spa/ :sftp:production/new_frontend/

update-production:
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'mv production/frontend production/old_frontend'
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'mv production/new_frontend production/frontend'
	sshpass -v -p "${IONOS_MAIN_ACCOUNT_PASS}" ssh -o "StrictHostKeyChecking=no" ${IONOS_MAIN_ACCOUNT_USER}@access950750563.webspace-data.io 'rm -rf production/old_frontend'
