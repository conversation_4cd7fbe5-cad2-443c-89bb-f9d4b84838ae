{
  "extends": "@quasar/app-vite/tsconfig-preset",
  "compilerOptions": {
    "baseUrl": ".",
    "experimentalDecorators": true,
    "useDefineForClassFields": false,
    "strictPropertyInitialization": false,
    "paths" : {
      "src/*": ["./src/*"]
    },
    "noImplicitAny": false // added since there were errors with lodash: https://pjausovec.medium.com/how-to-fix-error-ts7016-could-not-find-a-declaration-file-for-module-xyz-has-an-any-type-ecab588800a8
  }
}
