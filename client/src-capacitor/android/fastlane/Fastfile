# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

# Notes:
# - The version code an be found here:
#   - client/src-capacitor/android/app/build.gradle

require 'iniparse'
require 'json'
require 'pathname'

default_platform(:android)

platform :android do
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Submit a new Beta Build to Crashlytics Beta"
  lane :beta do
    gradle(task: "clean assembleRelease")
    crashlytics

    # sh "your_script.sh"
    # You can also use other beta testing services here
  end


   desc "Deploy a new version to the Google Play Alpha track"
   lane :deploy_to_alpha do
     config = IniParse.parse(File.read(File.expand_path("~/.keystore/config.ini")))
     signing_config = config['signingConfig']

     # This reverts changes made by the quasar tooling when one opens the project in Android Studio
     fix_main_activity()
     update_server_url("../app/src/main/assets/capacitor.config.json") # Config for iOS: ./ios/App/App/capacitor.config.json

     gradle(
       task: "clean bundleRelease",
       # Adds a signing config to sign the bundle. Note that this is the upload key. The actual signing key is managed by Google.
       properties: {
         "android.injected.signing.store.file" => File.expand_path("~/.keystore/clubmanager-release-key.keystore"),
         "android.injected.signing.store.password" => signing_config['storePassword'],
         "android.injected.signing.key.alias" => "clubmanager",
         "android.injected.signing.key.password" => signing_config['keyPassword'],
       }
     )
     upload_to_play_store(track: 'alpha')
   end

   desc "Deploy a new version to the Google Play Internal track"
   lane :deploy_to_internal do
     config = IniParse.parse(File.read(File.expand_path("~/.keystore/config.ini")))
     signing_config = config['signingConfig']

     # This reverts changes made by the quasar tooling when one opens the project in Android Studio
     fix_main_activity()
     update_server_url("../app/src/main/assets/capacitor.config.json") # Config for iOS: ./ios/App/App/capacitor.config.json

     gradle(
       task: "clean bundleRelease",
       # Adds a signing config to sign the bundle. Note that this is the upload key. The actual signing key is managed by Google.
       properties: {
         "android.injected.signing.store.file" => File.expand_path("~/.keystore/clubmanager-release-key.keystore"),
         "android.injected.signing.store.password" => signing_config['storePassword'],
         "android.injected.signing.key.alias" => "clubmanager",
         "android.injected.signing.key.password" => signing_config['keyPassword'],
       }
     )
     upload_to_play_store(track: 'internal')
   end
end

def fix_main_activity()
  # Copy FixedMainActivity.java to the desired location
  source_path = File.expand_path("FixedMainActivity.java")
  destination_path = File.expand_path("../app/src/main/java/com/numoapp/numo/app/MainActivity.java")
  FileUtils.cp(source_path, destination_path)

  UI.message "Copied #{Pathname.new(source_path).basename} to #{Pathname.new(destination_path).dirname}\n"

  # Delete EnableHttpsSelfSigned.java if it exists
  file_to_delete = File.expand_path("../app/src/main/java/com/numoapp/numo/app/EnableHttpsSelfSigned.java")
  File.delete(file_to_delete) if File.exist?(file_to_delete)

  UI.message "Deleted #{Pathname.new(file_to_delete).basename}\n"
end

def update_server_url(config_path)
  # Read in the capacitor.config.json file
  config = JSON.parse(File.read(config_path))

  # Update the server.url property in the config object
  config['server'] ||= {}
  config['server']['url'] = 'https://app.numo-app.com'
  config['server']['allowNavigation'] = ['app-staging.numo-app.com', 'app-testing.numo-app.com']

  # Write the updated config object back to the file
  File.write(config_path, JSON.pretty_generate(config))

  UI.message "Updated #{Pathname.new(config_path).basename} with server URL: #{config['server']['url']}\n"
end
