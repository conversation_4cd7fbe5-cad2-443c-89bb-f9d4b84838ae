const fs = require('fs');
const path = require('path');

// Get the config path from the command line arguments
const args = process.argv.slice(2);
const configPath = args[0];

// Read in the capacitor.config.json file
const configFile = fs.readFileSync(configPath);
const config = JSON.parse(configFile);

// Determine which URL to use based on the build type
const isProduction = process.env.NODE_ENV === 'production';
const serverUrl = isProduction ? 'https://app.numo-app.com' : 'https://app-staging.numo-app.com';

// Update the server.url property in the config object
if (config.server === undefined) {
  config.server = {};
}
config.server.url = serverUrl;
config.server.cleartext = false;

// Write the updated config object back to the file
fs.writeFileSync(configPath, JSON.stringify(config, null, 2));

console.log(`Updated ${path.basename(configPath)} with server URL: ${serverUrl}\n`);
