# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:ios)

platform :ios do
  desc "Push a new beta build to TestFlight"
  lane :beta do
    increment_build_number(xcodeproj: "App.xcodeproj")
    build_app(workspace: "App.xcworkspace", scheme: "App")
    upload_to_testflight
  end

  desc "Get certificates"
  lane :certificates do
    sync_code_signing(
      type: "development",
      app_identifier: ['com.numoapp.numo.app'],
      force_for_new_devices: true,
      readonly: true
    )

    sync_code_signing(
      type: "appstore",
      app_identifier: 'com.numoapp.numo.app',
      readonly: true
    )
  end

  desc "Generate new certificates"
  lane :generate_new_certificates do
    sync_code_signing(
      type: "development",
      app_identifier: ['com.numoapp.numo.app'],
      force_for_new_devices: true,
      readonly: false
    )

    sync_code_signing(
      type: "appstore",
      app_identifier: ['com.numoapp.numo.app'],
      force_for_new_devices: true,
      readonly: false
    )
  end
end
