PODS:
  - Capacitor (7.2.0):
    - Capac<PERSON><PERSON><PERSON>ova
  - CapacitorApp (7.0.1):
    - Capacitor
  - CapacitorClipboard (7.0.1):
    - Capacitor
  - Capacitor<PERSON>ordova (7.2.0)
  - CapacitorDevice (7.0.1):
    - Capacitor
  - CapacitorLocalNotifications (7.0.1):
    - Capacitor
  - CapacitorNativeSettings (5.0.1):
    - Capacitor
  - CapacitorPreferences (7.0.1):
    - Capacitor
  - CapacitorPushNotifications (7.0.1):
    - Capacitor
  - Firebase/CoreOnly (10.16.0):
    - FirebaseCore (= 10.16.0)
  - Firebase/Messaging (10.16.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.16.0)
  - FirebaseCore (10.16.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreInternal (10.16.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.16.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.16.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.2.5):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.11.5):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.11.5):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.11.5):
    - GoogleUtilities/Environment
  - GoogleUtilities/Network (7.11.5):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.11.5)"
  - GoogleUtilities/Reachability (7.11.5):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.11.5):
    - GoogleUtilities/Logger
  - nanopb (2.30909.0):
    - nanopb/decode (= 2.30909.0)
    - nanopb/encode (= 2.30909.0)
  - nanopb/decode (2.30909.0)
  - nanopb/encode (2.30909.0)
  - PromisesObjC (2.3.1)

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../node_modules/@capacitor/app`)"
  - "CapacitorClipboard (from `../../node_modules/@capacitor/clipboard`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorDevice (from `../../node_modules/@capacitor/device`)"
  - "CapacitorLocalNotifications (from `../../node_modules/@capacitor/local-notifications`)"
  - CapacitorNativeSettings (from `../../node_modules/capacitor-native-settings`)
  - "CapacitorPreferences (from `../../node_modules/@capacitor/preferences`)"
  - "CapacitorPushNotifications (from `../../node_modules/@capacitor/push-notifications`)"
  - Firebase/Messaging

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PromisesObjC

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../node_modules/@capacitor/app"
  CapacitorClipboard:
    :path: "../../node_modules/@capacitor/clipboard"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorDevice:
    :path: "../../node_modules/@capacitor/device"
  CapacitorLocalNotifications:
    :path: "../../node_modules/@capacitor/local-notifications"
  CapacitorNativeSettings:
    :path: "../../node_modules/capacitor-native-settings"
  CapacitorPreferences:
    :path: "../../node_modules/@capacitor/preferences"
  CapacitorPushNotifications:
    :path: "../../node_modules/@capacitor/push-notifications"

SPEC CHECKSUMS:
  Capacitor: 106e7a4205f4618d582b886a975657c61179138d
  CapacitorApp: d63334c052278caf5d81585d80b21905c6f93f39
  CapacitorClipboard: b98aead5dc7ec595547fc2c5d75bacd2ae3338bc
  CapacitorCordova: 5967b9ba03915ef1d585469d6e31f31dc49be96f
  CapacitorDevice: fe3f190e1d718f4607bdc6b73993433d1c84f409
  CapacitorLocalNotifications: 4ea60b9347eac60b37cb4daaf62d12060bb00972
  CapacitorNativeSettings: 6e94ed3c0465206756f320df18efc52f053ce3c7
  CapacitorPreferences: cbf154e5e5519b7f5ab33817a334dda1e98387f9
  CapacitorPushNotifications: 0b653a3264d56daccf0e8769fff15d2f48306de6
  Firebase: 25899099b77d255a636e3579c3d9dce10ec150d5
  FirebaseCore: 65a801af84cca84361ef9eac3fd868656968a53b
  FirebaseCoreInternal: 26233f705cc4531236818a07ac84d20c333e505a
  FirebaseInstallations: b822f91a61f7d1ba763e5ccc9d4f2e6f2ed3b3ee
  FirebaseMessaging: 80b4a086d20ed4fd385a702f4bfa920e14f5064d
  GoogleDataTransport: 54dee9d48d14580407f8f5fbf2f496e92437a2f2
  GoogleUtilities: 13e2c67ede716b8741c7989e26893d151b2b2084
  nanopb: b552cce312b6c8484180ef47159bc0f65a1f0431
  PromisesObjC: c50d2056b5253dadbd6c2bea79b0674bd5a52fa4

PODFILE CHECKSUM: f2c96198ed9b350373e00935714f65f362d6d9c4

COCOAPODS: 1.15.0
