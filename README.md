

## Serverless config

- Set the environment variable AWS_SDK_LOAD_CONFIG to 1
- Configure the AWS credentials
    - The profiles need to be configured in the credentials file (not the config file)
    - Windows: %USERPROFILE%/.aws/credentials, Linux: ~/.aws/credentials

AWS credentials file
```
[serverless]
role_arn=arn:aws:iam::946406392255:role/Developer
region=eu-central-1
aws_access_key_id=AKIA.....
aws_secret_access_key=SA1P.....
```