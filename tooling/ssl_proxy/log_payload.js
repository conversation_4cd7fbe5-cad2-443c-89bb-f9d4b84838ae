function logPayload(req, res, next) {
  // Filter for specific URL
  if (req.url === '/user/update-device-info') {
    let requestBody = '';
    let originalWrite = res.write;
    let originalEnd = res.end;
    let responseBody = '';

    // Collect request payload
    req.on('data', chunk => {
      requestBody += chunk.toString();
    });

    // Intercept response write methods
    res.write = function (chunk) {
      responseBody += chunk.toString();
      originalWrite.apply(res, arguments);
    };

    // Intercept response end method
    res.end = function (chunk) {
      if (chunk) responseBody += chunk.toString();
      originalEnd.apply(res, arguments);

      // Log the payloads and status code
      console.log("Request headers:", req.headers);
      console.log('Request Payload:', requestBody);
      console.log('Response Payload:', responseBody);
      console.log('Status Code:', res.statusCode);
    };
  }

  next();
}

module.exports = logPayload;