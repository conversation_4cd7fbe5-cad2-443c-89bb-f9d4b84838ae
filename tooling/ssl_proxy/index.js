const https = require('https');
const fs = require('fs');
const httpProxy = require('http-proxy');
const logPayload = require('./log_payload');

// Read SSL certificate files
const sslOptions = {
    key: fs.readFileSync('/etc/letsencrypt/live/app-testing.numo-app.com/privkey.pem', 'utf8'),
    cert: fs.readFileSync('/etc/letsencrypt/live/app-testing.numo-app.com/fullchain.pem', 'utf8')
};

// Create a proxy server
const proxy = httpProxy.createProxyServer({
    changeOrigin: true,
});

// Create an HTTPS server
const server = https.createServer(sslOptions, (req, res) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);

    logPayload(req, res, () => {
        const hostname = req.headers.host;

        // Depending on the domain, proxy to different local ports
        switch (hostname) {
            case 'app-testing.numo-app.com':
                proxy.web(req, res, { target: 'http://127.0.0.1:9000' });
                break;
            case 'api-testing.numo-app.com':
                proxy.web(req, res, { target: 'https://api.server.ddev.site:8443' });
                break;
            default:
                res.writeHead(404, { 'Content-Type': 'text/plain' });
                res.end('Not found: ' + hostname);
        }
    });
});

// Listen on port 443
server.listen(443, () => {
    console.log('HTTPS proxy server listening on port 443');
});
