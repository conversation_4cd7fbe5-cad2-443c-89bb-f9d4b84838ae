### Login and get token
POST https://api.server.ddev.site:8443/auth/login
Accept: application/json
Content-Type: application/json

{
  "email": "f<PERSON>zoe<PERSON><PERSON>@gmail.com",
  "password": "12345678"
}

> {% client.global.set("auth_token", response.body.token) %}

### Request Device Information
GET https://api.server.ddev.site:8443/v1/deviceInfos
Accept: application/vnd.api+json
Content-Type: application/vnd.api+json
Authorization: Bearer 676|Ovh7VijD0OlnROUBIaiZAuyGDV3SanyaxuPDrnfN

#{% client.global.get("auth_token") %}