---
description: 
globs: 
alwaysApply: true
---
## Database Models

### Core Models

#### Users & Authentication
- `users`: Core user authentication table
  - Links to `persons` via `person_id`
  - Handles email verification, password resets, and 2FA
  - Uses UUIDs for IDs

#### Teams & Members
- `teams`: Team management
  - Basic team information (name, icon)
  - Uses UUIDs for IDs
- `team_members`: Team membership
  - Links users (`person_id`) to teams (`team_id`)
  - Includes role assignment (`team_role_id`)
  - Custom name field for team-specific display names

#### Roles & Permissions
- `team_roles`: Defines different roles within teams
- `team_permissions`: Available permissions
- `team_permission_team_role`: Many-to-many relationship between roles and permissions
- `team_member_team_role`: Many-to-many relationship between members and roles

#### Events & Scheduling
- `team_events`: Team events and matches
  - Supports different event types (matches, training, etc.)
  - Includes scheduling information (date, time)
  - Supports series events via `series_id` and `series_type`
  - Handles cancellations and responses
  - Includes match-specific fields for sports events

#### Absences & Attendance
- `absences`: Tracks member absences
- `absence_targets`: Links absences to specific events

#### Comments & Communication
- `comments`: Team communication system
- `tracked_notifications`: Notification tracking

### Key Relationships
- Users → Persons (1:1)
- Teams → Team Members (1:N)
- Team Members → Team Roles (N:M)
- Team Roles → Permissions (N:M)
- Teams → Events (1:N)
- Team Members → Absences (1:N)
- Events → Absences (1:N)

### Additional Features
- Device tracking (`device_info`)
- Registration management (`registration_allowlist`)
- Session management (`sessions`)
- Failed job tracking (`failed_jobs`)