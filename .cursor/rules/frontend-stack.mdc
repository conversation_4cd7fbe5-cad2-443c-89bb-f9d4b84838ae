---
description: 
globs: 
alwaysApply: true
---

# Frontend Stack Documentation

## Core Framework
- Built with Quasar Framework (Vue.js 3) and TypeScript
- Uses Vite as the build tool
- Key dependencies:
  - Vue 3.4+
  - Quasar 2.11+
  - Vue Router 4
  - Pinia for state management
  - Vue Query (TanStack) for data fetching
  - Spraypaint.js for JSON:API communication
  - Vue I18n for internationalization

## Project Structure
- `src/`: Main source code directory
  - `boot/`: Application initialization and boot files
  - `components/`: Reusable Vue components
  - `composables/`: Vue composition API hooks
  - `css/`: Global styles and SCSS files
  - `helpers/`: Utility functions and helpers
  - `i18n/`: Internationalization files
  - `layouts/`: Page layout components
  - `models/`: Spraypaint.js model definitions
  - `pages/`: Route components
  - `queries/`: Vue Query query definitions
  - `router/`: Vue Router configuration
  - `stores/`: Pinia store definitions
  - `types/`: TypeScript type definitions

## Key Features and Patterns

### State Management
- Pinia stores for global state
- Vue Query for server state management
- Persistent state using pinia-plugin-persistedstate
- Type-safe stores with TypeScript

### API Communication
- Spraypaint.js for JSON:API communication
- Axios for direct HTTP requests
- Vue Query for data fetching and caching
- Type-safe API models and queries

### Mobile Integration
- Capacitor for native mobile features
- Native plugins:
  - Push notifications
  - Local notifications
  - Device information
  - Clipboard
  - Storage
  - Browser
  - App lifecycle

### Development Tools
- ESLint for code linting
- Prettier for code formatting
- TypeScript for type safety
- Vue DevTools for debugging
- Cypress for testing
- Storybook for component documentation

## Development Guidelines
- Use TypeScript for all new code
- Follow Vue 3 Composition API patterns
- Implement proper error handling
- Use Vue Query for data fetching
- Maintain consistent component structure
- Follow Quasar design guidelines
- Write unit tests for critical components
- Use Cypress for E2E testing

## Build and Deployment
- Multiple build modes:
  - SPA (web)
  - PWA
  - Mobile (Capacitor)
- Environment configurations:
  - Development
  - Testing
  - Staging
  - Production
- Mobile app signing and deployment
- Automated testing in CI/CD

## Integration Points
- Backend JSON:API communication
- Mobile native features
- Push notifications
- Calendar integration
- Internationalization
- Sentry error tracking

- Quasar with Vue.js is the main framework for the frontend
- The backend provides a JSON:API that is talked to via Spraypaint.js (https://www.graphiti.dev/js/)
- Spraypaint models can be found at client/models
- The frontend is used in mobile apps as described in [mobile-stack.mdc](mdc:html/html/html/html/.cursor/rules/mobile-stack.mdc)