---
description: 
globs: 
alwaysApply: true
---
# Mobile Stack Documentation

## Core Framework
- Built on top of the Quasar/Vue.js frontend using Capacitor
- Capacitor version: 5.3.0+
- Native platforms:
  - Android (Java/Kotlin)
  - iOS (Swift)
- Key Capacitor plugins:
  - App lifecycle management
  - Push notifications
  - Local notifications
  - Device information
  - Clipboard
  - Storage
  - Native settings

## Project Structure
- `src-capacitor/`: Mobile-specific code
  - `android/`: Android native code and resources
  - `ios/`: iOS native code and resources
  - `www/`: Web assets for mobile app
  - Configuration files:
    - `capacitor.config.json`: Main Capacitor configuration
    - `update-capacitor-config.js`: Configuration update script

## Mobile-Specific Features

### Native Integration
- Deep linking support
- Push notification handling
- Local notification scheduling
- Device information access
- Native storage capabilities
- Clipboard integration
- App settings management

### Platform-Specific Customizations
- Android:
  - Custom splash screen implementation
  - Scale type configurations
  - Release signing configuration
- iOS:
  - Native UI customizations
  - Push notification capabilities
  - App lifecycle management

### Development and Testing
- Development modes:
  - Local development with SSL
  - Staging environment
  - Production environment
- Testing capabilities:
  - Native device testing
  - Emulator/Simulator testing
  - Push notification testing
  - Deep linking testing

## Build and Deployment

### Android
- Build process:
  - Debug builds for testing
  - Release builds with signing
  - Keystore management
- Deployment:
  - Internal testing
  - Google Play Store
  - Manual APK distribution

### iOS
- Build process:
  - Development builds
  - Ad-hoc distribution
  - App Store builds
- Deployment:
  - TestFlight for beta testing
  - App Store distribution
  - Enterprise distribution

## Integration Points
- Backend API communication
- Push notification services
- Deep linking handling
- Native device features
- App lifecycle management
- Storage synchronization

## Development Guidelines
- Maintain platform-specific code in respective directories
- Follow native platform guidelines for UI/UX
- Implement proper error handling for native features
- Test on both platforms regularly
- Keep Capacitor plugins updated
- Document native-specific implementations
- Follow platform-specific security guidelines
