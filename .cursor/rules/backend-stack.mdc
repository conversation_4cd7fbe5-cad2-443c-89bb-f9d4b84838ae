---
description: 
globs: 
alwaysApply: true
---
## Core Framework
- The backend is built using <PERSON><PERSON> (located in `server/` directory)
- Uses Laravel JSON:API implementation (https://laraveljsonapi.io/) for API development
- PHP 8.3+ required
- Key Laravel packages:
  - Laravel JSON:API (with custom sideposting fork)
  - Laravel Fortify for authentication
  - Laravel Sanctum for API tokens
  - Laravel Cashier for payments
  - Laravel Pennant for feature flags

## Key Components and Patterns

### JSON:API Implementation
- JSON:API specific classes are located at `server/app/JsonApi/V1/`
- Essential JSON:API classes to be aware of:
  - `CollectionQuery`: Handles query parameters and filtering
  - `Request`: Validates and processes incoming requests
  - `Resource`: Defines the API resource structure
  - `Schema`: Describes the data model and relationships
- Custom base classes:
  - `CommonResource`: Base class for all JSON:API resources
  - `CommonSchema`: Base class for all JSON:API schemas
  - `CommonResourceRequest`: Base class for all JSON:API requests

### API Controllers
- Primary API controllers are located at `server/app/Http/Controllers/Api/V1/`
- These controllers may contain extended lifecycle hooks for JSON:API requests
- While most endpoints follow JSON:API standards, some exceptions exist for practical reasons
- New endpoints should generally follow JSON:API standards unless there's a compelling reason not to

### Custom Directory Structure
- `app/Actions/`: Contains action classes for business logic
- `app/Services/`: Service classes for complex operations
- `app/Traits/`: Reusable trait implementations
- `app/Types/`: Custom type definitions
- `app/JsonApi/Filters/`: Custom JSON:API filters
- `app/JsonApi/Authorizers/`: Authorization logic for JSON:API
- `app/JsonApi/Fields/`: Custom field implementations

## Development Guidelines
- Prefer JSON:API endpoints for data access and manipulation
- Use standard Laravel controllers only when JSON:API implementation is impractical
- Maintain consistency in API response formats
- Document any deviations from JSON:API standards
- Follow PHPStan and PHP CS Fixer rules (see phpstan.neon and .php-cs-fixer.php)
- Use Laravel's built-in testing tools and JSON:API testing package

## Integration Points
- Frontend communicates via Spraypaint.js (https://www.graphiti.dev/js/)
- Mobile apps access the API through Capacitor integration
- Ensure API endpoints are compatible with both web and mobile clients
- Sentry integration for error tracking
- FCM (Firebase Cloud Messaging) for push notifications

## Development Tools
- PHPStan for static analysis
- PHP CS Fixer for code style
- Laravel Debugbar in development
- Clockwork for debugging
- DDEV for docker development