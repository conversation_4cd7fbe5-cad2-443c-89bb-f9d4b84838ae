{"version": "0.2.0", "configurations": [{"name": "Listen for Xdebug", "type": "php", "request": "launch", "hostname": "0.0.0.0", "port": 9003, "pathMappings": {"/var/www/html": "${workspaceFolder}"}, "preLaunchTask": "DDEV: Enable Xdebug", "postDebugTask": "DDEV: Disable Xdebug"}, {"name": "Debug Quasar - Chrome", "request": "launch", "type": "chrome", "url": "http://localhost:8080", "breakOnLoad": true, "sourceMaps": true, "sourceMapPathOverrides": {"webpack://clubmanager/./src/*": "${webRoot}/*"}, "webRoot": "${workspaceFolder}/client/src"}, {"name": "Debug Quasar - Firefox", "type": "firefox", "request": "launch", "reAttach": true, "url": "http://localhost:8080/", "webRoot": "${workspaceFolder}/client/src", "pathMappings": [{"url": "webpack://clubmanager/src", "path": "${workspaceFolder}/client/src"}, {"url": "webpack://src/", "path": "${webRoot}/"}]}]}